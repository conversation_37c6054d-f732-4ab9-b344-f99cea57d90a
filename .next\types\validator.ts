// This file is generated automatically by Next.js
// Do not edit this file manually
// This file validates that all pages and layouts export the correct types

import type { AppRoutes, LayoutRoutes, ParamMap } from "./routes.js"
import type { ResolvingMetadata, ResolvingViewport } from "next/dist/lib/metadata/types/metadata-interface.js"

type AppPageConfig<Route extends AppRoutes = AppRoutes> = {
  default: React.ComponentType<{ params: Promise<ParamMap[Route]> } & any> | ((props: { params: Promise<ParamMap[Route]> } & any) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type LayoutConfig<Route extends LayoutRoutes = LayoutRoutes> = {
  default: React.ComponentType<LayoutProps<Route>> | ((props: LayoutProps<Route>) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}


// Validate ..\..\src\app\alerts\page.tsx
{
  const handler = {} as typeof import("..\\..\\src\\app\\alerts\\page.js")
  handler satisfies AppPageConfig<"/alerts">
}

// Validate ..\..\src\app\analytics\page.tsx
{
  const handler = {} as typeof import("..\\..\\src\\app\\analytics\\page.js")
  handler satisfies AppPageConfig<"/analytics">
}

// Validate ..\..\src\app\page.tsx
{
  const handler = {} as typeof import("..\\..\\src\\app\\page.js")
  handler satisfies AppPageConfig<"/">
}

// Validate ..\..\src\app\settings\page.tsx
{
  const handler = {} as typeof import("..\\..\\src\\app\\settings\\page.js")
  handler satisfies AppPageConfig<"/settings">
}







// Validate ..\..\src\app\layout.tsx
{
  const handler = {} as typeof import("..\\..\\src\\app\\layout.js")
  handler satisfies LayoutConfig<"/">
}
