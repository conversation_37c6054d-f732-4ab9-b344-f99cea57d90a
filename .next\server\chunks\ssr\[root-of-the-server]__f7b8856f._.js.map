{"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_a71539c9.module.css [app-rsc] (css module)", "turbopack:///[next]/internal/font/google/geist_mono_8d43a2aa.module.css [app-rsc] (css module)", "turbopack:///[project]/src/app/providers.tsx/__nextjs-internal-proxy.mjs", "turbopack:///[project]/src/components/ui/sonner.tsx/__nextjs-internal-proxy.mjs", "turbopack:///[next]/internal/font/google/geist_a71539c9.js", "turbopack:///[next]/internal/font/google/geist_mono_8d43a2aa.js", "turbopack:///[project]/src/app/layout.tsx"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_a71539c9-module__T19VSG__className\",\n  \"variable\": \"geist_a71539c9-module__T19VSG__variable\",\n});\n", "__turbopack_context__.v({\n  \"className\": \"geist_mono_8d43a2aa-module__8Li5zG__className\",\n  \"variable\": \"geist_mono_8d43a2aa-module__8Li5zG__variable\",\n});\n", "// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Providers = registerClientReference(\n    function() { throw new Error(\"Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/providers.tsx\",\n    \"Providers\",\n);\n", "// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sonner.tsx\",\n    \"Toaster\",\n);\n", "import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist%22,%22arguments%22:[{%22variable%22:%22--font-geist-sans%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist', 'Geist Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n", "import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist_Mono%22,%22arguments%22:[{%22variable%22:%22--font-geist-mono%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist Mono', 'Geist Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n", "import type { <PERSON>ada<PERSON> } from \"next\";\nimport { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from \"next/font/google\";\nimport \"./globals.css\";\nimport { Providers } from \"./providers\";\nimport { Toaster } from \"@/components/ui/sonner\";\n\nconst geistSans = Geist({\n  variable: \"--font-geist-sans\",\n  subsets: [\"latin\"],\n});\n\nconst geistMono = Geist_Mono({\n  variable: \"--font-geist-mono\",\n  subsets: [\"latin\"],\n});\n\nexport const metadata: Metadata = {\n  title: \"Wildlife Guardian - Animal Poaching Alert System\",\n  description: \"Real-time wildlife monitoring and poaching detection system\",\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\" className=\"dark\">\n      <body\n        className={`${geistSans.variable} ${geistMono.variable} antialiased`}\n      >\n        <Providers>\n          {children}\n          <Toaster />\n        </Providers>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": "0BAAA,EAAA,CAAA,CAAA,CACA,UAAA,2CACA,SAAA,yCACA,cCHA,EAAA,CAAA,CAAA,CACA,UAAA,gDACA,SAAA,8CACA,oDCDO,IAAM,EAAY,CAAA,EAAA,AADzB,EAAA,CAAA,CAAA,OACyB,uBAAA,AAAuB,EAC5C,WAAa,MAAM,AAAI,MAAM,gOAAkO,EAC/P,sDACA,8DAHG,IAAM,EAAY,CAAA,EADzB,AACyB,EADzB,CAAA,CAAA,OACyB,uBAAuB,AAAvB,EACrB,WAAa,MAAM,AAAI,MAAM,gOAAkO,EAC/P,kCACA,uHCHG,IAAM,EAAU,CAAA,EAAA,AADvB,EAAA,CAAA,CAAA,OACuB,uBAAA,AAAuB,EAC1C,WAAa,MAAM,AAAI,MAAM,4NAA8N,EAC3P,6DACA,0DAHG,IAAM,EAAU,CAAA,EAAA,AADvB,EAAA,CAAA,CAAA,OACuB,uBAAA,AAAuB,EAC1C,WAAa,MAAM,AAAI,MAAM,4NAA8N,EAC3P,yCACA,4JCLJ,EAAA,EAAA,CAAA,CAAA,OACA,IAAM,EAAW,CACb,UAAW,EAAA,OAAS,CAAC,SAAS,CAC9B,MAAO,CACH,WAAY,4BACZ,UAAW,QAEf,CACJ,CAE0B,MAAM,CAA5B,EAAA,OAAS,CAAC,QAAQ,GAClB,EAAS,QAAQ,CAAG,EAAA,OAAS,CAAC,QAAA,AAAQ,ECX1C,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,IAAM,EAAW,CACb,UAAW,EAAA,OAAS,CAAC,SAAS,CAC9B,MAAO,CACH,WAAY,sCACZ,UAAW,QAEf,CACJ,CAE0B,MAAM,CAA5B,EAAA,OAAS,CAAC,QAAQ,GAClB,EAAS,QAAQ,CAAG,EAAA,OAAS,CAAC,QAAQ,AAAR,ECRlC,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAYO,IAAM,EAAqB,CAChC,MAAO,mDACP,YAAa,6DACf,EAEe,SAAS,EAAW,UACjC,CAAQ,CAGR,EACA,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,KAAK,KAAK,UAAU,gBACxB,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CACC,UAAW,CAAA,EFfJ,AEeO,EAAU,QAAQ,CAAC,CAAC,EAAE,ADf7B,ECeuC,QAAQ,CAAC,YAAY,CAAC,UAEpE,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,SAAS,CAAA,WACP,EACD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAO,CAAA,CAAA,SAKlB", "ignoreList": [0, 1, 2, 3, 4, 5]}