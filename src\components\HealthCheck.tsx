'use client';

import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  Database,
  Camera,
  Brain,
  Wifi,
  WifiOff
} from 'lucide-react';
import { apiService } from '@/services/api';
import { cn } from '@/lib/utils';

export function HealthCheck() {
  const { data: health, isLoading, error } = useQuery({
    queryKey: ['health-status'],
    queryFn: () => apiService.getHealthStatus(),
    refetchInterval: 15000, // Check every 15 seconds
    retry: 3,
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'up':
        return 'text-green-500';
      case 'degraded':
        return 'text-yellow-500';
      case 'unhealthy':
      case 'down':
        return 'text-red-500';
      default:
        return 'text-gray-500';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'up':
        return <CheckCircle className="h-4 w-4" />;
      case 'degraded':
        return <AlertCircle className="h-4 w-4" />;
      case 'unhealthy':
      case 'down':
        return <XCircle className="h-4 w-4" />;
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'up':
        return <Badge className="bg-green-500/10 text-green-500 border-green-500/20">Online</Badge>;
      case 'degraded':
        return <Badge className="bg-yellow-500/10 text-yellow-500 border-yellow-500/20">Degraded</Badge>;
      case 'unhealthy':
      case 'down':
        return <Badge className="bg-red-500/10 text-red-500 border-red-500/20">Offline</Badge>;
      default:
        return <Badge variant="secondary">Unknown</Badge>;
    }
  };

  if (error) {
    return (
      <Alert className="border-red-500/20 bg-red-500/10">
        <XCircle className="h-4 w-4 text-red-500" />
        <AlertDescription className="text-red-500">
          Unable to connect to backend services. Please check your connection.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {isLoading ? (
            <Wifi className="h-5 w-5 text-muted-foreground animate-pulse" />
          ) : error ? (
            <WifiOff className="h-5 w-5 text-red-500" />
          ) : (
            <div className={cn("h-5 w-5", getStatusColor(health?.status || 'unknown'))}>
              {getStatusIcon(health?.status || 'unknown')}
            </div>
          )}
          System Health
          {health && getStatusBadge(health.status)}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="text-sm text-muted-foreground">Checking system status...</div>
        ) : health ? (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center gap-2">
                <Database className={cn("h-4 w-4", getStatusColor(health.services.database))} />
                <span className="text-sm">Database</span>
                {getStatusBadge(health.services.database)}
              </div>
              
              <div className="flex items-center gap-2">
                <Camera className={cn("h-4 w-4", getStatusColor(health.services.camera))} />
                <span className="text-sm">Camera</span>
                {getStatusBadge(health.services.camera)}
              </div>
              
              <div className="flex items-center gap-2">
                <Brain className={cn("h-4 w-4", getStatusColor(health.services.ai_model))} />
                <span className="text-sm">AI Model</span>
                {getStatusBadge(health.services.ai_model)}
              </div>
            </div>
            
            <div className="text-xs text-muted-foreground">
              Last updated: {new Date(health.timestamp).toLocaleString()}
              {health.uptime && (
                <span className="ml-2">
                  • Uptime: {Math.floor(health.uptime / 3600)}h {Math.floor((health.uptime % 3600) / 60)}m
                </span>
              )}
            </div>
          </div>
        ) : (
          <div className="text-sm text-muted-foreground">No health data available</div>
        )}
      </CardContent>
    </Card>
  );
}
