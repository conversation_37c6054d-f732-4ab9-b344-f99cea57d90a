module.exports=[37927,a=>{"use strict";a.s(["QueryClientProvider",()=>f,"useQueryClient",()=>e]);var b=a.i(72131),c=a.i(87924),d=b.createContext(void 0),e=a=>{let c=b.useContext(d);if(a)return a;if(!c)throw Error("No QueryClient set, use QueryClientProvider to set one");return c},f=({client:a,children:e})=>(b.useEffect(()=>(a.mount(),()=>{a.unmount()}),[a]),(0,c.jsx)(d.Provider,{value:a,children:e}))},23292,a=>{"use strict";a.s(["Toaster",()=>r,"toast",()=>n]);var b=a.i(72131),c=a.i(35112);let d=Array(12).fill(0),e=({visible:a,className:c})=>b.default.createElement("div",{className:["sonner-loading-wrapper",c].filter(Boolean).join(" "),"data-visible":a},b.default.createElement("div",{className:"sonner-spinner"},d.map((a,c)=>b.default.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${c}`})))),f=b.default.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},b.default.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),g=b.default.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},b.default.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),h=b.default.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},b.default.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),i=b.default.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},b.default.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),j=b.default.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},b.default.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),b.default.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"})),k=1,l=new class{constructor(){this.subscribe=a=>(this.subscribers.push(a),()=>{let b=this.subscribers.indexOf(a);this.subscribers.splice(b,1)}),this.publish=a=>{this.subscribers.forEach(b=>b(a))},this.addToast=a=>{this.publish(a),this.toasts=[...this.toasts,a]},this.create=a=>{var b;let{message:c,...d}=a,e="number"==typeof(null==a?void 0:a.id)||(null==(b=a.id)?void 0:b.length)>0?a.id:k++,f=this.toasts.find(a=>a.id===e),g=void 0===a.dismissible||a.dismissible;return this.dismissedToasts.has(e)&&this.dismissedToasts.delete(e),f?this.toasts=this.toasts.map(b=>b.id===e?(this.publish({...b,...a,id:e,title:c}),{...b,...a,id:e,dismissible:g,title:c}):b):this.addToast({title:c,...d,dismissible:g,id:e}),e},this.dismiss=a=>(a?(this.dismissedToasts.add(a),requestAnimationFrame(()=>this.subscribers.forEach(b=>b({id:a,dismiss:!0})))):this.toasts.forEach(a=>{this.subscribers.forEach(b=>b({id:a.id,dismiss:!0}))}),a),this.message=(a,b)=>this.create({...b,message:a}),this.error=(a,b)=>this.create({...b,message:a,type:"error"}),this.success=(a,b)=>this.create({...b,type:"success",message:a}),this.info=(a,b)=>this.create({...b,type:"info",message:a}),this.warning=(a,b)=>this.create({...b,type:"warning",message:a}),this.loading=(a,b)=>this.create({...b,type:"loading",message:a}),this.promise=(a,c)=>{let d,e;if(!c)return;void 0!==c.loading&&(e=this.create({...c,promise:a,type:"loading",message:c.loading,description:"function"!=typeof c.description?c.description:void 0}));let f=Promise.resolve(a instanceof Function?a():a),g=void 0!==e,h=f.then(async a=>{if(d=["resolve",a],b.default.isValidElement(a))g=!1,this.create({id:e,type:"default",message:a});else if(m(a)&&!a.ok){g=!1;let d="function"==typeof c.error?await c.error(`HTTP error! status: ${a.status}`):c.error,f="function"==typeof c.description?await c.description(`HTTP error! status: ${a.status}`):c.description,h="object"!=typeof d||b.default.isValidElement(d)?{message:d}:d;this.create({id:e,type:"error",description:f,...h})}else if(a instanceof Error){g=!1;let d="function"==typeof c.error?await c.error(a):c.error,f="function"==typeof c.description?await c.description(a):c.description,h="object"!=typeof d||b.default.isValidElement(d)?{message:d}:d;this.create({id:e,type:"error",description:f,...h})}else if(void 0!==c.success){g=!1;let d="function"==typeof c.success?await c.success(a):c.success,f="function"==typeof c.description?await c.description(a):c.description,h="object"!=typeof d||b.default.isValidElement(d)?{message:d}:d;this.create({id:e,type:"success",description:f,...h})}}).catch(async a=>{if(d=["reject",a],void 0!==c.error){g=!1;let d="function"==typeof c.error?await c.error(a):c.error,f="function"==typeof c.description?await c.description(a):c.description,h="object"!=typeof d||b.default.isValidElement(d)?{message:d}:d;this.create({id:e,type:"error",description:f,...h})}}).finally(()=>{g&&(this.dismiss(e),e=void 0),null==c.finally||c.finally.call(c)}),i=()=>new Promise((a,b)=>h.then(()=>"reject"===d[0]?b(d[1]):a(d[1])).catch(b));return"string"!=typeof e&&"number"!=typeof e?{unwrap:i}:Object.assign(e,{unwrap:i})},this.custom=(a,b)=>{let c=(null==b?void 0:b.id)||k++;return this.create({jsx:a(c),id:c,...b}),c},this.getActiveToasts=()=>this.toasts.filter(a=>!this.dismissedToasts.has(a.id)),this.subscribers=[],this.toasts=[],this.dismissedToasts=new Set}},m=a=>a&&"object"==typeof a&&"ok"in a&&"boolean"==typeof a.ok&&"status"in a&&"number"==typeof a.status,n=Object.assign((a,b)=>{let c=(null==b?void 0:b.id)||k++;return l.addToast({title:a,...b,id:c}),c},{success:l.success,info:l.info,warning:l.warning,error:l.error,custom:l.custom,message:l.message,promise:l.promise,dismiss:l.dismiss,loading:l.loading},{getHistory:()=>l.toasts,getToasts:()=>l.getActiveToasts()});function o(a){return void 0!==a.label}function p(...a){return a.filter(Boolean).join(" ")}!function(a){if(!a||"undefined"==typeof document)return;let b=document.head||document.getElementsByTagName("head")[0],c=document.createElement("style");c.type="text/css",b.appendChild(c),c.styleSheet?c.styleSheet.cssText=a:c.appendChild(document.createTextNode(a))}("[data-sonner-toaster][dir=ltr],html[dir=ltr]{--toast-icon-margin-start:-3px;--toast-icon-margin-end:4px;--toast-svg-margin-start:-1px;--toast-svg-margin-end:0px;--toast-button-margin-start:auto;--toast-button-margin-end:0;--toast-close-button-start:0;--toast-close-button-end:unset;--toast-close-button-transform:translate(-35%, -35%)}[data-sonner-toaster][dir=rtl],html[dir=rtl]{--toast-icon-margin-start:4px;--toast-icon-margin-end:-3px;--toast-svg-margin-start:0px;--toast-svg-margin-end:-1px;--toast-button-margin-start:0;--toast-button-margin-end:auto;--toast-close-button-start:unset;--toast-close-button-end:0;--toast-close-button-transform:translate(35%, -35%)}[data-sonner-toaster]{position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1:hsl(0, 0%, 99%);--gray2:hsl(0, 0%, 97.3%);--gray3:hsl(0, 0%, 95.1%);--gray4:hsl(0, 0%, 93%);--gray5:hsl(0, 0%, 90.9%);--gray6:hsl(0, 0%, 88.7%);--gray7:hsl(0, 0%, 85.8%);--gray8:hsl(0, 0%, 78%);--gray9:hsl(0, 0%, 56.1%);--gray10:hsl(0, 0%, 52.3%);--gray11:hsl(0, 0%, 43.5%);--gray12:hsl(0, 0%, 9%);--border-radius:8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:0;z-index:999999999;transition:transform .4s ease}@media (hover:none) and (pointer:coarse){[data-sonner-toaster][data-lifted=true]{transform:none}}[data-sonner-toaster][data-x-position=right]{right:var(--offset-right)}[data-sonner-toaster][data-x-position=left]{left:var(--offset-left)}[data-sonner-toaster][data-x-position=center]{left:50%;transform:translateX(-50%)}[data-sonner-toaster][data-y-position=top]{top:var(--offset-top)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--offset-bottom)}[data-sonner-toast]{--y:translateY(100%);--lift-amount:calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:0;overflow-wrap:anywhere}[data-sonner-toast][data-styled=true]{padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px rgba(0,0,0,.1);width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}[data-sonner-toast]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-y-position=top]{top:0;--y:translateY(-100%);--lift:1;--lift-amount:calc(1 * var(--gap))}[data-sonner-toast][data-y-position=bottom]{bottom:0;--y:translateY(100%);--lift:-1;--lift-amount:calc(var(--lift) * var(--gap))}[data-sonner-toast][data-styled=true] [data-description]{font-weight:400;line-height:1.4;color:#3f3f3f}[data-rich-colors=true][data-sonner-toast][data-styled=true] [data-description]{color:inherit}[data-sonner-toaster][data-sonner-theme=dark] [data-description]{color:#e8e8e8}[data-sonner-toast][data-styled=true] [data-title]{font-weight:500;line-height:1.5;color:inherit}[data-sonner-toast][data-styled=true] [data-icon]{display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}[data-sonner-toast][data-promise=true] [data-icon]>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}[data-sonner-toast][data-styled=true] [data-icon]>*{flex-shrink:0}[data-sonner-toast][data-styled=true] [data-icon] svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}[data-sonner-toast][data-styled=true] [data-content]{display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;font-weight:500;cursor:pointer;outline:0;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}[data-sonner-toast][data-styled=true] [data-button]:focus-visible{box-shadow:0 0 0 2px rgba(0,0,0,.4)}[data-sonner-toast][data-styled=true] [data-button]:first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}[data-sonner-toast][data-styled=true] [data-cancel]{color:var(--normal-text);background:rgba(0,0,0,.08)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-styled=true] [data-cancel]{background:rgba(255,255,255,.3)}[data-sonner-toast][data-styled=true] [data-close-button]{position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);background:var(--normal-bg);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast][data-styled=true] [data-close-button]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-styled=true] [data-disabled=true]{cursor:not-allowed}[data-sonner-toast][data-styled=true]:hover [data-close-button]:hover{background:var(--gray2);border-color:var(--gray5)}[data-sonner-toast][data-swiping=true]::before{content:'';position:absolute;left:-100%;right:-100%;height:100%;z-index:-1}[data-sonner-toast][data-y-position=top][data-swiping=true]::before{bottom:50%;transform:scaleY(3) translateY(50%)}[data-sonner-toast][data-y-position=bottom][data-swiping=true]::before{top:50%;transform:scaleY(3) translateY(-50%)}[data-sonner-toast][data-swiping=false][data-removed=true]::before{content:'';position:absolute;inset:0;transform:scaleY(2)}[data-sonner-toast][data-expanded=true]::after{content:'';position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}[data-sonner-toast][data-mounted=true]{--y:translateY(0);opacity:1}[data-sonner-toast][data-expanded=false][data-front=false]{--scale:var(--toasts-before) * 0.05 + 1;--y:translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}[data-sonner-toast]>*{transition:opacity .4s}[data-sonner-toast][data-x-position=right]{right:0}[data-sonner-toast][data-x-position=left]{left:0}[data-sonner-toast][data-expanded=false][data-front=false][data-styled=true]>*{opacity:0}[data-sonner-toast][data-visible=false]{opacity:0;pointer-events:none}[data-sonner-toast][data-mounted=true][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}[data-sonner-toast][data-removed=true][data-front=true][data-swipe-out=false]{--y:translateY(calc(var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=false]{--y:translateY(40%);opacity:0;transition:transform .5s,opacity .2s}[data-sonner-toast][data-removed=true][data-front=false]::before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y,0)) translateX(var(--swipe-amount-x,0));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width:600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-sonner-theme=light]{--normal-bg:#fff;--normal-border:var(--gray4);--normal-text:var(--gray12);--success-bg:hsl(143, 85%, 96%);--success-border:hsl(145, 92%, 87%);--success-text:hsl(140, 100%, 27%);--info-bg:hsl(208, 100%, 97%);--info-border:hsl(221, 91%, 93%);--info-text:hsl(210, 92%, 45%);--warning-bg:hsl(49, 100%, 97%);--warning-border:hsl(49, 91%, 84%);--warning-text:hsl(31, 92%, 45%);--error-bg:hsl(359, 100%, 97%);--error-border:hsl(359, 100%, 94%);--error-text:hsl(360, 100%, 45%)}[data-sonner-toaster][data-sonner-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg:#000;--normal-border:hsl(0, 0%, 20%);--normal-text:var(--gray1)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg:#fff;--normal-border:var(--gray3);--normal-text:var(--gray12)}[data-sonner-toaster][data-sonner-theme=dark]{--normal-bg:#000;--normal-bg-hover:hsl(0, 0%, 12%);--normal-border:hsl(0, 0%, 20%);--normal-border-hover:hsl(0, 0%, 25%);--normal-text:var(--gray1);--success-bg:hsl(150, 100%, 6%);--success-border:hsl(147, 100%, 12%);--success-text:hsl(150, 86%, 65%);--info-bg:hsl(215, 100%, 6%);--info-border:hsl(223, 43%, 17%);--info-text:hsl(216, 87%, 65%);--warning-bg:hsl(64, 100%, 6%);--warning-border:hsl(60, 100%, 9%);--warning-text:hsl(46, 87%, 65%);--error-bg:hsl(358, 76%, 10%);--error-border:hsl(357, 89%, 16%);--error-text:hsl(358, 100%, 81%)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size:16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:first-child{animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}100%{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}100%{opacity:.15}}@media (prefers-reduced-motion){.sonner-loading-bar,[data-sonner-toast],[data-sonner-toast]>*{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}");let q=a=>{var c,d,k,l,m,n,q,r,s,t,u;let{invert:v,toast:w,unstyled:x,interacting:y,setHeights:z,visibleToasts:A,heights:B,index:C,toasts:D,expanded:E,removeToast:F,defaultRichColors:G,closeButton:H,style:I,cancelButtonStyle:J,actionButtonStyle:K,className:L="",descriptionClassName:M="",duration:N,position:O,gap:P,expandByDefault:Q,classNames:R,icons:S,closeButtonAriaLabel:T="Close toast"}=a,[U,V]=b.default.useState(null),[W,X]=b.default.useState(null),[Y,Z]=b.default.useState(!1),[$,_]=b.default.useState(!1),[aa,ab]=b.default.useState(!1),[ac,ad]=b.default.useState(!1),[ae,af]=b.default.useState(!1),[ag,ah]=b.default.useState(0),[ai,aj]=b.default.useState(0),ak=b.default.useRef(w.duration||N||4e3),al=b.default.useRef(null),am=b.default.useRef(null),an=0===C,ao=C+1<=A,ap=w.type,aq=!1!==w.dismissible,ar=w.className||"",as=w.descriptionClassName||"",at=b.default.useMemo(()=>B.findIndex(a=>a.toastId===w.id)||0,[B,w.id]),au=b.default.useMemo(()=>{var a;return null!=(a=w.closeButton)?a:H},[w.closeButton,H]),av=b.default.useMemo(()=>w.duration||N||4e3,[w.duration,N]),aw=b.default.useRef(0),ax=b.default.useRef(0),ay=b.default.useRef(0),az=b.default.useRef(null),[aA,aB]=O.split("-"),aC=b.default.useMemo(()=>B.reduce((a,b,c)=>c>=at?a:a+b.height,0),[B,at]),aD=(()=>{let[a,c]=b.default.useState(document.hidden);return b.default.useEffect(()=>{let a=()=>{c(document.hidden)};return document.addEventListener("visibilitychange",a),()=>window.removeEventListener("visibilitychange",a)},[]),a})(),aE=w.invert||v,aF="loading"===ap;ax.current=b.default.useMemo(()=>at*P+aC,[at,aC]),b.default.useEffect(()=>{ak.current=av},[av]),b.default.useEffect(()=>{Z(!0)},[]),b.default.useEffect(()=>{let a=am.current;if(a){let b=a.getBoundingClientRect().height;return aj(b),z(a=>[{toastId:w.id,height:b,position:w.position},...a]),()=>z(a=>a.filter(a=>a.toastId!==w.id))}},[z,w.id]),b.default.useLayoutEffect(()=>{if(!Y)return;let a=am.current,b=a.style.height;a.style.height="auto";let c=a.getBoundingClientRect().height;a.style.height=b,aj(c),z(a=>a.find(a=>a.toastId===w.id)?a.map(a=>a.toastId===w.id?{...a,height:c}:a):[{toastId:w.id,height:c,position:w.position},...a])},[Y,w.title,w.description,z,w.id,w.jsx,w.action,w.cancel]);let aG=b.default.useCallback(()=>{_(!0),ah(ax.current),z(a=>a.filter(a=>a.toastId!==w.id)),setTimeout(()=>{F(w)},200)},[w,F,z,ax]);b.default.useEffect(()=>{let a;if((!w.promise||"loading"!==ap)&&w.duration!==1/0&&"loading"!==w.type)return E||y||aD?(()=>{if(ay.current<aw.current){let a=new Date().getTime()-aw.current;ak.current=ak.current-a}ay.current=new Date().getTime()})():ak.current!==1/0&&(aw.current=new Date().getTime(),a=setTimeout(()=>{null==w.onAutoClose||w.onAutoClose.call(w,w),aG()},ak.current)),()=>clearTimeout(a)},[E,y,w,ap,aD,aG]),b.default.useEffect(()=>{w.delete&&(aG(),null==w.onDismiss||w.onDismiss.call(w,w))},[aG,w.delete]);let aH=w.icon||(null==S?void 0:S[ap])||(a=>{switch(a){case"success":return f;case"info":return h;case"warning":return g;case"error":return i;default:return null}})(ap);return b.default.createElement("li",{tabIndex:0,ref:am,className:p(L,ar,null==R?void 0:R.toast,null==w||null==(c=w.classNames)?void 0:c.toast,null==R?void 0:R.default,null==R?void 0:R[ap],null==w||null==(d=w.classNames)?void 0:d[ap]),"data-sonner-toast":"","data-rich-colors":null!=(t=w.richColors)?t:G,"data-styled":!(w.jsx||w.unstyled||x),"data-mounted":Y,"data-promise":!!w.promise,"data-swiped":ae,"data-removed":$,"data-visible":ao,"data-y-position":aA,"data-x-position":aB,"data-index":C,"data-front":an,"data-swiping":aa,"data-dismissible":aq,"data-type":ap,"data-invert":aE,"data-swipe-out":ac,"data-swipe-direction":W,"data-expanded":!!(E||Q&&Y),"data-testid":w.testId,style:{"--index":C,"--toasts-before":C,"--z-index":D.length-C,"--offset":`${$?ag:ax.current}px`,"--initial-height":Q?"auto":`${ai}px`,...I,...w.style},onDragEnd:()=>{ab(!1),V(null),az.current=null},onPointerDown:a=>{2!==a.button&&!aF&&aq&&(al.current=new Date,ah(ax.current),a.target.setPointerCapture(a.pointerId),"BUTTON"!==a.target.tagName&&(ab(!0),az.current={x:a.clientX,y:a.clientY}))},onPointerUp:()=>{var a,b,c,d,e;if(ac||!aq)return;az.current=null;let f=Number((null==(a=am.current)?void 0:a.style.getPropertyValue("--swipe-amount-x").replace("px",""))||0),g=Number((null==(b=am.current)?void 0:b.style.getPropertyValue("--swipe-amount-y").replace("px",""))||0),h=new Date().getTime()-(null==(c=al.current)?void 0:c.getTime()),i="x"===U?f:g,j=Math.abs(i)/h;if(Math.abs(i)>=45||j>.11){ah(ax.current),null==w.onDismiss||w.onDismiss.call(w,w),"x"===U?X(f>0?"right":"left"):X(g>0?"down":"up"),aG(),ad(!0);return}null==(d=am.current)||d.style.setProperty("--swipe-amount-x","0px"),null==(e=am.current)||e.style.setProperty("--swipe-amount-y","0px"),af(!1),ab(!1),V(null)},onPointerMove:b=>{var c,d,e,f;if(!az.current||!aq||(null==(c=window.getSelection())?void 0:c.toString().length)>0)return;let g=b.clientY-az.current.y,h=b.clientX-az.current.x,i=null!=(f=a.swipeDirections)?f:function(a){let[b,c]=a.split("-"),d=[];return b&&d.push(b),c&&d.push(c),d}(O);!U&&(Math.abs(h)>1||Math.abs(g)>1)&&V(Math.abs(h)>Math.abs(g)?"x":"y");let j={x:0,y:0},k=a=>1/(1.5+Math.abs(a)/20);if("y"===U){if(i.includes("top")||i.includes("bottom"))if(i.includes("top")&&g<0||i.includes("bottom")&&g>0)j.y=g;else{let a=g*k(g);j.y=Math.abs(a)<Math.abs(g)?a:g}}else if("x"===U&&(i.includes("left")||i.includes("right")))if(i.includes("left")&&h<0||i.includes("right")&&h>0)j.x=h;else{let a=h*k(h);j.x=Math.abs(a)<Math.abs(h)?a:h}(Math.abs(j.x)>0||Math.abs(j.y)>0)&&af(!0),null==(d=am.current)||d.style.setProperty("--swipe-amount-x",`${j.x}px`),null==(e=am.current)||e.style.setProperty("--swipe-amount-y",`${j.y}px`)}},au&&!w.jsx&&"loading"!==ap?b.default.createElement("button",{"aria-label":T,"data-disabled":aF,"data-close-button":!0,onClick:aF||!aq?()=>{}:()=>{aG(),null==w.onDismiss||w.onDismiss.call(w,w)},className:p(null==R?void 0:R.closeButton,null==w||null==(k=w.classNames)?void 0:k.closeButton)},null!=(u=null==S?void 0:S.close)?u:j):null,(ap||w.icon||w.promise)&&null!==w.icon&&((null==S?void 0:S[ap])!==null||w.icon)?b.default.createElement("div",{"data-icon":"",className:p(null==R?void 0:R.icon,null==w||null==(l=w.classNames)?void 0:l.icon)},w.promise||"loading"===w.type&&!w.icon?w.icon||function(){var a,c;return(null==S?void 0:S.loading)?b.default.createElement("div",{className:p(null==R?void 0:R.loader,null==w||null==(c=w.classNames)?void 0:c.loader,"sonner-loader"),"data-visible":"loading"===ap},S.loading):b.default.createElement(e,{className:p(null==R?void 0:R.loader,null==w||null==(a=w.classNames)?void 0:a.loader),visible:"loading"===ap})}():null,"loading"!==w.type?aH:null):null,b.default.createElement("div",{"data-content":"",className:p(null==R?void 0:R.content,null==w||null==(m=w.classNames)?void 0:m.content)},b.default.createElement("div",{"data-title":"",className:p(null==R?void 0:R.title,null==w||null==(n=w.classNames)?void 0:n.title)},w.jsx?w.jsx:"function"==typeof w.title?w.title():w.title),w.description?b.default.createElement("div",{"data-description":"",className:p(M,as,null==R?void 0:R.description,null==w||null==(q=w.classNames)?void 0:q.description)},"function"==typeof w.description?w.description():w.description):null),b.default.isValidElement(w.cancel)?w.cancel:w.cancel&&o(w.cancel)?b.default.createElement("button",{"data-button":!0,"data-cancel":!0,style:w.cancelButtonStyle||J,onClick:a=>{o(w.cancel)&&aq&&(null==w.cancel.onClick||w.cancel.onClick.call(w.cancel,a),aG())},className:p(null==R?void 0:R.cancelButton,null==w||null==(r=w.classNames)?void 0:r.cancelButton)},w.cancel.label):null,b.default.isValidElement(w.action)?w.action:w.action&&o(w.action)?b.default.createElement("button",{"data-button":!0,"data-action":!0,style:w.actionButtonStyle||K,onClick:a=>{o(w.action)&&(null==w.action.onClick||w.action.onClick.call(w.action,a),a.defaultPrevented||aG())},className:p(null==R?void 0:R.actionButton,null==w||null==(s=w.classNames)?void 0:s.actionButton)},w.action.label):null)},r=b.default.forwardRef(function(a,d){let{id:e,invert:f,position:g="bottom-right",hotkey:h=["altKey","KeyT"],expand:i,closeButton:j,className:k,offset:m,mobileOffset:n,theme:o="light",richColors:p,duration:r,style:s,visibleToasts:t=3,toastOptions:u,dir:v="ltr",gap:w=14,icons:x,containerAriaLabel:y="Notifications"}=a,[z,A]=b.default.useState([]),B=b.default.useMemo(()=>e?z.filter(a=>a.toasterId===e):z.filter(a=>!a.toasterId),[z,e]),C=b.default.useMemo(()=>Array.from(new Set([g].concat(B.filter(a=>a.position).map(a=>a.position)))),[B,g]),[D,E]=b.default.useState([]),[F,G]=b.default.useState(!1),[H,I]=b.default.useState(!1),[J,K]=b.default.useState("system"!==o?o:"light"),L=b.default.useRef(null),M=h.join("+").replace(/Key/g,"").replace(/Digit/g,""),N=b.default.useRef(null),O=b.default.useRef(!1),P=b.default.useCallback(a=>{A(b=>{var c;return(null==(c=b.find(b=>b.id===a.id))?void 0:c.delete)||l.dismiss(a.id),b.filter(({id:b})=>b!==a.id)})},[]);return b.default.useEffect(()=>l.subscribe(a=>{if(a.dismiss)return void requestAnimationFrame(()=>{A(b=>b.map(b=>b.id===a.id?{...b,delete:!0}:b))});setTimeout(()=>{c.default.flushSync(()=>{A(b=>{let c=b.findIndex(b=>b.id===a.id);return -1!==c?[...b.slice(0,c),{...b[c],...a},...b.slice(c+1)]:[a,...b]})})})}),[z]),b.default.useEffect(()=>{if("system"!==o)return void K(o);"system"===o&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?K("dark"):K("light"))},[o]),b.default.useEffect(()=>{z.length<=1&&G(!1)},[z]),b.default.useEffect(()=>{let a=a=>{var b,c;h.every(b=>a[b]||a.code===b)&&(G(!0),null==(c=L.current)||c.focus()),"Escape"===a.code&&(document.activeElement===L.current||(null==(b=L.current)?void 0:b.contains(document.activeElement)))&&G(!1)};return document.addEventListener("keydown",a),()=>document.removeEventListener("keydown",a)},[h]),b.default.useEffect(()=>{if(L.current)return()=>{N.current&&(N.current.focus({preventScroll:!0}),N.current=null,O.current=!1)}},[L.current]),b.default.createElement("section",{ref:d,"aria-label":`${y} ${M}`,tabIndex:-1,"aria-live":"polite","aria-relevant":"additions text","aria-atomic":"false",suppressHydrationWarning:!0},C.map((c,d)=>{var e;let[g,h]=c.split("-");return B.length?b.default.createElement("ol",{key:c,dir:"auto"===v?"ltr":v,tabIndex:-1,ref:L,className:k,"data-sonner-toaster":!0,"data-sonner-theme":J,"data-y-position":g,"data-x-position":h,style:{"--front-toast-height":`${(null==(e=D[0])?void 0:e.height)||0}px`,"--width":"356px","--gap":`${w}px`,...s,...function(a,b){let c={};return[a,b].forEach((a,b)=>{let d=1===b,e=d?"--mobile-offset":"--offset",f=d?"16px":"24px";function g(a){["top","right","bottom","left"].forEach(b=>{c[`${e}-${b}`]="number"==typeof a?`${a}px`:a})}"number"==typeof a||"string"==typeof a?g(a):"object"==typeof a?["top","right","bottom","left"].forEach(b=>{void 0===a[b]?c[`${e}-${b}`]=f:c[`${e}-${b}`]="number"==typeof a[b]?`${a[b]}px`:a[b]}):g(f)}),c}(m,n)},onBlur:a=>{O.current&&!a.currentTarget.contains(a.relatedTarget)&&(O.current=!1,N.current&&(N.current.focus({preventScroll:!0}),N.current=null))},onFocus:a=>{!(a.target instanceof HTMLElement&&"false"===a.target.dataset.dismissible)&&(O.current||(O.current=!0,N.current=a.relatedTarget))},onMouseEnter:()=>G(!0),onMouseMove:()=>G(!0),onMouseLeave:()=>{H||G(!1)},onDragEnd:()=>G(!1),onPointerDown:a=>{a.target instanceof HTMLElement&&"false"===a.target.dataset.dismissible||I(!0)},onPointerUp:()=>I(!1)},B.filter(a=>!a.position&&0===d||a.position===c).map((d,e)=>{var g,h;return b.default.createElement(q,{key:d.id,icons:x,index:e,toast:d,defaultRichColors:p,duration:null!=(g=null==u?void 0:u.duration)?g:r,className:null==u?void 0:u.className,descriptionClassName:null==u?void 0:u.descriptionClassName,invert:f,visibleToasts:t,closeButton:null!=(h=null==u?void 0:u.closeButton)?h:j,interacting:H,position:c,style:null==u?void 0:u.style,unstyled:null==u?void 0:u.unstyled,classNames:null==u?void 0:u.classNames,cancelButtonStyle:null==u?void 0:u.cancelButtonStyle,actionButtonStyle:null==u?void 0:u.actionButtonStyle,closeButtonAriaLabel:null==u?void 0:u.closeButtonAriaLabel,removeToast:P,toasts:B.filter(a=>a.position==d.position),heights:D.filter(a=>a.position==d.position),setHeights:E,expandByDefault:i,gap:w,expanded:F,swipeDirections:a.swipeDirections})})):null}))})},84391,a=>{"use strict";a.s(["apiService",()=>c]);let b="http://localhost:8000",c=new class{async request(a,c){let d=`${b}${a}`;try{let a=await fetch(d,{headers:{"Content-Type":"application/json",...c?.headers},...c});if(!a.ok)throw Error(`HTTP error! status: ${a.status}`);return await a.json()}catch(b){throw console.error(`API request failed for ${a}:`,b),b}}async getHealthStatus(){return this.request("/health")}async getAlerts(a){let b=a?`?limit=${a}`:"";return this.request(`/alerts${b}`)}async getAlert(a){return this.request(`/alerts/${a}`)}async updateAlertStatus(a,b){return this.request(`/alerts/${a}`,{method:"PATCH",body:JSON.stringify({status:b})})}async getAlertStats(){return this.request("/alerts/stats")}getCameraStreamUrl(){return`${b}/stream`}getWebSocketUrl(){return"ws://localhost:8000/alerts/stream"}}},58338,a=>{"use strict";a.s(["WebSocketProvider",()=>h,"useWebSocketContext",()=>i],58338);var b=a.i(87924),c=a.i(72131),d=a.i(37927),e=a.i(23292),f=a.i(84391);let g=(0,c.createContext)(void 0);function h({children:a}){let h=function(){let[a,b]=(0,c.useState)(!1),[g,h]=(0,c.useState)("disconnected"),i=(0,c.useRef)(null),j=(0,d.useQueryClient)(),k=(0,c.useRef)(null),l=(0,c.useRef)(0),m=()=>{if(i.current?.readyState!==WebSocket.OPEN)try{h("connecting");let a=f.apiService.getWebSocketUrl();i.current=new WebSocket(a),i.current.onopen=()=>{console.log("WebSocket connected"),b(!0),h("connected"),l.current=0,e.toast.success("Real-time alerts connected",{duration:2e3})},i.current.onmessage=a=>{try{let b=JSON.parse(a.data);p(b)}catch(a){console.error("Failed to parse WebSocket message:",a)}},i.current.onclose=a=>{console.log("WebSocket disconnected:",a.code,a.reason),b(!1),h("disconnected"),1e3!==a.code&&l.current<5&&o()},i.current.onerror=a=>{console.error("WebSocket error:",a),h("error"),e.toast.error("Real-time connection error",{description:"Attempting to reconnect...",duration:3e3})}}catch(a){console.error("Failed to create WebSocket connection:",a),h("error")}},n=()=>{k.current&&(clearTimeout(k.current),k.current=null),i.current&&(i.current.close(1e3,"Manual disconnect"),i.current=null),b(!1),h("disconnected")},o=()=>{k.current&&clearTimeout(k.current);let a=Math.min(1e3*Math.pow(2,l.current),3e4);l.current++,console.log(`Scheduling reconnect attempt ${l.current} in ${a}ms`),k.current=setTimeout(()=>{l.current<=5?m():e.toast.error("Failed to reconnect to real-time alerts",{description:"Please refresh the page to try again.",duration:5e3})},a)},p=a=>{switch(a.type){case"new_alert":q(a.data);break;case"status_update":r(a.data);break;case"system_message":s(a.data);break;default:console.log("Unknown message type:",a.type)}},q=a=>{j.invalidateQueries({queryKey:["alerts"]}),j.invalidateQueries({queryKey:["alert-stats"]}),e.toast.error(`New Alert: ${a.objects.join(", ")} detected`,{description:`Confidence: ${Math.round(100*a.confidence)}% • ${new Date(a.timestamp).toLocaleTimeString()}`,duration:5e3,action:{label:"View",onClick:()=>{window.location.href="/alerts"}}})},r=a=>{j.invalidateQueries({queryKey:["health-status"]}),e.toast.info("System status updated",{duration:2e3})},s=a=>{e.toast.info(a.message||"System notification",{duration:3e3})};return(0,c.useEffect)(()=>(m(),()=>{n()}),[]),{isConnected:a,connectionStatus:g,connect:m,disconnect:n}}();return(0,b.jsx)(g.Provider,{value:h,children:a})}function i(){let a=(0,c.useContext)(g);if(void 0===a)throw Error("useWebSocketContext must be used within a WebSocketProvider");return a}}];

//# sourceMappingURL=_2aa02c2b._.js.map