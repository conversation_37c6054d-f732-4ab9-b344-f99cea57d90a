(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,12598,t=>{"use strict";t.s(["QueryClientProvider",()=>n,"useQueryClient",()=>o]);var e=t.i(71645),a=t.i(43476),r=e.createContext(void 0),o=t=>{let a=e.useContext(r);if(t)return t;if(!a)throw Error("No QueryClient set, use QueryClientProvider to set one");return a},n=t=>{let{client:o,children:n}=t;return e.useEffect(()=>(o.mount(),()=>{o.unmount()}),[o]),(0,a.jsx)(r.Provider,{value:o,children:n})}},46696,t=>{"use strict";t.s(["Toaster",()=>v,"toast",()=>m]);var e=t.i(71645),a=t.i(74080);let r=Array(12).fill(0),o=t=>{let{visible:a,className:o}=t;return e.default.createElement("div",{className:["sonner-loading-wrapper",o].filter(Boolean).join(" "),"data-visible":a},e.default.createElement("div",{className:"sonner-spinner"},r.map((t,a)=>e.default.createElement("div",{className:"sonner-loading-bar",key:"spinner-bar-".concat(a)}))))},n=e.default.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},e.default.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),s=e.default.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},e.default.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),i=e.default.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},e.default.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),l=e.default.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},e.default.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),d=e.default.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},e.default.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),e.default.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"})),c=1,u=new class{constructor(){this.subscribe=t=>(this.subscribers.push(t),()=>{let e=this.subscribers.indexOf(t);this.subscribers.splice(e,1)}),this.publish=t=>{this.subscribers.forEach(e=>e(t))},this.addToast=t=>{this.publish(t),this.toasts=[...this.toasts,t]},this.create=t=>{var e;let{message:a,...r}=t,o="number"==typeof(null==t?void 0:t.id)||(null==(e=t.id)?void 0:e.length)>0?t.id:c++,n=this.toasts.find(t=>t.id===o),s=void 0===t.dismissible||t.dismissible;return this.dismissedToasts.has(o)&&this.dismissedToasts.delete(o),n?this.toasts=this.toasts.map(e=>e.id===o?(this.publish({...e,...t,id:o,title:a}),{...e,...t,id:o,dismissible:s,title:a}):e):this.addToast({title:a,...r,dismissible:s,id:o}),o},this.dismiss=t=>(t?(this.dismissedToasts.add(t),requestAnimationFrame(()=>this.subscribers.forEach(e=>e({id:t,dismiss:!0})))):this.toasts.forEach(t=>{this.subscribers.forEach(e=>e({id:t.id,dismiss:!0}))}),t),this.message=(t,e)=>this.create({...e,message:t}),this.error=(t,e)=>this.create({...e,message:t,type:"error"}),this.success=(t,e)=>this.create({...e,type:"success",message:t}),this.info=(t,e)=>this.create({...e,type:"info",message:t}),this.warning=(t,e)=>this.create({...e,type:"warning",message:t}),this.loading=(t,e)=>this.create({...e,type:"loading",message:t}),this.promise=(t,a)=>{let r,o;if(!a)return;void 0!==a.loading&&(o=this.create({...a,promise:t,type:"loading",message:a.loading,description:"function"!=typeof a.description?a.description:void 0}));let n=Promise.resolve(t instanceof Function?t():t),s=void 0!==o,i=n.then(async t=>{if(r=["resolve",t],e.default.isValidElement(t))s=!1,this.create({id:o,type:"default",message:t});else if(f(t)&&!t.ok){s=!1;let r="function"==typeof a.error?await a.error("HTTP error! status: ".concat(t.status)):a.error,n="function"==typeof a.description?await a.description("HTTP error! status: ".concat(t.status)):a.description,i="object"!=typeof r||e.default.isValidElement(r)?{message:r}:r;this.create({id:o,type:"error",description:n,...i})}else if(t instanceof Error){s=!1;let r="function"==typeof a.error?await a.error(t):a.error,n="function"==typeof a.description?await a.description(t):a.description,i="object"!=typeof r||e.default.isValidElement(r)?{message:r}:r;this.create({id:o,type:"error",description:n,...i})}else if(void 0!==a.success){s=!1;let r="function"==typeof a.success?await a.success(t):a.success,n="function"==typeof a.description?await a.description(t):a.description,i="object"!=typeof r||e.default.isValidElement(r)?{message:r}:r;this.create({id:o,type:"success",description:n,...i})}}).catch(async t=>{if(r=["reject",t],void 0!==a.error){s=!1;let r="function"==typeof a.error?await a.error(t):a.error,n="function"==typeof a.description?await a.description(t):a.description,i="object"!=typeof r||e.default.isValidElement(r)?{message:r}:r;this.create({id:o,type:"error",description:n,...i})}}).finally(()=>{s&&(this.dismiss(o),o=void 0),null==a.finally||a.finally.call(a)}),l=()=>new Promise((t,e)=>i.then(()=>"reject"===r[0]?e(r[1]):t(r[1])).catch(e));return"string"!=typeof o&&"number"!=typeof o?{unwrap:l}:Object.assign(o,{unwrap:l})},this.custom=(t,e)=>{let a=(null==e?void 0:e.id)||c++;return this.create({jsx:t(a),id:a,...e}),a},this.getActiveToasts=()=>this.toasts.filter(t=>!this.dismissedToasts.has(t.id)),this.subscribers=[],this.toasts=[],this.dismissedToasts=new Set}},f=t=>t&&"object"==typeof t&&"ok"in t&&"boolean"==typeof t.ok&&"status"in t&&"number"==typeof t.status,m=Object.assign((t,e)=>{let a=(null==e?void 0:e.id)||c++;return u.addToast({title:t,...e,id:a}),a},{success:u.success,info:u.info,warning:u.warning,error:u.error,custom:u.custom,message:u.message,promise:u.promise,dismiss:u.dismiss,loading:u.loading},{getHistory:()=>u.toasts,getToasts:()=>u.getActiveToasts()});function p(t){return void 0!==t.label}function h(){for(var t=arguments.length,e=Array(t),a=0;a<t;a++)e[a]=arguments[a];return e.filter(Boolean).join(" ")}!function(t){if(!t||"undefined"==typeof document)return;let e=document.head||document.getElementsByTagName("head")[0],a=document.createElement("style");a.type="text/css",e.appendChild(a),a.styleSheet?a.styleSheet.cssText=t:a.appendChild(document.createTextNode(t))}("[data-sonner-toaster][dir=ltr],html[dir=ltr]{--toast-icon-margin-start:-3px;--toast-icon-margin-end:4px;--toast-svg-margin-start:-1px;--toast-svg-margin-end:0px;--toast-button-margin-start:auto;--toast-button-margin-end:0;--toast-close-button-start:0;--toast-close-button-end:unset;--toast-close-button-transform:translate(-35%, -35%)}[data-sonner-toaster][dir=rtl],html[dir=rtl]{--toast-icon-margin-start:4px;--toast-icon-margin-end:-3px;--toast-svg-margin-start:0px;--toast-svg-margin-end:-1px;--toast-button-margin-start:0;--toast-button-margin-end:auto;--toast-close-button-start:unset;--toast-close-button-end:0;--toast-close-button-transform:translate(35%, -35%)}[data-sonner-toaster]{position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1:hsl(0, 0%, 99%);--gray2:hsl(0, 0%, 97.3%);--gray3:hsl(0, 0%, 95.1%);--gray4:hsl(0, 0%, 93%);--gray5:hsl(0, 0%, 90.9%);--gray6:hsl(0, 0%, 88.7%);--gray7:hsl(0, 0%, 85.8%);--gray8:hsl(0, 0%, 78%);--gray9:hsl(0, 0%, 56.1%);--gray10:hsl(0, 0%, 52.3%);--gray11:hsl(0, 0%, 43.5%);--gray12:hsl(0, 0%, 9%);--border-radius:8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:0;z-index:999999999;transition:transform .4s ease}@media (hover:none) and (pointer:coarse){[data-sonner-toaster][data-lifted=true]{transform:none}}[data-sonner-toaster][data-x-position=right]{right:var(--offset-right)}[data-sonner-toaster][data-x-position=left]{left:var(--offset-left)}[data-sonner-toaster][data-x-position=center]{left:50%;transform:translateX(-50%)}[data-sonner-toaster][data-y-position=top]{top:var(--offset-top)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--offset-bottom)}[data-sonner-toast]{--y:translateY(100%);--lift-amount:calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:0;overflow-wrap:anywhere}[data-sonner-toast][data-styled=true]{padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px rgba(0,0,0,.1);width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}[data-sonner-toast]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-y-position=top]{top:0;--y:translateY(-100%);--lift:1;--lift-amount:calc(1 * var(--gap))}[data-sonner-toast][data-y-position=bottom]{bottom:0;--y:translateY(100%);--lift:-1;--lift-amount:calc(var(--lift) * var(--gap))}[data-sonner-toast][data-styled=true] [data-description]{font-weight:400;line-height:1.4;color:#3f3f3f}[data-rich-colors=true][data-sonner-toast][data-styled=true] [data-description]{color:inherit}[data-sonner-toaster][data-sonner-theme=dark] [data-description]{color:#e8e8e8}[data-sonner-toast][data-styled=true] [data-title]{font-weight:500;line-height:1.5;color:inherit}[data-sonner-toast][data-styled=true] [data-icon]{display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}[data-sonner-toast][data-promise=true] [data-icon]>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}[data-sonner-toast][data-styled=true] [data-icon]>*{flex-shrink:0}[data-sonner-toast][data-styled=true] [data-icon] svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}[data-sonner-toast][data-styled=true] [data-content]{display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;font-weight:500;cursor:pointer;outline:0;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}[data-sonner-toast][data-styled=true] [data-button]:focus-visible{box-shadow:0 0 0 2px rgba(0,0,0,.4)}[data-sonner-toast][data-styled=true] [data-button]:first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}[data-sonner-toast][data-styled=true] [data-cancel]{color:var(--normal-text);background:rgba(0,0,0,.08)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-styled=true] [data-cancel]{background:rgba(255,255,255,.3)}[data-sonner-toast][data-styled=true] [data-close-button]{position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);background:var(--normal-bg);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast][data-styled=true] [data-close-button]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-styled=true] [data-disabled=true]{cursor:not-allowed}[data-sonner-toast][data-styled=true]:hover [data-close-button]:hover{background:var(--gray2);border-color:var(--gray5)}[data-sonner-toast][data-swiping=true]::before{content:'';position:absolute;left:-100%;right:-100%;height:100%;z-index:-1}[data-sonner-toast][data-y-position=top][data-swiping=true]::before{bottom:50%;transform:scaleY(3) translateY(50%)}[data-sonner-toast][data-y-position=bottom][data-swiping=true]::before{top:50%;transform:scaleY(3) translateY(-50%)}[data-sonner-toast][data-swiping=false][data-removed=true]::before{content:'';position:absolute;inset:0;transform:scaleY(2)}[data-sonner-toast][data-expanded=true]::after{content:'';position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}[data-sonner-toast][data-mounted=true]{--y:translateY(0);opacity:1}[data-sonner-toast][data-expanded=false][data-front=false]{--scale:var(--toasts-before) * 0.05 + 1;--y:translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}[data-sonner-toast]>*{transition:opacity .4s}[data-sonner-toast][data-x-position=right]{right:0}[data-sonner-toast][data-x-position=left]{left:0}[data-sonner-toast][data-expanded=false][data-front=false][data-styled=true]>*{opacity:0}[data-sonner-toast][data-visible=false]{opacity:0;pointer-events:none}[data-sonner-toast][data-mounted=true][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}[data-sonner-toast][data-removed=true][data-front=true][data-swipe-out=false]{--y:translateY(calc(var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=false]{--y:translateY(40%);opacity:0;transition:transform .5s,opacity .2s}[data-sonner-toast][data-removed=true][data-front=false]::before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y,0)) translateX(var(--swipe-amount-x,0));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width:600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-sonner-theme=light]{--normal-bg:#fff;--normal-border:var(--gray4);--normal-text:var(--gray12);--success-bg:hsl(143, 85%, 96%);--success-border:hsl(145, 92%, 87%);--success-text:hsl(140, 100%, 27%);--info-bg:hsl(208, 100%, 97%);--info-border:hsl(221, 91%, 93%);--info-text:hsl(210, 92%, 45%);--warning-bg:hsl(49, 100%, 97%);--warning-border:hsl(49, 91%, 84%);--warning-text:hsl(31, 92%, 45%);--error-bg:hsl(359, 100%, 97%);--error-border:hsl(359, 100%, 94%);--error-text:hsl(360, 100%, 45%)}[data-sonner-toaster][data-sonner-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg:#000;--normal-border:hsl(0, 0%, 20%);--normal-text:var(--gray1)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg:#fff;--normal-border:var(--gray3);--normal-text:var(--gray12)}[data-sonner-toaster][data-sonner-theme=dark]{--normal-bg:#000;--normal-bg-hover:hsl(0, 0%, 12%);--normal-border:hsl(0, 0%, 20%);--normal-border-hover:hsl(0, 0%, 25%);--normal-text:var(--gray1);--success-bg:hsl(150, 100%, 6%);--success-border:hsl(147, 100%, 12%);--success-text:hsl(150, 86%, 65%);--info-bg:hsl(215, 100%, 6%);--info-border:hsl(223, 43%, 17%);--info-text:hsl(216, 87%, 65%);--warning-bg:hsl(64, 100%, 6%);--warning-border:hsl(60, 100%, 9%);--warning-text:hsl(46, 87%, 65%);--error-bg:hsl(358, 76%, 10%);--error-border:hsl(357, 89%, 16%);--error-text:hsl(358, 100%, 81%)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size:16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:first-child{animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}100%{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}100%{opacity:.15}}@media (prefers-reduced-motion){.sonner-loading-bar,[data-sonner-toast],[data-sonner-toast]>*{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}");let g=t=>{var a,r,c,u,f,m,g,b,v,y,w;let{invert:x,toast:k,unstyled:E,interacting:S,setHeights:T,visibleToasts:C,heights:N,index:M,toasts:B,expanded:j,removeToast:P,defaultRichColors:R,closeButton:z,style:A,cancelButtonStyle:D,actionButtonStyle:Y,className:I="",descriptionClassName:L="",duration:W,position:H,gap:q,expandByDefault:U,classNames:V,icons:O,closeButtonAriaLabel:F="Close toast"}=t,[K,Q]=e.default.useState(null),[X,_]=e.default.useState(null),[J,G]=e.default.useState(!1),[Z,$]=e.default.useState(!1),[tt,te]=e.default.useState(!1),[ta,tr]=e.default.useState(!1),[to,tn]=e.default.useState(!1),[ts,ti]=e.default.useState(0),[tl,td]=e.default.useState(0),tc=e.default.useRef(k.duration||W||4e3),tu=e.default.useRef(null),tf=e.default.useRef(null),tm=0===M,tp=M+1<=C,th=k.type,tg=!1!==k.dismissible,tb=k.className||"",tv=k.descriptionClassName||"",ty=e.default.useMemo(()=>N.findIndex(t=>t.toastId===k.id)||0,[N,k.id]),tw=e.default.useMemo(()=>{var t;return null!=(t=k.closeButton)?t:z},[k.closeButton,z]),tx=e.default.useMemo(()=>k.duration||W||4e3,[k.duration,W]),tk=e.default.useRef(0),tE=e.default.useRef(0),tS=e.default.useRef(0),tT=e.default.useRef(null),[tC,tN]=H.split("-"),tM=e.default.useMemo(()=>N.reduce((t,e,a)=>a>=ty?t:t+e.height,0),[N,ty]),tB=(()=>{let[t,a]=e.default.useState(document.hidden);return e.default.useEffect(()=>{let t=()=>{a(document.hidden)};return document.addEventListener("visibilitychange",t),()=>window.removeEventListener("visibilitychange",t)},[]),t})(),tj=k.invert||x,tP="loading"===th;tE.current=e.default.useMemo(()=>ty*q+tM,[ty,tM]),e.default.useEffect(()=>{tc.current=tx},[tx]),e.default.useEffect(()=>{G(!0)},[]),e.default.useEffect(()=>{let t=tf.current;if(t){let e=t.getBoundingClientRect().height;return td(e),T(t=>[{toastId:k.id,height:e,position:k.position},...t]),()=>T(t=>t.filter(t=>t.toastId!==k.id))}},[T,k.id]),e.default.useLayoutEffect(()=>{if(!J)return;let t=tf.current,e=t.style.height;t.style.height="auto";let a=t.getBoundingClientRect().height;t.style.height=e,td(a),T(t=>t.find(t=>t.toastId===k.id)?t.map(t=>t.toastId===k.id?{...t,height:a}:t):[{toastId:k.id,height:a,position:k.position},...t])},[J,k.title,k.description,T,k.id,k.jsx,k.action,k.cancel]);let tR=e.default.useCallback(()=>{$(!0),ti(tE.current),T(t=>t.filter(t=>t.toastId!==k.id)),setTimeout(()=>{P(k)},200)},[k,P,T,tE]);e.default.useEffect(()=>{let t;if((!k.promise||"loading"!==th)&&k.duration!==1/0&&"loading"!==k.type)return j||S||tB?(()=>{if(tS.current<tk.current){let t=new Date().getTime()-tk.current;tc.current=tc.current-t}tS.current=new Date().getTime()})():tc.current!==1/0&&(tk.current=new Date().getTime(),t=setTimeout(()=>{null==k.onAutoClose||k.onAutoClose.call(k,k),tR()},tc.current)),()=>clearTimeout(t)},[j,S,k,th,tB,tR]),e.default.useEffect(()=>{k.delete&&(tR(),null==k.onDismiss||k.onDismiss.call(k,k))},[tR,k.delete]);let tz=k.icon||(null==O?void 0:O[th])||(t=>{switch(t){case"success":return n;case"info":return i;case"warning":return s;case"error":return l;default:return null}})(th);return e.default.createElement("li",{tabIndex:0,ref:tf,className:h(I,tb,null==V?void 0:V.toast,null==k||null==(a=k.classNames)?void 0:a.toast,null==V?void 0:V.default,null==V?void 0:V[th],null==k||null==(r=k.classNames)?void 0:r[th]),"data-sonner-toast":"","data-rich-colors":null!=(y=k.richColors)?y:R,"data-styled":!(k.jsx||k.unstyled||E),"data-mounted":J,"data-promise":!!k.promise,"data-swiped":to,"data-removed":Z,"data-visible":tp,"data-y-position":tC,"data-x-position":tN,"data-index":M,"data-front":tm,"data-swiping":tt,"data-dismissible":tg,"data-type":th,"data-invert":tj,"data-swipe-out":ta,"data-swipe-direction":X,"data-expanded":!!(j||U&&J),"data-testid":k.testId,style:{"--index":M,"--toasts-before":M,"--z-index":B.length-M,"--offset":"".concat(Z?ts:tE.current,"px"),"--initial-height":U?"auto":"".concat(tl,"px"),...A,...k.style},onDragEnd:()=>{te(!1),Q(null),tT.current=null},onPointerDown:t=>{2!==t.button&&!tP&&tg&&(tu.current=new Date,ti(tE.current),t.target.setPointerCapture(t.pointerId),"BUTTON"!==t.target.tagName&&(te(!0),tT.current={x:t.clientX,y:t.clientY}))},onPointerUp:()=>{var t,e,a,r,o;if(ta||!tg)return;tT.current=null;let n=Number((null==(t=tf.current)?void 0:t.style.getPropertyValue("--swipe-amount-x").replace("px",""))||0),s=Number((null==(e=tf.current)?void 0:e.style.getPropertyValue("--swipe-amount-y").replace("px",""))||0),i=new Date().getTime()-(null==(a=tu.current)?void 0:a.getTime()),l="x"===K?n:s,d=Math.abs(l)/i;if(Math.abs(l)>=45||d>.11){ti(tE.current),null==k.onDismiss||k.onDismiss.call(k,k),"x"===K?_(n>0?"right":"left"):_(s>0?"down":"up"),tR(),tr(!0);return}null==(r=tf.current)||r.style.setProperty("--swipe-amount-x","0px"),null==(o=tf.current)||o.style.setProperty("--swipe-amount-y","0px"),tn(!1),te(!1),Q(null)},onPointerMove:e=>{var a,r,o,n;if(!tT.current||!tg||(null==(a=window.getSelection())?void 0:a.toString().length)>0)return;let s=e.clientY-tT.current.y,i=e.clientX-tT.current.x,l=null!=(n=t.swipeDirections)?n:function(t){let[e,a]=t.split("-"),r=[];return e&&r.push(e),a&&r.push(a),r}(H);!K&&(Math.abs(i)>1||Math.abs(s)>1)&&Q(Math.abs(i)>Math.abs(s)?"x":"y");let d={x:0,y:0},c=t=>1/(1.5+Math.abs(t)/20);if("y"===K){if(l.includes("top")||l.includes("bottom"))if(l.includes("top")&&s<0||l.includes("bottom")&&s>0)d.y=s;else{let t=s*c(s);d.y=Math.abs(t)<Math.abs(s)?t:s}}else if("x"===K&&(l.includes("left")||l.includes("right")))if(l.includes("left")&&i<0||l.includes("right")&&i>0)d.x=i;else{let t=i*c(i);d.x=Math.abs(t)<Math.abs(i)?t:i}(Math.abs(d.x)>0||Math.abs(d.y)>0)&&tn(!0),null==(r=tf.current)||r.style.setProperty("--swipe-amount-x","".concat(d.x,"px")),null==(o=tf.current)||o.style.setProperty("--swipe-amount-y","".concat(d.y,"px"))}},tw&&!k.jsx&&"loading"!==th?e.default.createElement("button",{"aria-label":F,"data-disabled":tP,"data-close-button":!0,onClick:tP||!tg?()=>{}:()=>{tR(),null==k.onDismiss||k.onDismiss.call(k,k)},className:h(null==V?void 0:V.closeButton,null==k||null==(c=k.classNames)?void 0:c.closeButton)},null!=(w=null==O?void 0:O.close)?w:d):null,(th||k.icon||k.promise)&&null!==k.icon&&((null==O?void 0:O[th])!==null||k.icon)?e.default.createElement("div",{"data-icon":"",className:h(null==V?void 0:V.icon,null==k||null==(u=k.classNames)?void 0:u.icon)},k.promise||"loading"===k.type&&!k.icon?k.icon||function(){var t,a;return(null==O?void 0:O.loading)?e.default.createElement("div",{className:h(null==V?void 0:V.loader,null==k||null==(a=k.classNames)?void 0:a.loader,"sonner-loader"),"data-visible":"loading"===th},O.loading):e.default.createElement(o,{className:h(null==V?void 0:V.loader,null==k||null==(t=k.classNames)?void 0:t.loader),visible:"loading"===th})}():null,"loading"!==k.type?tz:null):null,e.default.createElement("div",{"data-content":"",className:h(null==V?void 0:V.content,null==k||null==(f=k.classNames)?void 0:f.content)},e.default.createElement("div",{"data-title":"",className:h(null==V?void 0:V.title,null==k||null==(m=k.classNames)?void 0:m.title)},k.jsx?k.jsx:"function"==typeof k.title?k.title():k.title),k.description?e.default.createElement("div",{"data-description":"",className:h(L,tv,null==V?void 0:V.description,null==k||null==(g=k.classNames)?void 0:g.description)},"function"==typeof k.description?k.description():k.description):null),e.default.isValidElement(k.cancel)?k.cancel:k.cancel&&p(k.cancel)?e.default.createElement("button",{"data-button":!0,"data-cancel":!0,style:k.cancelButtonStyle||D,onClick:t=>{p(k.cancel)&&tg&&(null==k.cancel.onClick||k.cancel.onClick.call(k.cancel,t),tR())},className:h(null==V?void 0:V.cancelButton,null==k||null==(b=k.classNames)?void 0:b.cancelButton)},k.cancel.label):null,e.default.isValidElement(k.action)?k.action:k.action&&p(k.action)?e.default.createElement("button",{"data-button":!0,"data-action":!0,style:k.actionButtonStyle||Y,onClick:t=>{p(k.action)&&(null==k.action.onClick||k.action.onClick.call(k.action,t),t.defaultPrevented||tR())},className:h(null==V?void 0:V.actionButton,null==k||null==(v=k.classNames)?void 0:v.actionButton)},k.action.label):null)};function b(){if("undefined"==typeof window||"undefined"==typeof document)return"ltr";let t=document.documentElement.getAttribute("dir");return"auto"!==t&&t?t:window.getComputedStyle(document.documentElement).direction}let v=e.default.forwardRef(function(t,r){let{id:o,invert:n,position:s="bottom-right",hotkey:i=["altKey","KeyT"],expand:l,closeButton:d,className:c,offset:f,mobileOffset:m,theme:p="light",richColors:h,duration:v,style:y,visibleToasts:w=3,toastOptions:x,dir:k=b(),gap:E=14,icons:S,containerAriaLabel:T="Notifications"}=t,[C,N]=e.default.useState([]),M=e.default.useMemo(()=>o?C.filter(t=>t.toasterId===o):C.filter(t=>!t.toasterId),[C,o]),B=e.default.useMemo(()=>Array.from(new Set([s].concat(M.filter(t=>t.position).map(t=>t.position)))),[M,s]),[j,P]=e.default.useState([]),[R,z]=e.default.useState(!1),[A,D]=e.default.useState(!1),[Y,I]=e.default.useState("system"!==p?p:"undefined"!=typeof window&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),L=e.default.useRef(null),W=i.join("+").replace(/Key/g,"").replace(/Digit/g,""),H=e.default.useRef(null),q=e.default.useRef(!1),U=e.default.useCallback(t=>{N(e=>{var a;return(null==(a=e.find(e=>e.id===t.id))?void 0:a.delete)||u.dismiss(t.id),e.filter(e=>{let{id:a}=e;return a!==t.id})})},[]);return e.default.useEffect(()=>u.subscribe(t=>{if(t.dismiss)return void requestAnimationFrame(()=>{N(e=>e.map(e=>e.id===t.id?{...e,delete:!0}:e))});setTimeout(()=>{a.default.flushSync(()=>{N(e=>{let a=e.findIndex(e=>e.id===t.id);return -1!==a?[...e.slice(0,a),{...e[a],...t},...e.slice(a+1)]:[t,...e]})})})}),[C]),e.default.useEffect(()=>{if("system"!==p)return void I(p);if("system"===p&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?I("dark"):I("light")),"undefined"==typeof window)return;let t=window.matchMedia("(prefers-color-scheme: dark)");try{t.addEventListener("change",t=>{let{matches:e}=t;e?I("dark"):I("light")})}catch(e){t.addListener(t=>{let{matches:e}=t;try{e?I("dark"):I("light")}catch(t){console.error(t)}})}},[p]),e.default.useEffect(()=>{C.length<=1&&z(!1)},[C]),e.default.useEffect(()=>{let t=t=>{var e,a;i.every(e=>t[e]||t.code===e)&&(z(!0),null==(a=L.current)||a.focus()),"Escape"===t.code&&(document.activeElement===L.current||(null==(e=L.current)?void 0:e.contains(document.activeElement)))&&z(!1)};return document.addEventListener("keydown",t),()=>document.removeEventListener("keydown",t)},[i]),e.default.useEffect(()=>{if(L.current)return()=>{H.current&&(H.current.focus({preventScroll:!0}),H.current=null,q.current=!1)}},[L.current]),e.default.createElement("section",{ref:r,"aria-label":"".concat(T," ").concat(W),tabIndex:-1,"aria-live":"polite","aria-relevant":"additions text","aria-atomic":"false",suppressHydrationWarning:!0},B.map((a,r)=>{var o;let[s,i]=a.split("-");return M.length?e.default.createElement("ol",{key:a,dir:"auto"===k?b():k,tabIndex:-1,ref:L,className:c,"data-sonner-toaster":!0,"data-sonner-theme":Y,"data-y-position":s,"data-x-position":i,style:{"--front-toast-height":"".concat((null==(o=j[0])?void 0:o.height)||0,"px"),"--width":"".concat(356,"px"),"--gap":"".concat(E,"px"),...y,...function(t,e){let a={};return[t,e].forEach((t,e)=>{let r=1===e,o=r?"--mobile-offset":"--offset",n=r?"16px":"24px";function s(t){["top","right","bottom","left"].forEach(e=>{a["".concat(o,"-").concat(e)]="number"==typeof t?"".concat(t,"px"):t})}"number"==typeof t||"string"==typeof t?s(t):"object"==typeof t?["top","right","bottom","left"].forEach(e=>{void 0===t[e]?a["".concat(o,"-").concat(e)]=n:a["".concat(o,"-").concat(e)]="number"==typeof t[e]?"".concat(t[e],"px"):t[e]}):s(n)}),a}(f,m)},onBlur:t=>{q.current&&!t.currentTarget.contains(t.relatedTarget)&&(q.current=!1,H.current&&(H.current.focus({preventScroll:!0}),H.current=null))},onFocus:t=>{!(t.target instanceof HTMLElement&&"false"===t.target.dataset.dismissible)&&(q.current||(q.current=!0,H.current=t.relatedTarget))},onMouseEnter:()=>z(!0),onMouseMove:()=>z(!0),onMouseLeave:()=>{A||z(!1)},onDragEnd:()=>z(!1),onPointerDown:t=>{t.target instanceof HTMLElement&&"false"===t.target.dataset.dismissible||D(!0)},onPointerUp:()=>D(!1)},M.filter(t=>!t.position&&0===r||t.position===a).map((r,o)=>{var s,i;return e.default.createElement(g,{key:r.id,icons:S,index:o,toast:r,defaultRichColors:h,duration:null!=(s=null==x?void 0:x.duration)?s:v,className:null==x?void 0:x.className,descriptionClassName:null==x?void 0:x.descriptionClassName,invert:n,visibleToasts:w,closeButton:null!=(i=null==x?void 0:x.closeButton)?i:d,interacting:A,position:a,style:null==x?void 0:x.style,unstyled:null==x?void 0:x.unstyled,classNames:null==x?void 0:x.classNames,cancelButtonStyle:null==x?void 0:x.cancelButtonStyle,actionButtonStyle:null==x?void 0:x.actionButtonStyle,closeButtonAriaLabel:null==x?void 0:x.closeButtonAriaLabel,removeToast:U,toasts:M.filter(t=>t.position==r.position),heights:j.filter(t=>t.position==r.position),setHeights:P,expandByDefault:l,gap:E,expanded:R,swipeDirections:t.swipeDirections})})):null}))})},16973,t=>{"use strict";t.s(["apiService",()=>a]);let e="http://localhost:8000",a=new class{async request(t,a){let r="".concat(e).concat(t);try{let t=await fetch(r,{headers:{"Content-Type":"application/json",...null==a?void 0:a.headers},...a});if(!t.ok)throw Error("HTTP error! status: ".concat(t.status));return await t.json()}catch(e){throw console.error("API request failed for ".concat(t,":"),e),e}}async getHealthStatus(){return this.request("/health")}async getAlerts(t){return this.request("/alerts".concat(t?"?limit=".concat(t):""))}async getAlert(t){return this.request("/alerts/".concat(t))}async updateAlertStatus(t,e){return this.request("/alerts/".concat(t),{method:"PATCH",body:JSON.stringify({status:e})})}async getAlertStats(){return this.request("/alerts/stats")}getCameraStreamUrl(){return"".concat(e,"/stream")}getWebSocketUrl(){return"".concat("ws://localhost:8000","/alerts/stream")}}},99346,t=>{"use strict";t.s(["WebSocketProvider",()=>i,"useWebSocketContext",()=>l],99346);var e=t.i(43476),a=t.i(71645),r=t.i(12598),o=t.i(46696),n=t.i(16973);let s=(0,a.createContext)(void 0);function i(t){let{children:i}=t,l=function(){let[t,e]=(0,a.useState)(!1),[s,i]=(0,a.useState)("disconnected"),l=(0,a.useRef)(null),d=(0,r.useQueryClient)(),c=(0,a.useRef)(null),u=(0,a.useRef)(0),f=()=>{var t;if((null==(t=l.current)?void 0:t.readyState)!==WebSocket.OPEN)try{i("connecting");let t=n.apiService.getWebSocketUrl();l.current=new WebSocket(t),l.current.onopen=()=>{console.log("WebSocket connected"),e(!0),i("connected"),u.current=0,o.toast.success("Real-time alerts connected",{duration:2e3})},l.current.onmessage=t=>{try{let e=JSON.parse(t.data);h(e)}catch(t){console.error("Failed to parse WebSocket message:",t)}},l.current.onclose=t=>{console.log("WebSocket disconnected:",t.code,t.reason),e(!1),i("disconnected"),1e3!==t.code&&u.current<5&&p()},l.current.onerror=t=>{console.error("WebSocket error:",t),i("error"),o.toast.error("Real-time connection error",{description:"Attempting to reconnect...",duration:3e3})}}catch(t){console.error("Failed to create WebSocket connection:",t),i("error")}},m=()=>{c.current&&(clearTimeout(c.current),c.current=null),l.current&&(l.current.close(1e3,"Manual disconnect"),l.current=null),e(!1),i("disconnected")},p=()=>{c.current&&clearTimeout(c.current);let t=Math.min(1e3*Math.pow(2,u.current),3e4);u.current++,console.log("Scheduling reconnect attempt ".concat(u.current," in ").concat(t,"ms")),c.current=setTimeout(()=>{u.current<=5?f():o.toast.error("Failed to reconnect to real-time alerts",{description:"Please refresh the page to try again.",duration:5e3})},t)},h=t=>{switch(t.type){case"new_alert":g(t.data);break;case"status_update":b(t.data);break;case"system_message":v(t.data);break;default:console.log("Unknown message type:",t.type)}},g=t=>{d.invalidateQueries({queryKey:["alerts"]}),d.invalidateQueries({queryKey:["alert-stats"]}),o.toast.error("New Alert: ".concat(t.objects.join(", ")," detected"),{description:"Confidence: ".concat(Math.round(100*t.confidence),"% • ").concat(new Date(t.timestamp).toLocaleTimeString()),duration:5e3,action:{label:"View",onClick:()=>{window.location.href="/alerts"}}})},b=t=>{d.invalidateQueries({queryKey:["health-status"]}),o.toast.info("System status updated",{duration:2e3})},v=t=>{o.toast.info(t.message||"System notification",{duration:3e3})};return(0,a.useEffect)(()=>(f(),()=>{m()}),[]),{isConnected:t,connectionStatus:s,connect:f,disconnect:m}}();return(0,e.jsx)(s.Provider,{value:l,children:i})}function l(){let t=(0,a.useContext)(s);if(void 0===t)throw Error("useWebSocketContext must be used within a WebSocketProvider");return t}}]);