module.exports=[99570,a=>{"use strict";a.s(["Button",()=>g]);var b=a.i(87924),c=a.i(11011),d=a.i(187),e=a.i(68114);let f=(0,d.cva)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function g({className:a,variant:d,size:g,asChild:h=!1,...i}){let j=h?c.Slot:"button";return(0,b.jsx)(j,{"data-slot":"button",className:(0,e.cn)(f({variant:d,size:g,className:a})),...i})}},24987,a=>{"use strict";a.s(["MapPin",()=>b],24987);let b=(0,a.i(70106).default)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},16201,62722,a=>{"use strict";a.s(["CheckCircle",()=>c],16201);var b=a.i(70106);let c=(0,b.default)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);a.s(["XCircle",()=>d],62722);let d=(0,b.default)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},92e3,a=>{"use strict";a.s(["AlertCircle",()=>b],92e3);let b=(0,a.i(70106).default)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},24348,a=>{"use strict";a.s(["Alert",()=>f,"AlertDescription",()=>g]);var b=a.i(87924),c=a.i(187),d=a.i(68114);let e=(0,c.cva)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function f({className:a,variant:c,...f}){return(0,b.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,d.cn)(e({variant:c}),a),...f})}function g({className:a,...c}){return(0,b.jsx)("div",{"data-slot":"alert-description",className:(0,d.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",a),...c})}},13513,a=>{"use strict";a.s(["Camera",()=>b],13513);let b=(0,a.i(70106).default)("camera",[["path",{d:"M13.997 4a2 2 0 0 1 1.76 1.05l.486.9A2 2 0 0 0 18.003 7H20a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V9a2 2 0 0 1 2-2h1.997a2 2 0 0 0 1.759-1.048l.489-.904A2 2 0 0 1 10.004 4z",key:"18u6gg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},72107,a=>{"use strict";a.s(["CameraFeed",()=>r],72107);var b=a.i(87924),c=a.i(72131),d=a.i(91119),e=a.i(99570),f=a.i(86304),g=a.i(24348),h=a.i(13513),i=a.i(70106);let j=(0,i.default)("play",[["path",{d:"M5 5a2 2 0 0 1 3.008-1.728l11.997 6.998a2 2 0 0 1 .003 3.458l-12 7A2 2 0 0 1 5 19z",key:"10ikf1"}]]),k=(0,i.default)("pause",[["rect",{x:"14",y:"3",width:"5",height:"18",rx:"1",key:"kaeet6"}],["rect",{x:"5",y:"3",width:"5",height:"18",rx:"1",key:"1wsw3u"}]]),l=(0,i.default)("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]),m=(0,i.default)("maximize",[["path",{d:"M8 3H5a2 2 0 0 0-2 2v3",key:"1dcmit"}],["path",{d:"M21 8V5a2 2 0 0 0-2-2h-3",key:"1e4gt3"}],["path",{d:"M3 16v3a2 2 0 0 0 2 2h3",key:"wsl5sc"}],["path",{d:"M16 21h3a2 2 0 0 0 2-2v-3",key:"18trek"}]]);var n=a.i(92e3),o=a.i(85264),p=a.i(84391),q=a.i(68114);function r({className:a,autoPlay:i=!0}){let r=(0,c.useRef)(null),[s,t]=(0,c.useState)(!1),[u,v]=(0,c.useState)(!0),[w,x]=(0,c.useState)(null),y=p.apiService.getCameraStreamUrl();return(0,c.useEffect)(()=>{let a=r.current;if(!a)return;let b=()=>{v(!0),x(null)},c=()=>{v(!1),i&&a.play().catch(a=>{console.error("Auto-play failed:",a),x("Auto-play failed. Click play to start the stream.")})},d=()=>{t(!0),x(null)},e=()=>{t(!1)},f=()=>{v(!1),t(!1),x("Failed to load camera stream. Please check your connection.")};return a.addEventListener("loadstart",b),a.addEventListener("canplay",c),a.addEventListener("play",d),a.addEventListener("pause",e),a.addEventListener("error",f),()=>{a.removeEventListener("loadstart",b),a.removeEventListener("canplay",c),a.removeEventListener("play",d),a.removeEventListener("pause",e),a.removeEventListener("error",f)}},[i]),(0,b.jsxs)(d.Card,{className:(0,q.cn)("overflow-hidden",a),children:[(0,b.jsx)(d.CardHeader,{children:(0,b.jsxs)(d.CardTitle,{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{className:"flex items-center gap-2",children:[(0,b.jsx)(h.Camera,{className:"h-5 w-5"}),"Camera Feed",u?(0,b.jsx)(f.Badge,{variant:"secondary",children:"Connecting..."}):w?(0,b.jsx)(f.Badge,{className:"bg-red-500/10 text-red-500 border-red-500/20",children:"Offline"}):s?(0,b.jsx)(f.Badge,{className:"bg-green-500/10 text-green-500 border-green-500/20",children:"Live"}):(0,b.jsx)(f.Badge,{variant:"secondary",children:"Paused"})]}),(0,b.jsxs)("div",{className:"flex items-center gap-2",children:[(0,b.jsx)(e.Button,{variant:"outline",size:"sm",onClick:()=>{let a=r.current;a&&(x(null),v(!0),a.load())},disabled:u,children:(0,b.jsx)(l,{className:"h-4 w-4"})}),(0,b.jsx)(e.Button,{variant:"outline",size:"sm",onClick:()=>{let a=r.current;a&&(document.fullscreenElement?document.exitFullscreen():a.requestFullscreen().catch(a=>{console.error("Fullscreen failed:",a)}))},children:(0,b.jsx)(m,{className:"h-4 w-4"})})]})]})}),(0,b.jsx)(d.CardContent,{className:"p-0",children:(0,b.jsx)("div",{className:"relative bg-black aspect-video",children:w?(0,b.jsxs)(g.Alert,{className:"m-4 border-red-500/20 bg-red-500/10",children:[(0,b.jsx)(n.AlertCircle,{className:"h-4 w-4 text-red-500"}),(0,b.jsx)(g.AlertDescription,{className:"text-red-500",children:w})]}):(0,b.jsxs)(b.Fragment,{children:[(0,b.jsxs)("video",{ref:r,className:"w-full h-full object-cover",controls:!1,muted:!0,playsInline:!0,children:[(0,b.jsx)("source",{src:y,type:"video/mp4"}),(0,b.jsx)("source",{src:y,type:"application/x-mpegURL"}),"Your browser does not support the video tag."]}),u&&(0,b.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-black/50",children:(0,b.jsxs)("div",{className:"text-white text-center",children:[(0,b.jsx)(o.Wifi,{className:"h-8 w-8 mx-auto mb-2 animate-pulse"}),(0,b.jsx)("p",{children:"Connecting to camera..."})]})}),!u&&!w&&(0,b.jsx)("div",{className:"absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity bg-black/20",children:(0,b.jsx)(e.Button,{variant:"secondary",size:"lg",onClick:()=>{let a=r.current;a&&(s?a.pause():a.play().catch(a=>{console.error("Play failed:",a),x("Failed to play stream. Please try again.")}))},className:"bg-black/50 hover:bg-black/70",children:s?(0,b.jsx)(k,{className:"h-6 w-6"}):(0,b.jsx)(j,{className:"h-6 w-6"})})})]})})})]})}},42828,a=>{"use strict";a.s(["AlertsTable",()=>C],42828);var b=a.i(87924),c=a.i(33217),d=a.i(72131),e=a.i(12794),f=a.i(18544),g=a.i(33791),h=a.i(42871),i=class extends g.Subscribable{#a;#b=void 0;#c;#d;constructor(a,b){super(),this.#a=a,this.setOptions(b),this.bindMethods(),this.#e()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(a){let b=this.options;this.options=this.#a.defaultMutationOptions(a),(0,h.shallowEqualObjects)(this.options,b)||this.#a.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#c,observer:this}),b?.mutationKey&&this.options.mutationKey&&(0,h.hashKey)(b.mutationKey)!==(0,h.hashKey)(this.options.mutationKey)?this.reset():this.#c?.state.status==="pending"&&this.#c.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#c?.removeObserver(this)}onMutationUpdate(a){this.#e(),this.#f(a)}getCurrentResult(){return this.#b}reset(){this.#c?.removeObserver(this),this.#c=void 0,this.#e(),this.#f()}mutate(a,b){return this.#d=b,this.#c?.removeObserver(this),this.#c=this.#a.getMutationCache().build(this.#a,this.options),this.#c.addObserver(this),this.#c.execute(a)}#e(){let a=this.#c?.state??(0,e.getDefaultState)();this.#b={...a,isPending:"pending"===a.status,isSuccess:"success"===a.status,isError:"error"===a.status,isIdle:"idle"===a.status,mutate:this.mutate,reset:this.reset}}#f(a){f.notifyManager.batch(()=>{if(this.#d&&this.hasListeners()){let b=this.#b.variables,c=this.#b.context;a?.type==="success"?(this.#d.onSuccess?.(a.data,b,c),this.#d.onSettled?.(a.data,null,b,c)):a?.type==="error"&&(this.#d.onError?.(a.error,b,c),this.#d.onSettled?.(void 0,a.error,b,c))}this.listeners.forEach(a=>{a(this.#b)})})}},j=a.i(37927),k=a.i(68114);function l({className:a,...c}){return(0,b.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,b.jsx)("table",{"data-slot":"table",className:(0,k.cn)("w-full caption-bottom text-sm",a),...c})})}function m({className:a,...c}){return(0,b.jsx)("thead",{"data-slot":"table-header",className:(0,k.cn)("[&_tr]:border-b",a),...c})}function n({className:a,...c}){return(0,b.jsx)("tbody",{"data-slot":"table-body",className:(0,k.cn)("[&_tr:last-child]:border-0",a),...c})}function o({className:a,...c}){return(0,b.jsx)("tr",{"data-slot":"table-row",className:(0,k.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",a),...c})}function p({className:a,...c}){return(0,b.jsx)("th",{"data-slot":"table-head",className:(0,k.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...c})}function q({className:a,...c}){return(0,b.jsx)("td",{"data-slot":"table-cell",className:(0,k.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...c})}var r=a.i(86304),s=a.i(99570),t=a.i(91119),u=a.i(73570),v=a.i(24987);let w=(0,a.i(70106).default)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var x=a.i(16201),y=a.i(62722),z=a.i(92e3),A=a.i(84391),B=a.i(23292);function C({limit:a,showActions:e=!0}){let g=(0,j.useQueryClient)(),{data:C,isLoading:D,error:E}=(0,c.useQuery)({queryKey:["alerts",a],queryFn:()=>A.apiService.getAlerts(a),refetchInterval:1e4}),F=function(a,b){let c=(0,j.useQueryClient)(void 0),[e]=d.useState(()=>new i(c,a));d.useEffect(()=>{e.setOptions(a)},[e,a]);let g=d.useSyncExternalStore(d.useCallback(a=>e.subscribe(f.notifyManager.batchCalls(a)),[e]),()=>e.getCurrentResult(),()=>e.getCurrentResult()),k=d.useCallback((a,b)=>{e.mutate(a,b).catch(h.noop)},[e]);if(g.error&&(0,h.shouldThrowError)(e.options.throwOnError,[g.error]))throw g.error;return{...g,mutate:k,mutateAsync:g.mutate}}({mutationFn:({id:a,status:b})=>A.apiService.updateAlertStatus(a,b),onSuccess:()=>{g.invalidateQueries({queryKey:["alerts"]}),g.invalidateQueries({queryKey:["alert-stats"]}),B.toast.success("Alert status updated successfully")},onError:a=>{B.toast.error("Failed to update alert status"),console.error("Update failed:",a)}}),G=(a,b)=>{F.mutate({id:a.id,status:b})};return D?(0,b.jsxs)(t.Card,{children:[(0,b.jsx)(t.CardHeader,{children:(0,b.jsx)(t.CardTitle,{children:"Alerts"})}),(0,b.jsx)(t.CardContent,{children:(0,b.jsx)("div",{className:"space-y-3",children:[void 0,void 0,void 0,void 0,void 0].map((a,c)=>(0,b.jsx)("div",{className:"animate-pulse",children:(0,b.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,b.jsx)("div",{className:"h-4 w-4 bg-muted rounded"}),(0,b.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,b.jsx)("div",{className:"h-4 bg-muted rounded w-3/4"}),(0,b.jsx)("div",{className:"h-3 bg-muted rounded w-1/2"})]})]})},c))})})]}):E?(0,b.jsxs)(t.Card,{children:[(0,b.jsx)(t.CardHeader,{children:(0,b.jsx)(t.CardTitle,{children:"Alerts"})}),(0,b.jsx)(t.CardContent,{children:(0,b.jsxs)("div",{className:"text-center py-6",children:[(0,b.jsx)(y.XCircle,{className:"h-8 w-8 text-red-500 mx-auto mb-2"}),(0,b.jsx)("p",{className:"text-sm text-muted-foreground",children:"Failed to load alerts"})]})})]}):C&&0!==C.length?(0,b.jsxs)(t.Card,{children:[(0,b.jsx)(t.CardHeader,{children:(0,b.jsxs)(t.CardTitle,{className:"flex items-center gap-2",children:[(0,b.jsx)(u.AlertTriangle,{className:"h-5 w-5"}),"Alerts (",C.length,")"]})}),(0,b.jsx)(t.CardContent,{children:(0,b.jsx)("div",{className:"rounded-md border",children:(0,b.jsxs)(l,{children:[(0,b.jsx)(m,{children:(0,b.jsxs)(o,{children:[(0,b.jsx)(p,{children:"Status"}),(0,b.jsx)(p,{children:"Detection"}),(0,b.jsx)(p,{children:"Confidence"}),(0,b.jsx)(p,{children:"Time"}),(0,b.jsx)(p,{children:"Location"}),e&&(0,b.jsx)(p,{children:"Actions"})]})}),(0,b.jsx)(n,{children:C.map(a=>(0,b.jsxs)(o,{children:[(0,b.jsx)(q,{children:(0,b.jsxs)(r.Badge,{className:(0,k.cn)("flex items-center gap-1 w-fit",(a=>{switch(a){case"active":return"bg-red-500/10 text-red-500 border-red-500/20";case"investigating":return"bg-yellow-500/10 text-yellow-500 border-yellow-500/20";case"resolved":return"bg-green-500/10 text-green-500 border-green-500/20";default:return"bg-gray-500/10 text-gray-500 border-gray-500/20"}})(a.status)),children:[(a=>{switch(a){case"active":default:return(0,b.jsx)(u.AlertTriangle,{className:"h-4 w-4"});case"investigating":return(0,b.jsx)(z.AlertCircle,{className:"h-4 w-4"});case"resolved":return(0,b.jsx)(x.CheckCircle,{className:"h-4 w-4"})}})(a.status),a.status]})}),(0,b.jsx)(q,{children:(0,b.jsx)("div",{className:"font-medium",children:a.objects.join(", ")})}),(0,b.jsx)(q,{children:(0,b.jsxs)("div",{className:"flex items-center gap-1",children:[(0,b.jsx)("div",{className:(0,k.cn)("h-2 w-2 rounded-full",a.confidence>.8?"bg-green-500":a.confidence>.6?"bg-yellow-500":"bg-red-500")}),Math.round(100*a.confidence),"%"]})}),(0,b.jsx)(q,{children:(0,b.jsx)("div",{className:"text-sm",children:new Date(a.timestamp).toLocaleString()})}),(0,b.jsx)(q,{children:a.location?(0,b.jsxs)("div",{className:"flex items-center gap-1 text-sm",children:[(0,b.jsx)(v.MapPin,{className:"h-3 w-3"}),a.location.latitude.toFixed(4),","," ",a.location.longitude.toFixed(4)]}):(0,b.jsx)("span",{className:"text-muted-foreground text-sm",children:"-"})}),e&&(0,b.jsx)(q,{children:(0,b.jsxs)("div",{className:"flex items-center gap-1",children:["active"===a.status&&(0,b.jsx)(s.Button,{variant:"outline",size:"sm",onClick:()=>G(a,"investigating"),disabled:F.isPending,children:"Investigate"}),"investigating"===a.status&&(0,b.jsx)(s.Button,{variant:"outline",size:"sm",onClick:()=>G(a,"resolved"),disabled:F.isPending,children:"Resolve"}),a.image_url&&(0,b.jsx)(s.Button,{variant:"ghost",size:"sm",onClick:()=>window.open(a.image_url,"_blank"),children:(0,b.jsx)(w,{className:"h-4 w-4"})})]})})]},a.id))})]})})})]}):(0,b.jsxs)(t.Card,{children:[(0,b.jsx)(t.CardHeader,{children:(0,b.jsx)(t.CardTitle,{children:"Alerts"})}),(0,b.jsx)(t.CardContent,{children:(0,b.jsxs)("div",{className:"text-center py-6",children:[(0,b.jsx)(u.AlertTriangle,{className:"h-8 w-8 text-muted-foreground mx-auto mb-2"}),(0,b.jsx)("p",{className:"text-sm text-muted-foreground",children:"No alerts found"})]})})]})}}];

//# sourceMappingURL=_bbb71847._.js.map