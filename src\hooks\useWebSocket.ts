import { useEffect, useRef, useState } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { WebSocketMessage, Alert } from "@/types";
import { apiService } from "@/services/api";

export function useWebSocket() {
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<
    "connecting" | "connected" | "disconnected" | "error"
  >("disconnected");
  const wsRef = useRef<WebSocket | null>(null);
  const queryClient = useQueryClient();
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;

  const connect = () => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    try {
      setConnectionStatus("connecting");
      const wsUrl = apiService.getWebSocketUrl();
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        console.log("WebSocket connected");
        setIsConnected(true);
        setConnectionStatus("connected");
        reconnectAttempts.current = 0;

        toast.success("Real-time alerts connected", {
          duration: 2000,
        });
      };

      wsRef.current.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          handleMessage(message);
        } catch (error) {
          console.error("Failed to parse WebSocket message:", error);
        }
      };

      wsRef.current.onclose = (event) => {
        console.log("WebSocket disconnected:", event.code, event.reason);
        setIsConnected(false);
        setConnectionStatus("disconnected");

        // Attempt to reconnect if not a normal closure
        if (
          event.code !== 1000 &&
          reconnectAttempts.current < maxReconnectAttempts
        ) {
          scheduleReconnect();
        }
      };

      wsRef.current.onerror = (error) => {
        console.error("WebSocket error:", error);
        setConnectionStatus("error");

        toast.error("Real-time connection error", {
          description: "Attempting to reconnect...",
          duration: 3000,
        });
      };
    } catch (error) {
      console.error("Failed to create WebSocket connection:", error);
      setConnectionStatus("error");
    }
  };

  const disconnect = () => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (wsRef.current) {
      wsRef.current.close(1000, "Manual disconnect");
      wsRef.current = null;
    }

    setIsConnected(false);
    setConnectionStatus("disconnected");
  };

  const scheduleReconnect = () => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }

    const delay = Math.min(
      1000 * Math.pow(2, reconnectAttempts.current),
      30000
    ); // Exponential backoff, max 30s
    reconnectAttempts.current++;

    console.log(
      `Scheduling reconnect attempt ${reconnectAttempts.current} in ${delay}ms`
    );

    reconnectTimeoutRef.current = setTimeout(() => {
      if (reconnectAttempts.current <= maxReconnectAttempts) {
        connect();
      } else {
        toast.error("Failed to reconnect to real-time alerts", {
          description: "Please refresh the page to try again.",
          duration: 5000,
        });
      }
    }, delay);
  };

  const handleMessage = (message: WebSocketMessage) => {
    switch (message.type) {
      case "new_alert":
        handleNewAlert(message.data as Alert);
        break;
      case "status_update":
        handleStatusUpdate(message.data);
        break;
      case "system_message":
        handleSystemMessage(message.data as { message?: string });
        break;
      default:
        console.log("Unknown message type:", message.type);
    }
  };

  const handleNewAlert = (alert: Alert) => {
    // Invalidate queries to refresh data
    queryClient.invalidateQueries({ queryKey: ["alerts"] });
    queryClient.invalidateQueries({ queryKey: ["alert-stats"] });

    // Show toast notification
    toast.error(`New Alert: ${alert.objects.join(", ")} detected`, {
      description: `Confidence: ${Math.round(
        alert.confidence * 100
      )}% • ${new Date(alert.timestamp).toLocaleTimeString()}`,
      duration: 5000,
      action: {
        label: "View",
        onClick: () => {
          // Navigate to alerts page or show alert details
          window.location.href = "/alerts";
        },
      },
    });
  };

  const handleStatusUpdate = (_data: unknown) => {
    // Invalidate relevant queries
    queryClient.invalidateQueries({ queryKey: ["health-status"] });

    toast.info("System status updated", {
      duration: 2000,
    });
  };

  const handleSystemMessage = (data: { message?: string }) => {
    toast.info(data.message || "System notification", {
      duration: 3000,
    });
  };

  useEffect(() => {
    connect();

    return () => {
      disconnect();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return {
    isConnected,
    connectionStatus,
    connect,
    disconnect,
  };
}
