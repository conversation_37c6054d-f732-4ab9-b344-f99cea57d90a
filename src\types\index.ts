// Alert Types
export interface Alert {
  id: string;
  timestamp: string;
  objects: string[];
  confidence: number;
  location?: {
    latitude: number;
    longitude: number;
  };
  image_url?: string;
  status: "active" | "resolved" | "investigating";
}

// Analytics Types
export interface AlertStats {
  total_alerts: number;
  alerts_today: number;
  alerts_this_week: number;
  alerts_this_month: number;
  detection_counts: {
    [key: string]: number;
  };
  hourly_distribution: {
    hour: number;
    count: number;
  }[];
  daily_distribution: {
    date: string;
    count: number;
  }[];
}

// Health Check Types
export interface HealthStatus {
  status: "healthy" | "unhealthy" | "degraded";
  timestamp: string;
  services: {
    database: "up" | "down";
    camera: "up" | "down";
    ai_model: "up" | "down";
  };
  uptime: number;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

// WebSocket Message Types
export interface WebSocketMessage {
  type: "new_alert" | "status_update" | "system_message";
  data: Alert | HealthStatus | { message: string } | unknown;
  timestamp: string;
}

// Camera Stream Types
export interface CameraStream {
  id: string;
  name: string;
  url: string;
  status: "active" | "inactive" | "error";
  location?: string;
}
