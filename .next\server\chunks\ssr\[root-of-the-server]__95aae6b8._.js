module.exports=[56704,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},20635,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},32319,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},9270,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored.contexts.AppRouterContext},38783,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored["react-ssr"].ReactServerDOMTurbopackClient},36313,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored.contexts.HooksClientContext},18341,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored.contexts.ServerInsertedHtml},51234,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"HandleISRError",{enumerable:!0,get:function(){return e}});let d=a.r(56704).workAsyncStorage;function e(a){let{error:b}=a;if(d){let a=d.getStore();if((null==a?void 0:a.isRevalidate)||(null==a?void 0:a.isStaticGeneration))throw console.error(b),b}return null}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},40622,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"default",{enumerable:!0,get:function(){return g}});let d=a.r(87924),e=a.r(51234),f={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},text:{fontSize:"14px",fontWeight:400,lineHeight:"28px",margin:"0 8px"}},g=function(a){let{error:b}=a,c=null==b?void 0:b.digest;return(0,d.jsxs)("html",{id:"__next_error__",children:[(0,d.jsx)("head",{}),(0,d.jsxs)("body",{children:[(0,d.jsx)(e.HandleISRError,{error:b}),(0,d.jsx)("div",{style:f.error,children:(0,d.jsxs)("div",{children:[(0,d.jsxs)("h2",{style:f.text,children:["Application error: a ",c?"server":"client","-side exception has occurred while loading ",window.location.hostname," (see the"," ",c?"server logs":"browser console"," for more information)."]}),c?(0,d.jsx)("p",{style:f.text,children:"Digest: "+c}):null]})})]})]})};("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},33217,a=>{"use strict";a.s(["useQuery",()=>r],33217);var b=a.i(99745),c=a.i(18544),d=a.i(76644),e=a.i(33791),f=a.i(79715),g=a.i(42871),h=class extends e.Subscribable{constructor(a,b){super(),this.options=b,this.#a=a,this.#b=null,this.#c=(0,f.pendingThenable)(),this.bindMethods(),this.setOptions(b)}#a;#d=void 0;#e=void 0;#f=void 0;#g;#h;#c;#b;#i;#j;#k;#l;#m;#n;#o=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#d.addObserver(this),i(this.#d,this.options)?this.#p():this.updateResult(),this.#q())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return j(this.#d,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return j(this.#d,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#r(),this.#s(),this.#d.removeObserver(this)}setOptions(a){let b=this.options,c=this.#d;if(this.options=this.#a.defaultQueryOptions(a),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof(0,g.resolveEnabled)(this.options.enabled,this.#d))throw Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#t(),this.#d.setOptions(this.options),b._defaulted&&!(0,g.shallowEqualObjects)(this.options,b)&&this.#a.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#d,observer:this});let d=this.hasListeners();d&&k(this.#d,c,this.options,b)&&this.#p(),this.updateResult(),d&&(this.#d!==c||(0,g.resolveEnabled)(this.options.enabled,this.#d)!==(0,g.resolveEnabled)(b.enabled,this.#d)||(0,g.resolveStaleTime)(this.options.staleTime,this.#d)!==(0,g.resolveStaleTime)(b.staleTime,this.#d))&&this.#u();let e=this.#v();d&&(this.#d!==c||(0,g.resolveEnabled)(this.options.enabled,this.#d)!==(0,g.resolveEnabled)(b.enabled,this.#d)||e!==this.#n)&&this.#w(e)}getOptimisticResult(a){var b,c;let d=this.#a.getQueryCache().build(this.#a,a),e=this.createResult(d,a);return b=this,c=e,(0,g.shallowEqualObjects)(b.getCurrentResult(),c)||(this.#f=e,this.#h=this.options,this.#g=this.#d.state),e}getCurrentResult(){return this.#f}trackResult(a,b){return new Proxy(a,{get:(a,c)=>(this.trackProp(c),b?.(c),"promise"!==c||this.options.experimental_prefetchInRender||"pending"!==this.#c.status||this.#c.reject(Error("experimental_prefetchInRender feature flag is not enabled")),Reflect.get(a,c))})}trackProp(a){this.#o.add(a)}getCurrentQuery(){return this.#d}refetch({...a}={}){return this.fetch({...a})}fetchOptimistic(a){let b=this.#a.defaultQueryOptions(a),c=this.#a.getQueryCache().build(this.#a,b);return c.fetch().then(()=>this.createResult(c,b))}fetch(a){return this.#p({...a,cancelRefetch:a.cancelRefetch??!0}).then(()=>(this.updateResult(),this.#f))}#p(a){this.#t();let b=this.#d.fetch(this.options,a);return a?.throwOnError||(b=b.catch(g.noop)),b}#u(){this.#r();let a=(0,g.resolveStaleTime)(this.options.staleTime,this.#d);if(g.isServer||this.#f.isStale||!(0,g.isValidTimeout)(a))return;let b=(0,g.timeUntilStale)(this.#f.dataUpdatedAt,a);this.#l=setTimeout(()=>{this.#f.isStale||this.updateResult()},b+1)}#v(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#d):this.options.refetchInterval)??!1}#w(a){this.#s(),this.#n=a,!g.isServer&&!1!==(0,g.resolveEnabled)(this.options.enabled,this.#d)&&(0,g.isValidTimeout)(this.#n)&&0!==this.#n&&(this.#m=setInterval(()=>{(this.options.refetchIntervalInBackground||b.focusManager.isFocused())&&this.#p()},this.#n))}#q(){this.#u(),this.#w(this.#v())}#r(){this.#l&&(clearTimeout(this.#l),this.#l=void 0)}#s(){this.#m&&(clearInterval(this.#m),this.#m=void 0)}createResult(a,b){let c,e=this.#d,h=this.options,j=this.#f,m=this.#g,n=this.#h,o=a!==e?a.state:this.#e,{state:p}=a,q={...p},r=!1;if(b._optimisticResults){let c=this.hasListeners(),f=!c&&i(a,b),g=c&&k(a,e,b,h);(f||g)&&(q={...q,...(0,d.fetchState)(p.data,a.options)}),"isRestoring"===b._optimisticResults&&(q.fetchStatus="idle")}let{error:s,errorUpdatedAt:t,status:u}=q;c=q.data;let v=!1;if(void 0!==b.placeholderData&&void 0===c&&"pending"===u){let a;j?.isPlaceholderData&&b.placeholderData===n?.placeholderData?(a=j.data,v=!0):a="function"==typeof b.placeholderData?b.placeholderData(this.#k?.state.data,this.#k):b.placeholderData,void 0!==a&&(u="success",c=(0,g.replaceData)(j?.data,a,b),r=!0)}if(b.select&&void 0!==c&&!v)if(j&&c===m?.data&&b.select===this.#i)c=this.#j;else try{this.#i=b.select,c=b.select(c),c=(0,g.replaceData)(j?.data,c,b),this.#j=c,this.#b=null}catch(a){this.#b=a}this.#b&&(s=this.#b,c=this.#j,t=Date.now(),u="error");let w="fetching"===q.fetchStatus,x="pending"===u,y="error"===u,z=x&&w,A=void 0!==c,B={status:u,fetchStatus:q.fetchStatus,isPending:x,isSuccess:"success"===u,isError:y,isInitialLoading:z,isLoading:z,data:c,dataUpdatedAt:q.dataUpdatedAt,error:s,errorUpdatedAt:t,failureCount:q.fetchFailureCount,failureReason:q.fetchFailureReason,errorUpdateCount:q.errorUpdateCount,isFetched:q.dataUpdateCount>0||q.errorUpdateCount>0,isFetchedAfterMount:q.dataUpdateCount>o.dataUpdateCount||q.errorUpdateCount>o.errorUpdateCount,isFetching:w,isRefetching:w&&!x,isLoadingError:y&&!A,isPaused:"paused"===q.fetchStatus,isPlaceholderData:r,isRefetchError:y&&A,isStale:l(a,b),refetch:this.refetch,promise:this.#c,isEnabled:!1!==(0,g.resolveEnabled)(b.enabled,a)};if(this.options.experimental_prefetchInRender){let b=a=>{"error"===B.status?a.reject(B.error):void 0!==B.data&&a.resolve(B.data)},c=()=>{b(this.#c=B.promise=(0,f.pendingThenable)())},d=this.#c;switch(d.status){case"pending":a.queryHash===e.queryHash&&b(d);break;case"fulfilled":("error"===B.status||B.data!==d.value)&&c();break;case"rejected":("error"!==B.status||B.error!==d.reason)&&c()}}return B}updateResult(){let a=this.#f,b=this.createResult(this.#d,this.options);if(this.#g=this.#d.state,this.#h=this.options,void 0!==this.#g.data&&(this.#k=this.#d),(0,g.shallowEqualObjects)(b,a))return;this.#f=b;let c=()=>{if(!a)return!0;let{notifyOnChangeProps:b}=this.options,c="function"==typeof b?b():b;if("all"===c||!c&&!this.#o.size)return!0;let d=new Set(c??this.#o);return this.options.throwOnError&&d.add("error"),Object.keys(this.#f).some(b=>this.#f[b]!==a[b]&&d.has(b))};this.#x({listeners:c()})}#t(){let a=this.#a.getQueryCache().build(this.#a,this.options);if(a===this.#d)return;let b=this.#d;this.#d=a,this.#e=a.state,this.hasListeners()&&(b?.removeObserver(this),a.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#q()}#x(a){c.notifyManager.batch(()=>{a.listeners&&this.listeners.forEach(a=>{a(this.#f)}),this.#a.getQueryCache().notify({query:this.#d,type:"observerResultsUpdated"})})}};function i(a,b){return!1!==(0,g.resolveEnabled)(b.enabled,a)&&void 0===a.state.data&&("error"!==a.state.status||!1!==b.retryOnMount)||void 0!==a.state.data&&j(a,b,b.refetchOnMount)}function j(a,b,c){if(!1!==(0,g.resolveEnabled)(b.enabled,a)&&"static"!==(0,g.resolveStaleTime)(b.staleTime,a)){let d="function"==typeof c?c(a):c;return"always"===d||!1!==d&&l(a,b)}return!1}function k(a,b,c,d){return(a!==b||!1===(0,g.resolveEnabled)(d.enabled,a))&&(!c.suspense||"error"!==a.state.status)&&l(a,c)}function l(a,b){return!1!==(0,g.resolveEnabled)(b.enabled,a)&&a.isStaleByTime((0,g.resolveStaleTime)(b.staleTime,a))}var m=a.i(72131),n=a.i(37927);a.i(87924);var o=m.createContext(function(){let a=!1;return{clearReset:()=>{a=!1},reset:()=>{a=!0},isReset:()=>a}}()),p=m.createContext(!1);p.Provider;var q=(a,b,c)=>b.fetchOptimistic(a).catch(()=>{c.clearReset()});function r(a,b){return function(a,b,d){let e=m.useContext(p),f=m.useContext(o),h=(0,n.useQueryClient)(d),i=h.defaultQueryOptions(a);if(h.getDefaultOptions().queries?._experimental_beforeQuery?.(i),i._optimisticResults=e?"isRestoring":"optimistic",i.suspense){let a=a=>"static"===a?a:Math.max(a??1e3,1e3),b=i.staleTime;i.staleTime="function"==typeof b?(...c)=>a(b(...c)):a(b),"number"==typeof i.gcTime&&(i.gcTime=Math.max(i.gcTime,1e3))}(i.suspense||i.throwOnError||i.experimental_prefetchInRender)&&!f.isReset()&&(i.retryOnMount=!1),m.useEffect(()=>{f.clearReset()},[f]);let j=!h.getQueryCache().get(i.queryHash),[k]=m.useState(()=>new b(h,i)),l=k.getOptimisticResult(i),r=!e&&!1!==a.subscribed;if(m.useSyncExternalStore(m.useCallback(a=>{let b=r?k.subscribe(c.notifyManager.batchCalls(a)):g.noop;return k.updateResult(),b},[k,r]),()=>k.getCurrentResult(),()=>k.getCurrentResult()),m.useEffect(()=>{k.setOptions(i)},[i,k]),i?.suspense&&l.isPending)throw q(i,k,f);if((({result:a,errorResetBoundary:b,throwOnError:c,query:d,suspense:e})=>a.isError&&!b.isReset()&&!a.isFetching&&d&&(e&&void 0===a.data||(0,g.shouldThrowError)(c,[a.error,d])))({result:l,errorResetBoundary:f,throwOnError:i.throwOnError,query:h.getQueryCache().get(i.queryHash),suspense:i.suspense}))throw l.error;if(h.getDefaultOptions().queries?._experimental_afterQuery?.(i,l),i.experimental_prefetchInRender&&!g.isServer&&l.isLoading&&l.isFetching&&!e){let a=j?q(i,k,f):h.getQueryCache().get(i.queryHash)?.promise;a?.catch(g.noop).finally(()=>{k.updateResult()})}return i.notifyOnChangeProps?l:k.trackResult(l)}(a,h,b)}},91119,a=>{"use strict";a.s(["Card",()=>d,"CardContent",()=>g,"CardHeader",()=>e,"CardTitle",()=>f]);var b=a.i(87924),c=a.i(68114);function d({className:a,...d}){return(0,b.jsx)("div",{"data-slot":"card",className:(0,c.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...d})}function e({className:a,...d}){return(0,b.jsx)("div",{"data-slot":"card-header",className:(0,c.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...d})}function f({className:a,...d}){return(0,b.jsx)("div",{"data-slot":"card-title",className:(0,c.cn)("leading-none font-semibold",a),...d})}function g({className:a,...d}){return(0,b.jsx)("div",{"data-slot":"card-content",className:(0,c.cn)("px-6",a),...d})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__95aae6b8._.js.map