(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,39946,26005,34093,70292,62351,t=>{"use strict";function e(t,e,i){if(!e.has(t))throw TypeError("attempted to "+i+" private field on non-instance");return e.get(t)}function i(t,i){var s=e(t,i,"get");return s.get?s.get.call(t):s.value}function s(t,e){if(e.has(t))throw TypeError("Cannot initialize the same private elements twice on an object")}function n(t,e,i){s(t,e),e.set(t,i)}function r(t,i,s){var n=e(t,i,"set");if(n.set)n.set.call(t,s);else{if(!n.writable)throw TypeError("attempted to set read only private field");n.value=s}return s}t.s(["_",()=>i],39946),t.s(["_",()=>e],26005),t.s(["_",()=>n],70292),t.s(["_",()=>s],34093),t.s(["_",()=>r],62351)},19273,t=>{"use strict";t.s(["addToEnd",()=>b,"addToStart",()=>w,"ensureQueryFn",()=>O,"functionalUpdate",()=>s,"hashKey",()=>c,"hashQueryKeyByOptions",()=>h,"isServer",()=>e,"isValidTimeout",()=>n,"matchMutation",()=>u,"matchQuery",()=>l,"noop",()=>i,"partialMatchKey",()=>d,"replaceData",()=>m,"resolveEnabled",()=>o,"resolveStaleTime",()=>a,"shallowEqualObjects",()=>f,"shouldThrowError",()=>S,"skipToken",()=>M,"sleep",()=>g,"timeUntilStale",()=>r]);var e="undefined"==typeof window||"Deno"in globalThis;function i(){}function s(t,e){return"function"==typeof t?t(e):t}function n(t){return"number"==typeof t&&t>=0&&t!==1/0}function r(t,e){return Math.max(t+(e||0)-Date.now(),0)}function a(t,e){return"function"==typeof t?t(e):t}function o(t,e){return"function"==typeof t?t(e):t}function l(t,e){let{type:i="all",exact:s,fetchStatus:n,predicate:r,queryKey:a,stale:o}=t;if(a){if(s){if(e.queryHash!==h(a,e.options))return!1}else if(!d(e.queryKey,a))return!1}if("all"!==i){let t=e.isActive();if("active"===i&&!t||"inactive"===i&&t)return!1}return("boolean"!=typeof o||e.isStale()===o)&&(!n||n===e.state.fetchStatus)&&(!r||!!r(e))}function u(t,e){let{exact:i,status:s,predicate:n,mutationKey:r}=t;if(r){if(!e.options.mutationKey)return!1;if(i){if(c(e.options.mutationKey)!==c(r))return!1}else if(!d(e.options.mutationKey,r))return!1}return(!s||e.state.status===s)&&(!n||!!n(e))}function h(t,e){return((null==e?void 0:e.queryKeyHashFn)||c)(t)}function c(t){return JSON.stringify(t,(t,e)=>y(e)?Object.keys(e).sort().reduce((t,i)=>(t[i]=e[i],t),{}):e)}function d(t,e){return t===e||typeof t==typeof e&&!!t&&!!e&&"object"==typeof t&&"object"==typeof e&&Object.keys(e).every(i=>d(t[i],e[i]))}function f(t,e){if(!e||Object.keys(t).length!==Object.keys(e).length)return!1;for(let i in t)if(t[i]!==e[i])return!1;return!0}function p(t){return Array.isArray(t)&&t.length===Object.keys(t).length}function y(t){if(!_(t))return!1;let e=t.constructor;if(void 0===e)return!0;let i=e.prototype;return!!_(i)&&!!i.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(t)===Object.prototype}function _(t){return"[object Object]"===Object.prototype.toString.call(t)}function g(t){return new Promise(e=>{setTimeout(e,t)})}function m(t,e,i){return"function"==typeof i.structuralSharing?i.structuralSharing(t,e):!1!==i.structuralSharing?function t(e,i){if(e===i)return e;let s=p(e)&&p(i);if(s||y(e)&&y(i)){let n=s?e:Object.keys(e),r=n.length,a=s?i:Object.keys(i),o=a.length,l=s?[]:{},u=new Set(n),h=0;for(let n=0;n<o;n++){let r=s?n:a[n];(!s&&u.has(r)||s)&&void 0===e[r]&&void 0===i[r]?(l[r]=void 0,h++):(l[r]=t(e[r],i[r]),l[r]===e[r]&&void 0!==e[r]&&h++)}return r===o&&h===r?e:l}return i}(t,e):e}function b(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,s=[...t,e];return i&&s.length>i?s.slice(1):s}function w(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,s=[e,...t];return i&&s.length>i?s.slice(0,-1):s}var M=Symbol();function O(t,e){return!t.queryFn&&(null==e?void 0:e.initialPromise)?()=>e.initialPromise:t.queryFn&&t.queryFn!==M?t.queryFn:()=>Promise.reject(Error("Missing queryFn: '".concat(t.queryHash,"'")))}function S(t,e){return"function"==typeof t?t(...e):!!t}},37696,88245,t=>{"use strict";function e(t,e,i){if(!e.has(t))throw TypeError("attempted to get private field on non-instance");return i}t.s(["_",()=>e],37696),t.s(["_",()=>s],88245);var i=t.i(34093);function s(t,e){(0,i._)(t,e),e.add(t)}},40143,t=>{"use strict";t.s(["notifyManager",()=>i]);var e=t=>setTimeout(t,0),i=function(){let t=[],i=0,s=t=>{t()},n=t=>{t()},r=e,a=e=>{i?t.push(e):r(()=>{s(e)})};return{batch:e=>{let a;i++;try{a=e()}finally{--i||(()=>{let e=t;t=[],e.length&&r(()=>{n(()=>{e.forEach(t=>{s(t)})})})})()}return a},batchCalls:t=>function(){for(var e=arguments.length,i=Array(e),s=0;s<e;s++)i[s]=arguments[s];a(()=>{t(...i)})},schedule:a,setNotifyFunction:t=>{s=t},setBatchNotifyFunction:t=>{n=t},setScheduler:t=>{r=t}}}()},15823,t=>{"use strict";t.s(["Subscribable",()=>e]);var e=class{subscribe(t){return this.listeners.add(t),this.onSubscribe(),()=>{this.listeners.delete(t),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}}},75555,t=>{"use strict";t.s(["focusManager",()=>u]);var e,i,s,n=t.i(39946),r=t.i(70292),a=t.i(62351),o=t.i(15823),l=t.i(19273),u=new(e=new WeakMap,i=new WeakMap,s=new WeakMap,class extends o.Subscribable{onSubscribe(){(0,n._)(this,i)||this.setEventListener((0,n._)(this,s))}onUnsubscribe(){var t;this.hasListeners()||(null==(t=(0,n._)(this,i))||t.call(this),(0,a._)(this,i,void 0))}setEventListener(t){var e;(0,a._)(this,s,t),null==(e=(0,n._)(this,i))||e.call(this),(0,a._)(this,i,t(t=>{"boolean"==typeof t?this.setFocused(t):this.onFocus()}))}setFocused(t){(0,n._)(this,e)!==t&&((0,a._)(this,e,t),this.onFocus())}onFocus(){let t=this.isFocused();this.listeners.forEach(e=>{e(t)})}isFocused(){var t;return"boolean"==typeof(0,n._)(this,e)?(0,n._)(this,e):(null==(t=globalThis.document)?void 0:t.visibilityState)!=="hidden"}constructor(){super(),(0,r._)(this,e,{writable:!0,value:void 0}),(0,r._)(this,i,{writable:!0,value:void 0}),(0,r._)(this,s,{writable:!0,value:void 0}),(0,a._)(this,s,t=>{if(!l.isServer&&window.addEventListener){let e=()=>t();return window.addEventListener("visibilitychange",e,!1),()=>{window.removeEventListener("visibilitychange",e)}}})}})},86491,14448,93803,36553,88587,t=>{"use strict";t.s(["Query",()=>Q,"fetchState",()=>A],86491),t.i(47167);var e,i,s,n,r,a,o,l,u,h,c,d,f=t.i(39946),p=t.i(70292),y=t.i(62351),_=t.i(37696),g=t.i(88245),m=t.i(19273),b=t.i(40143);t.s(["CancelledError",()=>T,"canFetch",()=>k,"createRetryer",()=>E],36553);var w=t.i(75555);t.s(["onlineManager",()=>O],14448);var M=t.i(15823),O=new(e=new WeakMap,i=new WeakMap,s=new WeakMap,class extends M.Subscribable{onSubscribe(){(0,f._)(this,i)||this.setEventListener((0,f._)(this,s))}onUnsubscribe(){var t;this.hasListeners()||(null==(t=(0,f._)(this,i))||t.call(this),(0,y._)(this,i,void 0))}setEventListener(t){var e;(0,y._)(this,s,t),null==(e=(0,f._)(this,i))||e.call(this),(0,y._)(this,i,t(this.setOnline.bind(this)))}setOnline(t){(0,f._)(this,e)!==t&&((0,y._)(this,e,t),this.listeners.forEach(e=>{e(t)}))}isOnline(){return(0,f._)(this,e)}constructor(){super(),(0,p._)(this,e,{writable:!0,value:!0}),(0,p._)(this,i,{writable:!0,value:void 0}),(0,p._)(this,s,{writable:!0,value:void 0}),(0,y._)(this,s,t=>{if(!m.isServer&&window.addEventListener){let e=()=>t(!0),i=()=>t(!1);return window.addEventListener("online",e,!1),window.addEventListener("offline",i,!1),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",i)}}})}});function S(){let t,e,i=new Promise((i,s)=>{t=i,e=s});function s(t){Object.assign(i,t),delete i.resolve,delete i.reject}return i.status="pending",i.catch(()=>{}),i.resolve=e=>{s({status:"fulfilled",value:e}),t(e)},i.reject=t=>{s({status:"rejected",reason:t}),e(t)},i}function P(t){return Math.min(1e3*2**t,3e4)}function k(t){return(null!=t?t:"online")!=="online"||O.isOnline()}t.s(["pendingThenable",()=>S],93803);var T=class extends Error{constructor(t){super("CancelledError"),this.revert=null==t?void 0:t.revert,this.silent=null==t?void 0:t.silent}};function E(t){let e,i=!1,s=0,n=S(),r=()=>w.focusManager.isFocused()&&("always"===t.networkMode||O.isOnline())&&t.canRun(),a=()=>k(t.networkMode)&&t.canRun(),o=t=>{"pending"===n.status&&(null==e||e(),n.resolve(t))},l=t=>{"pending"===n.status&&(null==e||e(),n.reject(t))},u=()=>new Promise(i=>{var s;e=t=>{("pending"!==n.status||r())&&i(t)},null==(s=t.onPause)||s.call(t)}).then(()=>{if(e=void 0,"pending"===n.status){var i;null==(i=t.onContinue)||i.call(t)}}),h=()=>{let e;if("pending"!==n.status)return;let a=0===s?t.initialPromise:void 0;try{e=null!=a?a:t.fn()}catch(t){e=Promise.reject(t)}Promise.resolve(e).then(o).catch(e=>{var a,o,c;if("pending"!==n.status)return;let d=null!=(o=t.retry)?o:3*!m.isServer,f=null!=(c=t.retryDelay)?c:P,p="function"==typeof f?f(s,e):f,y=!0===d||"number"==typeof d&&s<d||"function"==typeof d&&d(s,e);if(i||!y)return void l(e);s++,null==(a=t.onFail)||a.call(t,s,e),(0,m.sleep)(p).then(()=>r()?void 0:u()).then(()=>{i?l(e):h()})})};return{promise:n,status:()=>n.status,cancel:e=>{if("pending"===n.status){var i;l(new T(e)),null==(i=t.abort)||i.call(t)}},continue:()=>(null==e||e(),n),cancelRetry:()=>{i=!0},continueRetry:()=>{i=!1},canStart:a,start:()=>(a()?h():u().then(h),n)}}t.s(["Removable",()=>F],88587);var F=(n=new WeakMap,class{destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),(0,m.isValidTimeout)(this.gcTime)&&(0,y._)(this,n,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(t){this.gcTime=Math.max(this.gcTime||0,null!=t?t:m.isServer?1/0:3e5)}clearGcTimeout(){(0,f._)(this,n)&&(clearTimeout((0,f._)(this,n)),(0,y._)(this,n,void 0))}constructor(){(0,p._)(this,n,{writable:!0,value:void 0})}}),Q=(r=new WeakMap,a=new WeakMap,o=new WeakMap,l=new WeakMap,u=new WeakMap,h=new WeakMap,c=new WeakMap,d=new WeakSet,class extends F{get meta(){return this.options.meta}get promise(){var t;return null==(t=(0,f._)(this,u))?void 0:t.promise}setOptions(t){this.options={...(0,f._)(this,h),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||(0,f._)(this,o).remove(this)}setData(t,e){let i=(0,m.replaceData)(this.state.data,t,this.options);return(0,_._)(this,d,q).call(this,{data:i,type:"success",dataUpdatedAt:null==e?void 0:e.updatedAt,manual:null==e?void 0:e.manual}),i}setState(t,e){(0,_._)(this,d,q).call(this,{type:"setState",state:t,setStateOptions:e})}cancel(t){var e,i;let s=null==(e=(0,f._)(this,u))?void 0:e.promise;return null==(i=(0,f._)(this,u))||i.cancel(t),s?s.then(m.noop).catch(m.noop):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState((0,f._)(this,r))}isActive(){return this.observers.some(t=>!1!==(0,m.resolveEnabled)(t.options.enabled,this))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===m.skipToken||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0&&this.observers.some(t=>"static"===(0,m.resolveStaleTime)(t.options.staleTime,this))}isStale(){return this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):void 0===this.state.data||this.state.isInvalidated}isStaleByTime(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return void 0===this.state.data||"static"!==t&&(!!this.state.isInvalidated||!(0,m.timeUntilStale)(this.state.dataUpdatedAt,t))}onFocus(){var t;let e=this.observers.find(t=>t.shouldFetchOnWindowFocus());null==e||e.refetch({cancelRefetch:!1}),null==(t=(0,f._)(this,u))||t.continue()}onOnline(){var t;let e=this.observers.find(t=>t.shouldFetchOnReconnect());null==e||e.refetch({cancelRefetch:!1}),null==(t=(0,f._)(this,u))||t.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),(0,f._)(this,o).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(e=>e!==t),this.observers.length||((0,f._)(this,u)&&((0,f._)(this,c)?(0,f._)(this,u).cancel({revert:!0}):(0,f._)(this,u).cancelRetry()),this.scheduleGc()),(0,f._)(this,o).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||(0,_._)(this,d,q).call(this,{type:"invalidate"})}async fetch(t,e){var i,s,n,r,h,p,g,b,w,M,O,S;if("idle"!==this.state.fetchStatus&&(null==(i=(0,f._)(this,u))?void 0:i.status())!=="rejected"){if(void 0!==this.state.data&&(null==e?void 0:e.cancelRefetch))this.cancel({silent:!0});else if((0,f._)(this,u))return(0,f._)(this,u).continueRetry(),(0,f._)(this,u).promise}if(t&&this.setOptions(t),!this.options.queryFn){let t=this.observers.find(t=>t.options.queryFn);t&&this.setOptions(t.options)}let P=new AbortController,k=t=>{Object.defineProperty(t,"signal",{enumerable:!0,get:()=>((0,y._)(this,c,!0),P.signal)})},F=()=>{let t=(0,m.ensureQueryFn)(this.options,e),i=(()=>{let t={client:(0,f._)(this,l),queryKey:this.queryKey,meta:this.meta};return k(t),t})();return((0,y._)(this,c,!1),this.options.persister)?this.options.persister(t,i,this):t(i)},Q=(()=>{let t={fetchOptions:e,options:this.options,queryKey:this.queryKey,client:(0,f._)(this,l),state:this.state,fetchFn:F};return k(t),t})();null==(s=this.options.behavior)||s.onFetch(Q,this),(0,y._)(this,a,this.state),("idle"===this.state.fetchStatus||this.state.fetchMeta!==(null==(n=Q.fetchOptions)?void 0:n.meta))&&(0,_._)(this,d,q).call(this,{type:"fetch",meta:null==(r=Q.fetchOptions)?void 0:r.meta}),(0,y._)(this,u,E({initialPromise:null==e?void 0:e.initialPromise,fn:Q.fetchFn,abort:P.abort.bind(P),onFail:(t,e)=>{(0,_._)(this,d,q).call(this,{type:"failed",failureCount:t,error:e})},onPause:()=>{(0,_._)(this,d,q).call(this,{type:"pause"})},onContinue:()=>{(0,_._)(this,d,q).call(this,{type:"continue"})},retry:Q.options.retry,retryDelay:Q.options.retryDelay,networkMode:Q.options.networkMode,canRun:()=>!0}));try{let t=await (0,f._)(this,u).start();if(void 0===t)throw Error("".concat(this.queryHash," data is undefined"));return this.setData(t),null==(h=(p=(0,f._)(this,o).config).onSuccess)||h.call(p,t,this),null==(g=(b=(0,f._)(this,o).config).onSettled)||g.call(b,t,this.state.error,this),t}catch(t){if(t instanceof T){if(t.silent)return(0,f._)(this,u).promise;else if(t.revert){if(this.setState({...(0,f._)(this,a),fetchStatus:"idle"}),void 0===this.state.data)throw t;return this.state.data}}throw(0,_._)(this,d,q).call(this,{type:"error",error:t}),null==(w=(M=(0,f._)(this,o).config).onError)||w.call(M,t,this),null==(O=(S=(0,f._)(this,o).config).onSettled)||O.call(S,this.state.data,t,this),t}finally{this.scheduleGc()}}constructor(t){var e;super(),(0,g._)(this,d),(0,p._)(this,r,{writable:!0,value:void 0}),(0,p._)(this,a,{writable:!0,value:void 0}),(0,p._)(this,o,{writable:!0,value:void 0}),(0,p._)(this,l,{writable:!0,value:void 0}),(0,p._)(this,u,{writable:!0,value:void 0}),(0,p._)(this,h,{writable:!0,value:void 0}),(0,p._)(this,c,{writable:!0,value:void 0}),(0,y._)(this,c,!1),(0,y._)(this,h,t.defaultOptions),this.setOptions(t.options),this.observers=[],(0,y._)(this,l,t.client),(0,y._)(this,o,(0,f._)(this,l).getQueryCache()),this.queryKey=t.queryKey,this.queryHash=t.queryHash,(0,y._)(this,r,function(t){let e="function"==typeof t.initialData?t.initialData():t.initialData,i=void 0!==e,s=i?"function"==typeof t.initialDataUpdatedAt?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0;return{data:e,dataUpdateCount:0,dataUpdatedAt:i?null!=s?s:Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:i?"success":"pending",fetchStatus:"idle"}}(this.options)),this.state=null!=(e=t.state)?e:(0,f._)(this,r),this.scheduleGc()}});function A(t,e){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:k(e.networkMode)?"fetching":"paused",...void 0===t&&{error:null,status:"pending"}}}function q(t){let e=e=>{var i,s;switch(t.type){case"failed":return{...e,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...e,fetchStatus:"paused"};case"continue":return{...e,fetchStatus:"fetching"};case"fetch":return{...e,...A(e.data,this.options),fetchMeta:null!=(i=t.meta)?i:null};case"success":let n={...e,data:t.data,dataUpdateCount:e.dataUpdateCount+1,dataUpdatedAt:null!=(s=t.dataUpdatedAt)?s:Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};return(0,y._)(this,a,t.manual?n:void 0),n;case"error":let r=t.error;return{...e,error:r,errorUpdateCount:e.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:e.fetchFailureCount+1,fetchFailureReason:r,fetchStatus:"idle",status:"error"};case"invalidate":return{...e,isInvalidated:!0};case"setState":return{...e,...t.state}}};this.state=e(this.state),b.notifyManager.batch(()=>{this.observers.forEach(t=>{t.onQueryUpdate()}),(0,f._)(this,o).notify({query:this,type:"updated",action:t})})}},14272,t=>{"use strict";t.s(["Mutation",()=>f,"getDefaultState",()=>p]);var e,i,s,n,r=t.i(39946),a=t.i(70292),o=t.i(62351),l=t.i(37696),u=t.i(88245),h=t.i(40143),c=t.i(88587),d=t.i(36553),f=(e=new WeakMap,i=new WeakMap,s=new WeakMap,n=new WeakSet,class extends c.Removable{setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){(0,r._)(this,e).includes(t)||((0,r._)(this,e).push(t),this.clearGcTimeout(),(0,r._)(this,i).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){(0,o._)(this,e,(0,r._)(this,e).filter(e=>e!==t)),this.scheduleGc(),(0,r._)(this,i).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){(0,r._)(this,e).length||("pending"===this.state.status?this.scheduleGc():(0,r._)(this,i).remove(this))}continue(){var t,e;return null!=(e=null==(t=(0,r._)(this,s))?void 0:t.continue())?e:this.execute(this.state.variables)}async execute(t){var e,a,u,h,c,f,p,_,g,m,b,w,M,O,S,P,k,T,E,F,Q;let A=()=>{(0,l._)(this,n,y).call(this,{type:"continue"})};(0,o._)(this,s,(0,d.createRetryer)({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(Error("No mutationFn found")),onFail:(t,e)=>{(0,l._)(this,n,y).call(this,{type:"failed",failureCount:t,error:e})},onPause:()=>{(0,l._)(this,n,y).call(this,{type:"pause"})},onContinue:A,retry:null!=(e=this.options.retry)?e:0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>(0,r._)(this,i).canRun(this)}));let q="pending"===this.state.status,C=!(0,r._)(this,s).canStart();try{if(q)A();else{(0,l._)(this,n,y).call(this,{type:"pending",variables:t,isPaused:C}),await (null==(m=(b=(0,r._)(this,i).config).onMutate)?void 0:m.call(b,t,this));let e=await (null==(w=(M=this.options).onMutate)?void 0:w.call(M,t));e!==this.state.context&&(0,l._)(this,n,y).call(this,{type:"pending",context:e,variables:t,isPaused:C})}let e=await (0,r._)(this,s).start();return await (null==(a=(u=(0,r._)(this,i).config).onSuccess)?void 0:a.call(u,e,t,this.state.context,this)),await (null==(h=(c=this.options).onSuccess)?void 0:h.call(c,e,t,this.state.context)),await (null==(f=(p=(0,r._)(this,i).config).onSettled)?void 0:f.call(p,e,null,this.state.variables,this.state.context,this)),await (null==(_=(g=this.options).onSettled)?void 0:_.call(g,e,null,t,this.state.context)),(0,l._)(this,n,y).call(this,{type:"success",data:e}),e}catch(e){try{throw await (null==(O=(S=(0,r._)(this,i).config).onError)?void 0:O.call(S,e,t,this.state.context,this)),await (null==(P=(k=this.options).onError)?void 0:P.call(k,e,t,this.state.context)),await (null==(T=(E=(0,r._)(this,i).config).onSettled)?void 0:T.call(E,void 0,e,this.state.variables,this.state.context,this)),await (null==(F=(Q=this.options).onSettled)?void 0:F.call(Q,void 0,e,t,this.state.context)),e}finally{(0,l._)(this,n,y).call(this,{type:"error",error:e})}}finally{(0,r._)(this,i).runNext(this)}}constructor(t){super(),(0,u._)(this,n),(0,a._)(this,e,{writable:!0,value:void 0}),(0,a._)(this,i,{writable:!0,value:void 0}),(0,a._)(this,s,{writable:!0,value:void 0}),this.mutationId=t.mutationId,(0,o._)(this,i,t.mutationCache),(0,o._)(this,e,[]),this.state=t.state||p(),this.setOptions(t.options),this.scheduleGc()}});function p(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}function y(t){this.state=(e=>{switch(t.type){case"failed":return{...e,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...e,isPaused:!0};case"continue":return{...e,isPaused:!1};case"pending":return{...e,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...e,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...e,data:void 0,error:t.error,failureCount:e.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}})(this.state),h.notifyManager.batch(()=>{(0,r._)(this,e).forEach(e=>{e.onMutationUpdate(t)}),(0,r._)(this,i).notify({mutation:this,type:"updated",action:t})})}},44636,t=>{"use strict";t.s(["Providers",()=>x],44636);var e,i,s,n,r,a,o,l,u,h,c,d,f=t.i(43476),p=t.i(39946),y=t.i(70292),_=t.i(62351),g=t.i(26005);function m(t,e){var i=(0,g._)(t,e,"update");if(i.set){if(!i.get)throw TypeError("attempted to read set only private field");return"__destrWrapper"in i||(i.__destrWrapper={set value(v){i.set.call(t,v)},get value(){return i.get.call(t)}}),i.__destrWrapper}if(!i.writable)throw TypeError("attempted to set read only private field");return i}var b=t.i(19273),w=t.i(86491),M=t.i(40143),O=t.i(15823),S=(e=new WeakMap,class extends O.Subscribable{build(t,e,i){var s;let n=e.queryKey,r=null!=(s=e.queryHash)?s:(0,b.hashQueryKeyByOptions)(n,e),a=this.get(r);return a||(a=new w.Query({client:t,queryKey:n,queryHash:r,options:t.defaultQueryOptions(e),state:i,defaultOptions:t.getQueryDefaults(n)}),this.add(a)),a}add(t){(0,p._)(this,e).has(t.queryHash)||((0,p._)(this,e).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){let i=(0,p._)(this,e).get(t.queryHash);i&&(t.destroy(),i===t&&(0,p._)(this,e).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){M.notifyManager.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return(0,p._)(this,e).get(t)}getAll(){return[...(0,p._)(this,e).values()]}find(t){let e={exact:!0,...t};return this.getAll().find(t=>(0,b.matchQuery)(e,t))}findAll(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=this.getAll();return Object.keys(t).length>0?e.filter(e=>(0,b.matchQuery)(t,e)):e}notify(t){M.notifyManager.batch(()=>{this.listeners.forEach(e=>{e(t)})})}onFocus(){M.notifyManager.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){M.notifyManager.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}constructor(t={}){super(),(0,y._)(this,e,{writable:!0,value:void 0}),this.config=t,(0,_._)(this,e,new Map)}}),P=t.i(14272),k=O,T=(i=new WeakMap,s=new WeakMap,n=new WeakMap,class extends k.Subscribable{build(t,e,i){let s=new P.Mutation({mutationCache:this,mutationId:++m(this,n).value,options:t.defaultMutationOptions(e),state:i});return this.add(s),s}add(t){(0,p._)(this,i).add(t);let e=E(t);if("string"==typeof e){let i=(0,p._)(this,s).get(e);i?i.push(t):(0,p._)(this,s).set(e,[t])}this.notify({type:"added",mutation:t})}remove(t){if((0,p._)(this,i).delete(t)){let e=E(t);if("string"==typeof e){let i=(0,p._)(this,s).get(e);if(i)if(i.length>1){let e=i.indexOf(t);-1!==e&&i.splice(e,1)}else i[0]===t&&(0,p._)(this,s).delete(e)}}this.notify({type:"removed",mutation:t})}canRun(t){let e=E(t);if("string"!=typeof e)return!0;{let i=(0,p._)(this,s).get(e),n=null==i?void 0:i.find(t=>"pending"===t.state.status);return!n||n===t}}runNext(t){let e=E(t);if("string"!=typeof e)return Promise.resolve();{var i,n;let r=null==(i=(0,p._)(this,s).get(e))?void 0:i.find(e=>e!==t&&e.state.isPaused);return null!=(n=null==r?void 0:r.continue())?n:Promise.resolve()}}clear(){M.notifyManager.batch(()=>{(0,p._)(this,i).forEach(t=>{this.notify({type:"removed",mutation:t})}),(0,p._)(this,i).clear(),(0,p._)(this,s).clear()})}getAll(){return Array.from((0,p._)(this,i))}find(t){let e={exact:!0,...t};return this.getAll().find(t=>(0,b.matchMutation)(e,t))}findAll(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.getAll().filter(e=>(0,b.matchMutation)(t,e))}notify(t){M.notifyManager.batch(()=>{this.listeners.forEach(e=>{e(t)})})}resumePausedMutations(){let t=this.getAll().filter(t=>t.state.isPaused);return M.notifyManager.batch(()=>Promise.all(t.map(t=>t.continue().catch(b.noop))))}constructor(t={}){super(),(0,y._)(this,i,{writable:!0,value:void 0}),(0,y._)(this,s,{writable:!0,value:void 0}),(0,y._)(this,n,{writable:!0,value:void 0}),this.config=t,(0,_._)(this,i,new Set),(0,_._)(this,s,new Map),(0,_._)(this,n,0)}});function E(t){var e;return null==(e=t.options.scope)?void 0:e.id}var F=t.i(75555),Q=t.i(14448);function A(t){return{onFetch:(e,i)=>{var s,n,r,a,o;let l=e.options,u=null==(r=e.fetchOptions)||null==(n=r.meta)||null==(s=n.fetchMore)?void 0:s.direction,h=(null==(a=e.state.data)?void 0:a.pages)||[],c=(null==(o=e.state.data)?void 0:o.pageParams)||[],d={pages:[],pageParams:[]},f=0,p=async()=>{let i=!1,s=(0,b.ensureQueryFn)(e.options,e.fetchOptions),n=async(t,n,r)=>{if(i)return Promise.reject();if(null==n&&t.pages.length)return Promise.resolve(t);let a=(()=>{let t={client:e.client,queryKey:e.queryKey,pageParam:n,direction:r?"backward":"forward",meta:e.options.meta};return Object.defineProperty(t,"signal",{enumerable:!0,get:()=>(e.signal.aborted?i=!0:e.signal.addEventListener("abort",()=>{i=!0}),e.signal)}),t})(),o=await s(a),{maxPages:l}=e.options,u=r?b.addToStart:b.addToEnd;return{pages:u(t.pages,o,l),pageParams:u(t.pageParams,n,l)}};if(u&&h.length){let t="backward"===u,e={pages:h,pageParams:c},i=(t?function(t,e){var i;let{pages:s,pageParams:n}=e;return s.length>0?null==(i=t.getPreviousPageParam)?void 0:i.call(t,s[0],s,n[0],n):void 0}:q)(l,e);d=await n(e,i,t)}else{let e=null!=t?t:h.length;do{var r;let t=0===f?null!=(r=c[0])?r:l.initialPageParam:q(l,d);if(f>0&&null==t)break;d=await n(d,t),f++}while(f<e)}return d};e.options.persister?e.fetchFn=()=>{var t,s;return null==(t=(s=e.options).persister)?void 0:t.call(s,p,{client:e.client,queryKey:e.queryKey,meta:e.options.meta,signal:e.signal},i)}:e.fetchFn=p}}}function q(t,e){let{pages:i,pageParams:s}=e,n=i.length-1;return i.length>0?t.getNextPageParam(i[n],i,s[n],s):void 0}var C=(r=new WeakMap,a=new WeakMap,o=new WeakMap,l=new WeakMap,u=new WeakMap,h=new WeakMap,c=new WeakMap,d=new WeakMap,class{mount(){m(this,h).value++,1===(0,p._)(this,h)&&((0,_._)(this,c,F.focusManager.subscribe(async t=>{t&&(await this.resumePausedMutations(),(0,p._)(this,r).onFocus())})),(0,_._)(this,d,Q.onlineManager.subscribe(async t=>{t&&(await this.resumePausedMutations(),(0,p._)(this,r).onOnline())})))}unmount(){var t,e;m(this,h).value--,0===(0,p._)(this,h)&&(null==(t=(0,p._)(this,c))||t.call(this),(0,_._)(this,c,void 0),null==(e=(0,p._)(this,d))||e.call(this),(0,_._)(this,d,void 0))}isFetching(t){return(0,p._)(this,r).findAll({...t,fetchStatus:"fetching"}).length}isMutating(t){return(0,p._)(this,a).findAll({...t,status:"pending"}).length}getQueryData(t){var e;let i=this.defaultQueryOptions({queryKey:t});return null==(e=(0,p._)(this,r).get(i.queryHash))?void 0:e.state.data}ensureQueryData(t){let e=this.defaultQueryOptions(t),i=(0,p._)(this,r).build(this,e),s=i.state.data;return void 0===s?this.fetchQuery(t):(t.revalidateIfStale&&i.isStaleByTime((0,b.resolveStaleTime)(e.staleTime,i))&&this.prefetchQuery(e),Promise.resolve(s))}getQueriesData(t){return(0,p._)(this,r).findAll(t).map(t=>{let{queryKey:e,state:i}=t;return[e,i.data]})}setQueryData(t,e,i){let s=this.defaultQueryOptions({queryKey:t}),n=(0,p._)(this,r).get(s.queryHash),a=null==n?void 0:n.state.data,o=(0,b.functionalUpdate)(e,a);if(void 0!==o)return(0,p._)(this,r).build(this,s).setData(o,{...i,manual:!0})}setQueriesData(t,e,i){return M.notifyManager.batch(()=>(0,p._)(this,r).findAll(t).map(t=>{let{queryKey:s}=t;return[s,this.setQueryData(s,e,i)]}))}getQueryState(t){var e;let i=this.defaultQueryOptions({queryKey:t});return null==(e=(0,p._)(this,r).get(i.queryHash))?void 0:e.state}removeQueries(t){let e=(0,p._)(this,r);M.notifyManager.batch(()=>{e.findAll(t).forEach(t=>{e.remove(t)})})}resetQueries(t,e){let i=(0,p._)(this,r);return M.notifyManager.batch(()=>(i.findAll(t).forEach(t=>{t.reset()}),this.refetchQueries({type:"active",...t},e)))}cancelQueries(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i={revert:!0,...e};return Promise.all(M.notifyManager.batch(()=>(0,p._)(this,r).findAll(t).map(t=>t.cancel(i)))).then(b.noop).catch(b.noop)}invalidateQueries(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return M.notifyManager.batch(()=>{var i,s;return((0,p._)(this,r).findAll(t).forEach(t=>{t.invalidate()}),(null==t?void 0:t.refetchType)==="none")?Promise.resolve():this.refetchQueries({...t,type:null!=(s=null!=(i=null==t?void 0:t.refetchType)?i:null==t?void 0:t.type)?s:"active"},e)})}refetchQueries(t){var e;let i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s={...i,cancelRefetch:null==(e=i.cancelRefetch)||e};return Promise.all(M.notifyManager.batch(()=>(0,p._)(this,r).findAll(t).filter(t=>!t.isDisabled()&&!t.isStatic()).map(t=>{let e=t.fetch(void 0,s);return s.throwOnError||(e=e.catch(b.noop)),"paused"===t.state.fetchStatus?Promise.resolve():e}))).then(b.noop)}fetchQuery(t){let e=this.defaultQueryOptions(t);void 0===e.retry&&(e.retry=!1);let i=(0,p._)(this,r).build(this,e);return i.isStaleByTime((0,b.resolveStaleTime)(e.staleTime,i))?i.fetch(e):Promise.resolve(i.state.data)}prefetchQuery(t){return this.fetchQuery(t).then(b.noop).catch(b.noop)}fetchInfiniteQuery(t){return t.behavior=A(t.pages),this.fetchQuery(t)}prefetchInfiniteQuery(t){return this.fetchInfiniteQuery(t).then(b.noop).catch(b.noop)}ensureInfiniteQueryData(t){return t.behavior=A(t.pages),this.ensureQueryData(t)}resumePausedMutations(){return Q.onlineManager.isOnline()?(0,p._)(this,a).resumePausedMutations():Promise.resolve()}getQueryCache(){return(0,p._)(this,r)}getMutationCache(){return(0,p._)(this,a)}getDefaultOptions(){return(0,p._)(this,o)}setDefaultOptions(t){(0,_._)(this,o,t)}setQueryDefaults(t,e){(0,p._)(this,l).set((0,b.hashKey)(t),{queryKey:t,defaultOptions:e})}getQueryDefaults(t){let e=[...(0,p._)(this,l).values()],i={};return e.forEach(e=>{(0,b.partialMatchKey)(t,e.queryKey)&&Object.assign(i,e.defaultOptions)}),i}setMutationDefaults(t,e){(0,p._)(this,u).set((0,b.hashKey)(t),{mutationKey:t,defaultOptions:e})}getMutationDefaults(t){let e=[...(0,p._)(this,u).values()],i={};return e.forEach(e=>{(0,b.partialMatchKey)(t,e.mutationKey)&&Object.assign(i,e.defaultOptions)}),i}defaultQueryOptions(t){if(t._defaulted)return t;let e={...(0,p._)(this,o).queries,...this.getQueryDefaults(t.queryKey),...t,_defaulted:!0};return e.queryHash||(e.queryHash=(0,b.hashQueryKeyByOptions)(e.queryKey,e)),void 0===e.refetchOnReconnect&&(e.refetchOnReconnect="always"!==e.networkMode),void 0===e.throwOnError&&(e.throwOnError=!!e.suspense),!e.networkMode&&e.persister&&(e.networkMode="offlineFirst"),e.queryFn===b.skipToken&&(e.enabled=!1),e}defaultMutationOptions(t){return(null==t?void 0:t._defaulted)?t:{...(0,p._)(this,o).mutations,...(null==t?void 0:t.mutationKey)&&this.getMutationDefaults(t.mutationKey),...t,_defaulted:!0}}clear(){(0,p._)(this,r).clear(),(0,p._)(this,a).clear()}constructor(t={}){(0,y._)(this,r,{writable:!0,value:void 0}),(0,y._)(this,a,{writable:!0,value:void 0}),(0,y._)(this,o,{writable:!0,value:void 0}),(0,y._)(this,l,{writable:!0,value:void 0}),(0,y._)(this,u,{writable:!0,value:void 0}),(0,y._)(this,h,{writable:!0,value:void 0}),(0,y._)(this,c,{writable:!0,value:void 0}),(0,y._)(this,d,{writable:!0,value:void 0}),(0,_._)(this,r,t.queryCache||new S),(0,_._)(this,a,t.mutationCache||new T),(0,_._)(this,o,t.defaultOptions||{}),(0,_._)(this,l,new Map),(0,_._)(this,u,new Map),(0,_._)(this,h,0)}}),D=t.i(12598);t.i(47167);var R=function(){return null},W=t.i(71645),j=t.i(99346);function x(t){let{children:e}=t,[i]=(0,W.useState)(()=>new C({defaultOptions:{queries:{staleTime:6e4,refetchOnWindowFocus:!1}}}));return(0,f.jsx)(D.QueryClientProvider,{client:i,children:(0,f.jsxs)(j.WebSocketProvider,{children:[e,(0,f.jsx)(R,{initialIsOpen:!1})]})})}},13354,t=>{"use strict";t.s(["Toaster",()=>o],13354);var e=t.i(43476),i=t.i(71645),s=(t,e,i,s,n,r,a,o)=>{let l=document.documentElement,u=["light","dark"];function h(e){var i;(Array.isArray(t)?t:[t]).forEach(t=>{let i="class"===t,s=i&&r?n.map(t=>r[t]||t):n;i?(l.classList.remove(...s),l.classList.add(r&&r[e]?r[e]:e)):l.setAttribute(t,e)}),i=e,o&&u.includes(i)&&(l.style.colorScheme=i)}if(s)h(s);else try{let t=localStorage.getItem(e)||i,s=a&&"system"===t?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":t;h(s)}catch(t){}},n=i.createContext(void 0),r={setTheme:t=>{},themes:[]};i.memo(t=>{let{forcedTheme:e,storageKey:n,attribute:r,enableSystem:a,enableColorScheme:o,defaultTheme:l,value:u,themes:h,nonce:c,scriptProps:d}=t,f=JSON.stringify([r,n,l,e,h,u,a,o]).slice(1,-1);return i.createElement("script",{...d,suppressHydrationWarning:!0,nonce:"undefined"==typeof window?c:"",dangerouslySetInnerHTML:{__html:"(".concat(s.toString(),")(").concat(f,")")}})});var a=t.i(46696);let o=t=>{let{...s}=t,{theme:o="system"}=(()=>{var t;return null!=(t=i.useContext(n))?t:r})();return(0,e.jsx)(a.Toaster,{theme:o,className:"toaster group",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...s})}}]);