'use client';

import { Badge } from '@/components/ui/badge';
import { Alert } from '@/types';
import { AlertTriangle, Clock, MapPin } from 'lucide-react';
import { cn } from '@/lib/utils';

interface RecentAlertsProps {
  alerts?: Alert[];
  loading?: boolean;
}

export function RecentAlerts({ alerts, loading }: RecentAlertsProps) {
  const getStatusColor = (status: Alert['status']) => {
    switch (status) {
      case 'active':
        return 'bg-red-500/10 text-red-500 border-red-500/20';
      case 'investigating':
        return 'bg-yellow-500/10 text-yellow-500 border-yellow-500/20';
      case 'resolved':
        return 'bg-green-500/10 text-green-500 border-green-500/20';
      default:
        return 'bg-gray-500/10 text-gray-500 border-gray-500/20';
    }
  };

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const alertTime = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - alertTime.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  if (loading) {
    return (
      <div className="space-y-3">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="flex items-center space-x-3">
              <div className="h-8 w-8 bg-muted rounded-full"></div>
              <div className="flex-1 space-y-1">
                <div className="h-4 bg-muted rounded w-3/4"></div>
                <div className="h-3 bg-muted rounded w-1/2"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (!alerts || alerts.length === 0) {
    return (
      <div className="text-center py-6">
        <AlertTriangle className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
        <p className="text-sm text-muted-foreground">No recent alerts</p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {alerts.map((alert) => (
        <div key={alert.id} className="flex items-start space-x-3 p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors">
          <div className="flex-shrink-0">
            <AlertTriangle className="h-5 w-5 text-orange-500 mt-0.5" />
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between mb-1">
              <p className="text-sm font-medium text-foreground truncate">
                {alert.objects.join(', ')} detected
              </p>
              <Badge className={cn("text-xs", getStatusColor(alert.status))}>
                {alert.status}
              </Badge>
            </div>
            
            <div className="flex items-center text-xs text-muted-foreground space-x-3">
              <div className="flex items-center">
                <Clock className="h-3 w-3 mr-1" />
                {formatTimeAgo(alert.timestamp)}
              </div>
              
              <div className="flex items-center">
                <span>Confidence: {Math.round(alert.confidence * 100)}%</span>
              </div>
              
              {alert.location && (
                <div className="flex items-center">
                  <MapPin className="h-3 w-3 mr-1" />
                  <span>Location</span>
                </div>
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
