"use client";

import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { AlertTriangle, Clock, TrendingUp, Camera } from "lucide-react";
import { apiService } from "@/services/api";
import { HealthCheck } from "./HealthCheck";
import { RecentAlerts } from "./RecentAlerts";
import { CameraFeed } from "./CameraFeed";

export function DashboardOverview() {
  const { data: stats, isLoading: statsLoading } = useQuery({
    queryKey: ["alert-stats"],
    queryFn: () => apiService.getAlertStats(),
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  const { data: recentAlerts, isLoading: alertsLoading } = useQuery({
    queryKey: ["recent-alerts"],
    queryFn: () => apiService.getAlerts(5),
    refetchInterval: 10000, // Refetch every 10 seconds
  });

  return (
    <div className="space-y-6">
      {/* Health Status */}
      <HealthCheck />

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Alerts</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {statsLoading ? "..." : stats?.total_alerts || 0}
            </div>
            <p className="text-xs text-muted-foreground">All time detections</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Today</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {statsLoading ? "..." : stats?.alerts_today || 0}
            </div>
            <p className="text-xs text-muted-foreground">Alerts in last 24h</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">This Week</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {statsLoading ? "..." : stats?.alerts_this_week || 0}
            </div>
            <p className="text-xs text-muted-foreground">Weekly detections</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Active Cameras
            </CardTitle>
            <Camera className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1</div>
            <p className="text-xs text-muted-foreground">Monitoring zones</p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <div className="grid gap-4 lg:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle>Recent Alerts</CardTitle>
          </CardHeader>
          <CardContent>
            <RecentAlerts alerts={recentAlerts} loading={alertsLoading} />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Detection Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {statsLoading ? (
                <div className="text-sm text-muted-foreground">Loading...</div>
              ) : (
                stats?.detection_counts &&
                Object.entries(stats.detection_counts).map(
                  ([object, count]) => (
                    <div
                      key={object}
                      className="flex items-center justify-between"
                    >
                      <span className="text-sm font-medium capitalize">
                        {object}
                      </span>
                      <Badge variant="secondary">{count}</Badge>
                    </div>
                  )
                )
              )}
            </div>
          </CardContent>
        </Card>

        <div className="lg:col-span-1">
          <CameraFeed />
        </div>
      </div>
    </div>
  );
}
