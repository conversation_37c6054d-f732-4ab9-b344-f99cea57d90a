module.exports=[43518,a=>{"use strict";a.s(["Providers",()=>u],43518);var b=a.i(87924),c=a.i(42871),d=a.i(76644),e=a.i(18544),f=a.i(33791),g=class extends f.Subscribable{constructor(a={}){super(),this.config=a,this.#a=new Map}#a;build(a,b,e){let f=b.queryKey,g=b.queryHash??(0,c.hashQueryKeyByOptions)(f,b),h=this.get(g);return h||(h=new d.Query({client:a,queryKey:f,queryHash:g,options:a.defaultQueryOptions(b),state:e,defaultOptions:a.getQueryDefaults(f)}),this.add(h)),h}add(a){this.#a.has(a.queryHash)||(this.#a.set(a.queryHash,a),this.notify({type:"added",query:a}))}remove(a){let b=this.#a.get(a.queryHash);b&&(a.destroy(),b===a&&this.#a.delete(a.queryHash),this.notify({type:"removed",query:a}))}clear(){e.notifyManager.batch(()=>{this.getAll().forEach(a=>{this.remove(a)})})}get(a){return this.#a.get(a)}getAll(){return[...this.#a.values()]}find(a){let b={exact:!0,...a};return this.getAll().find(a=>(0,c.matchQuery)(b,a))}findAll(a={}){let b=this.getAll();return Object.keys(a).length>0?b.filter(b=>(0,c.matchQuery)(a,b)):b}notify(a){e.notifyManager.batch(()=>{this.listeners.forEach(b=>{b(a)})})}onFocus(){e.notifyManager.batch(()=>{this.getAll().forEach(a=>{a.onFocus()})})}onOnline(){e.notifyManager.batch(()=>{this.getAll().forEach(a=>{a.onOnline()})})}},h=a.i(12794),i=f,j=class extends i.Subscribable{constructor(a={}){super(),this.config=a,this.#b=new Set,this.#c=new Map,this.#d=0}#b;#c;#d;build(a,b,c){let d=new h.Mutation({mutationCache:this,mutationId:++this.#d,options:a.defaultMutationOptions(b),state:c});return this.add(d),d}add(a){this.#b.add(a);let b=k(a);if("string"==typeof b){let c=this.#c.get(b);c?c.push(a):this.#c.set(b,[a])}this.notify({type:"added",mutation:a})}remove(a){if(this.#b.delete(a)){let b=k(a);if("string"==typeof b){let c=this.#c.get(b);if(c)if(c.length>1){let b=c.indexOf(a);-1!==b&&c.splice(b,1)}else c[0]===a&&this.#c.delete(b)}}this.notify({type:"removed",mutation:a})}canRun(a){let b=k(a);if("string"!=typeof b)return!0;{let c=this.#c.get(b),d=c?.find(a=>"pending"===a.state.status);return!d||d===a}}runNext(a){let b=k(a);if("string"!=typeof b)return Promise.resolve();{let c=this.#c.get(b)?.find(b=>b!==a&&b.state.isPaused);return c?.continue()??Promise.resolve()}}clear(){e.notifyManager.batch(()=>{this.#b.forEach(a=>{this.notify({type:"removed",mutation:a})}),this.#b.clear(),this.#c.clear()})}getAll(){return Array.from(this.#b)}find(a){let b={exact:!0,...a};return this.getAll().find(a=>(0,c.matchMutation)(b,a))}findAll(a={}){return this.getAll().filter(b=>(0,c.matchMutation)(a,b))}notify(a){e.notifyManager.batch(()=>{this.listeners.forEach(b=>{b(a)})})}resumePausedMutations(){let a=this.getAll().filter(a=>a.state.isPaused);return e.notifyManager.batch(()=>Promise.all(a.map(a=>a.continue().catch(c.noop))))}};function k(a){return a.options.scope?.id}var l=a.i(99745),m=a.i(12552);function n(a){return{onFetch:(b,d)=>{let e=b.options,f=b.fetchOptions?.meta?.fetchMore?.direction,g=b.state.data?.pages||[],h=b.state.data?.pageParams||[],i={pages:[],pageParams:[]},j=0,k=async()=>{let d=!1,k=(0,c.ensureQueryFn)(b.options,b.fetchOptions),l=async(a,e,f)=>{if(d)return Promise.reject();if(null==e&&a.pages.length)return Promise.resolve(a);let g=(()=>{let a={client:b.client,queryKey:b.queryKey,pageParam:e,direction:f?"backward":"forward",meta:b.options.meta};return Object.defineProperty(a,"signal",{enumerable:!0,get:()=>(b.signal.aborted?d=!0:b.signal.addEventListener("abort",()=>{d=!0}),b.signal)}),a})(),h=await k(g),{maxPages:i}=b.options,j=f?c.addToStart:c.addToEnd;return{pages:j(a.pages,h,i),pageParams:j(a.pageParams,e,i)}};if(f&&g.length){let a="backward"===f,b={pages:g,pageParams:h},c=(a?function(a,{pages:b,pageParams:c}){return b.length>0?a.getPreviousPageParam?.(b[0],b,c[0],c):void 0}:o)(e,b);i=await l(b,c,a)}else{let b=a??g.length;do{let a=0===j?h[0]??e.initialPageParam:o(e,i);if(j>0&&null==a)break;i=await l(i,a),j++}while(j<b)}return i};b.options.persister?b.fetchFn=()=>b.options.persister?.(k,{client:b.client,queryKey:b.queryKey,meta:b.options.meta,signal:b.signal},d):b.fetchFn=k}}}function o(a,{pages:b,pageParams:c}){let d=b.length-1;return b.length>0?a.getNextPageParam(b[d],b,c[d],c):void 0}var p=class{#e;#f;#g;#h;#i;#j;#k;#l;constructor(a={}){this.#e=a.queryCache||new g,this.#f=a.mutationCache||new j,this.#g=a.defaultOptions||{},this.#h=new Map,this.#i=new Map,this.#j=0}mount(){this.#j++,1===this.#j&&(this.#k=l.focusManager.subscribe(async a=>{a&&(await this.resumePausedMutations(),this.#e.onFocus())}),this.#l=m.onlineManager.subscribe(async a=>{a&&(await this.resumePausedMutations(),this.#e.onOnline())}))}unmount(){this.#j--,0===this.#j&&(this.#k?.(),this.#k=void 0,this.#l?.(),this.#l=void 0)}isFetching(a){return this.#e.findAll({...a,fetchStatus:"fetching"}).length}isMutating(a){return this.#f.findAll({...a,status:"pending"}).length}getQueryData(a){let b=this.defaultQueryOptions({queryKey:a});return this.#e.get(b.queryHash)?.state.data}ensureQueryData(a){let b=this.defaultQueryOptions(a),d=this.#e.build(this,b),e=d.state.data;return void 0===e?this.fetchQuery(a):(a.revalidateIfStale&&d.isStaleByTime((0,c.resolveStaleTime)(b.staleTime,d))&&this.prefetchQuery(b),Promise.resolve(e))}getQueriesData(a){return this.#e.findAll(a).map(({queryKey:a,state:b})=>[a,b.data])}setQueryData(a,b,d){let e=this.defaultQueryOptions({queryKey:a}),f=this.#e.get(e.queryHash),g=f?.state.data,h=(0,c.functionalUpdate)(b,g);if(void 0!==h)return this.#e.build(this,e).setData(h,{...d,manual:!0})}setQueriesData(a,b,c){return e.notifyManager.batch(()=>this.#e.findAll(a).map(({queryKey:a})=>[a,this.setQueryData(a,b,c)]))}getQueryState(a){let b=this.defaultQueryOptions({queryKey:a});return this.#e.get(b.queryHash)?.state}removeQueries(a){let b=this.#e;e.notifyManager.batch(()=>{b.findAll(a).forEach(a=>{b.remove(a)})})}resetQueries(a,b){let c=this.#e;return e.notifyManager.batch(()=>(c.findAll(a).forEach(a=>{a.reset()}),this.refetchQueries({type:"active",...a},b)))}cancelQueries(a,b={}){let d={revert:!0,...b};return Promise.all(e.notifyManager.batch(()=>this.#e.findAll(a).map(a=>a.cancel(d)))).then(c.noop).catch(c.noop)}invalidateQueries(a,b={}){return e.notifyManager.batch(()=>(this.#e.findAll(a).forEach(a=>{a.invalidate()}),a?.refetchType==="none")?Promise.resolve():this.refetchQueries({...a,type:a?.refetchType??a?.type??"active"},b))}refetchQueries(a,b={}){let d={...b,cancelRefetch:b.cancelRefetch??!0};return Promise.all(e.notifyManager.batch(()=>this.#e.findAll(a).filter(a=>!a.isDisabled()&&!a.isStatic()).map(a=>{let b=a.fetch(void 0,d);return d.throwOnError||(b=b.catch(c.noop)),"paused"===a.state.fetchStatus?Promise.resolve():b}))).then(c.noop)}fetchQuery(a){let b=this.defaultQueryOptions(a);void 0===b.retry&&(b.retry=!1);let d=this.#e.build(this,b);return d.isStaleByTime((0,c.resolveStaleTime)(b.staleTime,d))?d.fetch(b):Promise.resolve(d.state.data)}prefetchQuery(a){return this.fetchQuery(a).then(c.noop).catch(c.noop)}fetchInfiniteQuery(a){return a.behavior=n(a.pages),this.fetchQuery(a)}prefetchInfiniteQuery(a){return this.fetchInfiniteQuery(a).then(c.noop).catch(c.noop)}ensureInfiniteQueryData(a){return a.behavior=n(a.pages),this.ensureQueryData(a)}resumePausedMutations(){return m.onlineManager.isOnline()?this.#f.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#e}getMutationCache(){return this.#f}getDefaultOptions(){return this.#g}setDefaultOptions(a){this.#g=a}setQueryDefaults(a,b){this.#h.set((0,c.hashKey)(a),{queryKey:a,defaultOptions:b})}getQueryDefaults(a){let b=[...this.#h.values()],d={};return b.forEach(b=>{(0,c.partialMatchKey)(a,b.queryKey)&&Object.assign(d,b.defaultOptions)}),d}setMutationDefaults(a,b){this.#i.set((0,c.hashKey)(a),{mutationKey:a,defaultOptions:b})}getMutationDefaults(a){let b=[...this.#i.values()],d={};return b.forEach(b=>{(0,c.partialMatchKey)(a,b.mutationKey)&&Object.assign(d,b.defaultOptions)}),d}defaultQueryOptions(a){if(a._defaulted)return a;let b={...this.#g.queries,...this.getQueryDefaults(a.queryKey),...a,_defaulted:!0};return b.queryHash||(b.queryHash=(0,c.hashQueryKeyByOptions)(b.queryKey,b)),void 0===b.refetchOnReconnect&&(b.refetchOnReconnect="always"!==b.networkMode),void 0===b.throwOnError&&(b.throwOnError=!!b.suspense),!b.networkMode&&b.persister&&(b.networkMode="offlineFirst"),b.queryFn===c.skipToken&&(b.enabled=!1),b}defaultMutationOptions(a){return a?._defaulted?a:{...this.#g.mutations,...a?.mutationKey&&this.getMutationDefaults(a.mutationKey),...a,_defaulted:!0}}clear(){this.#e.clear(),this.#f.clear()}},q=a.i(37927),r=function(){return null},s=a.i(72131),t=a.i(58338);function u({children:a}){let[c]=(0,s.useState)(()=>new p({defaultOptions:{queries:{staleTime:6e4,refetchOnWindowFocus:!1}}}));return(0,b.jsx)(q.QueryClientProvider,{client:c,children:(0,b.jsxs)(t.WebSocketProvider,{children:[a,(0,b.jsx)(r,{initialIsOpen:!1})]})})}},26419,a=>{"use strict";a.s(["Toaster",()=>h],26419);var b=a.i(87924),c=a.i(72131),d=(a,b,c,d,e,f,g,h)=>{let i=document.documentElement,j=["light","dark"];function k(b){var c;(Array.isArray(a)?a:[a]).forEach(a=>{let c="class"===a,d=c&&f?e.map(a=>f[a]||a):e;c?(i.classList.remove(...d),i.classList.add(f&&f[b]?f[b]:b)):i.setAttribute(a,b)}),c=b,h&&j.includes(c)&&(i.style.colorScheme=c)}if(d)k(d);else try{let a=localStorage.getItem(b)||c,d=g&&"system"===a?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":a;k(d)}catch(a){}},e=c.createContext(void 0),f={setTheme:a=>{},themes:[]};c.memo(({forcedTheme:a,storageKey:b,attribute:e,enableSystem:f,enableColorScheme:g,defaultTheme:h,value:i,themes:j,nonce:k,scriptProps:l})=>{let m=JSON.stringify([e,b,h,a,j,i,f,g]).slice(1,-1);return c.createElement("script",{...l,suppressHydrationWarning:!0,nonce:k,dangerouslySetInnerHTML:{__html:`(${d.toString()})(${m})`}})});var g=a.i(23292);let h=({...a})=>{let{theme:d="system"}=(()=>{var a;return null!=(a=c.useContext(e))?a:f})();return(0,b.jsx)(g.Toaster,{theme:d,className:"toaster group",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...a})}}];

//# sourceMappingURL=src_7ee99693._.js.map