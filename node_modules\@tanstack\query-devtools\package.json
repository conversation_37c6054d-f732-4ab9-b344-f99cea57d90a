{"name": "@tanstack/query-devtools", "version": "5.84.0", "description": "Developer tools to interact with and visualize the TanStack Query cache", "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/TanStack/query.git", "directory": "packages/query-devtools"}, "homepage": "https://tanstack.com/query", "funding": {"type": "github", "url": "https://github.com/sponsors/tanner<PERSON>ley"}, "type": "module", "main": "./build/index.cjs", "module": "./build/index.js", "types": "./build/index.d.ts", "browser": {}, "exports": {"@tanstack/custom-condition": "./src/index.ts", "solid": {"development": "./build/index.js", "import": "./build/index.js"}, "development": {"import": {"types": "./build/index.d.ts", "default": "./build/dev.js"}, "require": "./build/dev.cjs"}, "import": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "require": "./build/index.cjs"}, "files": ["build", "src", "!src/__tests__"], "devDependencies": {"@kobalte/core": "^0.13.4", "@solid-primitives/keyed": "^1.2.2", "@solid-primitives/resize-observer": "^2.0.26", "@solid-primitives/storage": "^1.3.11", "@tanstack/match-sorter-utils": "^8.19.4", "clsx": "^2.1.1", "goober": "^2.1.16", "npm-run-all2": "^5.0.0", "solid-js": "^1.9.7", "solid-transition-group": "^0.2.3", "superjson": "^2.2.2", "tsup-preset-solid": "^2.2.0", "vite-plugin-solid": "^2.11.6", "@tanstack/query-core": "5.83.1"}, "scripts": {"clean": "premove ./build ./coverage ./dist-ts", "compile": "tsc --build", "test:eslint": "eslint ./src", "test:types": "npm-run-all --serial test:types:*", "test:types:ts50": "node ../../node_modules/typescript50/lib/tsc.js --build", "test:types:ts51": "node ../../node_modules/typescript51/lib/tsc.js --build", "test:types:ts52": "node ../../node_modules/typescript52/lib/tsc.js --build", "test:types:ts53": "node ../../node_modules/typescript53/lib/tsc.js --build", "test:types:ts54": "node ../../node_modules/typescript54/lib/tsc.js --build", "test:types:ts55": "node ../../node_modules/typescript55/lib/tsc.js --build", "test:types:ts56": "node ../../node_modules/typescript56/lib/tsc.js --build", "test:types:ts57": "node ../../node_modules/typescript57/lib/tsc.js --build", "test:types:tscurrent": "tsc --build", "test:lib": "vitest", "test:lib:dev": "pnpm run test:lib --watch", "test:build": "publint --strict && attw --pack", "build": "tsup --tsconfig tsconfig.prod.json", "build:dev": "tsup --watch"}}