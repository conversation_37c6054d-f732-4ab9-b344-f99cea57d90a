(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,33525,(e,t,s)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),Object.defineProperty(s,"warnOnce",{enumerable:!0,get:function(){return i}});let i=e=>{}},66027,e=>{"use strict";e.s(["useQuery",()=>et],66027);var t,s,i,a,r,n,l,o,h,u,c,d,p,_,f,v,b,w,g,m,y,R,O,C,S=e.i(39946),k=e.i(70292),E=e.i(62351),T=e.i(37696),x=e.i(88245),M=e.i(75555),W=e.i(40143),U=e.i(86491),Q=e.i(15823),j=e.i(93803),q=e.i(19273),I=(t=new WeakMap,s=new WeakMap,i=new WeakMap,a=new WeakMap,r=new WeakMap,n=new WeakMap,l=new WeakMap,o=new WeakMap,h=new WeakMap,u=new WeakMap,c=new WeakMap,d=new WeakMap,p=new WeakMap,_=new WeakMap,f=new WeakMap,v=new WeakSet,b=new WeakSet,w=new WeakSet,g=new WeakSet,m=new WeakSet,y=new WeakSet,R=new WeakSet,O=new WeakSet,C=new WeakSet,class extends Q.Subscribable{bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&((0,S._)(this,s).addObserver(this),D((0,S._)(this,s),this.options)?(0,T._)(this,v,A).call(this):this.updateResult(),(0,T._)(this,m,z).call(this))}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return P((0,S._)(this,s),this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return P((0,S._)(this,s),this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,(0,T._)(this,y,K).call(this),(0,T._)(this,R,V).call(this),(0,S._)(this,s).removeObserver(this)}setOptions(e){let i=this.options,a=(0,S._)(this,s);if(this.options=(0,S._)(this,t).defaultQueryOptions(e),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof(0,q.resolveEnabled)(this.options.enabled,(0,S._)(this,s)))throw Error("Expected enabled to be a boolean or a callback that returns a boolean");(0,T._)(this,O,G).call(this),(0,S._)(this,s).setOptions(this.options),i._defaulted&&!(0,q.shallowEqualObjects)(this.options,i)&&(0,S._)(this,t).getQueryCache().notify({type:"observerOptionsUpdated",query:(0,S._)(this,s),observer:this});let r=this.hasListeners();r&&F((0,S._)(this,s),a,this.options,i)&&(0,T._)(this,v,A).call(this),this.updateResult(),r&&((0,S._)(this,s)!==a||(0,q.resolveEnabled)(this.options.enabled,(0,S._)(this,s))!==(0,q.resolveEnabled)(i.enabled,(0,S._)(this,s))||(0,q.resolveStaleTime)(this.options.staleTime,(0,S._)(this,s))!==(0,q.resolveStaleTime)(i.staleTime,(0,S._)(this,s)))&&(0,T._)(this,b,H).call(this);let n=(0,T._)(this,w,B).call(this);r&&((0,S._)(this,s)!==a||(0,q.resolveEnabled)(this.options.enabled,(0,S._)(this,s))!==(0,q.resolveEnabled)(i.enabled,(0,S._)(this,s))||n!==(0,S._)(this,_))&&(0,T._)(this,g,N).call(this,n)}getOptimisticResult(e){var i,l;let o=(0,S._)(this,t).getQueryCache().build((0,S._)(this,t),e),h=this.createResult(o,e);return i=this,l=h,(0,q.shallowEqualObjects)(i.getCurrentResult(),l)||((0,E._)(this,a,h),(0,E._)(this,n,this.options),(0,E._)(this,r,(0,S._)(this,s).state)),h}getCurrentResult(){return(0,S._)(this,a)}trackResult(e,t){return new Proxy(e,{get:(e,s)=>(this.trackProp(s),null==t||t(s),"promise"!==s||this.options.experimental_prefetchInRender||"pending"!==(0,S._)(this,l).status||(0,S._)(this,l).reject(Error("experimental_prefetchInRender feature flag is not enabled")),Reflect.get(e,s))})}trackProp(e){(0,S._)(this,f).add(e)}getCurrentQuery(){return(0,S._)(this,s)}refetch(){let{...e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.fetch({...e})}fetchOptimistic(e){let s=(0,S._)(this,t).defaultQueryOptions(e),i=(0,S._)(this,t).getQueryCache().build((0,S._)(this,t),s);return i.fetch().then(()=>this.createResult(i,s))}fetch(e){var t;return(0,T._)(this,v,A).call(this,{...e,cancelRefetch:null==(t=e.cancelRefetch)||t}).then(()=>(this.updateResult(),(0,S._)(this,a)))}createResult(e,t){let d,p=(0,S._)(this,s),_=this.options,f=(0,S._)(this,a),v=(0,S._)(this,r),b=(0,S._)(this,n),w=e!==p?e.state:(0,S._)(this,i),{state:g}=e,m={...g},y=!1;if(t._optimisticResults){let s=this.hasListeners(),i=!s&&D(e,t),a=s&&F(e,p,t,_);(i||a)&&(m={...m,...(0,U.fetchState)(g.data,e.options)}),"isRestoring"===t._optimisticResults&&(m.fetchStatus="idle")}let{error:R,errorUpdatedAt:O,status:C}=m;d=m.data;let k=!1;if(void 0!==t.placeholderData&&void 0===d&&"pending"===C){let e;if((null==f?void 0:f.isPlaceholderData)&&t.placeholderData===(null==b?void 0:b.placeholderData))e=f.data,k=!0;else{var T;e="function"==typeof t.placeholderData?t.placeholderData(null==(T=(0,S._)(this,c))?void 0:T.state.data,(0,S._)(this,c)):t.placeholderData}void 0!==e&&(C="success",d=(0,q.replaceData)(null==f?void 0:f.data,e,t),y=!0)}if(t.select&&void 0!==d&&!k)if(f&&d===(null==v?void 0:v.data)&&t.select===(0,S._)(this,h))d=(0,S._)(this,u);else try{(0,E._)(this,h,t.select),d=t.select(d),d=(0,q.replaceData)(null==f?void 0:f.data,d,t),(0,E._)(this,u,d),(0,E._)(this,o,null)}catch(e){(0,E._)(this,o,e)}(0,S._)(this,o)&&(R=(0,S._)(this,o),d=(0,S._)(this,u),O=Date.now(),C="error");let x="fetching"===m.fetchStatus,M="pending"===C,W="error"===C,Q=M&&x,I=void 0!==d,P={status:C,fetchStatus:m.fetchStatus,isPending:M,isSuccess:"success"===C,isError:W,isInitialLoading:Q,isLoading:Q,data:d,dataUpdatedAt:m.dataUpdatedAt,error:R,errorUpdatedAt:O,failureCount:m.fetchFailureCount,failureReason:m.fetchFailureReason,errorUpdateCount:m.errorUpdateCount,isFetched:m.dataUpdateCount>0||m.errorUpdateCount>0,isFetchedAfterMount:m.dataUpdateCount>w.dataUpdateCount||m.errorUpdateCount>w.errorUpdateCount,isFetching:x,isRefetching:x&&!M,isLoadingError:W&&!I,isPaused:"paused"===m.fetchStatus,isPlaceholderData:y,isRefetchError:W&&I,isStale:L(e,t),refetch:this.refetch,promise:(0,S._)(this,l),isEnabled:!1!==(0,q.resolveEnabled)(t.enabled,e)};if(this.options.experimental_prefetchInRender){let t=e=>{"error"===P.status?e.reject(P.error):void 0!==P.data&&e.resolve(P.data)},s=()=>{t((0,E._)(this,l,P.promise=(0,j.pendingThenable)()))},i=(0,S._)(this,l);switch(i.status){case"pending":e.queryHash===p.queryHash&&t(i);break;case"fulfilled":("error"===P.status||P.data!==i.value)&&s();break;case"rejected":("error"!==P.status||P.error!==i.reason)&&s()}}return P}updateResult(){let e=(0,S._)(this,a),t=this.createResult((0,S._)(this,s),this.options);if((0,E._)(this,r,(0,S._)(this,s).state),(0,E._)(this,n,this.options),void 0!==(0,S._)(this,r).data&&(0,E._)(this,c,(0,S._)(this,s)),(0,q.shallowEqualObjects)(t,e))return;(0,E._)(this,a,t);let i=()=>{if(!e)return!0;let{notifyOnChangeProps:t}=this.options,s="function"==typeof t?t():t;if("all"===s||!s&&!(0,S._)(this,f).size)return!0;let i=new Set(null!=s?s:(0,S._)(this,f));return this.options.throwOnError&&i.add("error"),Object.keys((0,S._)(this,a)).some(t=>(0,S._)(this,a)[t]!==e[t]&&i.has(t))};(0,T._)(this,C,J).call(this,{listeners:i()})}onQueryUpdate(){this.updateResult(),this.hasListeners()&&(0,T._)(this,m,z).call(this)}constructor(e,S){super(),(0,x._)(this,v),(0,x._)(this,b),(0,x._)(this,w),(0,x._)(this,g),(0,x._)(this,m),(0,x._)(this,y),(0,x._)(this,R),(0,x._)(this,O),(0,x._)(this,C),(0,k._)(this,t,{writable:!0,value:void 0}),(0,k._)(this,s,{writable:!0,value:void 0}),(0,k._)(this,i,{writable:!0,value:void 0}),(0,k._)(this,a,{writable:!0,value:void 0}),(0,k._)(this,r,{writable:!0,value:void 0}),(0,k._)(this,n,{writable:!0,value:void 0}),(0,k._)(this,l,{writable:!0,value:void 0}),(0,k._)(this,o,{writable:!0,value:void 0}),(0,k._)(this,h,{writable:!0,value:void 0}),(0,k._)(this,u,{writable:!0,value:void 0}),(0,k._)(this,c,{writable:!0,value:void 0}),(0,k._)(this,d,{writable:!0,value:void 0}),(0,k._)(this,p,{writable:!0,value:void 0}),(0,k._)(this,_,{writable:!0,value:void 0}),(0,k._)(this,f,{writable:!0,value:new Set}),this.options=S,(0,E._)(this,t,e),(0,E._)(this,o,null),(0,E._)(this,l,(0,j.pendingThenable)()),this.bindMethods(),this.setOptions(S)}});function D(e,t){return!1!==(0,q.resolveEnabled)(t.enabled,e)&&void 0===e.state.data&&("error"!==e.state.status||!1!==t.retryOnMount)||void 0!==e.state.data&&P(e,t,t.refetchOnMount)}function P(e,t,s){if(!1!==(0,q.resolveEnabled)(t.enabled,e)&&"static"!==(0,q.resolveStaleTime)(t.staleTime,e)){let i="function"==typeof s?s(e):s;return"always"===i||!1!==i&&L(e,t)}return!1}function F(e,t,s,i){return(e!==t||!1===(0,q.resolveEnabled)(i.enabled,e))&&(!s.suspense||"error"!==e.state.status)&&L(e,s)}function L(e,t){return!1!==(0,q.resolveEnabled)(t.enabled,e)&&e.isStaleByTime((0,q.resolveStaleTime)(t.staleTime,e))}function A(e){(0,T._)(this,O,G).call(this);let t=(0,S._)(this,s).fetch(this.options,e);return(null==e?void 0:e.throwOnError)||(t=t.catch(q.noop)),t}function H(){(0,T._)(this,y,K).call(this);let e=(0,q.resolveStaleTime)(this.options.staleTime,(0,S._)(this,s));if(q.isServer||(0,S._)(this,a).isStale||!(0,q.isValidTimeout)(e))return;let t=(0,q.timeUntilStale)((0,S._)(this,a).dataUpdatedAt,e);(0,E._)(this,d,setTimeout(()=>{(0,S._)(this,a).isStale||this.updateResult()},t+1))}function B(){var e;return null!=(e="function"==typeof this.options.refetchInterval?this.options.refetchInterval((0,S._)(this,s)):this.options.refetchInterval)&&e}function N(e){(0,T._)(this,R,V).call(this),(0,E._)(this,_,e),!q.isServer&&!1!==(0,q.resolveEnabled)(this.options.enabled,(0,S._)(this,s))&&(0,q.isValidTimeout)((0,S._)(this,_))&&0!==(0,S._)(this,_)&&(0,E._)(this,p,setInterval(()=>{(this.options.refetchIntervalInBackground||M.focusManager.isFocused())&&(0,T._)(this,v,A).call(this)},(0,S._)(this,_)))}function z(){(0,T._)(this,b,H).call(this),(0,T._)(this,g,N).call(this,(0,T._)(this,w,B).call(this))}function K(){(0,S._)(this,d)&&(clearTimeout((0,S._)(this,d)),(0,E._)(this,d,void 0))}function V(){(0,S._)(this,p)&&(clearInterval((0,S._)(this,p)),(0,E._)(this,p,void 0))}function G(){let e=(0,S._)(this,t).getQueryCache().build((0,S._)(this,t),this.options);if(e===(0,S._)(this,s))return;let a=(0,S._)(this,s);(0,E._)(this,s,e),(0,E._)(this,i,e.state),this.hasListeners()&&(null==a||a.removeObserver(this),e.addObserver(this))}function J(e){W.notifyManager.batch(()=>{e.listeners&&this.listeners.forEach(e=>{e((0,S._)(this,a))}),(0,S._)(this,t).getQueryCache().notify({query:(0,S._)(this,s),type:"observerResultsUpdated"})})}e.i(47167);var X=e.i(71645),Y=e.i(12598);e.i(43476);var Z=X.createContext(function(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}()),$=X.createContext(!1);$.Provider;var ee=(e,t,s)=>t.fetchOptimistic(e).catch(()=>{s.clearReset()});function et(e,t){return function(e,t,s){var i,a,r,n,l;let o=X.useContext($),h=X.useContext(Z),u=(0,Y.useQueryClient)(s),c=u.defaultQueryOptions(e);if(null==(a=u.getDefaultOptions().queries)||null==(i=a._experimental_beforeQuery)||i.call(a,c),c._optimisticResults=o?"isRestoring":"optimistic",c.suspense){let e=e=>"static"===e?e:Math.max(null!=e?e:1e3,1e3),t=c.staleTime;c.staleTime="function"==typeof t?function(){for(var s=arguments.length,i=Array(s),a=0;a<s;a++)i[a]=arguments[a];return e(t(...i))}:e(t),"number"==typeof c.gcTime&&(c.gcTime=Math.max(c.gcTime,1e3))}(c.suspense||c.throwOnError||c.experimental_prefetchInRender)&&!h.isReset()&&(c.retryOnMount=!1),X.useEffect(()=>{h.clearReset()},[h]);let d=!u.getQueryCache().get(c.queryHash),[p]=X.useState(()=>new t(u,c)),_=p.getOptimisticResult(c),f=!o&&!1!==e.subscribed;if(X.useSyncExternalStore(X.useCallback(e=>{let t=f?p.subscribe(W.notifyManager.batchCalls(e)):q.noop;return p.updateResult(),t},[p,f]),()=>p.getCurrentResult(),()=>p.getCurrentResult()),X.useEffect(()=>{p.setOptions(c)},[c,p]),(null==c?void 0:c.suspense)&&_.isPending)throw ee(c,p,h);if((e=>{let{result:t,errorResetBoundary:s,throwOnError:i,query:a,suspense:r}=e;return t.isError&&!s.isReset()&&!t.isFetching&&a&&(r&&void 0===t.data||(0,q.shouldThrowError)(i,[t.error,a]))})({result:_,errorResetBoundary:h,throwOnError:c.throwOnError,query:u.getQueryCache().get(c.queryHash),suspense:c.suspense}))throw _.error;if(null==(n=u.getDefaultOptions().queries)||null==(r=n._experimental_afterQuery)||r.call(n,c,_),c.experimental_prefetchInRender&&!q.isServer&&_.isLoading&&_.isFetching&&!o){let e=d?ee(c,p,h):null==(l=u.getQueryCache().get(c.queryHash))?void 0:l.promise;null==e||e.catch(q.noop).finally(()=>{p.updateResult()})}return c.notifyOnChangeProps?_:p.trackResult(_)}(e,I,t)}},15288,e=>{"use strict";e.s(["Card",()=>i,"CardContent",()=>n,"CardHeader",()=>a,"CardTitle",()=>r]);var t=e.i(43476),s=e.i(75157);function i(e){let{className:i,...a}=e;return(0,t.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",i),...a})}function a(e){let{className:i,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",i),...a})}function r(e){let{className:i,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",i),...a})}function n(e){let{className:i,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",i),...a})}},25652,e=>{"use strict";e.s(["TrendingUp",()=>t],25652);let t=(0,e.i(75254).default)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])}]);