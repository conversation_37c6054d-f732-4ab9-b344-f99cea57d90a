import { Alert, AlertStats, HealthStatus } from "@/types";

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000";

class ApiService {
  private async request<T>(
    endpoint: string,
    options?: RequestInit
  ): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;

    try {
      const response = await fetch(url, {
        headers: {
          "Content-Type": "application/json",
          ...options?.headers,
        },
        ...options,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error);
      throw error;
    }
  }

  // Health Check
  async getHealthStatus(): Promise<HealthStatus> {
    return this.request<HealthStatus>("/health");
  }

  // Alerts
  async getAlerts(limit?: number): Promise<Alert[]> {
    const params = limit ? `?limit=${limit}` : "";
    return this.request<Alert[]>(`/alerts${params}`);
  }

  async getAlert(id: string): Promise<Alert> {
    return this.request<Alert>(`/alerts/${id}`);
  }

  async updateAlertStatus(id: string, status: Alert["status"]): Promise<Alert> {
    return this.request<Alert>(`/alerts/${id}`, {
      method: "PATCH",
      body: JSON.stringify({ status }),
    });
  }

  // Analytics
  async getAlertStats(): Promise<AlertStats> {
    return this.request<AlertStats>("/alerts/stats");
  }

  // Camera Stream
  getCameraStreamUrl(): string {
    return `${API_BASE_URL}/stream`;
  }

  // WebSocket URL
  getWebSocketUrl(): string {
    const wsUrl = process.env.NEXT_PUBLIC_WS_URL || "ws://localhost:8000";
    return `${wsUrl}/alerts/stream`;
  }
}

export const apiService = new ApiService();
