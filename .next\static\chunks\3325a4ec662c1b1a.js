(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,78756,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),r.isUnsafeProperty=function(e){return"__proto__"===e}},33858,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),r.isDeepKey=function(e){switch(typeof e){case"number":case"symbol":return!1;case"string":return e.includes(".")||e.includes("[")||e.includes("]")}}},96736,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),r.to<PERSON><PERSON>=function(e){var t;return"string"==typeof e||"symbol"==typeof e?e:Object.is(null==e||null==(t=e.valueOf)?void 0:t.call(e),-0)?"-0":String(e)}},79232,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),r.toPath=function(e){let t=[],r=e.length;if(0===r)return t;let n=0,i="",a="",o=!1;for(46===e.charCodeAt(0)&&(t.push(""),n++);n<r;){let l=e[n];a?"\\"===l&&n+1<r?i+=e[++n]:l===a?a="":i+=l:o?'"'===l||"'"===l?a=l:"]"===l?(o=!1,t.push(i),i=""):i+=l:"["===l?(o=!0,i&&(t.push(i),i="")):"."===l?i&&(t.push(i),i=""):i+=l,n++}return i&&t.push(i),t}},18915,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"});let n=e.r(78756),i=e.r(33858),a=e.r(96736),o=e.r(79232);r.get=function e(t,r,l){if(null==t)return l;switch(typeof r){case"string":{if(n.isUnsafeProperty(r))return l;let a=t[r];if(void 0===a)if(i.isDeepKey(r))return e(t,o.toPath(r),l);else return l;return a}case"number":case"symbol":{"number"==typeof r&&(r=a.toKey(r));let e=t[r];if(void 0===e)return l;return e}default:{if(Array.isArray(r)){var u=t,c=r,s=l;if(0===c.length)return s;let e=u;for(let t=0;t<c.length;t++){if(null==e||n.isUnsafeProperty(c[t]))return s;e=e[c[t]]}return void 0===e?s:e}if(r=Object.is(null==r?void 0:r.valueOf(),-0)?"-0":String(r),n.isUnsafeProperty(r))return l;let e=t[r];if(void 0===e)return l;return e}}}},92068,(e,t,r)=>{t.exports=e.r(18915).get},14595,(e,t,r)=>{"use strict";var n=e.r(71645),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=n.useSyncExternalStore,o=n.useRef,l=n.useEffect,u=n.useMemo,c=n.useDebugValue;r.useSyncExternalStoreWithSelector=function(e,t,r,n,s){var f=o(null);if(null===f.current){var h={hasValue:!1,value:null};f.current=h}else h=f.current;var d=a(e,(f=u(function(){function e(e){if(!l){if(l=!0,a=e,e=n(e),void 0!==s&&h.hasValue){var t=h.value;if(s(t,e))return o=t}return o=e}if(t=o,i(a,e))return t;var r=n(e);return void 0!==s&&s(t,r)?(a=e,t):(a=e,o=r)}var a,o,l=!1,u=void 0===r?null:r;return[function(){return e(t())},null===u?void 0:function(){return e(u())}]},[t,r,n,s]))[0],f[1]);return l(function(){h.hasValue=!0,h.value=d},[d]),c(d),d}},13027,(e,t,r)=>{"use strict";t.exports=e.r(14595)},55838,(e,t,r)=>{"use strict";var n=e.r(71645),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=n.useState,o=n.useEffect,l=n.useLayoutEffect,u=n.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!i(e,r)}catch(e){return!0}}var s="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=a({inst:{value:r,getSnapshot:t}}),i=n[0].inst,s=n[1];return l(function(){i.value=r,i.getSnapshot=t,c(i)&&s({inst:i})},[e,r,t]),o(function(){return c(i)&&s({inst:i}),e(function(){c(i)&&s({inst:i})})},[e]),u(r),r};r.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:s},2239,(e,t,r)=>{"use strict";t.exports=e.r(55838)},52822,(e,t,r)=>{"use strict";var n=e.r(71645),i=e.r(2239),a="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=i.useSyncExternalStore,l=n.useRef,u=n.useEffect,c=n.useMemo,s=n.useDebugValue;r.useSyncExternalStoreWithSelector=function(e,t,r,n,i){var f=l(null);if(null===f.current){var h={hasValue:!1,value:null};f.current=h}else h=f.current;var d=o(e,(f=c(function(){function e(e){if(!u){if(u=!0,o=e,e=n(e),void 0!==i&&h.hasValue){var t=h.value;if(i(t,e))return l=t}return l=e}if(t=l,a(o,e))return t;var r=n(e);return void 0!==i&&i(t,r)?(o=e,t):(o=e,l=r)}var o,l,u=!1,c=void 0===r?null:r;return[function(){return e(t())},null===c?void 0:function(){return e(c())}]},[t,r,n,i]))[0],f[1]);return u(function(){h.hasValue=!0,h.value=d},[d]),s(d),d}},30224,(e,t,r)=>{"use strict";t.exports=e.r(52822)},45403,(e,t,r)=>{"use strict";function n(e){return"symbol"==typeof e?1:null===e?2:void 0===e?3:4*(e!=e)}Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),r.compareValues=(e,t,r)=>{if(e!==t){let i=n(e),a=n(t);if(i===a&&0===i){if(e<t)return"desc"===r?1:-1;if(e>t)return"desc"===r?-1:1}return"desc"===r?a-i:i-a}return 0}},9542,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),r.isSymbol=function(e){return"symbol"==typeof e||e instanceof Symbol}},38104,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"});let n=e.r(9542),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;r.isKey=function(e,t){return!Array.isArray(e)&&(!!("number"==typeof e||"boolean"==typeof e||null==e||n.isSymbol(e))||"string"==typeof e&&(a.test(e)||!i.test(e))||null!=t&&Object.hasOwn(t,e))}},910,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"});let n=e.r(45403),i=e.r(38104),a=e.r(79232);r.orderBy=function(e,t,r,o){if(null==e)return[];r=o?void 0:r,Array.isArray(e)||(e=Object.values(e)),Array.isArray(t)||(t=null==t?[null]:[t]),0===t.length&&(t=[null]),Array.isArray(r)||(r=null==r?[]:[r]),r=r.map(e=>String(e));let l=(e,t)=>{let r=e;for(let e=0;e<t.length&&null!=r;++e)r=r[t[e]];return r},u=t.map(e=>(Array.isArray(e)&&1===e.length&&(e=e[0]),null==e||"function"==typeof e||Array.isArray(e)||i.isKey(e))?e:{key:e,path:a.toPath(e)});return e.map(e=>({original:e,criteria:u.map(t=>{var r,n;return r=t,null==(n=e)||null==r?n:"object"==typeof r&&"key"in r?Object.hasOwn(n,r.key)?n[r.key]:l(n,r.path):"function"==typeof r?r(n):Array.isArray(r)?l(n,r):"object"==typeof n?n[r]:n})})).slice().sort((e,t)=>{for(let i=0;i<u.length;i++){let a=n.compareValues(e.criteria[i],t.criteria[i],r[i]);if(0!==a)return a}return 0}).map(e=>e.original)}},44647,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),r.flatten=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,r=[],n=Math.floor(t),i=(e,t)=>{for(let a=0;a<e.length;a++){let o=e[a];Array.isArray(o)&&t<n?i(o,t+1):r.push(o)}};return i(e,0),r}},5257,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"});let n=/^(?:0|[1-9]\d*)$/;r.isIndex=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.MAX_SAFE_INTEGER;switch(typeof e){case"number":return Number.isInteger(e)&&e>=0&&e<t;case"symbol":return!1;case"string":return n.test(e)}}},70871,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),r.isLength=function(e){return Number.isSafeInteger(e)&&e>=0}},69642,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"});let n=e.r(70871);r.isArrayLike=function(e){return null!=e&&"function"!=typeof e&&n.isLength(e.length)}},90820,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),r.isObject=function(e){return null!==e&&("object"==typeof e||"function"==typeof e)}},23855,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),r.eq=function(e,t){return e===t||Number.isNaN(e)&&Number.isNaN(t)}},15111,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"});let n=e.r(5257),i=e.r(69642),a=e.r(90820),o=e.r(23855);r.isIterateeCall=function(e,t,r){return!!a.isObject(r)&&(!!("number"==typeof t&&i.isArrayLike(r)&&n.isIndex(t))&&t<r.length||"string"==typeof t&&t in r)&&o.eq(r[t],e)}},69955,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"});let n=e.r(910),i=e.r(44647),a=e.r(15111);r.sortBy=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),o=1;o<t;o++)r[o-1]=arguments[o];let l=r.length;return l>1&&a.isIterateeCall(e,r[0],r[1])?r=[]:l>2&&a.isIterateeCall(r[0],r[1],r[2])&&(r=[r[0]]),n.orderBy(e,i.flatten(r),["asc"])}},42342,(e,t,r)=>{t.exports=e.r(69955).sortBy},41015,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"});let n=e.r(9542);r.toNumber=function(e){return n.isSymbol(e)?NaN:Number(e)}},89793,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"});let n=e.r(41015);r.toFinite=function(e){return e?(e=n.toNumber(e))===1/0||e===-1/0?(e<0?-1:1)*Number.MAX_VALUE:e==e?e:0:0===e?e:0}},45264,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"});let n=e.r(15111),i=e.r(89793);r.range=function(e,t,r){r&&"number"!=typeof r&&n.isIterateeCall(e,t,r)&&(t=r=void 0),e=i.toFinite(e),void 0===t?(t=e,e=0):t=i.toFinite(t),r=void 0===r?e<t?1:-1:i.toFinite(r);let a=Math.max(Math.ceil((t-e)/(r||1)),0),o=Array(a);for(let t=0;t<a;t++)o[t]=e,e+=r;return o}},66814,(e,t,r)=>{t.exports=e.r(45264).range},51655,(e,t,r)=>{!function(r){"use strict";var n,i={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},a=!0,o="[DecimalError] ",l=o+"Invalid argument: ",u=o+"Exponent out of range: ",c=Math.floor,s=Math.pow,f=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,h=c(1286742750677284.5),d={};function p(e,t){var r,n,i,o,l,u,c,s,f=e.constructor,h=f.precision;if(!e.s||!t.s)return t.s||(t=new f(e)),a?P(t,h):t;if(c=e.d,s=t.d,l=e.e,i=t.e,c=c.slice(),o=l-i){for(o<0?(n=c,o=-o,u=s.length):(n=s,i=l,u=c.length),o>(u=(l=Math.ceil(h/7))>u?l+1:u+1)&&(o=u,n.length=1),n.reverse();o--;)n.push(0);n.reverse()}for((u=c.length)-(o=s.length)<0&&(o=u,n=s,s=c,c=n),r=0;o;)r=(c[--o]=c[o]+s[o]+r)/1e7|0,c[o]%=1e7;for(r&&(c.unshift(r),++i),u=c.length;0==c[--u];)c.pop();return t.d=c,t.e=i,a?P(t,h):t}function y(e,t,r){if(e!==~~e||e<t||e>r)throw Error(l+e)}function v(e){var t,r,n,i=e.length-1,a="",o=e[0];if(i>0){for(a+=o,t=1;t<i;t++)(r=7-(n=e[t]+"").length)&&(a+=w(r)),a+=n;(r=7-(n=(o=e[t])+"").length)&&(a+=w(r))}else if(0===o)return"0";for(;o%10==0;)o/=10;return a+o}d.absoluteValue=d.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e},d.comparedTo=d.cmp=function(e){var t,r,n,i;if(e=new this.constructor(e),this.s!==e.s)return this.s||-e.s;if(this.e!==e.e)return this.e>e.e^this.s<0?1:-1;for(t=0,r=(n=this.d.length)<(i=e.d.length)?n:i;t<r;++t)if(this.d[t]!==e.d[t])return this.d[t]>e.d[t]^this.s<0?1:-1;return n===i?0:n>i^this.s<0?1:-1},d.decimalPlaces=d.dp=function(){var e=this.d.length-1,t=(e-this.e)*7;if(e=this.d[e])for(;e%10==0;e/=10)t--;return t<0?0:t},d.dividedBy=d.div=function(e){return g(this,new this.constructor(e))},d.dividedToIntegerBy=d.idiv=function(e){var t=this.constructor;return P(g(this,new t(e),0,1),t.precision)},d.equals=d.eq=function(e){return!this.cmp(e)},d.exponent=function(){return b(this)},d.greaterThan=d.gt=function(e){return this.cmp(e)>0},d.greaterThanOrEqualTo=d.gte=function(e){return this.cmp(e)>=0},d.isInteger=d.isint=function(){return this.e>this.d.length-2},d.isNegative=d.isneg=function(){return this.s<0},d.isPositive=d.ispos=function(){return this.s>0},d.isZero=function(){return 0===this.s},d.lessThan=d.lt=function(e){return 0>this.cmp(e)},d.lessThanOrEqualTo=d.lte=function(e){return 1>this.cmp(e)},d.logarithm=d.log=function(e){var t,r=this.constructor,i=r.precision,l=i+5;if(void 0===e)e=new r(10);else if((e=new r(e)).s<1||e.eq(n))throw Error(o+"NaN");if(this.s<1)throw Error(o+(this.s?"NaN":"-Infinity"));return this.eq(n)?new r(0):(a=!1,t=g(O(this,l),O(e,l),l),a=!0,P(t,i))},d.minus=d.sub=function(e){return e=new this.constructor(e),this.s==e.s?A(this,e):p(this,(e.s=-e.s,e))},d.modulo=d.mod=function(e){var t,r=this.constructor,n=r.precision;if(!(e=new r(e)).s)throw Error(o+"NaN");return this.s?(a=!1,t=g(this,e,0,1).times(e),a=!0,this.minus(t)):P(new r(this),n)},d.naturalExponential=d.exp=function(){return m(this)},d.naturalLogarithm=d.ln=function(){return O(this)},d.negated=d.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e},d.plus=d.add=function(e){return e=new this.constructor(e),this.s==e.s?p(this,e):A(this,(e.s=-e.s,e))},d.precision=d.sd=function(e){var t,r,n;if(void 0!==e&&!!e!==e&&1!==e&&0!==e)throw Error(l+e);if(t=b(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return e&&t>r?t:r},d.squareRoot=d.sqrt=function(){var e,t,r,n,i,l,u,s=this.constructor;if(this.s<1){if(!this.s)return new s(0);throw Error(o+"NaN")}for(e=b(this),a=!1,0==(i=Math.sqrt(+this))||i==1/0?(((t=v(this.d)).length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=c((e+1)/2)-(e<0||e%2),n=new s(t=i==1/0?"5e"+e:(t=i.toExponential()).slice(0,t.indexOf("e")+1)+e)):n=new s(i.toString()),i=u=(r=s.precision)+3;;)if(n=(l=n).plus(g(this,l,u+2)).times(.5),v(l.d).slice(0,u)===(t=v(n.d)).slice(0,u)){if(t=t.slice(u-3,u+1),i==u&&"4999"==t){if(P(l,r+1,0),l.times(l).eq(this)){n=l;break}}else if("9999"!=t)break;u+=4}return a=!0,P(n,r)},d.times=d.mul=function(e){var t,r,n,i,o,l,u,c,s,f=this.constructor,h=this.d,d=(e=new f(e)).d;if(!this.s||!e.s)return new f(0);for(e.s*=this.s,r=this.e+e.e,(c=h.length)<(s=d.length)&&(o=h,h=d,d=o,l=c,c=s,s=l),o=[],n=l=c+s;n--;)o.push(0);for(n=s;--n>=0;){for(t=0,i=c+n;i>n;)u=o[i]+d[n]*h[i-n-1]+t,o[i--]=u%1e7|0,t=u/1e7|0;o[i]=(o[i]+t)%1e7|0}for(;!o[--l];)o.pop();return t?++r:o.shift(),e.d=o,e.e=r,a?P(e,f.precision):e},d.toDecimalPlaces=d.todp=function(e,t){var r=this,n=r.constructor;return(r=new n(r),void 0===e)?r:(y(e,0,1e9),void 0===t?t=n.rounding:y(t,0,8),P(r,e+b(r)+1,t))},d.toExponential=function(e,t){var r,n=this,i=n.constructor;return void 0===e?r=E(n,!0):(y(e,0,1e9),void 0===t?t=i.rounding:y(t,0,8),r=E(n=P(new i(n),e+1,t),!0,e+1)),r},d.toFixed=function(e,t){var r,n,i=this.constructor;return void 0===e?E(this):(y(e,0,1e9),void 0===t?t=i.rounding:y(t,0,8),r=E((n=P(new i(this),e+b(this)+1,t)).abs(),!1,e+b(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},d.toInteger=d.toint=function(){var e=this.constructor;return P(new e(this),b(this)+1,e.rounding)},d.toNumber=function(){return+this},d.toPower=d.pow=function(e){var t,r,i,l,u,s,f=this,h=f.constructor,d=+(e=new h(e));if(!e.s)return new h(n);if(!(f=new h(f)).s){if(e.s<1)throw Error(o+"Infinity");return f}if(f.eq(n))return f;if(i=h.precision,e.eq(n))return P(f,i);if(s=(t=e.e)>=(r=e.d.length-1),u=f.s,s){if((r=d<0?-d:d)<=0x1fffffffffffff){for(l=new h(n),t=Math.ceil(i/7+4),a=!1;r%2&&S((l=l.times(f)).d,t),0!==(r=c(r/2));)S((f=f.times(f)).d,t);return a=!0,e.s<0?new h(n).div(l):P(l,i)}}else if(u<0)throw Error(o+"NaN");return u=u<0&&1&e.d[Math.max(t,r)]?-1:1,f.s=1,a=!1,l=e.times(O(f,i+12)),a=!0,(l=m(l)).s=u,l},d.toPrecision=function(e,t){var r,n,i=this,a=i.constructor;return void 0===e?(r=b(i),n=E(i,r<=a.toExpNeg||r>=a.toExpPos)):(y(e,1,1e9),void 0===t?t=a.rounding:y(t,0,8),r=b(i=P(new a(i),e,t)),n=E(i,e<=r||r<=a.toExpNeg,e)),n},d.toSignificantDigits=d.tosd=function(e,t){var r=this.constructor;return void 0===e?(e=r.precision,t=r.rounding):(y(e,1,1e9),void 0===t?t=r.rounding:y(t,0,8)),P(new r(this),e,t)},d.toString=d.valueOf=d.val=d.toJSON=function(){var e=b(this),t=this.constructor;return E(this,e<=t.toExpNeg||e>=t.toExpPos)};var g=function(){function e(e,t){var r,n=0,i=e.length;for(e=e.slice();i--;)r=e[i]*t+n,e[i]=r%1e7|0,n=r/1e7|0;return n&&e.unshift(n),e}function t(e,t,r,n){var i,a;if(r!=n)a=r>n?1:-1;else for(i=a=0;i<r;i++)if(e[i]!=t[i]){a=e[i]>t[i]?1:-1;break}return a}function r(e,t,r){for(var n=0;r--;)e[r]-=n,n=+(e[r]<t[r]),e[r]=1e7*n+e[r]-t[r];for(;!e[0]&&e.length>1;)e.shift()}return function(n,i,a,l){var u,c,s,f,h,d,p,y,v,g,m,x,w,O,j,A,E,S,M=n.constructor,k=n.s==i.s?1:-1,T=n.d,C=i.d;if(!n.s)return new M(n);if(!i.s)throw Error(o+"Division by zero");for(s=0,c=n.e-i.e,E=C.length,j=T.length,y=(p=new M(k)).d=[];C[s]==(T[s]||0);)++s;if(C[s]>(T[s]||0)&&--c,(x=null==a?a=M.precision:l?a+(b(n)-b(i))+1:a)<0)return new M(0);if(x=x/7+2|0,s=0,1==E)for(f=0,C=C[0],x++;(s<j||f)&&x--;s++)w=1e7*f+(T[s]||0),y[s]=w/C|0,f=w%C|0;else{for((f=1e7/(C[0]+1)|0)>1&&(C=e(C,f),T=e(T,f),E=C.length,j=T.length),O=E,g=(v=T.slice(0,E)).length;g<E;)v[g++]=0;(S=C.slice()).unshift(0),A=C[0],C[1]>=1e7/2&&++A;do f=0,(u=t(C,v,E,g))<0?(m=v[0],E!=g&&(m=1e7*m+(v[1]||0)),(f=m/A|0)>1?(f>=1e7&&(f=1e7-1),d=(h=e(C,f)).length,g=v.length,1==(u=t(h,v,d,g))&&(f--,r(h,E<d?S:C,d))):(0==f&&(u=f=1),h=C.slice()),(d=h.length)<g&&h.unshift(0),r(v,h,g),-1==u&&(g=v.length,(u=t(C,v,E,g))<1&&(f++,r(v,E<g?S:C,g))),g=v.length):0===u&&(f++,v=[0]),y[s++]=f,u&&v[0]?v[g++]=T[O]||0:(v=[T[O]],g=1);while((O++<j||void 0!==v[0])&&x--)}return y[0]||y.shift(),p.e=c,P(p,l?a+b(p)+1:a)}}();function m(e,t){var r,i,o,l,c,f=0,h=0,d=e.constructor,p=d.precision;if(b(e)>16)throw Error(u+b(e));if(!e.s)return new d(n);for(null==t?(a=!1,c=p):c=t,l=new d(.03125);e.abs().gte(.1);)e=e.times(l),h+=5;for(c+=Math.log(s(2,h))/Math.LN10*2+5|0,r=i=o=new d(n),d.precision=c;;){if(i=P(i.times(e),c),r=r.times(++f),v((l=o.plus(g(i,r,c))).d).slice(0,c)===v(o.d).slice(0,c)){for(;h--;)o=P(o.times(o),c);return d.precision=p,null==t?(a=!0,P(o,p)):o}o=l}}function b(e){for(var t=7*e.e,r=e.d[0];r>=10;r/=10)t++;return t}function x(e,t,r){if(t>e.LN10.sd())throw a=!0,r&&(e.precision=r),Error(o+"LN10 precision limit exceeded");return P(new e(e.LN10),t)}function w(e){for(var t="";e--;)t+="0";return t}function O(e,t){var r,i,l,u,c,s,f,h,d,p=1,y=e,m=y.d,w=y.constructor,j=w.precision;if(y.s<1)throw Error(o+(y.s?"NaN":"-Infinity"));if(y.eq(n))return new w(0);if(null==t?(a=!1,h=j):h=t,y.eq(10))return null==t&&(a=!0),x(w,h);if(w.precision=h+=10,i=(r=v(m)).charAt(0),!(15e14>Math.abs(u=b(y))))return f=x(w,h+2,j).times(u+""),y=O(new w(i+"."+r.slice(1)),h-10).plus(f),w.precision=j,null==t?(a=!0,P(y,j)):y;for(;i<7&&1!=i||1==i&&r.charAt(1)>3;)i=(r=v((y=y.times(e)).d)).charAt(0),p++;for(u=b(y),i>1?(y=new w("0."+r),u++):y=new w(i+"."+r.slice(1)),s=c=y=g(y.minus(n),y.plus(n),h),d=P(y.times(y),h),l=3;;){if(c=P(c.times(d),h),v((f=s.plus(g(c,new w(l),h))).d).slice(0,h)===v(s.d).slice(0,h))return s=s.times(2),0!==u&&(s=s.plus(x(w,h+2,j).times(u+""))),s=g(s,new w(p),h),w.precision=j,null==t?(a=!0,P(s,j)):s;s=f,l+=2}}function j(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;48===t.charCodeAt(n);)++n;for(i=t.length;48===t.charCodeAt(i-1);)--i;if(t=t.slice(n,i)){if(i-=n,e.e=c((r=r-n-1)/7),e.d=[],n=(r+1)%7,r<0&&(n+=7),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=7;n<i;)e.d.push(+t.slice(n,n+=7));n=7-(t=t.slice(n)).length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),a&&(e.e>h||e.e<-h))throw Error(u+r)}else e.s=0,e.e=0,e.d=[0];return e}function P(e,t,r){var n,i,o,l,f,d,p,y,v=e.d;for(l=1,o=v[0];o>=10;o/=10)l++;if((n=t-l)<0)n+=7,i=t,p=v[y=0];else{if((y=Math.ceil((n+1)/7))>=(o=v.length))return e;for(l=1,p=o=v[y];o>=10;o/=10)l++;n%=7,i=n-7+l}if(void 0!==r&&(f=p/(o=s(10,l-i-1))%10|0,d=t<0||void 0!==v[y+1]||p%o,d=r<4?(f||d)&&(0==r||r==(e.s<0?3:2)):f>5||5==f&&(4==r||d||6==r&&(n>0?i>0?p/s(10,l-i):0:v[y-1])%10&1||r==(e.s<0?8:7))),t<1||!v[0])return d?(o=b(e),v.length=1,t=t-o-1,v[0]=s(10,(7-t%7)%7),e.e=c(-t/7)||0):(v.length=1,v[0]=e.e=e.s=0),e;if(0==n?(v.length=y,o=1,y--):(v.length=y+1,o=s(10,7-n),v[y]=i>0?(p/s(10,l-i)%s(10,i)|0)*o:0),d)for(;;)if(0==y){1e7==(v[0]+=o)&&(v[0]=1,++e.e);break}else{if(v[y]+=o,1e7!=v[y])break;v[y--]=0,o=1}for(n=v.length;0===v[--n];)v.pop();if(a&&(e.e>h||e.e<-h))throw Error(u+b(e));return e}function A(e,t){var r,n,i,o,l,u,c,s,f,h,d=e.constructor,p=d.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new d(e),a?P(t,p):t;if(c=e.d,h=t.d,n=t.e,s=e.e,c=c.slice(),l=s-n){for((f=l<0)?(r=c,l=-l,u=h.length):(r=h,n=s,u=c.length),l>(i=Math.max(Math.ceil(p/7),u)+2)&&(l=i,r.length=1),r.reverse(),i=l;i--;)r.push(0);r.reverse()}else{for((f=(i=c.length)<(u=h.length))&&(u=i),i=0;i<u;i++)if(c[i]!=h[i]){f=c[i]<h[i];break}l=0}for(f&&(r=c,c=h,h=r,t.s=-t.s),u=c.length,i=h.length-u;i>0;--i)c[u++]=0;for(i=h.length;i>l;){if(c[--i]<h[i]){for(o=i;o&&0===c[--o];)c[o]=1e7-1;--c[o],c[i]+=1e7}c[i]-=h[i]}for(;0===c[--u];)c.pop();for(;0===c[0];c.shift())--n;return c[0]?(t.d=c,t.e=n,a?P(t,p):t):new d(0)}function E(e,t,r){var n,i=b(e),a=v(e.d),o=a.length;return t?(r&&(n=r-o)>0?a=a.charAt(0)+"."+a.slice(1)+w(n):o>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(i<0?"e":"e+")+i):i<0?(a="0."+w(-i-1)+a,r&&(n=r-o)>0&&(a+=w(n))):i>=o?(a+=w(i+1-o),r&&(n=r-i-1)>0&&(a=a+"."+w(n))):((n=i+1)<o&&(a=a.slice(0,n)+"."+a.slice(n)),r&&(n=r-o)>0&&(i+1===o&&(a+="."),a+=w(n))),e.s<0?"-"+a:a}function S(e,t){if(e.length>t)return e.length=t,!0}function M(e){if(!e||"object"!=typeof e)throw Error(o+"Object expected");var t,r,n,i=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if(void 0!==(n=e[r=i[t]]))if(c(n)===n&&n>=i[t+1]&&n<=i[t+2])this[r]=n;else throw Error(l+r+": "+n);if(void 0!==(n=e[r="LN10"]))if(n==Math.LN10)this[r]=new this(n);else throw Error(l+r+": "+n);return this}if((i=function e(t){var r,n,i;function a(e){if(!(this instanceof a))return new a(e);if(this.constructor=a,e instanceof a){this.s=e.s,this.e=e.e,this.d=(e=e.d)?e.slice():e;return}if("number"==typeof e){if(0*e!=0)throw Error(l+e);if(e>0)this.s=1;else if(e<0)e=-e,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(e===~~e&&e<1e7){this.e=0,this.d=[e];return}return j(this,e.toString())}if("string"!=typeof e)throw Error(l+e);if(45===e.charCodeAt(0)?(e=e.slice(1),this.s=-1):this.s=1,f.test(e))j(this,e);else throw Error(l+e)}if(a.prototype=d,a.ROUND_UP=0,a.ROUND_DOWN=1,a.ROUND_CEIL=2,a.ROUND_FLOOR=3,a.ROUND_HALF_UP=4,a.ROUND_HALF_DOWN=5,a.ROUND_HALF_EVEN=6,a.ROUND_HALF_CEIL=7,a.ROUND_HALF_FLOOR=8,a.clone=e,a.config=a.set=M,void 0===t&&(t={}),t)for(r=0,i=["precision","rounding","toExpNeg","toExpPos","LN10"];r<i.length;)t.hasOwnProperty(n=i[r++])||(t[n]=this[n]);return a.config(t),a}(i)).default=i.Decimal=i,n=new i(1),"function"==typeof define&&define.amd){let t;e.r,void 0!==(t=i)&&e.v(t)}else t.exports?t.exports=i:(r||(r="undefined"!=typeof self&&self&&self.self==self?self:Function("return this")()),r.Decimal=i)}(e.e)},52210,(e,t,r)=>{"use strict";var n="function"==typeof Symbol&&Symbol.for,i=n?Symbol.for("react.element"):60103,a=n?Symbol.for("react.portal"):60106,o=n?Symbol.for("react.fragment"):60107,l=n?Symbol.for("react.strict_mode"):60108,u=n?Symbol.for("react.profiler"):60114,c=n?Symbol.for("react.provider"):60109,s=n?Symbol.for("react.context"):60110,f=n?Symbol.for("react.async_mode"):60111,h=n?Symbol.for("react.concurrent_mode"):60111,d=n?Symbol.for("react.forward_ref"):60112,p=n?Symbol.for("react.suspense"):60113,y=n?Symbol.for("react.suspense_list"):60120,v=n?Symbol.for("react.memo"):60115,g=n?Symbol.for("react.lazy"):60116,m=n?Symbol.for("react.block"):60121,b=n?Symbol.for("react.fundamental"):60117,x=n?Symbol.for("react.responder"):60118,w=n?Symbol.for("react.scope"):60119;function O(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case i:switch(e=e.type){case f:case h:case o:case u:case l:case p:return e;default:switch(e=e&&e.$$typeof){case s:case d:case g:case v:case c:return e;default:return t}}case a:return t}}}function j(e){return O(e)===h}r.AsyncMode=f,r.ConcurrentMode=h,r.ContextConsumer=s,r.ContextProvider=c,r.Element=i,r.ForwardRef=d,r.Fragment=o,r.Lazy=g,r.Memo=v,r.Portal=a,r.Profiler=u,r.StrictMode=l,r.Suspense=p,r.isAsyncMode=function(e){return j(e)||O(e)===f},r.isConcurrentMode=j,r.isContextConsumer=function(e){return O(e)===s},r.isContextProvider=function(e){return O(e)===c},r.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===i},r.isForwardRef=function(e){return O(e)===d},r.isFragment=function(e){return O(e)===o},r.isLazy=function(e){return O(e)===g},r.isMemo=function(e){return O(e)===v},r.isPortal=function(e){return O(e)===a},r.isProfiler=function(e){return O(e)===u},r.isStrictMode=function(e){return O(e)===l},r.isSuspense=function(e){return O(e)===p},r.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===o||e===h||e===u||e===l||e===p||e===y||"object"==typeof e&&null!==e&&(e.$$typeof===g||e.$$typeof===v||e.$$typeof===c||e.$$typeof===s||e.$$typeof===d||e.$$typeof===b||e.$$typeof===x||e.$$typeof===w||e.$$typeof===m)},r.typeOf=O},79684,(e,t,r)=>{"use strict";t.exports=e.r(52210)},78492,(e,t,r)=>{"use strict";var n=Object.prototype.hasOwnProperty,i="~";function a(){}function o(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function l(e,t,r,n,a){if("function"!=typeof r)throw TypeError("The listener must be a function");var l=new o(r,n||e,a),u=i?i+t:t;return e._events[u]?e._events[u].fn?e._events[u]=[e._events[u],l]:e._events[u].push(l):(e._events[u]=l,e._eventsCount++),e}function u(e,t){0==--e._eventsCount?e._events=new a:delete e._events[t]}function c(){this._events=new a,this._eventsCount=0}Object.create&&(a.prototype=Object.create(null),new a().__proto__||(i=!1)),c.prototype.eventNames=function(){var e,t,r=[];if(0===this._eventsCount)return r;for(t in e=this._events)n.call(e,t)&&r.push(i?t.slice(1):t);return Object.getOwnPropertySymbols?r.concat(Object.getOwnPropertySymbols(e)):r},c.prototype.listeners=function(e){var t=i?i+e:e,r=this._events[t];if(!r)return[];if(r.fn)return[r.fn];for(var n=0,a=r.length,o=Array(a);n<a;n++)o[n]=r[n].fn;return o},c.prototype.listenerCount=function(e){var t=i?i+e:e,r=this._events[t];return r?r.fn?1:r.length:0},c.prototype.emit=function(e,t,r,n,a,o){var l=i?i+e:e;if(!this._events[l])return!1;var u,c,s=this._events[l],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(e,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,t),!0;case 3:return s.fn.call(s.context,t,r),!0;case 4:return s.fn.call(s.context,t,r,n),!0;case 5:return s.fn.call(s.context,t,r,n,a),!0;case 6:return s.fn.call(s.context,t,r,n,a,o),!0}for(c=1,u=Array(f-1);c<f;c++)u[c-1]=arguments[c];s.fn.apply(s.context,u)}else{var h,d=s.length;for(c=0;c<d;c++)switch(s[c].once&&this.removeListener(e,s[c].fn,void 0,!0),f){case 1:s[c].fn.call(s[c].context);break;case 2:s[c].fn.call(s[c].context,t);break;case 3:s[c].fn.call(s[c].context,t,r);break;case 4:s[c].fn.call(s[c].context,t,r,n);break;default:if(!u)for(h=1,u=Array(f-1);h<f;h++)u[h-1]=arguments[h];s[c].fn.apply(s[c].context,u)}}return!0},c.prototype.on=function(e,t,r){return l(this,e,t,r,!1)},c.prototype.once=function(e,t,r){return l(this,e,t,r,!0)},c.prototype.removeListener=function(e,t,r,n){var a=i?i+e:e;if(!this._events[a])return this;if(!t)return u(this,a),this;var o=this._events[a];if(o.fn)o.fn!==t||n&&!o.once||r&&o.context!==r||u(this,a);else{for(var l=0,c=[],s=o.length;l<s;l++)(o[l].fn!==t||n&&!o[l].once||r&&o[l].context!==r)&&c.push(o[l]);c.length?this._events[a]=1===c.length?c[0]:c:u(this,a)}return this},c.prototype.removeAllListeners=function(e){var t;return e?(t=i?i+e:e,this._events[t]&&u(this,t)):(this._events=new a,this._eventsCount=0),this},c.prototype.off=c.prototype.removeListener,c.prototype.addListener=c.prototype.on,c.prefixed=i,c.EventEmitter=c,t.exports=c},43210,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),r.last=function(e){return e[e.length-1]}},75576,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),r.toArray=function(e){return Array.isArray(e)?e:Array.from(e)}},5518,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"});let n=e.r(43210),i=e.r(75576),a=e.r(69642);r.last=function(e){if(a.isArrayLike(e))return n.last(i.toArray(e))}},4178,(e,t,r)=>{t.exports=e.r(5518).last},18256,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),r.isPlainObject=function(e){if("object"!=typeof e||null==e)return!1;if(null===Object.getPrototypeOf(e))return!0;if("[object Object]"!==Object.prototype.toString.call(e)){var t;let r=e[Symbol.toStringTag];return null!=r&&!!(null==(t=Object.getOwnPropertyDescriptor(e,Symbol.toStringTag))?void 0:t.writable)&&e.toString()==="[object ".concat(r,"]")}let r=e;for(;null!==Object.getPrototypeOf(r);)r=Object.getPrototypeOf(r);return Object.getPrototypeOf(e)===r}},7328,(e,t,r)=>{t.exports=e.r(18256).isPlainObject},67034,(e,t,r)=>{!function(){var e={675:function(e,t){"use strict";t.byteLength=function(e){var t=u(e),r=t[0],n=t[1];return(r+n)*3/4-n},t.toByteArray=function(e){var t,r,a=u(e),o=a[0],l=a[1],c=new i((o+l)*3/4-l),s=0,f=l>0?o-4:o;for(r=0;r<f;r+=4)t=n[e.charCodeAt(r)]<<18|n[e.charCodeAt(r+1)]<<12|n[e.charCodeAt(r+2)]<<6|n[e.charCodeAt(r+3)],c[s++]=t>>16&255,c[s++]=t>>8&255,c[s++]=255&t;return 2===l&&(t=n[e.charCodeAt(r)]<<2|n[e.charCodeAt(r+1)]>>4,c[s++]=255&t),1===l&&(t=n[e.charCodeAt(r)]<<10|n[e.charCodeAt(r+1)]<<4|n[e.charCodeAt(r+2)]>>2,c[s++]=t>>8&255,c[s++]=255&t),c},t.fromByteArray=function(e){for(var t,n=e.length,i=n%3,a=[],o=0,l=n-i;o<l;o+=16383)a.push(function(e,t,n){for(var i,a=[],o=t;o<n;o+=3)i=(e[o]<<16&0xff0000)+(e[o+1]<<8&65280)+(255&e[o+2]),a.push(r[i>>18&63]+r[i>>12&63]+r[i>>6&63]+r[63&i]);return a.join("")}(e,o,o+16383>l?l:o+16383));return 1===i?a.push(r[(t=e[n-1])>>2]+r[t<<4&63]+"=="):2===i&&a.push(r[(t=(e[n-2]<<8)+e[n-1])>>10]+r[t>>4&63]+r[t<<2&63]+"="),a.join("")};for(var r=[],n=[],i="undefined"!=typeof Uint8Array?Uint8Array:Array,a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",o=0,l=a.length;o<l;++o)r[o]=a[o],n[a.charCodeAt(o)]=o;function u(e){var t=e.length;if(t%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");-1===r&&(r=t);var n=r===t?0:4-r%4;return[r,n]}n[45]=62,n[95]=63},72:function(e,t,r){"use strict";var n=r(675),i=r(783),a="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function o(e){if(e>0x7fffffff)throw RangeError('The value "'+e+'" is invalid for option "size"');var t=new Uint8Array(e);return Object.setPrototypeOf(t,l.prototype),t}function l(e,t,r){if("number"==typeof e){if("string"==typeof t)throw TypeError('The "string" argument must be of type string. Received type number');return s(e)}return u(e,t,r)}function u(e,t,r){if("string"==typeof e){var n=e,i=t;if(("string"!=typeof i||""===i)&&(i="utf8"),!l.isEncoding(i))throw TypeError("Unknown encoding: "+i);var a=0|d(n,i),u=o(a),c=u.write(n,i);return c!==a&&(u=u.slice(0,c)),u}if(ArrayBuffer.isView(e))return f(e);if(null==e)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e);if(k(e,ArrayBuffer)||e&&k(e.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(k(e,SharedArrayBuffer)||e&&k(e.buffer,SharedArrayBuffer)))return function(e,t,r){var n;if(t<0||e.byteLength<t)throw RangeError('"offset" is outside of buffer bounds');if(e.byteLength<t+(r||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(n=void 0===t&&void 0===r?new Uint8Array(e):void 0===r?new Uint8Array(e,t):new Uint8Array(e,t,r),l.prototype),n}(e,t,r);if("number"==typeof e)throw TypeError('The "value" argument must not be of type number. Received type number');var s=e.valueOf&&e.valueOf();if(null!=s&&s!==e)return l.from(s,t,r);var p=function(e){if(l.isBuffer(e)){var t=0|h(e.length),r=o(t);return 0===r.length||e.copy(r,0,0,t),r}return void 0!==e.length?"number"!=typeof e.length||function(e){return e!=e}(e.length)?o(0):f(e):"Buffer"===e.type&&Array.isArray(e.data)?f(e.data):void 0}(e);if(p)return p;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof e[Symbol.toPrimitive])return l.from(e[Symbol.toPrimitive]("string"),t,r);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e)}function c(e){if("number"!=typeof e)throw TypeError('"size" argument must be of type number');if(e<0)throw RangeError('The value "'+e+'" is invalid for option "size"')}function s(e){return c(e),o(e<0?0:0|h(e))}function f(e){for(var t=e.length<0?0:0|h(e.length),r=o(t),n=0;n<t;n+=1)r[n]=255&e[n];return r}t.Buffer=l,t.SlowBuffer=function(e){return+e!=e&&(e=0),l.alloc(+e)},t.INSPECT_MAX_BYTES=50,t.kMaxLength=0x7fffffff,l.TYPED_ARRAY_SUPPORT=function(){try{var e=new Uint8Array(1),t={foo:function(){return 42}};return Object.setPrototypeOf(t,Uint8Array.prototype),Object.setPrototypeOf(e,t),42===e.foo()}catch(e){return!1}}(),l.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(l.prototype,"parent",{enumerable:!0,get:function(){if(l.isBuffer(this))return this.buffer}}),Object.defineProperty(l.prototype,"offset",{enumerable:!0,get:function(){if(l.isBuffer(this))return this.byteOffset}}),l.poolSize=8192,l.from=function(e,t,r){return u(e,t,r)},Object.setPrototypeOf(l.prototype,Uint8Array.prototype),Object.setPrototypeOf(l,Uint8Array),l.alloc=function(e,t,r){return(c(e),e<=0)?o(e):void 0!==t?"string"==typeof r?o(e).fill(t,r):o(e).fill(t):o(e)},l.allocUnsafe=function(e){return s(e)},l.allocUnsafeSlow=function(e){return s(e)};function h(e){if(e>=0x7fffffff)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|e}function d(e,t){if(l.isBuffer(e))return e.length;if(ArrayBuffer.isView(e)||k(e,ArrayBuffer))return e.byteLength;if("string"!=typeof e)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof e);var r=e.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===r)return 0;for(var i=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return A(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return S(e).length;default:if(i)return n?-1:A(e).length;t=(""+t).toLowerCase(),i=!0}}function p(e,t,r){var i,a,o,l=!1;if((void 0===t||t<0)&&(t=0),t>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0||(r>>>=0)<=(t>>>=0)))return"";for(e||(e="utf8");;)switch(e){case"hex":return function(e,t,r){var n=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>n)&&(r=n);for(var i="",a=t;a<r;++a)i+=T[e[a]];return i}(this,t,r);case"utf8":case"utf-8":return m(this,t,r);case"ascii":return function(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(127&e[i]);return n}(this,t,r);case"latin1":case"binary":return function(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(e[i]);return n}(this,t,r);case"base64":return i=this,a=t,o=r,0===a&&o===i.length?n.fromByteArray(i):n.fromByteArray(i.slice(a,o));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(e,t,r){for(var n=e.slice(t,r),i="",a=0;a<n.length;a+=2)i+=String.fromCharCode(n[a]+256*n[a+1]);return i}(this,t,r);default:if(l)throw TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),l=!0}}function y(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}function v(e,t,r,n,i){var a;if(0===e.length)return -1;if("string"==typeof r?(n=r,r=0):r>0x7fffffff?r=0x7fffffff:r<-0x80000000&&(r=-0x80000000),(a=r*=1)!=a&&(r=i?0:e.length-1),r<0&&(r=e.length+r),r>=e.length)if(i)return -1;else r=e.length-1;else if(r<0)if(!i)return -1;else r=0;if("string"==typeof t&&(t=l.from(t,n)),l.isBuffer(t))return 0===t.length?-1:g(e,t,r,n,i);if("number"==typeof t){if(t&=255,"function"==typeof Uint8Array.prototype.indexOf)if(i)return Uint8Array.prototype.indexOf.call(e,t,r);else return Uint8Array.prototype.lastIndexOf.call(e,t,r);return g(e,[t],r,n,i)}throw TypeError("val must be string, number or Buffer")}function g(e,t,r,n,i){var a,o=1,l=e.length,u=t.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(e.length<2||t.length<2)return -1;o=2,l/=2,u/=2,r/=2}function c(e,t){return 1===o?e[t]:e.readUInt16BE(t*o)}if(i){var s=-1;for(a=r;a<l;a++)if(c(e,a)===c(t,-1===s?0:a-s)){if(-1===s&&(s=a),a-s+1===u)return s*o}else -1!==s&&(a-=a-s),s=-1}else for(r+u>l&&(r=l-u),a=r;a>=0;a--){for(var f=!0,h=0;h<u;h++)if(c(e,a+h)!==c(t,h)){f=!1;break}if(f)return a}return -1}l.isBuffer=function(e){return null!=e&&!0===e._isBuffer&&e!==l.prototype},l.compare=function(e,t){if(k(e,Uint8Array)&&(e=l.from(e,e.offset,e.byteLength)),k(t,Uint8Array)&&(t=l.from(t,t.offset,t.byteLength)),!l.isBuffer(e)||!l.isBuffer(t))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(e===t)return 0;for(var r=e.length,n=t.length,i=0,a=Math.min(r,n);i<a;++i)if(e[i]!==t[i]){r=e[i],n=t[i];break}return r<n?-1:+(n<r)},l.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},l.concat=function(e,t){if(!Array.isArray(e))throw TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return l.alloc(0);if(void 0===t)for(r=0,t=0;r<e.length;++r)t+=e[r].length;var r,n=l.allocUnsafe(t),i=0;for(r=0;r<e.length;++r){var a=e[r];if(k(a,Uint8Array)&&(a=l.from(a)),!l.isBuffer(a))throw TypeError('"list" argument must be an Array of Buffers');a.copy(n,i),i+=a.length}return n},l.byteLength=d,l.prototype._isBuffer=!0,l.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)y(this,t,t+1);return this},l.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)y(this,t,t+3),y(this,t+1,t+2);return this},l.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)y(this,t,t+7),y(this,t+1,t+6),y(this,t+2,t+5),y(this,t+3,t+4);return this},l.prototype.toString=function(){var e=this.length;return 0===e?"":0==arguments.length?m(this,0,e):p.apply(this,arguments)},l.prototype.toLocaleString=l.prototype.toString,l.prototype.equals=function(e){if(!l.isBuffer(e))throw TypeError("Argument must be a Buffer");return this===e||0===l.compare(this,e)},l.prototype.inspect=function(){var e="",r=t.INSPECT_MAX_BYTES;return e=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(e+=" ... "),"<Buffer "+e+">"},a&&(l.prototype[a]=l.prototype.inspect),l.prototype.compare=function(e,t,r,n,i){if(k(e,Uint8Array)&&(e=l.from(e,e.offset,e.byteLength)),!l.isBuffer(e))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof e);if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),t<0||r>e.length||n<0||i>this.length)throw RangeError("out of range index");if(n>=i&&t>=r)return 0;if(n>=i)return -1;if(t>=r)return 1;if(t>>>=0,r>>>=0,n>>>=0,i>>>=0,this===e)return 0;for(var a=i-n,o=r-t,u=Math.min(a,o),c=this.slice(n,i),s=e.slice(t,r),f=0;f<u;++f)if(c[f]!==s[f]){a=c[f],o=s[f];break}return a<o?-1:+(o<a)},l.prototype.includes=function(e,t,r){return -1!==this.indexOf(e,t,r)},l.prototype.indexOf=function(e,t,r){return v(this,e,t,r,!0)},l.prototype.lastIndexOf=function(e,t,r){return v(this,e,t,r,!1)};function m(e,t,r){r=Math.min(e.length,r);for(var n=[],i=t;i<r;){var a,o,l,u,c=e[i],s=null,f=c>239?4:c>223?3:c>191?2:1;if(i+f<=r)switch(f){case 1:c<128&&(s=c);break;case 2:(192&(a=e[i+1]))==128&&(u=(31&c)<<6|63&a)>127&&(s=u);break;case 3:a=e[i+1],o=e[i+2],(192&a)==128&&(192&o)==128&&(u=(15&c)<<12|(63&a)<<6|63&o)>2047&&(u<55296||u>57343)&&(s=u);break;case 4:a=e[i+1],o=e[i+2],l=e[i+3],(192&a)==128&&(192&o)==128&&(192&l)==128&&(u=(15&c)<<18|(63&a)<<12|(63&o)<<6|63&l)>65535&&u<1114112&&(s=u)}null===s?(s=65533,f=1):s>65535&&(s-=65536,n.push(s>>>10&1023|55296),s=56320|1023&s),n.push(s),i+=f}var h=n,d=h.length;if(d<=4096)return String.fromCharCode.apply(String,h);for(var p="",y=0;y<d;)p+=String.fromCharCode.apply(String,h.slice(y,y+=4096));return p}function b(e,t,r){if(e%1!=0||e<0)throw RangeError("offset is not uint");if(e+t>r)throw RangeError("Trying to access beyond buffer length")}function x(e,t,r,n,i,a){if(!l.isBuffer(e))throw TypeError('"buffer" argument must be a Buffer instance');if(t>i||t<a)throw RangeError('"value" argument is out of bounds');if(r+n>e.length)throw RangeError("Index out of range")}function w(e,t,r,n,i,a){if(r+n>e.length||r<0)throw RangeError("Index out of range")}function O(e,t,r,n,a){return t*=1,r>>>=0,a||w(e,t,r,4,34028234663852886e22,-34028234663852886e22),i.write(e,t,r,n,23,4),r+4}function j(e,t,r,n,a){return t*=1,r>>>=0,a||w(e,t,r,8,17976931348623157e292,-17976931348623157e292),i.write(e,t,r,n,52,8),r+8}l.prototype.write=function(e,t,r,n){if(void 0===t)n="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)n=t,r=this.length,t=0;else if(isFinite(t))t>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var i,a,o,l,u,c,s,f,h=this.length-t;if((void 0===r||r>h)&&(r=h),e.length>0&&(r<0||t<0)||t>this.length)throw RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var d=!1;;)switch(n){case"hex":return function(e,t,r,n){r=Number(r)||0;var i=e.length-r;n?(n=Number(n))>i&&(n=i):n=i;var a=t.length;n>a/2&&(n=a/2);for(var o=0;o<n;++o){var l,u=parseInt(t.substr(2*o,2),16);if((l=u)!=l)break;e[r+o]=u}return o}(this,e,t,r);case"utf8":case"utf-8":return i=t,a=r,M(A(e,this.length-i),this,i,a);case"ascii":return o=t,l=r,M(E(e),this,o,l);case"latin1":case"binary":return function(e,t,r,n){return M(E(t),e,r,n)}(this,e,t,r);case"base64":return u=t,c=r,M(S(e),this,u,c);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return s=t,f=r,M(function(e,t){for(var r,n,i=[],a=0;a<e.length&&!((t-=2)<0);++a)n=(r=e.charCodeAt(a))>>8,i.push(r%256),i.push(n);return i}(e,this.length-s),this,s,f);default:if(d)throw TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),d=!0}},l.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},l.prototype.slice=function(e,t){var r=this.length;e=~~e,t=void 0===t?r:~~t,e<0?(e+=r)<0&&(e=0):e>r&&(e=r),t<0?(t+=r)<0&&(t=0):t>r&&(t=r),t<e&&(t=e);var n=this.subarray(e,t);return Object.setPrototypeOf(n,l.prototype),n},l.prototype.readUIntLE=function(e,t,r){e>>>=0,t>>>=0,r||b(e,t,this.length);for(var n=this[e],i=1,a=0;++a<t&&(i*=256);)n+=this[e+a]*i;return n},l.prototype.readUIntBE=function(e,t,r){e>>>=0,t>>>=0,r||b(e,t,this.length);for(var n=this[e+--t],i=1;t>0&&(i*=256);)n+=this[e+--t]*i;return n},l.prototype.readUInt8=function(e,t){return e>>>=0,t||b(e,1,this.length),this[e]},l.prototype.readUInt16LE=function(e,t){return e>>>=0,t||b(e,2,this.length),this[e]|this[e+1]<<8},l.prototype.readUInt16BE=function(e,t){return e>>>=0,t||b(e,2,this.length),this[e]<<8|this[e+1]},l.prototype.readUInt32LE=function(e,t){return e>>>=0,t||b(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+0x1000000*this[e+3]},l.prototype.readUInt32BE=function(e,t){return e>>>=0,t||b(e,4,this.length),0x1000000*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},l.prototype.readIntLE=function(e,t,r){e>>>=0,t>>>=0,r||b(e,t,this.length);for(var n=this[e],i=1,a=0;++a<t&&(i*=256);)n+=this[e+a]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*t)),n},l.prototype.readIntBE=function(e,t,r){e>>>=0,t>>>=0,r||b(e,t,this.length);for(var n=t,i=1,a=this[e+--n];n>0&&(i*=256);)a+=this[e+--n]*i;return a>=(i*=128)&&(a-=Math.pow(2,8*t)),a},l.prototype.readInt8=function(e,t){return(e>>>=0,t||b(e,1,this.length),128&this[e])?-((255-this[e]+1)*1):this[e]},l.prototype.readInt16LE=function(e,t){e>>>=0,t||b(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?0xffff0000|r:r},l.prototype.readInt16BE=function(e,t){e>>>=0,t||b(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?0xffff0000|r:r},l.prototype.readInt32LE=function(e,t){return e>>>=0,t||b(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},l.prototype.readInt32BE=function(e,t){return e>>>=0,t||b(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},l.prototype.readFloatLE=function(e,t){return e>>>=0,t||b(e,4,this.length),i.read(this,e,!0,23,4)},l.prototype.readFloatBE=function(e,t){return e>>>=0,t||b(e,4,this.length),i.read(this,e,!1,23,4)},l.prototype.readDoubleLE=function(e,t){return e>>>=0,t||b(e,8,this.length),i.read(this,e,!0,52,8)},l.prototype.readDoubleBE=function(e,t){return e>>>=0,t||b(e,8,this.length),i.read(this,e,!1,52,8)},l.prototype.writeUIntLE=function(e,t,r,n){if(e*=1,t>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;x(this,e,t,r,i,0)}var a=1,o=0;for(this[t]=255&e;++o<r&&(a*=256);)this[t+o]=e/a&255;return t+r},l.prototype.writeUIntBE=function(e,t,r,n){if(e*=1,t>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;x(this,e,t,r,i,0)}var a=r-1,o=1;for(this[t+a]=255&e;--a>=0&&(o*=256);)this[t+a]=e/o&255;return t+r},l.prototype.writeUInt8=function(e,t,r){return e*=1,t>>>=0,r||x(this,e,t,1,255,0),this[t]=255&e,t+1},l.prototype.writeUInt16LE=function(e,t,r){return e*=1,t>>>=0,r||x(this,e,t,2,65535,0),this[t]=255&e,this[t+1]=e>>>8,t+2},l.prototype.writeUInt16BE=function(e,t,r){return e*=1,t>>>=0,r||x(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=255&e,t+2},l.prototype.writeUInt32LE=function(e,t,r){return e*=1,t>>>=0,r||x(this,e,t,4,0xffffffff,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e,t+4},l.prototype.writeUInt32BE=function(e,t,r){return e*=1,t>>>=0,r||x(this,e,t,4,0xffffffff,0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},l.prototype.writeIntLE=function(e,t,r,n){if(e*=1,t>>>=0,!n){var i=Math.pow(2,8*r-1);x(this,e,t,r,i-1,-i)}var a=0,o=1,l=0;for(this[t]=255&e;++a<r&&(o*=256);)e<0&&0===l&&0!==this[t+a-1]&&(l=1),this[t+a]=(e/o|0)-l&255;return t+r},l.prototype.writeIntBE=function(e,t,r,n){if(e*=1,t>>>=0,!n){var i=Math.pow(2,8*r-1);x(this,e,t,r,i-1,-i)}var a=r-1,o=1,l=0;for(this[t+a]=255&e;--a>=0&&(o*=256);)e<0&&0===l&&0!==this[t+a+1]&&(l=1),this[t+a]=(e/o|0)-l&255;return t+r},l.prototype.writeInt8=function(e,t,r){return e*=1,t>>>=0,r||x(this,e,t,1,127,-128),e<0&&(e=255+e+1),this[t]=255&e,t+1},l.prototype.writeInt16LE=function(e,t,r){return e*=1,t>>>=0,r||x(this,e,t,2,32767,-32768),this[t]=255&e,this[t+1]=e>>>8,t+2},l.prototype.writeInt16BE=function(e,t,r){return e*=1,t>>>=0,r||x(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=255&e,t+2},l.prototype.writeInt32LE=function(e,t,r){return e*=1,t>>>=0,r||x(this,e,t,4,0x7fffffff,-0x80000000),this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4},l.prototype.writeInt32BE=function(e,t,r){return e*=1,t>>>=0,r||x(this,e,t,4,0x7fffffff,-0x80000000),e<0&&(e=0xffffffff+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},l.prototype.writeFloatLE=function(e,t,r){return O(this,e,t,!0,r)},l.prototype.writeFloatBE=function(e,t,r){return O(this,e,t,!1,r)},l.prototype.writeDoubleLE=function(e,t,r){return j(this,e,t,!0,r)},l.prototype.writeDoubleBE=function(e,t,r){return j(this,e,t,!1,r)},l.prototype.copy=function(e,t,r,n){if(!l.isBuffer(e))throw TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),t>=e.length&&(t=e.length),t||(t=0),n>0&&n<r&&(n=r),n===r||0===e.length||0===this.length)return 0;if(t<0)throw RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw RangeError("Index out of range");if(n<0)throw RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),e.length-t<n-r&&(n=e.length-t+r);var i=n-r;if(this===e&&"function"==typeof Uint8Array.prototype.copyWithin)this.copyWithin(t,r,n);else if(this===e&&r<t&&t<n)for(var a=i-1;a>=0;--a)e[a+t]=this[a+r];else Uint8Array.prototype.set.call(e,this.subarray(r,n),t);return i},l.prototype.fill=function(e,t,r,n){if("string"==typeof e){if("string"==typeof t?(n=t,t=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw TypeError("encoding must be a string");if("string"==typeof n&&!l.isEncoding(n))throw TypeError("Unknown encoding: "+n);if(1===e.length){var i,a=e.charCodeAt(0);("utf8"===n&&a<128||"latin1"===n)&&(e=a)}}else"number"==typeof e?e&=255:"boolean"==typeof e&&(e=Number(e));if(t<0||this.length<t||this.length<r)throw RangeError("Out of range index");if(r<=t)return this;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(i=t;i<r;++i)this[i]=e;else{var o=l.isBuffer(e)?e:l.from(e,n),u=o.length;if(0===u)throw TypeError('The value "'+e+'" is invalid for argument "value"');for(i=0;i<r-t;++i)this[i+t]=o[i%u]}return this};var P=/[^+/0-9A-Za-z-_]/g;function A(e,t){t=t||1/0;for(var r,n=e.length,i=null,a=[],o=0;o<n;++o){if((r=e.charCodeAt(o))>55295&&r<57344){if(!i){if(r>56319||o+1===n){(t-=3)>-1&&a.push(239,191,189);continue}i=r;continue}if(r<56320){(t-=3)>-1&&a.push(239,191,189),i=r;continue}r=(i-55296<<10|r-56320)+65536}else i&&(t-=3)>-1&&a.push(239,191,189);if(i=null,r<128){if((t-=1)<0)break;a.push(r)}else if(r<2048){if((t-=2)<0)break;a.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;a.push(r>>12|224,r>>6&63|128,63&r|128)}else if(r<1114112){if((t-=4)<0)break;a.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}else throw Error("Invalid code point")}return a}function E(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}function S(e){return n.toByteArray(function(e){if((e=(e=e.split("=")[0]).trim().replace(P,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function M(e,t,r,n){for(var i=0;i<n&&!(i+r>=t.length)&&!(i>=e.length);++i)t[i+r]=e[i];return i}function k(e,t){return e instanceof t||null!=e&&null!=e.constructor&&null!=e.constructor.name&&e.constructor.name===t.name}var T=function(){for(var e="0123456789abcdef",t=Array(256),r=0;r<16;++r)for(var n=16*r,i=0;i<16;++i)t[n+i]=e[r]+e[i];return t}()},783:function(e,t){t.read=function(e,t,r,n,i){var a,o,l=8*i-n-1,u=(1<<l)-1,c=u>>1,s=-7,f=r?i-1:0,h=r?-1:1,d=e[t+f];for(f+=h,a=d&(1<<-s)-1,d>>=-s,s+=l;s>0;a=256*a+e[t+f],f+=h,s-=8);for(o=a&(1<<-s)-1,a>>=-s,s+=n;s>0;o=256*o+e[t+f],f+=h,s-=8);if(0===a)a=1-c;else{if(a===u)return o?NaN:1/0*(d?-1:1);o+=Math.pow(2,n),a-=c}return(d?-1:1)*o*Math.pow(2,a-n)},t.write=function(e,t,r,n,i,a){var o,l,u,c=8*a-i-1,s=(1<<c)-1,f=s>>1,h=5960464477539062e-23*(23===i),d=n?0:a-1,p=n?1:-1,y=+(t<0||0===t&&1/t<0);for(isNaN(t=Math.abs(t))||t===1/0?(l=+!!isNaN(t),o=s):(o=Math.floor(Math.log(t)/Math.LN2),t*(u=Math.pow(2,-o))<1&&(o--,u*=2),o+f>=1?t+=h/u:t+=h*Math.pow(2,1-f),t*u>=2&&(o++,u/=2),o+f>=s?(l=0,o=s):o+f>=1?(l=(t*u-1)*Math.pow(2,i),o+=f):(l=t*Math.pow(2,f-1)*Math.pow(2,i),o=0));i>=8;e[r+d]=255&l,d+=p,l/=256,i-=8);for(o=o<<i|l,c+=i;c>0;e[r+d]=255&o,d+=p,o/=256,c-=8);e[r+d-p]|=128*y}}},r={};function n(t){var i=r[t];if(void 0!==i)return i.exports;var a=r[t]={exports:{}},o=!0;try{e[t](a,a.exports,n),o=!1}finally{o&&delete r[t]}return a.exports}n.ab="/ROOT/node_modules/next/dist/compiled/buffer/",t.exports=n(72)}()},38351,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),r.isPlainObject=function(e){if(!e||"object"!=typeof e)return!1;let t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&"[object Object]"===Object.prototype.toString.call(e)}},9866,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),r.getSymbols=function(e){return Object.getOwnPropertySymbols(e).filter(t=>Object.prototype.propertyIsEnumerable.call(e,t))}},23678,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),r.getTag=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}},55813,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),r.argumentsTag="[object Arguments]",r.arrayBufferTag="[object ArrayBuffer]",r.arrayTag="[object Array]",r.bigInt64ArrayTag="[object BigInt64Array]",r.bigUint64ArrayTag="[object BigUint64Array]",r.booleanTag="[object Boolean]",r.dataViewTag="[object DataView]",r.dateTag="[object Date]",r.errorTag="[object Error]",r.float32ArrayTag="[object Float32Array]",r.float64ArrayTag="[object Float64Array]",r.functionTag="[object Function]",r.int16ArrayTag="[object Int16Array]",r.int32ArrayTag="[object Int32Array]",r.int8ArrayTag="[object Int8Array]",r.mapTag="[object Map]",r.numberTag="[object Number]",r.objectTag="[object Object]",r.regexpTag="[object RegExp]",r.setTag="[object Set]",r.stringTag="[object String]",r.symbolTag="[object Symbol]",r.uint16ArrayTag="[object Uint16Array]",r.uint32ArrayTag="[object Uint32Array]",r.uint8ArrayTag="[object Uint8Array]",r.uint8ClampedArrayTag="[object Uint8ClampedArray]"},1645,(e,t,r)=>{"use strict";var n=e.i(67034);Object.defineProperty(r,Symbol.toStringTag,{value:"Module"});let i=e.r(38351),a=e.r(9866),o=e.r(23678),l=e.r(55813),u=e.r(23855);r.isEqualWith=function(e,t,r){return function e(t,r,c,s,f,h,d){let p=d(t,r,c,s,f,h);if(void 0!==p)return p;if(typeof t==typeof r)switch(typeof t){case"bigint":case"string":case"boolean":case"symbol":case"undefined":case"function":return t===r;case"number":return t===r||Object.is(t,r)}return function t(r,c,s,f){if(Object.is(r,c))return!0;let h=o.getTag(r),d=o.getTag(c);if(h===l.argumentsTag&&(h=l.objectTag),d===l.argumentsTag&&(d=l.objectTag),h!==d)return!1;switch(h){case l.stringTag:return r.toString()===c.toString();case l.numberTag:{let e=r.valueOf(),t=c.valueOf();return u.eq(e,t)}case l.booleanTag:case l.dateTag:case l.symbolTag:return Object.is(r.valueOf(),c.valueOf());case l.regexpTag:return r.source===c.source&&r.flags===c.flags;case l.functionTag:return r===c}let p=(s=null!=s?s:new Map).get(r),y=s.get(c);if(null!=p&&null!=y)return p===c;s.set(r,c),s.set(c,r);try{switch(h){case l.mapTag:if(r.size!==c.size)return!1;for(let[t,n]of r.entries())if(!c.has(t)||!e(n,c.get(t),t,r,c,s,f))return!1;return!0;case l.setTag:{if(r.size!==c.size)return!1;let t=Array.from(r.values()),n=Array.from(c.values());for(let i=0;i<t.length;i++){let a=t[i],o=n.findIndex(t=>e(a,t,void 0,r,c,s,f));if(-1===o)return!1;n.splice(o,1)}return!0}case l.arrayTag:case l.uint8ArrayTag:case l.uint8ClampedArrayTag:case l.uint16ArrayTag:case l.uint32ArrayTag:case l.bigUint64ArrayTag:case l.int8ArrayTag:case l.int16ArrayTag:case l.int32ArrayTag:case l.bigInt64ArrayTag:case l.float32ArrayTag:case l.float64ArrayTag:if(void 0!==n.Buffer&&n.Buffer.isBuffer(r)!==n.Buffer.isBuffer(c)||r.length!==c.length)return!1;for(let t=0;t<r.length;t++)if(!e(r[t],c[t],t,r,c,s,f))return!1;return!0;case l.arrayBufferTag:if(r.byteLength!==c.byteLength)return!1;return t(new Uint8Array(r),new Uint8Array(c),s,f);case l.dataViewTag:if(r.byteLength!==c.byteLength||r.byteOffset!==c.byteOffset)return!1;return t(new Uint8Array(r),new Uint8Array(c),s,f);case l.errorTag:return r.name===c.name&&r.message===c.message;case l.objectTag:{if(!(t(r.constructor,c.constructor,s,f)||i.isPlainObject(r)&&i.isPlainObject(c)))return!1;let n=[...Object.keys(r),...a.getSymbols(r)],o=[...Object.keys(c),...a.getSymbols(c)];if(n.length!==o.length)return!1;for(let t=0;t<n.length;t++){let i=n[t],a=r[i];if(!Object.hasOwn(c,i))return!1;let o=c[i];if(!e(a,o,i,r,c,s,f))return!1}return!0}default:return!1}}finally{s.delete(r),s.delete(c)}}(t,r,h,d)}(e,t,void 0,void 0,void 0,void 0,r)}},75752,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),r.noop=function(){}},95144,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"});let n=e.r(1645),i=e.r(75752);r.isEqual=function(e,t){return n.isEqualWith(e,t,i.noop)}},13287,(e,t,r)=>{t.exports=e.r(95144).isEqual},10185,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),r.uniqBy=function(e,t){let r=new Map;for(let n=0;n<e.length;n++){let i=e[n],a=t(i);r.has(a)||r.set(a,i)}return Array.from(r.values())}},36074,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),r.identity=function(e){return e}},52603,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),r.isObjectLike=function(e){return"object"==typeof e&&null!==e}},44936,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"});let n=e.r(69642),i=e.r(52603);r.isArrayLikeObject=function(e){return i.isObjectLike(e)&&n.isArrayLike(e)}},77473,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"});let n=e.r(18915);r.property=function(e){return function(t){return n.get(t,e)}}},75234,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),r.isPrimitive=function(e){return null==e||"object"!=typeof e&&"function"!=typeof e}},44695,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"});let n=e.r(3450),i=e.r(90820),a=e.r(75234),o=e.r(23855);function l(e,t,r,n){if(t===e)return!0;switch(typeof t){case"object":return function(e,t,r,n){if(null==t)return!0;if(Array.isArray(t))return u(e,t,r,n);if(t instanceof Map){var i=e,o=t,l=r,s=n;if(0===o.size)return!0;if(!(i instanceof Map))return!1;for(let[e,t]of o.entries())if(!1===l(i.get(e),t,e,i,o,s))return!1;return!0}if(t instanceof Set)return c(e,t,r,n);let f=Object.keys(t);if(null==e)return 0===f.length;if(0===f.length)return!0;if(n&&n.has(t))return n.get(t)===e;n&&n.set(t,e);try{for(let i=0;i<f.length;i++){let o=f[i];if(!a.isPrimitive(e)&&!(o in e)||void 0===t[o]&&void 0!==e[o]||null===t[o]&&null!==e[o]||!r(e[o],t[o],o,e,t,n))return!1}return!0}finally{n&&n.delete(t)}}(e,t,r,n);case"function":if(Object.keys(t).length>0)return l(e,{...t},r,n);return o.eq(e,t);default:if(!i.isObject(e))return o.eq(e,t);if("string"==typeof t)return""===t;return!0}}function u(e,t,r,n){if(0===t.length)return!0;if(!Array.isArray(e))return!1;let i=new Set;for(let a=0;a<t.length;a++){let o=t[a],l=!1;for(let u=0;u<e.length;u++){if(i.has(u))continue;let c=e[u],s=!1;if(r(c,o,a,e,t,n)&&(s=!0),s){i.add(u),l=!0;break}}if(!l)return!1}return!0}function c(e,t,r,n){return 0===t.size||e instanceof Set&&u([...e],[...t],r,n)}r.isMatchWith=function(e,t,r){return"function"!=typeof r?n.isMatch(e,t):l(e,t,function e(t,n,i,a,o,u){let c=r(t,n,i,a,o,u);return void 0!==c?!!c:l(t,n,e,u)},new Map)},r.isSetMatch=c},3450,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"});let n=e.r(44695);r.isMatch=function(e,t){return n.isMatchWith(e,t,()=>void 0)}},55424,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),r.isTypedArray=function(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}},92348,(e,t,r)=>{"use strict";var n=e.i(67034);Object.defineProperty(r,Symbol.toStringTag,{value:"Module"});let i=e.r(9866),a=e.r(23678),o=e.r(55813),l=e.r(75234),u=e.r(55424);function c(e,t,r){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:new Map,f=arguments.length>4&&void 0!==arguments[4]?arguments[4]:void 0,h=null==f?void 0:f(e,t,r,i);if(void 0!==h)return h;if(l.isPrimitive(e))return e;if(i.has(e))return i.get(e);if(Array.isArray(e)){let t=Array(e.length);i.set(e,t);for(let n=0;n<e.length;n++)t[n]=c(e[n],n,r,i,f);return Object.hasOwn(e,"index")&&(t.index=e.index),Object.hasOwn(e,"input")&&(t.input=e.input),t}if(e instanceof Date)return new Date(e.getTime());if(e instanceof RegExp){let t=new RegExp(e.source,e.flags);return t.lastIndex=e.lastIndex,t}if(e instanceof Map){let t=new Map;for(let[n,a]of(i.set(e,t),e))t.set(n,c(a,n,r,i,f));return t}if(e instanceof Set){let t=new Set;for(let n of(i.set(e,t),e))t.add(c(n,void 0,r,i,f));return t}if(void 0!==n.Buffer&&n.Buffer.isBuffer(e))return e.subarray();if(u.isTypedArray(e)){let t=new(Object.getPrototypeOf(e)).constructor(e.length);i.set(e,t);for(let n=0;n<e.length;n++)t[n]=c(e[n],n,r,i,f);return t}if(e instanceof ArrayBuffer||"undefined"!=typeof SharedArrayBuffer&&e instanceof SharedArrayBuffer)return e.slice(0);if(e instanceof DataView){let t=new DataView(e.buffer.slice(0),e.byteOffset,e.byteLength);return i.set(e,t),s(t,e,r,i,f),t}if("undefined"!=typeof File&&e instanceof File){let t=new File([e],e.name,{type:e.type});return i.set(e,t),s(t,e,r,i,f),t}if(e instanceof Blob){let t=new Blob([e],{type:e.type});return i.set(e,t),s(t,e,r,i,f),t}if(e instanceof Error){let t=new e.constructor;return i.set(e,t),t.message=e.message,t.name=e.name,t.stack=e.stack,t.cause=e.cause,s(t,e,r,i,f),t}if("object"==typeof e&&function(e){switch(a.getTag(e)){case o.argumentsTag:case o.arrayTag:case o.arrayBufferTag:case o.dataViewTag:case o.booleanTag:case o.dateTag:case o.float32ArrayTag:case o.float64ArrayTag:case o.int8ArrayTag:case o.int16ArrayTag:case o.int32ArrayTag:case o.mapTag:case o.numberTag:case o.objectTag:case o.regexpTag:case o.setTag:case o.stringTag:case o.symbolTag:case o.uint8ArrayTag:case o.uint8ClampedArrayTag:case o.uint16ArrayTag:case o.uint32ArrayTag:return!0;default:return!1}}(e)){let t=Object.create(Object.getPrototypeOf(e));return i.set(e,t),s(t,e,r,i,f),t}return e}function s(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,n=arguments.length>3?arguments[3]:void 0,a=arguments.length>4?arguments[4]:void 0,o=[...Object.keys(t),...i.getSymbols(t)];for(let i=0;i<o.length;i++){let l=o[i],u=Object.getOwnPropertyDescriptor(e,l);(null==u||u.writable)&&(e[l]=c(t[l],l,r,n,a))}}r.cloneDeepWith=function(e,t){return c(e,void 0,e,new Map,t)},r.cloneDeepWithImpl=c,r.copyProperties=s},99510,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"});let n=e.r(92348);r.cloneDeep=function(e){return n.cloneDeepWithImpl(e,void 0,e,new Map,void 0)}},67284,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"});let n=e.r(3450),i=e.r(99510);r.matches=function(e){return e=i.cloneDeep(e),t=>n.isMatch(t,e)}},68110,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"});let n=e.r(92348),i=e.r(55813);r.cloneDeepWith=function(e,t){return n.cloneDeepWith(e,(r,a,o,l)=>{let u=null==t?void 0:t(r,a,o,l);if(void 0!==u)return u;if("object"==typeof e)switch(Object.prototype.toString.call(e)){case i.numberTag:case i.stringTag:case i.booleanTag:{let t=new e.constructor(null==e?void 0:e.valueOf());return n.copyProperties(t,e),t}case i.argumentsTag:{let t={};return n.copyProperties(t,e),t.length=e.length,t[Symbol.iterator]=e[Symbol.iterator],t}default:return}})}},93141,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"});let n=e.r(68110);r.cloneDeep=function(e){return n.cloneDeepWith(e)}},17858,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"});let n=e.r(23678);r.isArguments=function(e){return null!==e&&"object"==typeof e&&"[object Arguments]"===n.getTag(e)}},56518,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"});let n=e.r(33858),i=e.r(5257),a=e.r(17858),o=e.r(79232);r.has=function(e,t){let r;if(0===(r=Array.isArray(t)?t:"string"==typeof t&&n.isDeepKey(t)&&(null==e?void 0:e[t])==null?o.toPath(t):[t]).length)return!1;let l=e;for(let e=0;e<r.length;e++){let t=r[e];if((null==l||!Object.hasOwn(l,t))&&!((Array.isArray(l)||a.isArguments(l))&&i.isIndex(t)&&t<l.length))return!1;l=l[t]}return!0}},40656,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"});let n=e.r(3450),i=e.r(96736),a=e.r(93141),o=e.r(18915),l=e.r(56518);r.matchesProperty=function(e,t){switch(typeof e){case"object":Object.is(null==e?void 0:e.valueOf(),-0)&&(e="-0");break;case"number":e=i.toKey(e)}return t=a.cloneDeep(t),function(r){let i=o.get(r,e);return void 0===i?l.has(r,e):void 0===t?void 0===i:n.isMatch(i,t)}}},27519,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"});let n=e.r(36074),i=e.r(77473),a=e.r(67284),o=e.r(40656);r.iteratee=function(e){if(null==e)return n.identity;switch(typeof e){case"function":return e;case"object":if(Array.isArray(e)&&2===e.length)return o.matchesProperty(e[0],e[1]);return a.matches(e);case"string":case"symbol":case"number":return i.property(e)}}},77008,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"});let n=e.r(10185),i=e.r(36074),a=e.r(44936),o=e.r(27519);r.uniqBy=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:i.identity;return a.isArrayLikeObject(e)?n.uniqBy(Array.from(e),o.iteratee(t)):[]}},92809,(e,t,r)=>{t.exports=e.r(77008).uniqBy},24798,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),r.debounce=function(e,t){let r,{signal:n,edges:i}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=null,o=null!=i&&i.includes("leading"),l=null==i||i.includes("trailing"),u=()=>{null!==a&&(e.apply(r,a),r=void 0,a=null)},c=null,s=()=>{null!=c&&clearTimeout(c),c=setTimeout(()=>{c=null,l&&u(),f()},t)},f=()=>{null!==c&&(clearTimeout(c),c=null),r=void 0,a=null},h=function(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];if(null==n?void 0:n.aborted)return;r=this,a=t;let l=null==c;s(),o&&l&&u()};return h.schedule=s,h.cancel=f,h.flush=()=>{u()},null==n||n.addEventListener("abort",f,{once:!0}),h}},96074,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"});let n=e.r(24798);r.debounce=function(e){let t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};"object"!=typeof i&&(i={});let{leading:a=!1,trailing:o=!0,maxWait:l}=i,u=[,,];a&&(u[0]="leading"),o&&(u[1]="trailing");let c=null,s=n.debounce(function(){for(var r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];t=e.apply(this,n),c=null},r,{edges:u}),f=function(){for(var r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];return null!=l&&(null===c&&(c=Date.now()),Date.now()-c>=l)?(t=e.apply(this,n),c=Date.now(),s.cancel(),s.schedule(),t):(s.apply(this,n),t)};return f.cancel=s.cancel,f.flush=()=>(s.flush(),t),f}},46508,(e,t,r)=>{"use strict";Object.defineProperty(r,Symbol.toStringTag,{value:"Module"});let n=e.r(96074);r.throttle=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{leading:i=!0,trailing:a=!0}=r;return n.debounce(e,t,{leading:i,maxWait:t,trailing:a})}},58723,(e,t,r)=>{t.exports=e.r(46508).throttle},58630,e=>{"use strict";e.s(["AnalyticsCharts",()=>by],58630);var t,r,n,i,a,o,l,u,c,s=e.i(43476),f=e.i(66027),h=e.i(15288),d=e.i(71645);function p(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}e.i(47167);var y=Symbol.for("immer-nothing"),v=Symbol.for("immer-draftable"),g=Symbol.for("immer-state");function m(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];throw Error("[Immer] minified error nr: ".concat(e,". Full error at: https://bit.ly/3cXEKWf"))}var b=Object.getPrototypeOf;function x(e){return!!e&&!!e[g]}function w(e){var t;return!!e&&(j(e)||Array.isArray(e)||!!e[v]||!!(null==(t=e.constructor)?void 0:t[v])||M(e)||k(e))}var O=Object.prototype.constructor.toString();function j(e){if(!e||"object"!=typeof e)return!1;let t=b(e);if(null===t)return!0;let r=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return r===Object||"function"==typeof r&&Function.toString.call(r)===O}function P(e,t){0===A(e)?Reflect.ownKeys(e).forEach(r=>{t(r,e[r],e)}):e.forEach((r,n)=>t(n,r,e))}function A(e){let t=e[g];return t?t.type_:Array.isArray(e)?1:M(e)?2:3*!!k(e)}function E(e,t){return 2===A(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function S(e,t,r){let n=A(e);2===n?e.set(t,r):3===n?e.add(r):e[t]=r}function M(e){return e instanceof Map}function k(e){return e instanceof Set}function T(e){return e.copy_||e.base_}function C(e,t){if(M(e))return new Map(e);if(k(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);let r=j(e);if(!0!==t&&("class_only"!==t||r)){let t=b(e);return null!==t&&r?{...e}:Object.assign(Object.create(t),e)}{let t=Object.getOwnPropertyDescriptors(e);delete t[g];let r=Reflect.ownKeys(t);for(let n=0;n<r.length;n++){let i=r[n],a=t[i];!1===a.writable&&(a.writable=!0,a.configurable=!0),(a.get||a.set)&&(t[i]={configurable:!0,writable:!0,enumerable:a.enumerable,value:e[i]})}return Object.create(b(e),t)}}function _(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return N(e)||x(e)||!w(e)||(A(e)>1&&(e.set=e.add=e.clear=e.delete=D),Object.freeze(e),t&&Object.entries(e).forEach(e=>{let[t,r]=e;return _(r,!0)})),e}function D(){m(2)}function N(e){return Object.isFrozen(e)}var I={};function L(e){let t=I[e];return t||m(0,e),t}function B(e,t){t&&(L("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function R(e){z(e),e.drafts_.forEach(F),e.drafts_=null}function z(e){e===t&&(t=e.parent_)}function U(e){return t={drafts_:[],parent_:t,immer_:e,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function F(e){let t=e[g];0===t.type_||1===t.type_?t.revoke_():t.revoked_=!0}function K(e,t){t.unfinalizedDrafts_=t.drafts_.length;let r=t.drafts_[0];return void 0!==e&&e!==r?(r[g].modified_&&(R(t),m(4)),w(e)&&(e=q(t,e),t.parent_||W(t,e)),t.patches_&&L("Patches").generateReplacementPatches_(r[g].base_,e,t.patches_,t.inversePatches_)):e=q(t,r,[]),R(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==y?e:void 0}function q(e,t,r){if(N(t))return t;let n=t[g];if(!n)return P(t,(i,a)=>H(e,n,t,i,a,r)),t;if(n.scope_!==e)return t;if(!n.modified_)return W(e,n.base_,!0),n.base_;if(!n.finalized_){n.finalized_=!0,n.scope_.unfinalizedDrafts_--;let t=n.copy_,i=t,a=!1;3===n.type_&&(i=new Set(t),t.clear(),a=!0),P(i,(i,o)=>H(e,n,t,i,o,r,a)),W(e,t,!1),r&&e.patches_&&L("Patches").generatePatches_(n,r,e.patches_,e.inversePatches_)}return n.copy_}function H(e,t,r,n,i,a,o){if(x(i)){let o=q(e,i,a&&t&&3!==t.type_&&!E(t.assigned_,n)?a.concat(n):void 0);if(S(r,n,o),!x(o))return;e.canAutoFreeze_=!1}else o&&r.add(i);if(w(i)&&!N(i)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;q(e,i),(!t||!t.scope_.parent_)&&"symbol"!=typeof n&&Object.prototype.propertyIsEnumerable.call(r,n)&&W(e,i)}}function W(e,t){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&_(t,r)}var V={get(e,t){if(t===g)return e;let r=T(e);if(!E(r,t)){var n,i=e,a=r,o=t;let l=X(a,o);return l?"value"in l?l.value:null==(n=l.get)?void 0:n.call(i.draft_):void 0}let l=r[t];return e.finalized_||!w(l)?l:l===G(e.base_,t)?(Z(e),e.copy_[t]=J(l,e)):l},has:(e,t)=>t in T(e),ownKeys:e=>Reflect.ownKeys(T(e)),set(e,t,r){let n=X(T(e),t);if(null==n?void 0:n.set)return n.set.call(e.draft_,r),!0;if(!e.modified_){let n=G(T(e),t),i=null==n?void 0:n[g];if(i&&i.base_===r)return e.copy_[t]=r,e.assigned_[t]=!1,!0;if((r===n?0!==r||1/r==1/n:r!=r&&n!=n)&&(void 0!==r||E(e.base_,t)))return!0;Z(e),$(e)}return!!(e.copy_[t]===r&&(void 0!==r||t in e.copy_)||Number.isNaN(r)&&Number.isNaN(e.copy_[t]))||(e.copy_[t]=r,e.assigned_[t]=!0,!0)},deleteProperty:(e,t)=>(void 0!==G(e.base_,t)||t in e.base_?(e.assigned_[t]=!1,Z(e),$(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0),getOwnPropertyDescriptor(e,t){let r=T(e),n=Reflect.getOwnPropertyDescriptor(r,t);return n?{writable:!0,configurable:1!==e.type_||"length"!==t,enumerable:n.enumerable,value:r[t]}:n},defineProperty(){m(11)},getPrototypeOf:e=>b(e.base_),setPrototypeOf(){m(12)}},Y={};function G(e,t){let r=e[g];return(r?T(r):e)[t]}function X(e,t){if(!(t in e))return;let r=b(e);for(;r;){let e=Object.getOwnPropertyDescriptor(r,t);if(e)return e;r=b(r)}}function $(e){!e.modified_&&(e.modified_=!0,e.parent_&&$(e.parent_))}function Z(e){e.copy_||(e.copy_=C(e.base_,e.scope_.immer_.useStrictShallowCopy_))}function J(e,r){let n=M(e)?L("MapSet").proxyMap_(e,r):k(e)?L("MapSet").proxySet_(e,r):function(e,r){let n=Array.isArray(e),i={type_:+!!n,scope_:r?r.scope_:t,modified_:!1,finalized_:!1,assigned_:{},parent_:r,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1},a=i,o=V;n&&(a=[i],o=Y);let{revoke:l,proxy:u}=Proxy.revocable(a,o);return i.draft_=u,i.revoke_=l,u}(e,r);return(r?r.scope_:t).drafts_.push(n),n}function Q(e){return x(e)||m(10,e),function e(t){let r;if(!w(t)||N(t))return t;let n=t[g];if(n){if(!n.modified_)return n.base_;n.finalized_=!0,r=C(t,n.scope_.immer_.useStrictShallowCopy_)}else r=C(t,!0);return P(r,(t,n)=>{S(r,t,e(n))}),n&&(n.finalized_=!1),r}(e)}P(V,(e,t)=>{Y[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}}),Y.deleteProperty=function(e,t){return Y.set.call(this,e,t,void 0)},Y.set=function(e,t,r){return V.set.call(this,e[0],t,r,e[0])};var ee=new class{createDraft(e){w(e)||m(8),x(e)&&(e=Q(e));let t=U(this),r=J(e,void 0);return r[g].isManual_=!0,z(t),r}finishDraft(e,t){let r=e&&e[g];r&&r.isManual_||m(9);let{scope_:n}=r;return B(n,t),K(void 0,n)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let r;for(r=t.length-1;r>=0;r--){let n=t[r];if(0===n.path.length&&"replace"===n.op){e=n.value;break}}r>-1&&(t=t.slice(r+1));let n=L("Patches").applyPatches_;return x(e)?n(e,t):this.produce(e,e=>n(e,t))}constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(e,t,r)=>{let n;if("function"==typeof e&&"function"!=typeof t){let r=t;t=e;let n=this;return function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:r;for(var i=arguments.length,a=Array(i>1?i-1:0),o=1;o<i;o++)a[o-1]=arguments[o];return n.produce(e,e=>t.call(this,e,...a))}}if("function"!=typeof t&&m(6),void 0!==r&&"function"!=typeof r&&m(7),w(e)){let i=U(this),a=J(e,void 0),o=!0;try{n=t(a),o=!1}finally{o?R(i):z(i)}return B(i,r),K(n,i)}if(e&&"object"==typeof e)m(1,e);else{if(void 0===(n=t(e))&&(n=e),n===y&&(n=void 0),this.autoFreeze_&&_(n,!0),r){let t=[],i=[];L("Patches").generateReplacementPatches_(e,n,t,i),r(t,i)}return n}},this.produceWithPatches=(e,t)=>{let r,n;if("function"==typeof e){var i=this;return function(t){for(var r=arguments.length,n=Array(r>1?r-1:0),a=1;a<r;a++)n[a-1]=arguments[a];return i.produceWithPatches(t,t=>e(t,...n))}}return[this.produce(e,t,(e,t)=>{r=e,n=t}),r,n]},"boolean"==typeof(null==e?void 0:e.autoFreeze)&&this.setAutoFreeze(e.autoFreeze),"boolean"==typeof(null==e?void 0:e.useStrictShallowCopy)&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}},et=ee.produce;ee.produceWithPatches.bind(ee),ee.setAutoFreeze.bind(ee),ee.setUseStrictShallowCopy.bind(ee),ee.applyPatches.bind(ee),ee.createDraft.bind(ee),ee.finishDraft.bind(ee);var er=e=>Array.isArray(e)?e:[e],en=0,ei=class{get value(){return this._value}set value(e){this.value!==e&&(this._value=e,this.revision=++en)}constructor(e,t=ea){p(this,"revision",en),p(this,"_value",void 0),p(this,"_lastValue",void 0),p(this,"_isEqual",ea),this._value=this._lastValue=e,this._isEqual=t}};function ea(e,t){return e===t}function eo(e){return e instanceof ei||console.warn("Not a valid cell! ",e),e.value}var el=(e,t)=>!1;function eu(){return function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:ea;return new ei(e,t)}(null,el)}var ec=e=>{let t=e.collectionTag;null===t&&(t=e.collectionTag=eu()),eo(t)};Symbol();var es=0,ef=Object.getPrototypeOf({}),eh=class{constructor(e){p(this,"proxy",new Proxy(this,ed)),p(this,"tag",eu()),p(this,"tags",{}),p(this,"children",{}),p(this,"collectionTag",null),p(this,"id",es++),this.value=e,this.value=e,this.tag.value=e}},ed={get:(e,t)=>(function(){let{value:r}=e,n=Reflect.get(r,t);if("symbol"==typeof t||t in ef)return n;if("object"==typeof n&&null!==n){var i;let r=e.children[t];return void 0===r&&(r=e.children[t]=Array.isArray(i=n)?new ep(i):new eh(i)),r.tag&&eo(r.tag),r.proxy}{let r=e.tags[t];return void 0===r&&((r=e.tags[t]=eu()).value=n),eo(r),n}})(),ownKeys:e=>(ec(e),Reflect.ownKeys(e.value)),getOwnPropertyDescriptor:(e,t)=>Reflect.getOwnPropertyDescriptor(e.value,t),has:(e,t)=>Reflect.has(e.value,t)},ep=class{constructor(e){p(this,"proxy",new Proxy([this],ey)),p(this,"tag",eu()),p(this,"tags",{}),p(this,"children",{}),p(this,"collectionTag",null),p(this,"id",es++),this.value=e,this.value=e,this.tag.value=e}},ey={get(e,t){let[r]=e;return"length"===t&&ec(r),ed.get(r,t)},ownKeys(e){let[t]=e;return ed.ownKeys(t)},getOwnPropertyDescriptor(e,t){let[r]=e;return ed.getOwnPropertyDescriptor(r,t)},has(e,t){let[r]=e;return ed.has(r,t)}},ev="undefined"!=typeof WeakRef?WeakRef:class{deref(){return this.value}constructor(e){this.value=e}};function eg(){return{s:0,v:void 0,o:null,p:null}}function em(e){let t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=eg(),{resultEqualityCheck:i}=r,a=0;function o(){let r,o=n,{length:l}=arguments;for(let e=0;e<l;e++){let t=arguments[e];if("function"==typeof t||"object"==typeof t&&null!==t){let e=o.o;null===e&&(o.o=e=new WeakMap);let r=e.get(t);void 0===r?(o=eg(),e.set(t,o)):o=r}else{let e=o.p;null===e&&(o.p=e=new Map);let r=e.get(t);void 0===r?(o=eg(),e.set(t,o)):o=r}}let u=o;if(1===o.s)r=o.v;else if(r=e.apply(null,arguments),a++,i){var c,s;let e=null!=(s=null==t||null==(c=t.deref)?void 0:c.call(t))?s:t;null!=e&&i(e,r)&&(r=e,0!==a&&a--),t="object"==typeof r&&null!==r||"function"==typeof r?new ev(r):r}return u.s=1,u.v=r,r}return o.clearCache=()=>{n=eg(),o.resetResultsCount()},o.resultsCount=()=>a,o.resetResultsCount=()=>{a=0},o}var eb=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];let i="function"==typeof e?{memoize:e,memoizeOptions:r}:e,a=function(){let e;for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];let a=0,o=0,l={},u=r.pop();"object"==typeof u&&(l=u,u=r.pop()),function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"expected a function, instead received ".concat(typeof e);if("function"!=typeof e)throw TypeError(t)}(u,"createSelector expects an output function after the inputs, but received: [".concat(typeof u,"]"));let{memoize:c,memoizeOptions:s=[],argsMemoize:f=em,argsMemoizeOptions:h=[],devModeChecks:d={}}={...i,...l},p=er(s),y=er(h),v=function(e){let t=Array.isArray(e[0])?e[0]:e;return!function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"expected all items to be functions, instead received the following types: ";if(!e.every(e=>"function"==typeof e)){let r=e.map(e=>"function"==typeof e?"function ".concat(e.name||"unnamed","()"):typeof e).join(", ");throw TypeError("".concat(t,"[").concat(r,"]"))}}(t,"createSelector expects all input-selectors to be functions, but received the following types: "),t}(r),g=c(function(){return a++,u.apply(null,arguments)},...p);return Object.assign(f(function(){o++;let t=function(e,t){let r=[],{length:n}=e;for(let i=0;i<n;i++)r.push(e[i].apply(null,t));return r}(v,arguments);return e=g.apply(null,t)},...y),{resultFunc:u,memoizedResultFunc:g,dependencies:v,dependencyRecomputations:()=>o,resetDependencyRecomputations:()=>{o=0},lastResult:()=>e,recomputations:()=>a,resetRecomputations:()=>{a=0},memoize:c,argsMemoize:f})};return Object.assign(a,{withTypes:()=>a}),a}(em),ex=Object.assign(function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:eb;!function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"expected an object, instead received ".concat(typeof e);if("object"!=typeof e)throw TypeError(t)}(e,"createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ".concat(typeof e));let r=Object.keys(e);return t(r.map(t=>e[t]),function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.reduce((e,t,n)=>(e[r[n]]=t,e),{})})},{withTypes:()=>ex});function ew(e){return"Minified Redux error #".concat(e,"; visit https://redux.js.org/Errors?code=").concat(e," for the full message or use the non-minified dev environment for full errors. ")}var eO="function"==typeof Symbol&&Symbol.observable||"@@observable",ej=()=>Math.random().toString(36).substring(7).split("").join("."),eP={INIT:"@@redux/INIT".concat(ej()),REPLACE:"@@redux/REPLACE".concat(ej()),PROBE_UNKNOWN_ACTION:()=>"@@redux/PROBE_UNKNOWN_ACTION".concat(ej())};function eA(e){if("object"!=typeof e||null===e)return!1;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t||null===Object.getPrototypeOf(e)}function eE(e){let t,r=Object.keys(e),n={};for(let t=0;t<r.length;t++){let i=r[t];"function"==typeof e[i]&&(n[i]=e[i])}let i=Object.keys(n);try{Object.keys(n).forEach(e=>{let t=n[e];if(void 0===t(void 0,{type:eP.INIT}))throw Error(ew(12));if(void 0===t(void 0,{type:eP.PROBE_UNKNOWN_ACTION()}))throw Error(ew(13))})}catch(e){t=e}return function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length>1?arguments[1]:void 0;if(t)throw t;let a=!1,o={};for(let t=0;t<i.length;t++){let l=i[t],u=n[l],c=e[l],s=u(c,r);if(void 0===s)throw r&&r.type,Error(ew(14));o[l]=s,a=a||s!==c}return(a=a||i.length!==Object.keys(e).length)?o:e}}function eS(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return 0===t.length?e=>e:1===t.length?t[0]:t.reduce((e,t)=>function(){for(var r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];return e(t(...n))})}function eM(e){return eA(e)&&"type"in e&&"string"==typeof e.type}function ek(e){return t=>{let{dispatch:r,getState:n}=t;return t=>i=>"function"==typeof i?i(r,n,e):t(i)}}var eT=ek(),eC="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!=arguments.length)return"object"==typeof arguments[0]?eS:eS.apply(null,arguments)};function e_(e,t){function r(){for(var r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];if(t){let r=t(...n);if(!r)throw Error(tf(0));return{type:e,payload:r.payload,..."meta"in r&&{meta:r.meta},..."error"in r&&{error:r.error}}}return{type:e,payload:n[0]}}return r.toString=()=>"".concat(e),r.type=e,r.match=t=>eM(t)&&t.type===e,r}"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;var eD=class e extends Array{static get[Symbol.species](){return e}concat(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return super.concat.apply(this,t)}prepend(){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];return 1===r.length&&Array.isArray(r[0])?new e(...r[0].concat(this)):new e(...r.concat(this))}constructor(...t){super(...t),Object.setPrototypeOf(this,e.prototype)}};function eN(e){return w(e)?et(e,()=>{}):e}function eI(e,t,r){return e.has(t)?e.get(t):e.set(t,r(t)).get(t)}var eL=e=>t=>{setTimeout(t,e)},eB=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{type:"raf"};return t=>function(){for(var r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];let a=t(...n),o=!0,l=!1,u=!1,c=new Set,s="tick"===e.type?queueMicrotask:"raf"===e.type?"undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:eL(10):"callback"===e.type?e.queueNotification:eL(e.timeout),f=()=>{u=!1,l&&(l=!1,c.forEach(e=>e()))};return Object.assign({},a,{subscribe(e){let t=a.subscribe(()=>o&&e());return c.add(e),()=>{t(),c.delete(e)}},dispatch(e){try{var t;return(l=!(o=!(null==e||null==(t=e.meta)?void 0:t.RTK_autoBatch)))&&!u&&(u=!0,s(f)),a.dispatch(e)}finally{o=!0}}})}};function eR(e){let t,r={},n=[],i={addCase(e,t){let n="string"==typeof e?e:e.type;if(!n)throw Error(tf(28));if(n in r)throw Error(tf(29));return r[n]=t,i},addMatcher:(e,t)=>(n.push({matcher:e,reducer:t}),i),addDefaultCase:e=>(t=e,i)};return e(i),[r,n,t]}var ez=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:21,t="",r=e;for(;r--;)t+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return t},eU=Symbol.for("rtk-slice-createasyncthunk"),eF=(e=>(e.reducer="reducer",e.reducerWithPrepare="reducerWithPrepare",e.asyncThunk="asyncThunk",e))(eF||{}),eK=function(){var e;let{creators:t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=null==t||null==(e=t.asyncThunk)?void 0:e[eU];return function(e){let t,{name:n,reducerPath:i=n}=e;if(!n)throw Error(tf(11));let a=("function"==typeof e.reducers?e.reducers(function(){function e(e,t){return{_reducerDefinitionType:"asyncThunk",payloadCreator:e,...t}}return e.withTypes=()=>e,{reducer:e=>Object.assign({[e.name](){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];return e(...r)}}[e.name],{_reducerDefinitionType:"reducer"}),preparedReducer:(e,t)=>({_reducerDefinitionType:"reducerWithPrepare",prepare:e,reducer:t}),asyncThunk:e}}()):e.reducers)||{},o=Object.keys(a),l={},u={},c={},s=[],f={addCase(e,t){let r="string"==typeof e?e:e.type;if(!r)throw Error(tf(12));if(r in u)throw Error(tf(13));return u[r]=t,f},addMatcher:(e,t)=>(s.push({matcher:e,reducer:t}),f),exposeAction:(e,t)=>(c[e]=t,f),exposeCaseReducer:(e,t)=>(l[e]=t,f)};function h(){let[t={},r=[],n]="function"==typeof e.extraReducers?eR(e.extraReducers):[e.extraReducers],i={...t,...u};return function(e,t){let r,[n,i,a]=eR(t);if("function"==typeof e)r=()=>eN(e());else{let t=eN(e);r=()=>t}function o(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:r(),t=arguments.length>1?arguments[1]:void 0,o=[n[t.type],...i.filter(e=>{let{matcher:r}=e;return r(t)}).map(e=>{let{reducer:t}=e;return t})];return 0===o.filter(e=>!!e).length&&(o=[a]),o.reduce((e,r)=>{if(r)if(x(e)){let n=r(e,t);return void 0===n?e:n}else{if(w(e))return et(e,e=>r(e,t));let n=r(e,t);if(void 0===n){if(null===e)return e;throw Error("A case reducer on a non-draftable value must not return undefined")}return n}return e},e)}return o.getInitialState=r,o}(e.initialState,e=>{for(let t in i)e.addCase(t,i[t]);for(let t of s)e.addMatcher(t.matcher,t.reducer);for(let t of r)e.addMatcher(t.matcher,t.reducer);n&&e.addDefaultCase(n)})}o.forEach(t=>{let i=a[t],o={reducerName:t,type:"".concat(n,"/").concat(t),createNotation:"function"==typeof e.reducers};"asyncThunk"===i._reducerDefinitionType?function(e,t,r,n){let{type:i,reducerName:a}=e;if(!n)throw Error(tf(18));let{payloadCreator:o,fulfilled:l,pending:u,rejected:c,settled:s,options:f}=t,h=n(i,o,f);r.exposeAction(a,h),l&&r.addCase(h.fulfilled,l),u&&r.addCase(h.pending,u),c&&r.addCase(h.rejected,c),s&&r.addMatcher(h.settled,s),r.exposeCaseReducer(a,{fulfilled:l||eq,pending:u||eq,rejected:c||eq,settled:s||eq})}(o,i,f,r):function(e,t,r){let n,i,{type:a,reducerName:o,createNotation:l}=e;if("reducer"in t){if(l&&"reducerWithPrepare"!==t._reducerDefinitionType)throw Error(tf(17));n=t.reducer,i=t.prepare}else n=t;r.addCase(a,n).exposeCaseReducer(o,n).exposeAction(o,i?e_(a,i):e_(a))}(o,i,f)});let d=e=>e,p=new Map,y=new WeakMap;function v(e,r){return t||(t=h()),t(e,r)}function g(){return t||(t=h()),t.getInitialState()}function m(t){let r=arguments.length>1&&void 0!==arguments[1]&&arguments[1];function n(e){let i=e[t];return void 0===i&&r&&(i=eI(y,n,g)),i}function i(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:d,n=eI(p,r,()=>new WeakMap);return eI(n,t,()=>{var n;let i={};for(let[a,o]of Object.entries(null!=(n=e.selectors)?n:{}))i[a]=function(e,t,r,n){function i(i){for(var a=arguments.length,o=Array(a>1?a-1:0),l=1;l<a;l++)o[l-1]=arguments[l];let u=t(i);return void 0===u&&n&&(u=r()),e(u,...o)}return i.unwrapped=e,i}(o,t,()=>eI(y,t,g),r);return i})}return{reducerPath:t,getSelectors:i,get selectors(){return i(n)},selectSlice:n}}let b={name:n,reducer:v,actions:c,caseReducers:l,getInitialState:g,...m(i),injectInto(e){let{reducerPath:t,...r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=null!=t?t:i;return e.inject({reducerPath:n,reducer:v},r),{...b,...m(n,!0)}}};return b}}();function eq(){}var eH="listener",eW="completed",eV="cancelled",eY="task-".concat(eV),eG="task-".concat(eW),eX="".concat(eH,"-").concat(eV),e$="".concat(eH,"-").concat(eW),eZ=class{constructor(e){p(this,"name","TaskAbortError"),p(this,"message",void 0),this.code=e,this.message="".concat("task"," ").concat(eV," (reason: ").concat(e,")")}},eJ=(e,t)=>{if("function"!=typeof e)throw TypeError(tf(32))},eQ=()=>{},e0=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:eQ;return e.catch(t),e},e1=(e,t)=>(e.addEventListener("abort",t,{once:!0}),()=>e.removeEventListener("abort",t)),e2=(e,t)=>{let r=e.signal;r.aborted||("reason"in r||Object.defineProperty(r,"reason",{enumerable:!0,value:t,configurable:!0,writable:!0}),e.abort(t))},e5=e=>{if(e.aborted){let{reason:t}=e;throw new eZ(t)}};function e3(e,t){let r=eQ;return new Promise((n,i)=>{let a=()=>i(new eZ(e.reason));if(e.aborted)return void a();r=e1(e,a),t.finally(()=>r()).then(n,i)}).finally(()=>{r=eQ})}var e6=async(e,t)=>{try{await Promise.resolve();let t=await e();return{status:"ok",value:t}}catch(e){return{status:e instanceof eZ?"cancelled":"rejected",error:e}}finally{null==t||t()}},e8=e=>t=>e0(e3(e,t).then(t=>(e5(e),t))),e4=e=>{let t=e8(e);return e=>t(new Promise(t=>setTimeout(t,e)))},{assign:e7}=Object,e9={},te="listenerMiddleware",tt=e=>{let{type:t,actionCreator:r,matcher:n,predicate:i,effect:a}=e;if(t)i=e_(t).match;else if(r)t=r.type,i=r.match;else if(n)i=n;else if(i);else throw Error(tf(21));return eJ(a,"options.listener"),{predicate:i,type:t,effect:a}},tr=e7(e=>{let{type:t,predicate:r,effect:n}=tt(e);return{id:ez(),effect:n,type:t,predicate:r,pending:new Set,unsubscribe:()=>{throw Error(tf(22))}}},{withTypes:()=>tr}),tn=(e,t)=>{let{type:r,effect:n,predicate:i}=tt(t);return Array.from(e.values()).find(e=>("string"==typeof r?e.type===r:e.predicate===i)&&e.effect===n)},ti=e=>{e.pending.forEach(e=>{e2(e,eX)})},ta=(e,t,r)=>{try{e(t,r)}catch(e){setTimeout(()=>{throw e},0)}},to=e7(e_("".concat(te,"/add")),{withTypes:()=>to}),tl=e_("".concat(te,"/removeAll")),tu=e7(e_("".concat(te,"/remove")),{withTypes:()=>tu}),tc=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];console.error("".concat(te,"/error"),...t)},ts=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new Map,{extra:r,onError:n=tc}=e;eJ(n,"onError");let i=e=>{var r,n;return(n=null!=(r=tn(t,e))?r:tr(e)).unsubscribe=()=>t.delete(n.id),t.set(n.id,n),e=>{n.unsubscribe(),(null==e?void 0:e.cancelActive)&&ti(n)}};e7(i,{withTypes:()=>i});let a=e=>{let r=tn(t,e);return r&&(r.unsubscribe(),e.cancelActive&&ti(r)),!!r};e7(a,{withTypes:()=>a});let o=async(e,a,o,l)=>{let u=new AbortController,c=((e,t)=>{let r=async(r,n)=>{e5(t);let i=()=>{},a=[new Promise((t,n)=>{let a=e({predicate:r,effect:(e,r)=>{r.unsubscribe(),t([e,r.getState(),r.getOriginalState()])}});i=()=>{a(),n()}})];null!=n&&a.push(new Promise(e=>setTimeout(e,n,null)));try{let e=await e3(t,Promise.race(a));return e5(t),e}finally{i()}};return(e,t)=>e0(r(e,t))})(i,u.signal),s=[];try{var f;e.pending.add(u),await Promise.resolve(e.effect(a,e7({},o,{getOriginalState:l,condition:(e,t)=>c(e,t).then(Boolean),take:c,delay:e4(u.signal),pause:e8(u.signal),extra:r,signal:u.signal,fork:(f=u.signal,(e,t)=>{eJ(e,"taskExecutor");let r=new AbortController;e1(f,()=>e2(r,f.reason));let n=e6(async()=>{e5(f),e5(r.signal);let t=await e({pause:e8(r.signal),delay:e4(r.signal),signal:r.signal});return e5(r.signal),t},()=>e2(r,eG));return(null==t?void 0:t.autoJoin)&&s.push(n.catch(eQ)),{result:e8(f)(n),cancel(){e2(r,eY)}}}),unsubscribe:e.unsubscribe,subscribe:()=>{t.set(e.id,e)},cancelActiveListeners:()=>{e.pending.forEach((e,t,r)=>{e!==u&&(e2(e,eX),r.delete(e))})},cancel:()=>{e2(u,eX),e.pending.delete(u)},throwIfCancelled:()=>{e5(u.signal)}})))}catch(e){e instanceof eZ||ta(n,e,{raisedBy:"effect"})}finally{await Promise.all(s),e2(u,e$),e.pending.delete(u)}},l=()=>{t.forEach(ti),t.clear()};return{middleware:e=>r=>u=>{let c;if(!eM(u))return r(u);if(to.match(u))return i(u.payload);if(tl.match(u))return void l();if(tu.match(u))return a(u.payload);let s=e.getState(),f=()=>{if(s===e9)throw Error(tf(23));return s};try{if(c=r(u),t.size>0){let r=e.getState();for(let i of Array.from(t.values())){let t=!1;try{t=i.predicate(u,r,s)}catch(e){t=!1,ta(n,e,{raisedBy:"predicate"})}t&&o(i,u,e,f)}}}finally{s=e9}return c},startListening:i,stopListening:a,clearListeners:l}};function tf(e){return"Minified Redux Toolkit error #".concat(e,"; visit https://redux-toolkit.js.org/Errors?code=").concat(e," for the full message or use the non-minified dev environment for full errors. ")}Symbol.for("rtk-state-proxy-original");var th=e.i(92068),td=e=>0===e?0:e>0?1:-1,tp=e=>"number"==typeof e&&e!=+e,ty=e=>"string"==typeof e&&e.indexOf("%")===e.length-1,tv=e=>("number"==typeof e||e instanceof Number)&&!tp(e),tg=e=>tv(e)||"string"==typeof e,tm=0,tb=e=>{var t=++tm;return"".concat(e||"").concat(t)},tx=function(e,t){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!tv(e)&&"string"!=typeof e)return n;if(ty(e)){if(null==t)return n;var a=e.indexOf("%");r=t*parseFloat(e.slice(0,a))/100}else r=+e;return tp(r)&&(r=n),i&&null!=t&&r>t&&(r=t),r},tw=e=>{if(!Array.isArray(e))return!1;for(var t=e.length,r={},n=0;n<t;n++)if(r[e[n]])return!0;else r[e[n]]=!0;return!1},tO=(e,t)=>tv(e)&&tv(t)?r=>e+r*(t-e):()=>t;function tj(e,t,r){return tv(e)&&tv(t)?e+r*(t-e):t}function tP(e,t,r){if(e&&e.length)return e.find(e=>e&&("function"==typeof t?t(e):(0,th.default)(e,t))===r)}var tA=e=>null==e?e:"".concat(e.charAt(0).toUpperCase()).concat(e.slice(1));function tE(e,t){if(t){var r=Number.parseInt(t,10);if(!tp(r))return null==e?void 0:e[r]}}var tS=eK({name:"options",initialState:{chartName:"",tooltipPayloadSearcher:void 0,eventEmitter:void 0,defaultTooltipEventType:"axis"},reducers:{createEventEmitter:e=>{null==e.eventEmitter&&(e.eventEmitter=Symbol("rechartsEventEmitter"))}}}),tM=tS.reducer,{createEventEmitter:tk}=tS.actions;e.i(13027);var tT={notify(){},get:()=>[]},tC="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,t_="undefined"!=typeof navigator&&"ReactNative"===navigator.product,tD=tC||t_?d.useLayoutEffect:d.useEffect;Object.getOwnPropertyNames,Object.getOwnPropertySymbols,Object.getOwnPropertyDescriptor,Object.getPrototypeOf,Object.prototype;var tN=Symbol.for("react-redux-context"),tI="undefined"!=typeof globalThis?globalThis:{},tL=function(){var e;if(!d.createContext)return{};let t=null!=(e=tI[tN])?e:tI[tN]=new Map,r=t.get(d.createContext);return r||(r=d.createContext(null),t.set(d.createContext,r)),r}(),tB=function(e){let{children:t,context:r,serverState:n,store:i}=e,a=d.useMemo(()=>{let e=function(e,t){let r,n=tT,i=0,a=!1;function o(){c.onStateChange&&c.onStateChange()}function l(){if(i++,!r){let t,i;r=e.subscribe(o),t=null,i=null,n={clear(){t=null,i=null},notify(){let e=t;for(;e;)e.callback(),e=e.next},get(){let e=[],r=t;for(;r;)e.push(r),r=r.next;return e},subscribe(e){let r=!0,n=i={callback:e,next:null,prev:i};return n.prev?n.prev.next=n:t=n,function(){r&&null!==t&&(r=!1,n.next?n.next.prev=n.prev:i=n.prev,n.prev?n.prev.next=n.next:t=n.next)}}}}}function u(){i--,r&&0===i&&(r(),r=void 0,n.clear(),n=tT)}let c={addNestedSub:function(e){l();let t=n.subscribe(e),r=!1;return()=>{r||(r=!0,t(),u())}},notifyNestedSubs:function(){n.notify()},handleChangeWrapper:o,isSubscribed:function(){return a},trySubscribe:function(){a||(a=!0,l())},tryUnsubscribe:function(){a&&(a=!1,u())},getListeners:()=>n};return c}(i);return{store:i,subscription:e,getServerState:n?()=>n:void 0}},[i,n]),o=d.useMemo(()=>i.getState(),[i]);tD(()=>{let{subscription:e}=a;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),o!==i.getState()&&e.notifyNestedSubs(),()=>{e.tryUnsubscribe(),e.onStateChange=void 0}},[a,o]);let l=r||tL;return d.createElement(l.Provider,{value:a},t)};function tR(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:tL;return function(){return d.useContext(e)}}var tz=tR();var tU={active:!1,index:null,dataKey:void 0,coordinate:void 0},tF=eK({name:"tooltip",initialState:{itemInteraction:{click:tU,hover:tU},axisInteraction:{click:tU,hover:tU},keyboardInteraction:tU,syncInteraction:{active:!1,index:null,dataKey:void 0,label:void 0,coordinate:void 0},tooltipItemPayloads:[],settings:{shared:void 0,trigger:"hover",axisId:0,active:!1,defaultIndex:void 0}},reducers:{addTooltipEntrySettings(e,t){e.tooltipItemPayloads.push(t.payload)},removeTooltipEntrySettings(e,t){var r=Q(e).tooltipItemPayloads.indexOf(t.payload);r>-1&&e.tooltipItemPayloads.splice(r,1)},setTooltipSettingsState(e,t){e.settings=t.payload},setActiveMouseOverItemIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.itemInteraction.hover.active=!0,e.itemInteraction.hover.index=t.payload.activeIndex,e.itemInteraction.hover.dataKey=t.payload.activeDataKey,e.itemInteraction.hover.coordinate=t.payload.activeCoordinate},mouseLeaveChart(e){e.itemInteraction.hover.active=!1,e.axisInteraction.hover.active=!1},mouseLeaveItem(e){e.itemInteraction.hover.active=!1},setActiveClickItemIndex(e,t){e.syncInteraction.active=!1,e.itemInteraction.click.active=!0,e.keyboardInteraction.active=!1,e.itemInteraction.click.index=t.payload.activeIndex,e.itemInteraction.click.dataKey=t.payload.activeDataKey,e.itemInteraction.click.coordinate=t.payload.activeCoordinate},setMouseOverAxisIndex(e,t){e.syncInteraction.active=!1,e.axisInteraction.hover.active=!0,e.keyboardInteraction.active=!1,e.axisInteraction.hover.index=t.payload.activeIndex,e.axisInteraction.hover.dataKey=t.payload.activeDataKey,e.axisInteraction.hover.coordinate=t.payload.activeCoordinate},setMouseClickAxisIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.axisInteraction.click.active=!0,e.axisInteraction.click.index=t.payload.activeIndex,e.axisInteraction.click.dataKey=t.payload.activeDataKey,e.axisInteraction.click.coordinate=t.payload.activeCoordinate},setSyncInteraction(e,t){e.syncInteraction=t.payload},setKeyboardInteraction(e,t){e.keyboardInteraction.active=t.payload.active,e.keyboardInteraction.index=t.payload.activeIndex,e.keyboardInteraction.coordinate=t.payload.activeCoordinate,e.keyboardInteraction.dataKey=t.payload.activeDataKey}}}),{addTooltipEntrySettings:tK,removeTooltipEntrySettings:tq,setTooltipSettingsState:tH,setActiveMouseOverItemIndex:tW,mouseLeaveItem:tV,mouseLeaveChart:tY,setActiveClickItemIndex:tG,setMouseOverAxisIndex:tX,setMouseClickAxisIndex:t$,setSyncInteraction:tZ,setKeyboardInteraction:tJ}=tF.actions,tQ=tF.reducer,t0=eK({name:"chartData",initialState:{chartData:void 0,computedData:void 0,dataStartIndex:0,dataEndIndex:0},reducers:{setChartData(e,t){if(e.chartData=t.payload,null==t.payload){e.dataStartIndex=0,e.dataEndIndex=0;return}t.payload.length>0&&e.dataEndIndex!==t.payload.length-1&&(e.dataEndIndex=t.payload.length-1)},setComputedData(e,t){e.computedData=t.payload},setDataStartEndIndexes(e,t){var{startIndex:r,endIndex:n}=t.payload;null!=r&&(e.dataStartIndex=r),null!=n&&(e.dataEndIndex=n)}}}),{setChartData:t1,setDataStartEndIndexes:t2,setComputedData:t5}=t0.actions,t3=t0.reducer,t6=eK({name:"chartLayout",initialState:{layoutType:"horizontal",width:0,height:0,margin:{top:5,right:5,bottom:5,left:5},scale:1},reducers:{setLayout(e,t){e.layoutType=t.payload},setChartSize(e,t){e.width=t.payload.width,e.height=t.payload.height},setMargin(e,t){e.margin.top=t.payload.top,e.margin.right=t.payload.right,e.margin.bottom=t.payload.bottom,e.margin.left=t.payload.left},setScale(e,t){e.scale=t.payload}}}),{setMargin:t8,setLayout:t4,setChartSize:t7,setScale:t9}=t6.actions,re=t6.reducer,rt=e.i(30224),rr=(0,d.createContext)(null),rn=e=>e,ri=()=>{var e=(0,d.useContext)(rr);return e?e.store.dispatch:rn},ra=()=>{},ro=()=>ra,rl=(e,t)=>e===t;function ru(e){var t=(0,d.useContext)(rr);return(0,rt.useSyncExternalStoreWithSelector)(t?t.subscription.addNestedSub:ro,t?t.store.getState:ra,t?t.store.getState:ra,t?e:ra,rl)}var rc=e.i(42342),rs=e=>e.legend.settings;function rf(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}function rh(e){return function(){return e}}function rd(e,t){if((i=e.length)>1)for(var r,n,i,a=1,o=e[t[0]],l=o.length;a<i;++a)for(n=o,o=e[t[a]],r=0;r<l;++r)o[r][1]+=o[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}function rp(e){for(var t=e.length,r=Array(t);--t>=0;)r[t]=t;return r}function ry(e,t){return e[t]}function rv(e){let t=[];return t.key=e,t}function rg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function rm(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?rg(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):rg(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}eb([e=>e.legend.payload,rs],(e,t)=>{var{itemSorter:r}=t,n=e.flat(1);return r?(0,rc.default)(n,r):n}),Array.prototype.slice;var rb=Math.PI/180,rx=(e,t,r,n)=>({x:e+Math.cos(-rb*n)*r,y:t+Math.sin(-rb*n)*r}),rw=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0,width:0,height:0,brushBottom:0};return Math.min(Math.abs(e-(r.left||0)-(r.right||0)),Math.abs(t-(r.top||0)-(r.bottom||0)))/2};function rO(e,t,r){return Array.isArray(e)&&e&&t+r!==0?e.slice(t,r+1):e}function rj(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function rP(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?rj(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):rj(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function rA(e,t,r){return null==e||null==t?r:tg(t)?(0,th.default)(e,t,r):"function"==typeof t?t(e):r}var rE=(e,t)=>"horizontal"===e&&"xAxis"===t||"vertical"===e&&"yAxis"===t||"centric"===e&&"angleAxis"===t||"radial"===e&&"radiusAxis"===t,rS=(e,t,r,n)=>{if(n)return e.map(e=>e.coordinate);var i,a,o=e.map(e=>(e.coordinate===t&&(i=!0),e.coordinate===r&&(a=!0),e.coordinate));return i||o.push(t),a||o.push(r),o},rM=(e,t,r)=>{if(!e)return null;var{duplicateDomain:n,type:i,range:a,scale:o,realScaleType:l,isCategorical:u,categoricalDomain:c,tickCount:s,ticks:f,niceTicks:h,axisType:d}=e;if(!o)return null;var p="scaleBand"===l&&o.bandwidth?o.bandwidth()/2:2,y=(t||r)&&"category"===i&&o.bandwidth?o.bandwidth()/p:0;return(y="angleAxis"===d&&a&&a.length>=2?2*td(a[0]-a[1])*y:y,t&&(f||h))?(f||h||[]).map((e,t)=>({coordinate:o(n?n.indexOf(e):e)+y,value:e,offset:y,index:t})).filter(e=>!tp(e.coordinate)):u&&c?c.map((e,t)=>({coordinate:o(e)+y,value:e,index:t,offset:y})):o.ticks&&!r&&null!=s?o.ticks(s).map((e,t)=>({coordinate:o(e)+y,value:e,offset:y,index:t})):o.domain().map((e,t)=>({coordinate:o(e)+y,value:n?n[e]:e,index:t,offset:y}))},rk={sign:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,a=0,o=0;o<t;++o){var l=tp(e[o][r][1])?e[o][r][0]:e[o][r][1];l>=0?(e[o][r][0]=i,e[o][r][1]=i+l,i=e[o][r][1]):(e[o][r][0]=a,e[o][r][1]=a+l,a=e[o][r][1])}},expand:function(e,t){if((n=e.length)>0){for(var r,n,i,a=0,o=e[0].length;a<o;++a){for(i=r=0;r<n;++r)i+=e[r][a][1]||0;if(i)for(r=0;r<n;++r)e[r][a][1]/=i}rd(e,t)}},none:rd,silhouette:function(e,t){if((r=e.length)>0){for(var r,n=0,i=e[t[0]],a=i.length;n<a;++n){for(var o=0,l=0;o<r;++o)l+=e[o][n][1]||0;i[n][1]+=i[n][0]=-l/2}rd(e,t)}},wiggle:function(e,t){if((i=e.length)>0&&(n=(r=e[t[0]]).length)>0){for(var r,n,i,a=0,o=1;o<n;++o){for(var l=0,u=0,c=0;l<i;++l){for(var s=e[t[l]],f=s[o][1]||0,h=(f-(s[o-1][1]||0))/2,d=0;d<l;++d){var p=e[t[d]];h+=(p[o][1]||0)-(p[o-1][1]||0)}u+=f,c+=h*f}r[o-1][1]+=r[o-1][0]=a,u&&(a-=c/u)}r[o-1][1]+=r[o-1][0]=a,rd(e,t)}},positive:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,a=0;a<t;++a){var o=tp(e[a][r][1])?e[a][r][0]:e[a][r][1];o>=0?(e[a][r][0]=i,e[a][r][1]=i+o,i=e[a][r][1]):(e[a][r][0]=0,e[a][r][1]=0)}}};function rT(e){return null==e?void 0:String(e)}function rC(e){var{axis:t,ticks:r,bandSize:n,entry:i,index:a,dataKey:o}=e;if("category"===t.type){if(!t.allowDuplicatedCategory&&t.dataKey&&null!=i[t.dataKey]){var l=tP(r,"value",i[t.dataKey]);if(l)return l.coordinate+n/2}return r[a]?r[a].coordinate+n/2:null}var u=rA(i,null==o?t.dataKey:o);return null==u?null:t.scale(u)}var r_=e=>{var{axis:t,ticks:r,offset:n,bandSize:i,entry:a,index:o}=e;if("category"===t.type)return r[o]?r[o].coordinate+n:null;var l=rA(a,t.dataKey,t.scale.domain()[o]);return null==l?null:t.scale(l)-i/2+n},rD=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,rN=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,rI=(e,t,r)=>{if(e&&e.scale&&e.scale.bandwidth){var n=e.scale.bandwidth();if(!r||n>0)return n}if(e&&t&&t.length>=2){for(var i=(0,rc.default)(t,e=>e.coordinate),a=1/0,o=1,l=i.length;o<l;o++){var u=i[o],c=i[o-1];a=Math.min((u.coordinate||0)-(c.coordinate||0),a)}return a===1/0?0:a}return r?void 0:0};function rL(e){var{tooltipEntrySettings:t,dataKey:r,payload:n,value:i,name:a}=e;return rP(rP({},t),{},{dataKey:r,payload:n,value:i,name:a})}function rB(e,t){return e?String(e):"string"==typeof t?t:void 0}var rR=e=>e.layout.width,rz=e=>e.layout.height,rU=e=>e.layout.scale,rF=e=>e.layout.margin,rK=eb(e=>e.cartesianAxis.xAxis,e=>Object.values(e)),rq=eb(e=>e.cartesianAxis.yAxis,e=>Object.values(e)),rH="data-recharts-item-index",rW="data-recharts-item-data-key";function rV(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function rY(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?rV(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):rV(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var rG=eb([rR,rz,rF,e=>e.brush.height,rK,rq,rs,e=>e.legend.size],(e,t,r,n,i,a,o,l)=>{var u=a.reduce((e,t)=>{var{orientation:r}=t;if(!t.mirror&&!t.hide){var n="number"==typeof t.width?t.width:60;return rY(rY({},e),{},{[r]:e[r]+n})}return e},{left:r.left||0,right:r.right||0}),c=i.reduce((e,t)=>{var{orientation:r}=t;return t.mirror||t.hide?e:rY(rY({},e),{},{[r]:(0,th.default)(e,"".concat(r))+t.height})},{top:r.top||0,bottom:r.bottom||0}),s=rY(rY({},c),u),f=s.bottom;s.bottom+=n;var h=e-(s=((e,t,r)=>{if(t&&r){var{width:n,height:i}=r,{align:a,verticalAlign:o,layout:l}=t;if(("vertical"===l||"horizontal"===l&&"middle"===o)&&"center"!==a&&tv(e[a]))return rP(rP({},e),{},{[a]:e[a]+(n||0)});if(("horizontal"===l||"vertical"===l&&"center"===a)&&"middle"!==o&&tv(e[o]))return rP(rP({},e),{},{[o]:e[o]+(i||0)})}return e})(s,o,l)).left-s.right,d=t-s.top-s.bottom;return rY(rY({brushBottom:f},s),{},{width:Math.max(h,0),height:Math.max(d,0)})}),rX=eb(rG,e=>({x:e.left,y:e.top,width:e.width,height:e.height})),r$=eb(rR,rz,(e,t)=>({x:0,y:0,width:e,height:t})),rZ=(0,d.createContext)(null),rJ=()=>null!=(0,d.useContext)(rZ),rQ=e=>e.brush,r0=eb([rQ,rG,rF],(e,t,r)=>({height:e.height,x:tv(e.x)?e.x:t.left,y:tv(e.y)?e.y:t.top+t.height+t.brushBottom-((null==r?void 0:r.bottom)||0),width:tv(e.width)?e.width:t.width})),r1=()=>{var e,t=rJ(),r=ru(rX),n=ru(r0),i=null==(e=ru(rQ))?void 0:e.padding;return t&&n&&i?{width:n.width-i.left-i.right,height:n.height-i.top-i.bottom,x:i.left,y:i.top}:r},r2={top:0,bottom:0,left:0,right:0,width:0,height:0,brushBottom:0},r5=()=>{var e;return null!=(e=ru(rG))?e:r2},r3=()=>ru(rR),r6=()=>ru(rz),r8=e=>e.layout.layoutType,r4=()=>ru(r8),r7=e.i(66814);function r9(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e)}return this}function ne(e,t){switch(arguments.length){case 0:break;case 1:"function"==typeof e?this.interpolator(e):this.range(e);break;default:this.domain(e),"function"==typeof t?this.interpolator(t):this.range(t)}return this}e.s([],25212),e.s(["scaleBand",()=>no,"scaleDiverging",()=>o$,"scaleDivergingLog",()=>oZ,"scaleDivergingPow",()=>oQ,"scaleDivergingSqrt",()=>o0,"scaleDivergingSymlog",()=>oJ,"scaleIdentity",()=>iP,"scaleImplicit",()=>ni,"scaleLinear",()=>ij,"scaleLog",()=>iD,"scaleOrdinal",()=>na,"scalePoint",()=>nl,"scalePow",()=>iK,"scaleQuantile",()=>i$,"scaleQuantize",()=>iZ,"scaleRadial",()=>iW,"scaleSequential",()=>oq,"scaleSequentialLog",()=>oH,"scaleSequentialPow",()=>oV,"scaleSequentialQuantile",()=>oG,"scaleSequentialSqrt",()=>oY,"scaleSequentialSymlog",()=>oW,"scaleSqrt",()=>iq,"scaleSymlog",()=>iB,"scaleThreshold",()=>iJ,"scaleTime",()=>oz,"scaleUtc",()=>oU,"tickFormat",()=>iw],79357),e.i(25212),e.s(["scaleBand",()=>no,"scaleDiverging",()=>o$,"scaleDivergingLog",()=>oZ,"scaleDivergingPow",()=>oQ,"scaleDivergingSqrt",()=>o0,"scaleDivergingSymlog",()=>oJ,"scaleIdentity",()=>iP,"scaleImplicit",()=>ni,"scaleLinear",()=>ij,"scaleLog",()=>iD,"scaleOrdinal",()=>na,"scalePoint",()=>nl,"scalePow",()=>iK,"scaleQuantile",()=>i$,"scaleQuantize",()=>iZ,"scaleRadial",()=>iW,"scaleSequential",()=>oq,"scaleSequentialLog",()=>oH,"scaleSequentialPow",()=>oV,"scaleSequentialQuantile",()=>oG,"scaleSequentialSqrt",()=>oY,"scaleSequentialSymlog",()=>oW,"scaleSqrt",()=>iq,"scaleSymlog",()=>iB,"scaleThreshold",()=>iJ,"scaleTime",()=>oz,"scaleUtc",()=>oU,"tickFormat",()=>iw],29061),e.s([],67155),e.i(67155);class nt extends Map{get(e){return super.get(nr(this,e))}has(e){return super.has(nr(this,e))}set(e,t){return super.set(function(e,t){let{_intern:r,_key:n}=e,i=n(t);return r.has(i)?r.get(i):(r.set(i,t),t)}(this,e),t)}delete(e){return super.delete(function(e,t){let{_intern:r,_key:n}=e,i=n(t);return r.has(i)&&(t=r.get(i),r.delete(i)),t}(this,e))}constructor(e,t=nn){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:t}}),null!=e)for(let[t,r]of e)this.set(t,r)}}function nr(e,t){let{_intern:r,_key:n}=e,i=n(t);return r.has(i)?r.get(i):t}function nn(e){return null!==e&&"object"==typeof e?e.valueOf():e}let ni=Symbol("implicit");function na(){var e=new nt,t=[],r=[],n=ni;function i(i){let a=e.get(i);if(void 0===a){if(n!==ni)return n;e.set(i,a=t.push(i)-1)}return r[a%r.length]}return i.domain=function(r){if(!arguments.length)return t.slice();for(let n of(t=[],e=new nt,r))e.has(n)||e.set(n,t.push(n)-1);return i},i.range=function(e){return arguments.length?(r=Array.from(e),i):r.slice()},i.unknown=function(e){return arguments.length?(n=e,i):n},i.copy=function(){return na(t,r).unknown(n)},r9.apply(i,arguments),i}function no(){var e,t,r=na().unknown(void 0),n=r.domain,i=r.range,a=0,o=1,l=!1,u=0,c=0,s=.5;function f(){var r=n().length,f=o<a,h=f?o:a,d=f?a:o;e=(d-h)/Math.max(1,r-u+2*c),l&&(e=Math.floor(e)),h+=(d-h-e*(r-u))*s,t=e*(1-u),l&&(h=Math.round(h),t=Math.round(t));var p=(function(e,t,r){e*=1,t*=1,r=(i=arguments.length)<2?(t=e,e=0,1):i<3?1:+r;for(var n=-1,i=0|Math.max(0,Math.ceil((t-e)/r)),a=Array(i);++n<i;)a[n]=e+n*r;return a})(r).map(function(t){return h+e*t});return i(f?p.reverse():p)}return delete r.unknown,r.domain=function(e){return arguments.length?(n(e),f()):n()},r.range=function(e){return arguments.length?([a,o]=e,a*=1,o*=1,f()):[a,o]},r.rangeRound=function(e){return[a,o]=e,a*=1,o*=1,l=!0,f()},r.bandwidth=function(){return t},r.step=function(){return e},r.round=function(e){return arguments.length?(l=!!e,f()):l},r.padding=function(e){return arguments.length?(u=Math.min(1,c=+e),f()):u},r.paddingInner=function(e){return arguments.length?(u=Math.min(1,e),f()):u},r.paddingOuter=function(e){return arguments.length?(c=+e,f()):c},r.align=function(e){return arguments.length?(s=Math.max(0,Math.min(1,e)),f()):s},r.copy=function(){return no(n(),[a,o]).round(l).paddingInner(u).paddingOuter(c).align(s)},r9.apply(f(),arguments)}function nl(){return function e(t){var r=t.copy;return t.padding=t.paddingOuter,delete t.paddingInner,delete t.paddingOuter,t.copy=function(){return e(r())},t}(no.apply(null,arguments).paddingInner(1))}let nu=Math.sqrt(50),nc=Math.sqrt(10),ns=Math.sqrt(2);function nf(e,t,r){let n,i,a,o=(t-e)/Math.max(0,r),l=Math.floor(Math.log10(o)),u=o/Math.pow(10,l),c=u>=nu?10:u>=nc?5:u>=ns?2:1;return(l<0?(n=Math.round(e*(a=Math.pow(10,-l)/c)),i=Math.round(t*a),n/a<e&&++n,i/a>t&&--i,a=-a):(n=Math.round(e/(a=Math.pow(10,l)*c)),i=Math.round(t/a),n*a<e&&++n,i*a>t&&--i),i<n&&.5<=r&&r<2)?nf(e,t,2*r):[n,i,a]}function nh(e,t,r){if(t*=1,e*=1,!((r*=1)>0))return[];if(e===t)return[e];let n=t<e,[i,a,o]=n?nf(t,e,r):nf(e,t,r);if(!(a>=i))return[];let l=a-i+1,u=Array(l);if(n)if(o<0)for(let e=0;e<l;++e)u[e]=-((a-e)/o);else for(let e=0;e<l;++e)u[e]=(a-e)*o;else if(o<0)for(let e=0;e<l;++e)u[e]=-((i+e)/o);else for(let e=0;e<l;++e)u[e]=(i+e)*o;return u}function nd(e,t,r){return nf(e*=1,t*=1,r*=1)[2]}function np(e,t,r){t*=1,e*=1,r*=1;let n=t<e,i=n?nd(t,e,r):nd(e,t,r);return(n?-1:1)*(i<0?-(1/i):i)}function ny(e,t){return null==e||null==t?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function nv(e,t){return null==e||null==t?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function ng(e){let t,r,n;function i(e,n){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:e.length;if(i<a){if(0!==t(n,n))return a;do{let t=i+a>>>1;0>r(e[t],n)?i=t+1:a=t}while(i<a)}return i}return 2!==e.length?(t=ny,r=(t,r)=>ny(e(t),r),n=(t,r)=>e(t)-r):(t=e===ny||e===nv?e:nm,r=e,n=e),{left:i,center:function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:e.length,o=i(e,t,r,a-1);return o>r&&n(e[o-1],t)>-n(e[o],t)?o-1:o},right:function(e,n){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:e.length;if(i<a){if(0!==t(n,n))return a;do{let t=i+a>>>1;0>=r(e[t],n)?i=t+1:a=t}while(i<a)}return i}}}function nm(){return 0}function nb(e){return null===e?NaN:+e}let nx=ng(ny),nw=nx.right;function nO(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function nj(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function nP(){}nx.left,ng(nb).center;var nA="\\s*([+-]?\\d+)\\s*",nE="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",nS="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",nM=/^#([0-9a-f]{3,8})$/,nk=new RegExp("^rgb\\(".concat(nA,",").concat(nA,",").concat(nA,"\\)$")),nT=new RegExp("^rgb\\(".concat(nS,",").concat(nS,",").concat(nS,"\\)$")),nC=new RegExp("^rgba\\(".concat(nA,",").concat(nA,",").concat(nA,",").concat(nE,"\\)$")),n_=new RegExp("^rgba\\(".concat(nS,",").concat(nS,",").concat(nS,",").concat(nE,"\\)$")),nD=new RegExp("^hsl\\(".concat(nE,",").concat(nS,",").concat(nS,"\\)$")),nN=new RegExp("^hsla\\(".concat(nE,",").concat(nS,",").concat(nS,",").concat(nE,"\\)$")),nI={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function nL(){return this.rgb().formatHex()}function nB(){return this.rgb().formatRgb()}function nR(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=nM.exec(e))?(r=t[1].length,t=parseInt(t[1],16),6===r?nz(t):3===r?new nK(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===r?nU(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===r?nU(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=nk.exec(e))?new nK(t[1],t[2],t[3],1):(t=nT.exec(e))?new nK(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=nC.exec(e))?nU(t[1],t[2],t[3],t[4]):(t=n_.exec(e))?nU(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=nD.exec(e))?nG(t[1],t[2]/100,t[3]/100,1):(t=nN.exec(e))?nG(t[1],t[2]/100,t[3]/100,t[4]):nI.hasOwnProperty(e)?nz(nI[e]):"transparent"===e?new nK(NaN,NaN,NaN,0):null}function nz(e){return new nK(e>>16&255,e>>8&255,255&e,1)}function nU(e,t,r,n){return n<=0&&(e=t=r=NaN),new nK(e,t,r,n)}function nF(e,t,r,n){var i;return 1==arguments.length?((i=e)instanceof nP||(i=nR(i)),i)?new nK((i=i.rgb()).r,i.g,i.b,i.opacity):new nK:new nK(e,t,r,null==n?1:n)}function nK(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}function nq(){return"#".concat(nY(this.r)).concat(nY(this.g)).concat(nY(this.b))}function nH(){let e=nW(this.opacity);return"".concat(1===e?"rgb(":"rgba(").concat(nV(this.r),", ").concat(nV(this.g),", ").concat(nV(this.b)).concat(1===e?")":", ".concat(e,")"))}function nW(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function nV(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function nY(e){return((e=nV(e))<16?"0":"")+e.toString(16)}function nG(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new n$(e,t,r,n)}function nX(e){if(e instanceof n$)return new n$(e.h,e.s,e.l,e.opacity);if(e instanceof nP||(e=nR(e)),!e)return new n$;if(e instanceof n$)return e;var t=(e=e.rgb()).r/255,r=e.g/255,n=e.b/255,i=Math.min(t,r,n),a=Math.max(t,r,n),o=NaN,l=a-i,u=(a+i)/2;return l?(o=t===a?(r-n)/l+(r<n)*6:r===a?(n-t)/l+2:(t-r)/l+4,l/=u<.5?a+i:2-a-i,o*=60):l=u>0&&u<1?0:o,new n$(o,l,u,e.opacity)}function n$(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}function nZ(e){return(e=(e||0)%360)<0?e+360:e}function nJ(e){return Math.max(0,Math.min(1,e||0))}function nQ(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}function n0(e,t,r,n,i){var a=e*e,o=a*e;return((1-3*e+3*a-o)*t+(4-6*a+3*o)*r+(1+3*e+3*a-3*o)*n+o*i)/6}nO(nP,nR,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:nL,formatHex:nL,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return nX(this).formatHsl()},formatRgb:nB,toString:nB}),nO(nK,nF,nj(nP,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new nK(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new nK(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new nK(nV(this.r),nV(this.g),nV(this.b),nW(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:nq,formatHex:nq,formatHex8:function(){return"#".concat(nY(this.r)).concat(nY(this.g)).concat(nY(this.b)).concat(nY((isNaN(this.opacity)?1:this.opacity)*255))},formatRgb:nH,toString:nH})),nO(n$,function(e,t,r,n){return 1==arguments.length?nX(e):new n$(e,t,r,null==n?1:n)},nj(nP,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new n$(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new n$(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,i=2*r-n;return new nK(nQ(e>=240?e-240:e+120,i,n),nQ(e,i,n),nQ(e<120?e+240:e-120,i,n),this.opacity)},clamp(){return new n$(nZ(this.h),nJ(this.s),nJ(this.l),nW(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let e=nW(this.opacity);return"".concat(1===e?"hsl(":"hsla(").concat(nZ(this.h),", ").concat(100*nJ(this.s),"%, ").concat(100*nJ(this.l),"%").concat(1===e?")":", ".concat(e,")"))}}));let n1=e=>()=>e;function n2(e,t){var r=t-e;return r?function(t){return e+t*r}:n1(isNaN(e)?t:e)}let n5=function e(t){var r,n=1==(r=+t)?n2:function(e,t){var n,i,a;return t-e?(n=e,i=t,n=Math.pow(n,a=r),i=Math.pow(i,a)-n,a=1/a,function(e){return Math.pow(n+e*i,a)}):n1(isNaN(e)?t:e)};function i(e,t){var r=n((e=nF(e)).r,(t=nF(t)).r),i=n(e.g,t.g),a=n(e.b,t.b),o=n2(e.opacity,t.opacity);return function(t){return e.r=r(t),e.g=i(t),e.b=a(t),e.opacity=o(t),e+""}}return i.gamma=e,i}(1);function n3(e){return function(t){var r,n,i=t.length,a=Array(i),o=Array(i),l=Array(i);for(r=0;r<i;++r)n=nF(t[r]),a[r]=n.r||0,o[r]=n.g||0,l[r]=n.b||0;return a=e(a),o=e(o),l=e(l),n.opacity=1,function(e){return n.r=a(e),n.g=o(e),n.b=l(e),n+""}}}function n6(e,t){return e*=1,t*=1,function(r){return e*(1-r)+t*r}}n3(function(e){var t=e.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,t-1):Math.floor(r*t),i=e[n],a=e[n+1],o=n>0?e[n-1]:2*i-a,l=n<t-1?e[n+2]:2*a-i;return n0((r-n/t)*t,o,i,a,l)}}),n3(function(e){var t=e.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*t),i=e[(n+t-1)%t],a=e[n%t],o=e[(n+1)%t],l=e[(n+2)%t];return n0((r-n/t)*t,i,a,o,l)}});var n8=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,n4=RegExp(n8.source,"g");function n7(e,t){var r,n,i=typeof t;return null==t||"boolean"===i?n1(t):("number"===i?n6:"string"===i?(n=nR(t))?(t=n,n5):function(e,t){var r,n,i,a,o,l=n8.lastIndex=n4.lastIndex=0,u=-1,c=[],s=[];for(e+="",t+="";(i=n8.exec(e))&&(a=n4.exec(t));)(o=a.index)>l&&(o=t.slice(l,o),c[u]?c[u]+=o:c[++u]=o),(i=i[0])===(a=a[0])?c[u]?c[u]+=a:c[++u]=a:(c[++u]=null,s.push({i:u,x:n6(i,a)})),l=n4.lastIndex;return l<t.length&&(o=t.slice(l),c[u]?c[u]+=o:c[++u]=o),c.length<2?s[0]?(r=s[0].x,function(e){return r(e)+""}):(n=t,function(){return n}):(t=s.length,function(e){for(var r,n=0;n<t;++n)c[(r=s[n]).i]=r.x(e);return c.join("")})}:t instanceof nR?n5:t instanceof Date?function(e,t){var r=new Date;return e*=1,t*=1,function(n){return r.setTime(e*(1-n)+t*n),r}}:!ArrayBuffer.isView(r=t)||r instanceof DataView?Array.isArray(t)?function(e,t){var r,n=t?t.length:0,i=e?Math.min(n,e.length):0,a=Array(i),o=Array(n);for(r=0;r<i;++r)a[r]=n7(e[r],t[r]);for(;r<n;++r)o[r]=t[r];return function(e){for(r=0;r<i;++r)o[r]=a[r](e);return o}}:"function"!=typeof t.valueOf&&"function"!=typeof t.toString||isNaN(t)?function(e,t){var r,n={},i={};for(r in(null===e||"object"!=typeof e)&&(e={}),(null===t||"object"!=typeof t)&&(t={}),t)r in e?n[r]=n7(e[r],t[r]):i[r]=t[r];return function(e){for(r in n)i[r]=n[r](e);return i}}:n6:function(e,t){t||(t=[]);var r,n=e?Math.min(t.length,e.length):0,i=t.slice();return function(a){for(r=0;r<n;++r)i[r]=e[r]*(1-a)+t[r]*a;return i}})(e,t)}function n9(e,t){return e*=1,t*=1,function(r){return Math.round(e*(1-r)+t*r)}}function ie(e){return+e}var it=[0,1];function ir(e){return e}function ii(e,t){var r;return(t-=e*=1)?function(r){return(r-e)/t}:(r=isNaN(t)?NaN:.5,function(){return r})}function ia(e,t,r){var n=e[0],i=e[1],a=t[0],o=t[1];return i<n?(n=ii(i,n),a=r(o,a)):(n=ii(n,i),a=r(a,o)),function(e){return a(n(e))}}function io(e,t,r){var n=Math.min(e.length,t.length)-1,i=Array(n),a=Array(n),o=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++o<n;)i[o]=ii(e[o],e[o+1]),a[o]=r(t[o],t[o+1]);return function(t){var r=nw(e,t,1,n)-1;return a[r](i[r](t))}}function il(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function iu(){var e,t,r,n,i,a,o=it,l=it,u=n7,c=ir;function s(){var e,t,r,u=Math.min(o.length,l.length);return c!==ir&&(e=o[0],t=o[u-1],e>t&&(r=e,e=t,t=r),c=function(r){return Math.max(e,Math.min(t,r))}),n=u>2?io:ia,i=a=null,f}function f(t){return null==t||isNaN(t*=1)?r:(i||(i=n(o.map(e),l,u)))(e(c(t)))}return f.invert=function(r){return c(t((a||(a=n(l,o.map(e),n6)))(r)))},f.domain=function(e){return arguments.length?(o=Array.from(e,ie),s()):o.slice()},f.range=function(e){return arguments.length?(l=Array.from(e),s()):l.slice()},f.rangeRound=function(e){return l=Array.from(e),u=n9,s()},f.clamp=function(e){return arguments.length?(c=!!e||ir,s()):c!==ir},f.interpolate=function(e){return arguments.length?(u=e,s()):u},f.unknown=function(e){return arguments.length?(r=e,f):r},function(r,n){return e=r,t=n,s()}}function ic(){return iu()(ir,ir)}function is(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function ih(e){return(e=is(Math.abs(e)))?e[1]:NaN}var id=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function ip(e){var t;if(!(t=id.exec(e)))throw Error("invalid format: "+e);return new iy({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}function iy(e){this.fill=void 0===e.fill?" ":e.fill+"",this.align=void 0===e.align?">":e.align+"",this.sign=void 0===e.sign?"-":e.sign+"",this.symbol=void 0===e.symbol?"":e.symbol+"",this.zero=!!e.zero,this.width=void 0===e.width?void 0:+e.width,this.comma=!!e.comma,this.precision=void 0===e.precision?void 0:+e.precision,this.trim=!!e.trim,this.type=void 0===e.type?"":e.type+""}function iv(e,t){var r=is(e,t);if(!r)return e+"";var n=r[0],i=r[1];return i<0?"0."+Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+Array(i-n.length+2).join("0")}ip.prototype=iy.prototype,iy.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let ig={"%":(e,t)=>(100*e).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:function(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)},e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>iv(100*e,t),r:iv,s:function(e,t){var n=is(e,t);if(!n)return e+"";var i=n[0],a=n[1],o=a-(r=3*Math.max(-8,Math.min(8,Math.floor(a/3))))+1,l=i.length;return o===l?i:o>l?i+Array(o-l+1).join("0"):o>0?i.slice(0,o)+"."+i.slice(o):"0."+Array(1-o).join("0")+is(e,Math.max(0,t+o-1))[0]},X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function im(e){return e}var ib=Array.prototype.map,ix=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function iw(e,t,r,n){var o,l,u=np(e,t,r);switch((n=ip(null==n?",f":n)).type){case"s":var c=Math.max(Math.abs(e),Math.abs(t));return null!=n.precision||isNaN(l=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(ih(c)/3)))-ih(Math.abs(u))))||(n.precision=l),a(n,c);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(l=Math.max(0,ih(Math.abs(Math.max(Math.abs(e),Math.abs(t)))-(o=Math.abs(o=u)))-ih(o))+1)||(n.precision=l-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(l=Math.max(0,-ih(Math.abs(u))))||(n.precision=l-("%"===n.type)*2)}return i(n)}function iO(e){var t=e.domain;return e.ticks=function(e){var r=t();return nh(r[0],r[r.length-1],null==e?10:e)},e.tickFormat=function(e,r){var n=t();return iw(n[0],n[n.length-1],null==e?10:e,r)},e.nice=function(r){null==r&&(r=10);var n,i,a=t(),o=0,l=a.length-1,u=a[o],c=a[l],s=10;for(c<u&&(i=u,u=c,c=i,i=o,o=l,l=i);s-- >0;){if((i=nd(u,c,r))===n)return a[o]=u,a[l]=c,t(a);if(i>0)u=Math.floor(u/i)*i,c=Math.ceil(c/i)*i;else if(i<0)u=Math.ceil(u*i)/i,c=Math.floor(c*i)/i;else break;n=i}return e},e}function ij(){var e=ic();return e.copy=function(){return il(e,ij())},r9.apply(e,arguments),iO(e)}function iP(e){var t;function r(e){return null==e||isNaN(e*=1)?t:e}return r.invert=r,r.domain=r.range=function(t){return arguments.length?(e=Array.from(t,ie),r):e.slice()},r.unknown=function(e){return arguments.length?(t=e,r):t},r.copy=function(){return iP(e).unknown(t)},e=arguments.length?Array.from(e,ie):[0,1],iO(r)}function iA(e,t){e=e.slice();var r,n=0,i=e.length-1,a=e[n],o=e[i];return o<a&&(r=n,n=i,i=r,r=a,a=o,o=r),e[n]=t.floor(a),e[i]=t.ceil(o),e}function iE(e){return Math.log(e)}function iS(e){return Math.exp(e)}function iM(e){return-Math.log(-e)}function ik(e){return-Math.exp(-e)}function iT(e){return isFinite(e)?+("1e"+e):e<0?0:e}function iC(e){return(t,r)=>-e(-t,r)}function i_(e){let t,r,n=e(iE,iS),a=n.domain,o=10;function l(){var i,l;return t=(i=o)===Math.E?Math.log:10===i&&Math.log10||2===i&&Math.log2||(i=Math.log(i),e=>Math.log(e)/i),r=10===(l=o)?iT:l===Math.E?Math.exp:e=>Math.pow(l,e),a()[0]<0?(t=iC(t),r=iC(r),e(iM,ik)):e(iE,iS),n}return n.base=function(e){return arguments.length?(o=+e,l()):o},n.domain=function(e){return arguments.length?(a(e),l()):a()},n.ticks=e=>{let n,i,l=a(),u=l[0],c=l[l.length-1],s=c<u;s&&([u,c]=[c,u]);let f=t(u),h=t(c),d=null==e?10:+e,p=[];if(!(o%1)&&h-f<d){if(f=Math.floor(f),h=Math.ceil(h),u>0){for(;f<=h;++f)for(n=1;n<o;++n)if(!((i=f<0?n/r(-f):n*r(f))<u)){if(i>c)break;p.push(i)}}else for(;f<=h;++f)for(n=o-1;n>=1;--n)if(!((i=f>0?n/r(-f):n*r(f))<u)){if(i>c)break;p.push(i)}2*p.length<d&&(p=nh(u,c,d))}else p=nh(f,h,Math.min(h-f,d)).map(r);return s?p.reverse():p},n.tickFormat=(e,a)=>{if(null==e&&(e=10),null==a&&(a=10===o?"s":","),"function"!=typeof a&&(o%1||null!=(a=ip(a)).precision||(a.trim=!0),a=i(a)),e===1/0)return a;let l=Math.max(1,o*e/n.ticks().length);return e=>{let n=e/r(Math.round(t(e)));return n*o<o-.5&&(n*=o),n<=l?a(e):""}},n.nice=()=>a(iA(a(),{floor:e=>r(Math.floor(t(e))),ceil:e=>r(Math.ceil(t(e)))})),n}function iD(){let e=i_(iu()).domain([1,10]);return e.copy=()=>il(e,iD()).base(e.base()),r9.apply(e,arguments),e}function iN(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function iI(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function iL(e){var t=1,r=e(iN(1),iI(t));return r.constant=function(r){return arguments.length?e(iN(t=+r),iI(t)):t},iO(r)}function iB(){var e=iL(iu());return e.copy=function(){return il(e,iB()).constant(e.constant())},r9.apply(e,arguments)}function iR(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function iz(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function iU(e){return e<0?-e*e:e*e}function iF(e){var t=e(ir,ir),r=1;return t.exponent=function(t){return arguments.length?1==(r=+t)?e(ir,ir):.5===r?e(iz,iU):e(iR(r),iR(1/r)):r},iO(t)}function iK(){var e=iF(iu());return e.copy=function(){return il(e,iK()).exponent(e.exponent())},r9.apply(e,arguments),e}function iq(){return iK.apply(null,arguments).exponent(.5)}function iH(e){return Math.sign(e)*e*e}function iW(){var e,t=ic(),r=[0,1],n=!1;function i(r){var i,a=Math.sign(i=t(r))*Math.sqrt(Math.abs(i));return isNaN(a)?e:n?Math.round(a):a}return i.invert=function(e){return t.invert(iH(e))},i.domain=function(e){return arguments.length?(t.domain(e),i):t.domain()},i.range=function(e){return arguments.length?(t.range((r=Array.from(e,ie)).map(iH)),i):r.slice()},i.rangeRound=function(e){return i.range(e).round(!0)},i.round=function(e){return arguments.length?(n=!!e,i):n},i.clamp=function(e){return arguments.length?(t.clamp(e),i):t.clamp()},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return iW(t.domain(),r).round(n).clamp(t.clamp()).unknown(e)},r9.apply(i,arguments),iO(i)}function iV(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r<t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let i of e)null!=(i=t(i,++n,e))&&(r<i||void 0===r&&i>=i)&&(r=i)}return r}function iY(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r>t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let i of e)null!=(i=t(i,++n,e))&&(r>i||void 0===r&&i>=i)&&(r=i)}return r}function iG(e,t){return(null==e||!(e>=e))-(null==t||!(t>=t))||(e<t?-1:+(e>t))}function iX(e,t,r){let n=e[t];e[t]=e[r],e[r]=n}function i$(){var e,t=[],r=[],n=[];function i(){var e=0,i=Math.max(1,r.length);for(n=Array(i-1);++e<i;)n[e-1]=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:nb;if(!(!(n=e.length)||isNaN(t*=1))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,i=(n-1)*t,a=Math.floor(i),o=+r(e[a],a,e);return o+(r(e[a+1],a+1,e)-o)*(i-a)}}(t,e/i);return a}function a(t){return null==t||isNaN(t*=1)?e:r[nw(n,t)]}return a.invertExtent=function(e){var i=r.indexOf(e);return i<0?[NaN,NaN]:[i>0?n[i-1]:t[0],i<n.length?n[i]:t[t.length-1]]},a.domain=function(e){if(!arguments.length)return t.slice();for(let r of(t=[],e))null==r||isNaN(r*=1)||t.push(r);return t.sort(ny),i()},a.range=function(e){return arguments.length?(r=Array.from(e),i()):r.slice()},a.unknown=function(t){return arguments.length?(e=t,a):e},a.quantiles=function(){return n.slice()},a.copy=function(){return i$().domain(t).range(r).unknown(e)},r9.apply(a,arguments)}function iZ(){var e,t=0,r=1,n=1,i=[.5],a=[0,1];function o(t){return null!=t&&t<=t?a[nw(i,t,0,n)]:e}function l(){var e=-1;for(i=Array(n);++e<n;)i[e]=((e+1)*r-(e-n)*t)/(n+1);return o}return o.domain=function(e){return arguments.length?([t,r]=e,t*=1,r*=1,l()):[t,r]},o.range=function(e){return arguments.length?(n=(a=Array.from(e)).length-1,l()):a.slice()},o.invertExtent=function(e){var o=a.indexOf(e);return o<0?[NaN,NaN]:o<1?[t,i[0]]:o>=n?[i[n-1],r]:[i[o-1],i[o]]},o.unknown=function(t){return arguments.length&&(e=t),o},o.thresholds=function(){return i.slice()},o.copy=function(){return iZ().domain([t,r]).range(a).unknown(e)},r9.apply(iO(o),arguments)}function iJ(){var e,t=[.5],r=[0,1],n=1;function i(i){return null!=i&&i<=i?r[nw(t,i,0,n)]:e}return i.domain=function(e){return arguments.length?(n=Math.min((t=Array.from(e)).length,r.length-1),i):t.slice()},i.range=function(e){return arguments.length?(r=Array.from(e),n=Math.min(t.length,r.length-1),i):r.slice()},i.invertExtent=function(e){var n=r.indexOf(e);return[t[n-1],t[n]]},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return iJ().domain(t).range(r).unknown(e)},r9.apply(i,arguments)}i=(n=function(e){var t,n,i,a=void 0===e.grouping||void 0===e.thousands?im:(t=ib.call(e.grouping,Number),n=e.thousands+"",function(e,r){for(var i=e.length,a=[],o=0,l=t[0],u=0;i>0&&l>0&&(u+l+1>r&&(l=Math.max(1,r-u)),a.push(e.substring(i-=l,i+l)),!((u+=l+1)>r));)l=t[o=(o+1)%t.length];return a.reverse().join(n)}),o=void 0===e.currency?"":e.currency[0]+"",l=void 0===e.currency?"":e.currency[1]+"",u=void 0===e.decimal?".":e.decimal+"",c=void 0===e.numerals?im:(i=ib.call(e.numerals,String),function(e){return e.replace(/[0-9]/g,function(e){return i[+e]})}),s=void 0===e.percent?"%":e.percent+"",f=void 0===e.minus?"−":e.minus+"",h=void 0===e.nan?"NaN":e.nan+"";function d(e){var t=(e=ip(e)).fill,n=e.align,i=e.sign,d=e.symbol,p=e.zero,y=e.width,v=e.comma,g=e.precision,m=e.trim,b=e.type;"n"===b?(v=!0,b="g"):ig[b]||(void 0===g&&(g=12),m=!0,b="g"),(p||"0"===t&&"="===n)&&(p=!0,t="0",n="=");var x="$"===d?o:"#"===d&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",w="$"===d?l:/[%p]/.test(b)?s:"",O=ig[b],j=/[defgprs%]/.test(b);function P(e){var o,l,s,d=x,P=w;if("c"===b)P=O(e)+P,e="";else{var A=(e*=1)<0||1/e<0;if(e=isNaN(e)?h:O(Math.abs(e),g),m&&(e=function(e){e:for(var t,r=e.length,n=1,i=-1;n<r;++n)switch(e[n]){case".":i=t=n;break;case"0":0===i&&(i=n),t=n;break;default:if(!+e[n])break e;i>0&&(i=0)}return i>0?e.slice(0,i)+e.slice(t+1):e}(e)),A&&0==+e&&"+"!==i&&(A=!1),d=(A?"("===i?i:f:"-"===i||"("===i?"":i)+d,P=("s"===b?ix[8+r/3]:"")+P+(A&&"("===i?")":""),j){for(o=-1,l=e.length;++o<l;)if(48>(s=e.charCodeAt(o))||s>57){P=(46===s?u+e.slice(o+1):e.slice(o))+P,e=e.slice(0,o);break}}}v&&!p&&(e=a(e,1/0));var E=d.length+e.length+P.length,S=E<y?Array(y-E+1).join(t):"";switch(v&&p&&(e=a(S+e,S.length?y-P.length:1/0),S=""),n){case"<":e=d+e+P+S;break;case"=":e=d+S+e+P;break;case"^":e=S.slice(0,E=S.length>>1)+d+e+P+S.slice(E);break;default:e=S+d+e+P}return c(e)}return g=void 0===g?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,g)):Math.max(0,Math.min(20,g)),P.toString=function(){return e+""},P}return{format:d,formatPrefix:function(e,t){var r=d(((e=ip(e)).type="f",e)),n=3*Math.max(-8,Math.min(8,Math.floor(ih(t)/3))),i=Math.pow(10,-n),a=ix[8+n/3];return function(e){return r(i*e)+a}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,a=n.formatPrefix;let iQ=new Date,i0=new Date;function i1(e,t,r,n){function i(t){return e(t=0==arguments.length?new Date:new Date(+t)),t}return i.floor=t=>(e(t=new Date(+t)),t),i.ceil=r=>(e(r=new Date(r-1)),t(r,1),e(r),r),i.round=e=>{let t=i(e),r=i.ceil(e);return e-t<r-e?t:r},i.offset=(e,r)=>(t(e=new Date(+e),null==r?1:Math.floor(r)),e),i.range=(r,n,a)=>{let o,l=[];if(r=i.ceil(r),a=null==a?1:Math.floor(a),!(r<n)||!(a>0))return l;do l.push(o=new Date(+r)),t(r,a),e(r);while(o<r&&r<n)return l},i.filter=r=>i1(t=>{if(t>=t)for(;e(t),!r(t);)t.setTime(t-1)},(e,n)=>{if(e>=e)if(n<0)for(;++n<=0;)for(;t(e,-1),!r(e););else for(;--n>=0;)for(;t(e,1),!r(e););}),r&&(i.count=(t,n)=>(iQ.setTime(+t),i0.setTime(+n),e(iQ),e(i0),Math.floor(r(iQ,i0))),i.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?i.filter(n?t=>n(t)%e==0:t=>i.count(0,t)%e==0):i:null),i}let i2=i1(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());i2.every=e=>isFinite(e=Math.floor(e))&&e>0?i1(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)}):null,i2.range;let i5=i1(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());i5.every=e=>isFinite(e=Math.floor(e))&&e>0?i1(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)}):null,i5.range;let i3=i1(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());i3.range;let i6=i1(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());i6.range;function i8(e){return i1(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(e,t)=>{e.setDate(e.getDate()+7*t)},(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/6048e5)}let i4=i8(0),i7=i8(1),i9=i8(2),ae=i8(3),at=i8(4),ar=i8(5),an=i8(6);function ai(e){return i1(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+7*t)},(e,t)=>(t-e)/6048e5)}i4.range,i7.range,i9.range,ae.range,at.range,ar.range,an.range;let aa=ai(0),ao=ai(1),al=ai(2),au=ai(3),ac=ai(4),as=ai(5),af=ai(6);aa.range,ao.range,al.range,au.range,ac.range,as.range,af.range;let ah=i1(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/864e5,e=>e.getDate()-1);ah.range;let ad=i1(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>e.getUTCDate()-1);ad.range;let ap=i1(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>Math.floor(e/864e5));ap.range;let ay=i1(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds()-6e4*e.getMinutes())},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getHours());ay.range;let av=i1(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getUTCHours());av.range;let ag=i1(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds())},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getMinutes());ag.range;let am=i1(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getUTCMinutes());am.range;let ab=i1(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+1e3*t)},(e,t)=>(t-e)/1e3,e=>e.getUTCSeconds());ab.range;let ax=i1(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);function aw(e,t,r,n,i,a){let o=[[ab,1,1e3],[ab,5,5e3],[ab,15,15e3],[ab,30,3e4],[a,1,6e4],[a,5,3e5],[a,15,9e5],[a,30,18e5],[i,1,36e5],[i,3,108e5],[i,6,216e5],[i,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[t,1,2592e6],[t,3,7776e6],[e,1,31536e6]];function l(t,r,n){let i=Math.abs(r-t)/n,a=ng(e=>{let[,,t]=e;return t}).right(o,i);if(a===o.length)return e.every(np(t/31536e6,r/31536e6,n));if(0===a)return ax.every(Math.max(np(t,r,n),1));let[l,u]=o[i/o[a-1][2]<o[a][2]/i?a-1:a];return l.every(u)}return[function(e,t,r){let n=t<e;n&&([e,t]=[t,e]);let i=r&&"function"==typeof r.range?r:l(e,t,r),a=i?i.range(e,+t+1):[];return n?a.reverse():a},l]}ax.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?i1(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):ax:null,ax.range;let[aO,aj]=aw(i5,i6,aa,ap,av,am),[aP,aA]=aw(i2,i3,i4,ah,ay,ag);function aE(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function aS(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function aM(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}var ak={"-":"",_:" ",0:"0"},aT=/^\s*\d+/,aC=/^%/,a_=/[\\^$*+?|[\]().{}]/g;function aD(e,t,r){var n=e<0?"-":"",i=(n?-e:e)+"",a=i.length;return n+(a<r?Array(r-a+1).join(t)+i:i)}function aN(e){return e.replace(a_,"\\$&")}function aI(e){return RegExp("^(?:"+e.map(aN).join("|")+")","i")}function aL(e){return new Map(e.map((e,t)=>[e.toLowerCase(),t]))}function aB(e,t,r){var n=aT.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function aR(e,t,r){var n=aT.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function az(e,t,r){var n=aT.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function aU(e,t,r){var n=aT.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function aF(e,t,r){var n=aT.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function aK(e,t,r){var n=aT.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function aq(e,t,r){var n=aT.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function aH(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function aW(e,t,r){var n=aT.exec(t.slice(r,r+1));return n?(e.q=3*n[0]-3,r+n[0].length):-1}function aV(e,t,r){var n=aT.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function aY(e,t,r){var n=aT.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function aG(e,t,r){var n=aT.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function aX(e,t,r){var n=aT.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function a$(e,t,r){var n=aT.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function aZ(e,t,r){var n=aT.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function aJ(e,t,r){var n=aT.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function aQ(e,t,r){var n=aT.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function a0(e,t,r){var n=aC.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function a1(e,t,r){var n=aT.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function a2(e,t,r){var n=aT.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function a5(e,t){return aD(e.getDate(),t,2)}function a3(e,t){return aD(e.getHours(),t,2)}function a6(e,t){return aD(e.getHours()%12||12,t,2)}function a8(e,t){return aD(1+ah.count(i2(e),e),t,3)}function a4(e,t){return aD(e.getMilliseconds(),t,3)}function a7(e,t){return a4(e,t)+"000"}function a9(e,t){return aD(e.getMonth()+1,t,2)}function oe(e,t){return aD(e.getMinutes(),t,2)}function ot(e,t){return aD(e.getSeconds(),t,2)}function or(e){var t=e.getDay();return 0===t?7:t}function on(e,t){return aD(i4.count(i2(e)-1,e),t,2)}function oi(e){var t=e.getDay();return t>=4||0===t?at(e):at.ceil(e)}function oa(e,t){return e=oi(e),aD(at.count(i2(e),e)+(4===i2(e).getDay()),t,2)}function oo(e){return e.getDay()}function ol(e,t){return aD(i7.count(i2(e)-1,e),t,2)}function ou(e,t){return aD(e.getFullYear()%100,t,2)}function oc(e,t){return aD((e=oi(e)).getFullYear()%100,t,2)}function os(e,t){return aD(e.getFullYear()%1e4,t,4)}function of(e,t){var r=e.getDay();return aD((e=r>=4||0===r?at(e):at.ceil(e)).getFullYear()%1e4,t,4)}function oh(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+aD(t/60|0,"0",2)+aD(t%60,"0",2)}function od(e,t){return aD(e.getUTCDate(),t,2)}function op(e,t){return aD(e.getUTCHours(),t,2)}function oy(e,t){return aD(e.getUTCHours()%12||12,t,2)}function ov(e,t){return aD(1+ad.count(i5(e),e),t,3)}function og(e,t){return aD(e.getUTCMilliseconds(),t,3)}function om(e,t){return og(e,t)+"000"}function ob(e,t){return aD(e.getUTCMonth()+1,t,2)}function ox(e,t){return aD(e.getUTCMinutes(),t,2)}function ow(e,t){return aD(e.getUTCSeconds(),t,2)}function oO(e){var t=e.getUTCDay();return 0===t?7:t}function oj(e,t){return aD(aa.count(i5(e)-1,e),t,2)}function oP(e){var t=e.getUTCDay();return t>=4||0===t?ac(e):ac.ceil(e)}function oA(e,t){return e=oP(e),aD(ac.count(i5(e),e)+(4===i5(e).getUTCDay()),t,2)}function oE(e){return e.getUTCDay()}function oS(e,t){return aD(ao.count(i5(e)-1,e),t,2)}function oM(e,t){return aD(e.getUTCFullYear()%100,t,2)}function ok(e,t){return aD((e=oP(e)).getUTCFullYear()%100,t,2)}function oT(e,t){return aD(e.getUTCFullYear()%1e4,t,4)}function oC(e,t){var r=e.getUTCDay();return aD((e=r>=4||0===r?ac(e):ac.ceil(e)).getUTCFullYear()%1e4,t,4)}function o_(){return"+0000"}function oD(){return"%"}function oN(e){return+e}function oI(e){return Math.floor(e/1e3)}function oL(e){return new Date(e)}function oB(e){return e instanceof Date?+e:+new Date(+e)}function oR(e,t,r,n,i,a,o,l,u,c){var s=ic(),f=s.invert,h=s.domain,d=c(".%L"),p=c(":%S"),y=c("%I:%M"),v=c("%I %p"),g=c("%a %d"),m=c("%b %d"),b=c("%B"),x=c("%Y");function w(e){return(u(e)<e?d:l(e)<e?p:o(e)<e?y:a(e)<e?v:n(e)<e?i(e)<e?g:m:r(e)<e?b:x)(e)}return s.invert=function(e){return new Date(f(e))},s.domain=function(e){return arguments.length?h(Array.from(e,oB)):h().map(oL)},s.ticks=function(t){var r=h();return e(r[0],r[r.length-1],null==t?10:t)},s.tickFormat=function(e,t){return null==t?w:c(t)},s.nice=function(e){var r=h();return e&&"function"==typeof e.range||(e=t(r[0],r[r.length-1],null==e?10:e)),e?h(iA(r,e)):s},s.copy=function(){return il(s,oR(e,t,r,n,i,a,o,l,u,c))},s}function oz(){return r9.apply(oR(aP,aA,i2,i3,i4,ah,ay,ag,ab,l).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function oU(){return r9.apply(oR(aO,aj,i5,i6,aa,ad,av,am,ab,u).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function oF(){var e,t,r,n,i,a=0,o=1,l=ir,u=!1;function c(t){return null==t||isNaN(t*=1)?i:l(0===r?.5:(t=(n(t)-e)*r,u?Math.max(0,Math.min(1,t)):t))}function s(e){return function(t){var r,n;return arguments.length?([r,n]=t,l=e(r,n),c):[l(0),l(1)]}}return c.domain=function(i){return arguments.length?([a,o]=i,e=n(a*=1),t=n(o*=1),r=e===t?0:1/(t-e),c):[a,o]},c.clamp=function(e){return arguments.length?(u=!!e,c):u},c.interpolator=function(e){return arguments.length?(l=e,c):l},c.range=s(n7),c.rangeRound=s(n9),c.unknown=function(e){return arguments.length?(i=e,c):i},function(i){return n=i,e=i(a),t=i(o),r=e===t?0:1/(t-e),c}}function oK(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function oq(){var e=iO(oF()(ir));return e.copy=function(){return oK(e,oq())},ne.apply(e,arguments)}function oH(){var e=i_(oF()).domain([1,10]);return e.copy=function(){return oK(e,oH()).base(e.base())},ne.apply(e,arguments)}function oW(){var e=iL(oF());return e.copy=function(){return oK(e,oW()).constant(e.constant())},ne.apply(e,arguments)}function oV(){var e=iF(oF());return e.copy=function(){return oK(e,oV()).exponent(e.exponent())},ne.apply(e,arguments)}function oY(){return oV.apply(null,arguments).exponent(.5)}function oG(){var e=[],t=ir;function r(r){if(null!=r&&!isNaN(r*=1))return t((nw(e,r,1)-1)/(e.length-1))}return r.domain=function(t){if(!arguments.length)return e.slice();for(let r of(e=[],t))null==r||isNaN(r*=1)||e.push(r);return e.sort(ny),r},r.interpolator=function(e){return arguments.length?(t=e,r):t},r.range=function(){return e.map((r,n)=>t(n/(e.length-1)))},r.quantiles=function(t){return Array.from({length:t+1},(r,n)=>(function(e,t,r){if(!(!(n=(e=Float64Array.from(function*(e,t){if(void 0===t)for(let t of e)null!=t&&(t*=1)>=t&&(yield t);else{let r=-1;for(let n of e)null!=(n=t(n,++r,e))&&(n*=1)>=n&&(yield n)}}(e,void 0))).length)||isNaN(t*=1))){if(t<=0||n<2)return iY(e);if(t>=1)return iV(e);var n,i=(n-1)*t,a=Math.floor(i),o=iV((function e(t,r){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1/0,a=arguments.length>4?arguments[4]:void 0;if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),i=Math.floor(Math.min(t.length-1,i)),!(n<=r&&r<=i))return t;for(a=void 0===a?iG:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:ny;if(e===ny)return iG;if("function"!=typeof e)throw TypeError("compare is not a function");return(t,r)=>{let n=e(t,r);return n||0===n?n:(0===e(r,r))-(0===e(t,t))}}(a);i>n;){if(i-n>600){let o=i-n+1,l=r-n+1,u=Math.log(o),c=.5*Math.exp(2*u/3),s=.5*Math.sqrt(u*c*(o-c)/o)*(l-o/2<0?-1:1),f=Math.max(n,Math.floor(r-l*c/o+s)),h=Math.min(i,Math.floor(r+(o-l)*c/o+s));e(t,r,f,h,a)}let o=t[r],l=n,u=i;for(iX(t,n,r),a(t[i],o)>0&&iX(t,n,i);l<u;){for(iX(t,l,u),++l,--u;0>a(t[l],o);)++l;for(;a(t[u],o)>0;)--u}0===a(t[n],o)?iX(t,n,u):iX(t,++u,i),u<=r&&(n=u+1),r<=u&&(i=u-1)}return t})(e,a).subarray(0,a+1));return o+(iY(e.subarray(a+1))-o)*(i-a)}})(e,n/t))},r.copy=function(){return oG(t).domain(e)},ne.apply(r,arguments)}function oX(){var e,t,r,n,i,a,o,l=0,u=.5,c=1,s=1,f=ir,h=!1;function d(e){return isNaN(e*=1)?o:(e=.5+((e=+a(e))-t)*(s*e<s*t?n:i),f(h?Math.max(0,Math.min(1,e)):e))}function p(e){return function(t){var r,n,i;return arguments.length?([r,n,i]=t,f=function(e,t){void 0===t&&(t=e,e=n7);for(var r=0,n=t.length-1,i=t[0],a=Array(n<0?0:n);r<n;)a[r]=e(i,i=t[++r]);return function(e){var t=Math.max(0,Math.min(n-1,Math.floor(e*=n)));return a[t](e-t)}}(e,[r,n,i]),d):[f(0),f(.5),f(1)]}}return d.domain=function(o){return arguments.length?([l,u,c]=o,e=a(l*=1),t=a(u*=1),r=a(c*=1),n=e===t?0:.5/(t-e),i=t===r?0:.5/(r-t),s=t<e?-1:1,d):[l,u,c]},d.clamp=function(e){return arguments.length?(h=!!e,d):h},d.interpolator=function(e){return arguments.length?(f=e,d):f},d.range=p(n7),d.rangeRound=p(n9),d.unknown=function(e){return arguments.length?(o=e,d):o},function(o){return a=o,e=o(l),t=o(u),r=o(c),n=e===t?0:.5/(t-e),i=t===r?0:.5/(r-t),s=t<e?-1:1,d}}function o$(){var e=iO(oX()(ir));return e.copy=function(){return oK(e,o$())},ne.apply(e,arguments)}function oZ(){var e=i_(oX()).domain([.1,1,10]);return e.copy=function(){return oK(e,oZ()).base(e.base())},ne.apply(e,arguments)}function oJ(){var e=iL(oX());return e.copy=function(){return oK(e,oJ()).constant(e.constant())},ne.apply(e,arguments)}function oQ(){var e=iF(oX());return e.copy=function(){return oK(e,oQ()).exponent(e.exponent())},ne.apply(e,arguments)}function o0(){return oQ.apply(null,arguments).exponent(.5)}l=(o=function(e){var t=e.dateTime,r=e.date,n=e.time,i=e.periods,a=e.days,o=e.shortDays,l=e.months,u=e.shortMonths,c=aI(i),s=aL(i),f=aI(a),h=aL(a),d=aI(o),p=aL(o),y=aI(l),v=aL(l),g=aI(u),m=aL(u),b={a:function(e){return o[e.getDay()]},A:function(e){return a[e.getDay()]},b:function(e){return u[e.getMonth()]},B:function(e){return l[e.getMonth()]},c:null,d:a5,e:a5,f:a7,g:oc,G:of,H:a3,I:a6,j:a8,L:a4,m:a9,M:oe,p:function(e){return i[+(e.getHours()>=12)]},q:function(e){return 1+~~(e.getMonth()/3)},Q:oN,s:oI,S:ot,u:or,U:on,V:oa,w:oo,W:ol,x:null,X:null,y:ou,Y:os,Z:oh,"%":oD},x={a:function(e){return o[e.getUTCDay()]},A:function(e){return a[e.getUTCDay()]},b:function(e){return u[e.getUTCMonth()]},B:function(e){return l[e.getUTCMonth()]},c:null,d:od,e:od,f:om,g:ok,G:oC,H:op,I:oy,j:ov,L:og,m:ob,M:ox,p:function(e){return i[+(e.getUTCHours()>=12)]},q:function(e){return 1+~~(e.getUTCMonth()/3)},Q:oN,s:oI,S:ow,u:oO,U:oj,V:oA,w:oE,W:oS,x:null,X:null,y:oM,Y:oT,Z:o_,"%":oD},w={a:function(e,t,r){var n=d.exec(t.slice(r));return n?(e.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(e,t,r){var n=f.exec(t.slice(r));return n?(e.w=h.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(e,t,r){var n=g.exec(t.slice(r));return n?(e.m=m.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(e,t,r){var n=y.exec(t.slice(r));return n?(e.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(e,r,n){return P(e,t,r,n)},d:aY,e:aY,f:aQ,g:aq,G:aK,H:aX,I:aX,j:aG,L:aJ,m:aV,M:a$,p:function(e,t,r){var n=c.exec(t.slice(r));return n?(e.p=s.get(n[0].toLowerCase()),r+n[0].length):-1},q:aW,Q:a1,s:a2,S:aZ,u:aR,U:az,V:aU,w:aB,W:aF,x:function(e,t,n){return P(e,r,t,n)},X:function(e,t,r){return P(e,n,t,r)},y:aq,Y:aK,Z:aH,"%":a0};function O(e,t){return function(r){var n,i,a,o=[],l=-1,u=0,c=e.length;for(r instanceof Date||(r=new Date(+r));++l<c;)37===e.charCodeAt(l)&&(o.push(e.slice(u,l)),null!=(i=ak[n=e.charAt(++l)])?n=e.charAt(++l):i="e"===n?" ":"0",(a=t[n])&&(n=a(r,i)),o.push(n),u=l+1);return o.push(e.slice(u,l)),o.join("")}}function j(e,t){return function(r){var n,i,a=aM(1900,void 0,1);if(P(a,e,r+="",0)!=r.length)return null;if("Q"in a)return new Date(a.Q);if("s"in a)return new Date(1e3*a.s+("L"in a?a.L:0));if(!t||"Z"in a||(a.Z=0),"p"in a&&(a.H=a.H%12+12*a.p),void 0===a.m&&(a.m="q"in a?a.q:0),"V"in a){if(a.V<1||a.V>53)return null;"w"in a||(a.w=1),"Z"in a?(n=(i=(n=aS(aM(a.y,0,1))).getUTCDay())>4||0===i?ao.ceil(n):ao(n),n=ad.offset(n,(a.V-1)*7),a.y=n.getUTCFullYear(),a.m=n.getUTCMonth(),a.d=n.getUTCDate()+(a.w+6)%7):(n=(i=(n=aE(aM(a.y,0,1))).getDay())>4||0===i?i7.ceil(n):i7(n),n=ah.offset(n,(a.V-1)*7),a.y=n.getFullYear(),a.m=n.getMonth(),a.d=n.getDate()+(a.w+6)%7)}else("W"in a||"U"in a)&&("w"in a||(a.w="u"in a?a.u%7:+("W"in a)),i="Z"in a?aS(aM(a.y,0,1)).getUTCDay():aE(aM(a.y,0,1)).getDay(),a.m=0,a.d="W"in a?(a.w+6)%7+7*a.W-(i+5)%7:a.w+7*a.U-(i+6)%7);return"Z"in a?(a.H+=a.Z/100|0,a.M+=a.Z%100,aS(a)):aE(a)}}function P(e,t,r,n){for(var i,a,o=0,l=t.length,u=r.length;o<l;){if(n>=u)return -1;if(37===(i=t.charCodeAt(o++))){if(!(a=w[(i=t.charAt(o++))in ak?t.charAt(o++):i])||(n=a(e,r,n))<0)return -1}else if(i!=r.charCodeAt(n++))return -1}return n}return b.x=O(r,b),b.X=O(n,b),b.c=O(t,b),x.x=O(r,x),x.X=O(n,x),x.c=O(t,x),{format:function(e){var t=O(e+="",b);return t.toString=function(){return e},t},parse:function(e){var t=j(e+="",!1);return t.toString=function(){return e},t},utcFormat:function(e){var t=O(e+="",x);return t.toString=function(){return e},t},utcParse:function(e){var t=j(e+="",!0);return t.toString=function(){return e},t}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,o.parse,u=o.utcFormat,o.utcParse,e.i(29061);var o1=e.i(79357),o2=e=>e.chartData,o5=eb([o2],e=>{var t=null!=e.chartData?e.chartData.length-1:0;return{chartData:e.chartData,computedData:e.computedData,dataEndIndex:t,dataStartIndex:0}}),o3=(e,t,r,n)=>n?o5(e):o2(e);function o6(e){return Number.isFinite(e)}function o8(e){return"number"==typeof e&&e>0&&Number.isFinite(e)}function o4(e){if(Array.isArray(e)&&2===e.length){var[t,r]=e;if(o6(t)&&o6(r))return!0}return!1}function o7(e,t,r){return r?e:[Math.min(e[0],t[0]),Math.max(e[1],t[1])]}var o9=e.i(51655),le=e=>e,lt={},lr=e=>function t(){let r;return 0==arguments.length||1==arguments.length&&(r=arguments.length<=0?void 0:arguments[0],r===lt)?t:e(...arguments)},ln=(e,t)=>1===e?t:lr(function(){for(var r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];var a=n.filter(e=>e!==lt).length;return a>=e?t(...n):ln(e-a,lr(function(){for(var e=arguments.length,r=Array(e),i=0;i<e;i++)r[i]=arguments[i];return t(...n.map(e=>e===lt?r.shift():e),...r)}))}),li=e=>ln(e.length,e),la=(e,t)=>{for(var r=[],n=e;n<t;++n)r[n-e]=n;return r},lo=li((e,t)=>Array.isArray(t)?t.map(e):Object.keys(t).map(e=>t[e]).map(e)),ll=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];if(!t.length)return le;var n=t.reverse(),i=n[0],a=n.slice(1);return function(){return a.reduce((e,t)=>t(e),i(...arguments))}},lu=e=>Array.isArray(e)?e.reverse():e.split("").reverse().join(""),lc=e=>{var t=null,r=null;return function(){for(var n=arguments.length,i=Array(n),a=0;a<n;a++)i[a]=arguments[a];return t&&i.every((e,r)=>{var n;return e===(null==(n=t)?void 0:n[r])})?r:(t=i,r=e(...i))}};function ls(e){return 0===e?1:Math.floor(new o9.default(e).abs().log(10).toNumber())+1}function lf(e,t,r){for(var n=new o9.default(e),i=0,a=[];n.lt(t)&&i<1e5;)a.push(n.toNumber()),n=n.add(r),i++;return a}li((e,t,r)=>{var n=+e;return n+r*(t-n)}),li((e,t,r)=>{var n=t-e;return(r-e)/(n=n||1/0)}),li((e,t,r)=>{var n=t-e;return Math.max(0,Math.min(1,(r-e)/(n=n||1/0)))});var lh=e=>{var[t,r]=e,[n,i]=[t,r];return t>r&&([n,i]=[r,t]),[n,i]},ld=(e,t,r)=>{if(e.lte(0))return new o9.default(0);var n=ls(e.toNumber()),i=new o9.default(10).pow(n),a=e.div(i),o=1!==n?.05:.1,l=new o9.default(Math.ceil(a.div(o).toNumber())).add(r).mul(o).mul(i);return new o9.default(t?l.toNumber():Math.ceil(l.toNumber()))},lp=function(e,t,r,n){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((t-e)/(r-1)))return{step:new o9.default(0),tickMin:new o9.default(0),tickMax:new o9.default(0)};var o=ld(new o9.default(t).sub(e).div(r-1),n,a),l=Math.ceil((i=e<=0&&t>=0?new o9.default(0):(i=new o9.default(e).add(t).div(2)).sub(new o9.default(i).mod(o))).sub(e).div(o).toNumber()),u=Math.ceil(new o9.default(t).sub(i).div(o).toNumber()),c=l+u+1;return c>r?lp(e,t,r,n,a+1):(c<r&&(u=t>0?u+(r-c):u,l=t>0?l:l+(r-c)),{step:o,tickMin:i.sub(new o9.default(l).mul(o)),tickMax:i.add(new o9.default(u).mul(o))})},ly=lc(function(e){var[t,r]=e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(n,2),[o,l]=lh([t,r]);if(o===-1/0||l===1/0){var u=l===1/0?[o,...la(0,n-1).map(()=>1/0)]:[...la(0,n-1).map(()=>-1/0),l];return t>r?lu(u):u}if(o===l){var c=new o9.default(1),s=new o9.default(o);if(!s.isint()&&i){var f=Math.abs(o);f<1?(c=new o9.default(10).pow(ls(o)-1),s=new o9.default(Math.floor(s.div(c).toNumber())).mul(c)):f>1&&(s=new o9.default(Math.floor(o)))}else 0===o?s=new o9.default(Math.floor((n-1)/2)):i||(s=new o9.default(Math.floor(o)));var h=Math.floor((n-1)/2);return ll(lo(e=>s.add(new o9.default(e-h).mul(c)).toNumber()),la)(0,n)}var{step:d,tickMin:p,tickMax:y}=lp(o,l,a,i,0),v=lf(p,y.add(new o9.default(.1).mul(d)),d);return t>r?lu(v):v}),lv=lc(function(e,t){var[r,n]=e,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],[a,o]=lh([r,n]);if(a===-1/0||o===1/0)return[r,n];if(a===o)return[a];var l=Math.max(t,2),u=ld(new o9.default(o).sub(a).div(l-1),i,0),c=[...lf(new o9.default(a),new o9.default(o),u),o];return!1===i&&(c=c.map(e=>Math.round(e))),r>n?lu(c):c}),lg=e=>e.rootProps.maxBarSize,lm=e=>e.rootProps.barCategoryGap,lb=e=>e.rootProps.stackOffset,lx=e=>e.options.chartName,lw=e=>e.rootProps.syncId,lO=e=>e.rootProps.syncMethod,lj=e=>e.options.eventEmitter,lP={allowDuplicatedCategory:!0,angleAxisId:0,reversed:!1,scale:"auto",tick:!0,type:"category"},lA={allowDataOverflow:!1,allowDuplicatedCategory:!0,radiusAxisId:0,scale:"auto",tick:!0,tickCount:5,type:"number"},lE=(e,t)=>{if(e&&t)return null!=e&&e.reversed?[t[1],t[0]]:t},lS={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!1,dataKey:void 0,domain:void 0,id:lP.angleAxisId,includeHidden:!1,name:void 0,reversed:lP.reversed,scale:lP.scale,tick:lP.tick,tickCount:void 0,ticks:void 0,type:lP.type,unit:void 0},lM={allowDataOverflow:lA.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:lA.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:lA.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:lA.scale,tick:lA.tick,tickCount:lA.tickCount,ticks:void 0,type:lA.type,unit:void 0},lk={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:lP.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:lP.angleAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:lP.scale,tick:lP.tick,tickCount:void 0,ticks:void 0,type:"number",unit:void 0},lT={allowDataOverflow:lA.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:lA.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:lA.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:lA.scale,tick:lA.tick,tickCount:lA.tickCount,ticks:void 0,type:"category",unit:void 0},lC=(e,t)=>null!=e.polarAxis.angleAxis[t]?e.polarAxis.angleAxis[t]:"radial"===e.layout.layoutType?lk:lS,l_=(e,t)=>null!=e.polarAxis.radiusAxis[t]?e.polarAxis.radiusAxis[t]:"radial"===e.layout.layoutType?lT:lM,lD=e=>e.polarOptions,lN=eb([rR,rz,rG],rw),lI=eb([lD,lN],(e,t)=>{if(null!=e)return tx(e.innerRadius,t,0)}),lL=eb([lD,lN],(e,t)=>{if(null!=e)return tx(e.outerRadius,t,.8*t)}),lB=eb([lD],e=>{if(null==e)return[0,0];var{startAngle:t,endAngle:r}=e;return[t,r]});eb([lC,lB],lE);var lR=eb([lN,lI,lL],(e,t,r)=>{if(null!=e&&null!=t&&null!=r)return[t,r]});eb([l_,lR],lE);var lz=eb([r8,lD,lI,lL,rR,rz],(e,t,r,n,i,a)=>{if(("centric"===e||"radial"===e)&&null!=t&&null!=r&&null!=n){var{cx:o,cy:l,startAngle:u,endAngle:c}=t;return{cx:tx(o,i,i/2),cy:tx(l,a,a/2),innerRadius:r,outerRadius:n,startAngle:u,endAngle:c,clockWise:!1}}}),lU=(e,t)=>t,lF=(e,t,r)=>r;function lK(e){return null==e?void 0:e.id}var lq=e=>{var t=r8(e);return"horizontal"===t?"xAxis":"vertical"===t?"yAxis":"centric"===t?"angleAxis":"radiusAxis"},lH=e=>e.tooltip.settings.axisId,lW=e=>{var t=lq(e),r=lH(e);return l3(e,t,r)};function lV(e,t,r){var{chartData:n=[]}=t,i=null==r?void 0:r.dataKey,a=new Map;return e.forEach(e=>{var t,r=null!=(t=e.data)?t:n;if(null!=r&&0!==r.length){var o=lK(e);r.forEach((t,r)=>{var n,l=null==i?r:String(rA(t,i,null)),u=rA(t,e.dataKey,0);Object.assign(n=a.has(l)?a.get(l):{},{[o]:u}),a.set(l,n)})}}),Array.from(a.values())}function lY(e){return null!=e.stackId&&null!=e.dataKey}function lG(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function lX(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?lG(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):lG(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var l$=[0,"auto"],lZ={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:void 0,height:30,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"bottom",padding:{left:0,right:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"category",unit:void 0},lJ=(e,t)=>{var r=e.cartesianAxis.xAxis[t];return null==r?lZ:r},lQ={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:l$,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"left",padding:{top:0,bottom:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"number",unit:void 0,width:60},l0=(e,t)=>{var r=e.cartesianAxis.yAxis[t];return null==r?lQ:r},l1={domain:[0,"auto"],includeHidden:!1,reversed:!1,allowDataOverflow:!1,allowDuplicatedCategory:!1,dataKey:void 0,id:0,name:"",range:[64,64],scale:"auto",type:"number",unit:""},l2=(e,t)=>{var r=e.cartesianAxis.zAxis[t];return null==r?l1:r},l5=(e,t,r)=>{switch(t){case"xAxis":return lJ(e,r);case"yAxis":return l0(e,r);case"zAxis":return l2(e,r);case"angleAxis":return lC(e,r);case"radiusAxis":return l_(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},l3=(e,t,r)=>{switch(t){case"xAxis":return lJ(e,r);case"yAxis":return l0(e,r);case"angleAxis":return lC(e,r);case"radiusAxis":return l_(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},l6=e=>e.graphicalItems.cartesianItems.some(e=>"bar"===e.type)||e.graphicalItems.polarItems.some(e=>"radialBar"===e.type);function l8(e,t){return r=>{switch(e){case"xAxis":return"xAxisId"in r&&r.xAxisId===t;case"yAxis":return"yAxisId"in r&&r.yAxisId===t;case"zAxis":return"zAxisId"in r&&r.zAxisId===t;case"angleAxis":return"angleAxisId"in r&&r.angleAxisId===t;case"radiusAxis":return"radiusAxisId"in r&&r.radiusAxisId===t;default:return!1}}}var l4=e=>e.graphicalItems.cartesianItems,l7=eb([lU,lF],l8),l9=(e,t,r)=>e.filter(r).filter(e=>(null==t?void 0:t.includeHidden)===!0||!e.hide),ue=eb([l4,l5,l7],l9),ut=eb([ue],e=>e.filter(e=>"area"===e.type||"bar"===e.type).filter(lY)),ur=e=>e.filter(e=>!("stackId"in e)||void 0===e.stackId),un=eb([ue],ur),ui=e=>e.map(e=>e.data).filter(Boolean).flat(1),ua=eb([ue],ui),uo=(e,t)=>{var{chartData:r=[],dataStartIndex:n,dataEndIndex:i}=t;return e.length>0?e:r.slice(n,i+1)},ul=eb([ua,o3],uo),uu=(e,t,r)=>(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:rA(e,t.dataKey)})):r.length>0?r.map(e=>e.dataKey).flatMap(t=>e.map(e=>({value:rA(e,t)}))):e.map(e=>({value:e})),uc=eb([ul,l5,ue],uu);function us(e,t){switch(e){case"xAxis":return"x"===t.direction;case"yAxis":return"y"===t.direction;default:return!1}}function uf(e){return e.filter(e=>tg(e)||e instanceof Date).map(Number).filter(e=>!1===tp(e))}var uh=eb([ut,o3,lW],lV),ud=(e,t,r)=>Object.fromEntries(Object.entries(t.reduce((e,t)=>(null==t.stackId||(null==e[t.stackId]&&(e[t.stackId]=[]),e[t.stackId].push(t)),e),{})).map(t=>{var[n,i]=t;return[n,{stackedData:((e,t,r)=>{var n=rk[r];return(function(){var e=rh([]),t=rp,r=rd,n=ry;function i(i){var a,o,l=Array.from(e.apply(this,arguments),rv),u=l.length,c=-1;for(let e of i)for(a=0,++c;a<u;++a)(l[a][c]=[0,+n(e,l[a].key,c,i)]).data=e;for(a=0,o=rf(t(l));a<u;++a)l[o[a]].index=a;return r(l,o),l}return i.keys=function(t){return arguments.length?(e="function"==typeof t?t:rh(Array.from(t)),i):e},i.value=function(e){return arguments.length?(n="function"==typeof e?e:rh(+e),i):n},i.order=function(e){return arguments.length?(t=null==e?rp:"function"==typeof e?e:rh(Array.from(e)),i):t},i.offset=function(e){return arguments.length?(r=null==e?rd:e,i):r},i})().keys(t).value((e,t)=>+rA(e,t,0)).order(rp).offset(n)(e)})(e,i.map(lK),r),graphicalItems:i}]})),up=eb([uh,ut,lb],ud),uy=(e,t,r)=>{var{dataStartIndex:n,dataEndIndex:i}=t;if("zAxis"!==r){var a=((e,t,r)=>{if(null!=e)return(e=>[e[0]===1/0?0:e[0],e[1]===-1/0?0:e[1]])(Object.keys(e).reduce((n,i)=>{var{stackedData:a}=e[i],o=a.reduce((e,n)=>{var i=(e=>{var t=e.flat(2).filter(tv);return[Math.min(...t),Math.max(...t)]})(rO(n,t,r));return[Math.min(e[0],i[0]),Math.max(e[1],i[1])]},[1/0,-1/0]);return[Math.min(o[0],n[0]),Math.max(o[1],n[1])]},[1/0,-1/0]))})(e,n,i);if(null==a||0!==a[0]||0!==a[1])return a}},uv=eb([up,o2,lU],uy),ug=(e,t,r,n,i)=>r.length>0?e.flatMap(e=>r.flatMap(r=>{var a,o,l=null==(a=n[r.id])?void 0:a.filter(e=>us(i,e)),u=rA(e,null!=(o=t.dataKey)?o:r.dataKey);return{value:u,errorDomain:function(e,t,r){return!r||"number"!=typeof t||tp(t)||!r.length?[]:uf(r.flatMap(r=>{var n,i,a=rA(e,r.dataKey);if(Array.isArray(a)?[n,i]=a:n=i=a,o6(n)&&o6(i))return[t-n,t+i]}))}(e,u,l)}})).filter(Boolean):(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:rA(e,t.dataKey),errorDomain:[]})):e.map(e=>({value:e,errorDomain:[]})),um=e=>e.errorBars,ub=(e,t,r)=>e.flatMap(e=>t[e.id]).filter(Boolean).filter(e=>us(r,e));eb([un,um,lU],ub);var ux=eb([ul,l5,un,um,lU],ug);function uw(e){var{value:t}=e;if(tg(t)||t instanceof Date)return t}var uO=e=>{var t=uf(e.flatMap(e=>[e.value,e.errorDomain]).flat(1));if(0!==t.length)return[Math.min(...t),Math.max(...t)]},uj=e=>{var t;if(null==e||!("domain"in e))return l$;if(null!=e.domain)return e.domain;if(null!=e.ticks){if("number"===e.type){var r=uf(e.ticks);return[Math.min(...r),Math.max(...r)]}if("category"===e.type)return e.ticks.map(String)}return null!=(t=null==e?void 0:e.domain)?t:l$},uP=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=t.filter(Boolean);if(0!==n.length){var i=n.flat();return[Math.min(...i),Math.max(...i)]}},uA=e=>e.referenceElements.dots,uE=(e,t,r)=>e.filter(e=>"extendDomain"===e.ifOverflow).filter(e=>"xAxis"===t?e.xAxisId===r:e.yAxisId===r),uS=eb([uA,lU,lF],uE),uM=e=>e.referenceElements.areas,uk=eb([uM,lU,lF],uE),uT=e=>e.referenceElements.lines,uC=eb([uT,lU,lF],uE),u_=(e,t)=>{var r=uf(e.map(e=>"xAxis"===t?e.x:e.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},uD=eb(uS,lU,u_),uN=(e,t)=>{var r=uf(e.flatMap(e=>["xAxis"===t?e.x1:e.y1,"xAxis"===t?e.x2:e.y2]));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},uI=eb([uk,lU],uN),uL=(e,t)=>{var r=uf(e.map(e=>"xAxis"===t?e.x:e.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},uB=eb(uC,lU,uL),uR=eb(uD,uB,uI,(e,t,r)=>uP(e,r,t)),uz=eb([l5],uj),uU=(e,t,r,n,i,a,o)=>{var l=function(e,t){if(t&&"function"!=typeof e&&Array.isArray(e)&&2===e.length){var r,n,[i,a]=e;if(o6(i))r=i;else if("function"==typeof i)return;if(o6(a))n=a;else if("function"==typeof a)return;var o=[r,n];if(o4(o))return o}}(t,e.allowDataOverflow);return null!=l?l:function(e,t,r){if(r||null!=t){if("function"==typeof e&&null!=t)try{var n=e(t,r);if(o4(n))return o7(n,t,r)}catch(e){}if(Array.isArray(e)&&2===e.length){var i,a,[o,l]=e;if("auto"===o)null!=t&&(i=Math.min(...t));else if(tv(o))i=o;else if("function"==typeof o)try{null!=t&&(i=o(null==t?void 0:t[0]))}catch(e){}else if("string"==typeof o&&rD.test(o)){var u=rD.exec(o);if(null==u||null==t)i=void 0;else{var c=+u[1];i=t[0]-c}}else i=null==t?void 0:t[0];if("auto"===l)null!=t&&(a=Math.max(...t));else if(tv(l))a=l;else if("function"==typeof l)try{null!=t&&(a=l(null==t?void 0:t[1]))}catch(e){}else if("string"==typeof l&&rN.test(l)){var s=rN.exec(l);if(null==s||null==t)a=void 0;else{var f=+s[1];a=t[1]+f}}else a=null==t?void 0:t[1];var h=[i,a];if(o4(h))return null==t?h:o7(h,t,r)}}}(t,"vertical"===a&&"xAxis"===o||"horizontal"===a&&"yAxis"===o?uP(r,i,uO(n)):uP(i,uO(n)),e.allowDataOverflow)},uF=eb([l5,uz,uv,ux,uR,r8,lU],uU),uK=[0,1],uq=(e,t,r,n,i,a,o)=>{if(null!=e&&null!=r&&0!==r.length||void 0!==o){var{dataKey:l,type:u}=e,c=rE(t,a);return c&&null==l?(0,r7.default)(0,r.length):"category"===u?((e,t,r)=>{var n=e.map(uw).filter(e=>null!=e);return r&&(null==t.dataKey||t.allowDuplicatedCategory&&tw(n))?(0,r7.default)(0,e.length):t.allowDuplicatedCategory?n:Array.from(new Set(n))})(n,e,c):"expand"===i?uK:o}},uH=eb([l5,r8,ul,uc,lb,lU,uF],uq),uW=(e,t,r,n,i)=>{if(null!=e){var{scale:a,type:o}=e;if("auto"===a)return"radial"===t&&"radiusAxis"===i?"band":"radial"===t&&"angleAxis"===i?"linear":"category"===o&&n&&(n.indexOf("LineChart")>=0||n.indexOf("AreaChart")>=0||n.indexOf("ComposedChart")>=0&&!r)?"point":"category"===o?"band":"linear";if("string"==typeof a){var l="scale".concat(tA(a));return l in o1?l:"point"}}},uV=eb([l5,r8,l6,lx,lU],uW);function uY(e,t,r,n){if(null!=r&&null!=n){if("function"==typeof e.scale)return e.scale.copy().domain(r).range(n);var i=function(e){if(null!=e){if(e in o1)return o1[e]();var t="scale".concat(tA(e));if(t in o1)return o1[t]()}}(t);if(null!=i){var a=i.domain(r).range(n);return(e=>{var t=e.domain();if(t&&!(t.length<=2)){var r=t.length,n=e.range(),i=Math.min(n[0],n[1])-1e-4,a=Math.max(n[0],n[1])+1e-4,o=e(t[0]),l=e(t[r-1]);(o<i||o>a||l<i||l>a)&&e.domain([t[0],t[r-1]])}})(a),a}}}var uG=(e,t,r)=>{var n=uj(t);if("auto"===r||"linear"===r){if(null!=t&&t.tickCount&&Array.isArray(n)&&("auto"===n[0]||"auto"===n[1])&&o4(e))return ly(e,t.tickCount,t.allowDecimals);if(null!=t&&t.tickCount&&"number"===t.type&&o4(e))return lv(e,t.tickCount,t.allowDecimals)}},uX=eb([uH,l3,uV],uG),u$=(e,t,r,n)=>"angleAxis"!==n&&(null==e?void 0:e.type)==="number"&&o4(t)&&Array.isArray(r)&&r.length>0?[Math.min(t[0],r[0]),Math.max(t[1],r[r.length-1])]:t,uZ=eb([l5,uH,uX,lU],u$),uJ=eb(uc,l5,(e,t)=>{if(t&&"number"===t.type){var r=1/0,n=Array.from(uf(e.map(e=>e.value))).sort((e,t)=>e-t);if(n.length<2)return 1/0;var i=n[n.length-1]-n[0];if(0===i)return 1/0;for(var a=0;a<n.length-1;a++)r=Math.min(r,n[a+1]-n[a]);return r/i}}),uQ=eb(uJ,r8,lm,rG,(e,t,r,n)=>n,(e,t,r,n,i)=>{if(!o6(e))return 0;var a="vertical"===t?n.height:n.width;if("gap"===i)return e*a/2;if("no-gap"===i){var o=tx(r,e*a),l=e*a/2;return l-o-(l-o)/a*o}return 0}),u0=eb(lJ,(e,t)=>{var r=lJ(e,t);return null==r||"string"!=typeof r.padding?0:uQ(e,"xAxis",t,r.padding)},(e,t)=>{if(null==e)return{left:0,right:0};var r,n,{padding:i}=e;return"string"==typeof i?{left:t,right:t}:{left:(null!=(r=i.left)?r:0)+t,right:(null!=(n=i.right)?n:0)+t}}),u1=eb(l0,(e,t)=>{var r=l0(e,t);return null==r||"string"!=typeof r.padding?0:uQ(e,"yAxis",t,r.padding)},(e,t)=>{if(null==e)return{top:0,bottom:0};var r,n,{padding:i}=e;return"string"==typeof i?{top:t,bottom:t}:{top:(null!=(r=i.top)?r:0)+t,bottom:(null!=(n=i.bottom)?n:0)+t}}),u2=eb([rG,u0,r0,rQ,(e,t,r)=>r],(e,t,r,n,i)=>{var{padding:a}=n;return i?[a.left,r.width-a.right]:[e.left+t.left,e.left+e.width-t.right]}),u5=eb([rG,r8,u1,r0,rQ,(e,t,r)=>r],(e,t,r,n,i,a)=>{var{padding:o}=i;return a?[n.height-o.bottom,o.top]:"horizontal"===t?[e.top+e.height-r.bottom,e.top+r.top]:[e.top+r.top,e.top+e.height-r.bottom]}),u3=(e,t,r,n)=>{var i;switch(t){case"xAxis":return u2(e,r,n);case"yAxis":return u5(e,r,n);case"zAxis":return null==(i=l2(e,r))?void 0:i.range;case"angleAxis":return lB(e);case"radiusAxis":return lR(e,r);default:return}},u6=eb([l5,u3],lE),u8=eb([l5,uV,uZ,u6],uY);function u4(e,t){return e.id<t.id?-1:+(e.id>t.id)}eb([ue,um,lU],ub);var u7=(e,t)=>t,u9=(e,t,r)=>r,ce=eb(rK,u7,u9,(e,t,r)=>e.filter(e=>e.orientation===t).filter(e=>e.mirror===r).sort(u4)),ct=eb(rq,u7,u9,(e,t,r)=>e.filter(e=>e.orientation===t).filter(e=>e.mirror===r).sort(u4)),cr=(e,t)=>({width:e.width,height:t.height}),cn=eb(rG,lJ,cr),ci=eb(rz,rG,ce,u7,u9,(e,t,r,n,i)=>{var a,o={};return r.forEach(r=>{var l=cr(t,r);null==a&&(a=((e,t,r)=>{switch(t){case"top":return e.top;case"bottom":return r-e.bottom;default:return 0}})(t,n,e));var u="top"===n&&!i||"bottom"===n&&i;o[r.id]=a-Number(u)*l.height,a+=(u?-1:1)*l.height}),o}),ca=eb(rR,rG,ct,u7,u9,(e,t,r,n,i)=>{var a,o={};return r.forEach(r=>{var l=((e,t)=>({width:"number"==typeof t.width?t.width:60,height:e.height}))(t,r);null==a&&(a=((e,t,r)=>{switch(t){case"left":return e.left;case"right":return r-e.right;default:return 0}})(t,n,e));var u="left"===n&&!i||"right"===n&&i;o[r.id]=a-Number(u)*l.width,a+=(u?-1:1)*l.width}),o}),co=eb(rG,l0,(e,t)=>({width:"number"==typeof t.width?t.width:60,height:e.height})),cl=(e,t,r)=>{switch(t){case"xAxis":return cn(e,r).width;case"yAxis":return co(e,r).height;default:return}},cu=(e,t,r,n)=>{if(null!=r){var{allowDuplicatedCategory:i,type:a,dataKey:o}=r,l=rE(e,n),u=t.map(e=>e.value);if(o&&l&&"category"===a&&i&&tw(u))return u}},cc=eb([r8,uc,l5,lU],cu),cs=(e,t,r,n)=>{if(null!=r&&null!=r.dataKey){var{type:i,scale:a}=r;if(rE(e,n)&&("number"===i||"auto"!==a))return t.map(e=>e.value)}},cf=eb([r8,uc,l3,lU],cs),ch=eb([r8,(e,t,r)=>{switch(t){case"xAxis":return lJ(e,r);case"yAxis":return l0(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},uV,u8,cc,cf,u3,uX,lU],(e,t,r,n,i,a,o,l,u)=>{if(null==t)return null;var c=rE(e,u);return{angle:t.angle,interval:t.interval,minTickGap:t.minTickGap,orientation:t.orientation,tick:t.tick,tickCount:t.tickCount,tickFormatter:t.tickFormatter,ticks:t.ticks,type:t.type,unit:t.unit,axisType:u,categoricalDomain:a,duplicateDomain:i,isCategorical:c,niceTicks:l,range:o,realScaleType:r,scale:n}}),cd=eb([r8,l3,uV,u8,uX,u3,cc,cf,lU],(e,t,r,n,i,a,o,l,u)=>{if(null!=t&&null!=n){var c=rE(e,u),{type:s,ticks:f,tickCount:h}=t,d="scaleBand"===r&&"function"==typeof n.bandwidth?n.bandwidth()/2:2,p="category"===s&&n.bandwidth?n.bandwidth()/d:0;p="angleAxis"===u&&null!=a&&a.length>=2?2*td(a[0]-a[1])*p:p;var y=f||i;return y?y.map((e,t)=>({index:t,coordinate:n(o?o.indexOf(e):e)+p,value:e,offset:p})).filter(e=>!tp(e.coordinate)):c&&l?l.map((e,t)=>({coordinate:n(e)+p,value:e,index:t,offset:p})):n.ticks?n.ticks(h).map(e=>({coordinate:n(e)+p,value:e,offset:p})):n.domain().map((e,t)=>({coordinate:n(e)+p,value:o?o[e]:e,index:t,offset:p}))}}),cp=eb([r8,l3,u8,u3,cc,cf,lU],(e,t,r,n,i,a,o)=>{if(null!=t&&null!=r&&null!=n&&n[0]!==n[1]){var l=rE(e,o),{tickCount:u}=t,c=0;return(c="angleAxis"===o&&(null==n?void 0:n.length)>=2?2*td(n[0]-n[1])*c:c,l&&a)?a.map((e,t)=>({coordinate:r(e)+c,value:e,index:t,offset:c})):r.ticks?r.ticks(u).map(e=>({coordinate:r(e)+c,value:e,offset:c})):r.domain().map((e,t)=>({coordinate:r(e)+c,value:i?i[e]:e,index:t,offset:c}))}}),cy=eb(l5,u8,(e,t)=>{if(null!=e&&null!=t)return lX(lX({},e),{},{scale:t})}),cv=eb([l5,uV,uH,u6],uY);eb((e,t,r)=>l2(e,r),cv,(e,t)=>{if(null!=e&&null!=t)return lX(lX({},e),{},{scale:t})});var cg=eb([r8,rK,rq],(e,t,r)=>{switch(e){case"horizontal":return t.some(e=>e.reversed)?"right-to-left":"left-to-right";case"vertical":return r.some(e=>e.reversed)?"bottom-to-top":"top-to-bottom";case"centric":case"radial":return"left-to-right";default:return}}),cm=e=>e.options.defaultTooltipEventType,cb=e=>e.options.validateTooltipEventTypes;function cx(e,t,r){if(null==e)return t;var n=e?"axis":"item";return null==r?t:r.includes(n)?n:t}function cw(e,t){return cx(t,cm(e),cb(e))}var cO=(e,t)=>{var r,n=Number(t);if(!tp(n)&&null!=t)return n>=0?null==e||null==(r=e[n])?void 0:r.value:void 0};function cj(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function cP(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?cj(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):cj(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var cA=(e,t,r,n)=>{if(null==t)return tU;var i=function(e,t,r){return"axis"===t?"click"===r?e.axisInteraction.click:e.axisInteraction.hover:"click"===r?e.itemInteraction.click:e.itemInteraction.hover}(e,t,r);if(null==i)return tU;if(i.active)return i;if(e.keyboardInteraction.active)return e.keyboardInteraction;if(e.syncInteraction.active&&null!=e.syncInteraction.index)return e.syncInteraction;var a=!0===e.settings.active;if(null!=i.index){if(a)return cP(cP({},i),{},{active:!0})}else if(null!=n)return{active:!0,coordinate:void 0,dataKey:void 0,index:n};return cP(cP({},tU),{},{coordinate:i.coordinate})},cE=(e,t)=>{var r=null==e?void 0:e.index;if(null==r)return null;var n=Number(r);if(!o6(n))return r;var i=Infinity;return t.length>0&&(i=t.length-1),String(Math.max(0,Math.min(n,i)))},cS=(e,t,r,n,i,a,o,l)=>{if(null!=a&&null!=l){var u=o[0],c=null==u?void 0:l(u.positions,a);if(null!=c)return c;var s=null==i?void 0:i[Number(a)];if(s)if("horizontal"===r)return{x:s.coordinate,y:(n.top+t)/2};else return{x:(n.left+e)/2,y:s.coordinate}}},cM=(e,t,r,n)=>{var i;return"axis"===t?e.tooltipItemPayloads:0===e.tooltipItemPayloads.length?[]:null==(i="hover"===r?e.itemInteraction.hover.dataKey:e.itemInteraction.click.dataKey)&&null!=n?[e.tooltipItemPayloads[0]]:e.tooltipItemPayloads.filter(e=>{var t;return(null==(t=e.settings)?void 0:t.dataKey)===i})},ck=e=>e.options.tooltipPayloadSearcher,cT=e=>e.tooltip;function cC(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function c_(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?cC(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):cC(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var cD=(e,t,r,n,i,a,o)=>{if(null!=t&&null!=a){var{chartData:l,computedData:u,dataStartIndex:c,dataEndIndex:s}=r;return e.reduce((e,r)=>{var f,h,d,p,y,{dataDefinedOnItem:v,settings:g}=r,m=(f=v,h=l,null!=f?f:h),b=Array.isArray(m)?rO(m,c,s):m,x=null!=(d=null==g?void 0:g.dataKey)?d:null==n?void 0:n.dataKey,w=null==g?void 0:g.nameKey;return Array.isArray(p=null!=n&&n.dataKey&&Array.isArray(b)&&!Array.isArray(b[0])&&"axis"===o?tP(b,n.dataKey,i):a(b,t,u,w))?p.forEach(t=>{var r=c_(c_({},g),{},{name:t.name,unit:t.unit,color:void 0,fill:void 0});e.push(rL({tooltipEntrySettings:r,dataKey:t.dataKey,payload:t.payload,value:rA(t.payload,t.dataKey),name:t.name}))}):e.push(rL({tooltipEntrySettings:g,dataKey:x,payload:p,value:rA(p,x),name:null!=(y=rA(p,w))?y:null==g?void 0:g.name})),e},[])}},cN=eb([lW,r8,l6,lx,lq],uW),cI=eb([e=>e.graphicalItems.cartesianItems,e=>e.graphicalItems.polarItems],(e,t)=>[...e,...t]),cL=eb([lq,lH],l8),cB=eb([cI,lW,cL],l9),cR=eb([cB],e=>e.filter(lY)),cz=eb([cB],ui),cU=eb([cz,o2],uo),cF=eb([cR,o2,lW],lV),cK=eb([cU,lW,cB],uu),cq=eb([lW],uj),cH=eb([cB],e=>e.filter(lY)),cW=eb([cF,cH,lb],ud),cV=eb([cW,o2,lq],uy),cY=eb([cB],ur),cG=eb([cU,lW,cY,um,lq],ug),cX=eb([uA,lq,lH],uE),c$=eb([cX,lq],u_),cZ=eb([uM,lq,lH],uE),cJ=eb([cZ,lq],uN),cQ=eb([uT,lq,lH],uE),c0=eb([cQ,lq],uL),c1=eb([c$,c0,cJ],uP),c2=eb([lW,cq,cV,cG,c1,r8,lq],uU),c5=eb([lW,r8,cU,cK,lb,lq,c2],uq),c3=eb([c5,lW,cN],uG),c6=eb([lW,c5,c3,lq],u$),c8=e=>{var t=lq(e),r=lH(e);return u3(e,t,r,!1)},c4=eb([lW,c8],lE),c7=eb([lW,cN,c6,c4],uY),c9=eb([r8,cK,lW,lq],cu),se=eb([r8,cK,lW,lq],cs),st=eb([r8,lW,cN,c7,c8,c9,se,lq],(e,t,r,n,i,a,o,l)=>{if(t){var{type:u}=t,c=rE(e,l);if(n){var s="scaleBand"===r&&n.bandwidth?n.bandwidth()/2:2,f="category"===u&&n.bandwidth?n.bandwidth()/s:0;return(f="angleAxis"===l&&null!=i&&(null==i?void 0:i.length)>=2?2*td(i[0]-i[1])*f:f,c&&o)?o.map((e,t)=>({coordinate:n(e)+f,value:e,index:t,offset:f})):n.domain().map((e,t)=>({coordinate:n(e)+f,value:a?a[e]:e,index:t,offset:f}))}}}),sr=eb([cm,cb,e=>e.tooltip.settings],(e,t,r)=>cx(r.shared,e,t)),sn=e=>e.tooltip.settings.trigger,si=e=>e.tooltip.settings.defaultIndex,sa=eb([cT,sr,sn,si],cA),so=eb([sa,cU],cE),sl=eb([st,so],cO),su=eb([sa],e=>{if(e)return e.dataKey}),sc=eb([cT,sr,sn,si],cM),ss=eb([rR,rz,r8,rG,st,si,sc,ck],cS),sf=eb([sa,ss],(e,t)=>null!=e&&e.coordinate?e.coordinate:t),sh=eb([sa],e=>e.active),sd=eb([sc,so,o2,lW,sl,ck,sr],cD),sp=eb([sd],e=>{if(null!=e)return Array.from(new Set(e.map(e=>e.payload).filter(e=>null!=e)))}),sy=()=>ru(lx),sv=(e,t)=>t,sg=(e,t,r)=>r,sm=(e,t,r,n)=>n,sb=eb(st,e=>(0,rc.default)(e,e=>e.coordinate)),sx=eb([cT,sv,sg,sm],cA),sw=eb([sx,cU],cE),sO=eb([cT,sv,sg,sm],cM),sj=eb([rR,rz,r8,rG,st,sm,sO,ck],cS),sP=eb([sx,sj],(e,t)=>{var r;return null!=(r=e.coordinate)?r:t}),sA=eb(st,sw,cO),sE=eb([sO,sw,o2,lW,sA,ck,sv],cD),sS=eb([sx],e=>({isActive:e.active,activeIndex:e.index})),sM=eb([(e,t)=>t,r8,lz,lq,c4,st,sb,rG],(e,t,r,n,i,a,o,l)=>{if(e&&t&&n&&i&&a){var u=function(e,t,r,n,i){return"horizontal"===r||"vertical"===r?e>=i.left&&e<=i.left+i.width&&t>=i.top&&t<=i.top+i.height?{x:e,y:t}:null:n?((e,t)=>{var r,{x:n,y:i}=e,{radius:a,angle:o}=((e,t)=>{var{x:r,y:n}=e,{cx:i,cy:a}=t,o=((e,t)=>{var{x:r,y:n}=e,{x:i,y:a}=t;return Math.sqrt((r-i)**2+(n-a)**2)})({x:r,y:n},{x:i,y:a});if(o<=0)return{radius:o,angle:0};var l=Math.acos((r-i)/o);return n>a&&(l=2*Math.PI-l),{radius:o,angle:180*l/Math.PI,angleInRadian:l}})({x:n,y:i},t),{innerRadius:l,outerRadius:u}=t;if(a<l||a>u||0===a)return null;var{startAngle:c,endAngle:s}=(e=>{var{startAngle:t,endAngle:r}=e,n=Math.min(Math.floor(t/360),Math.floor(r/360));return{startAngle:t-360*n,endAngle:r-360*n}})(t),f=o;if(c<=s){for(;f>s;)f-=360;for(;f<c;)f+=360;r=f>=c&&f<=s}else{for(;f>c;)f-=360;for(;f<s;)f+=360;r=f>=s&&f<=c}return r?rm(rm({},t),{},{radius:a,angle:((e,t)=>{var{startAngle:r,endAngle:n}=t;return e+360*Math.min(Math.floor(r/360),Math.floor(n/360))})(f,t)}):null})({x:e,y:t},n):null}(e.chartX,e.chartY,t,r,l);if(u){var c=((e,t,r,n,i)=>{var a,o=-1,l=null!=(a=null==t?void 0:t.length)?a:0;if(l<=1||null==e)return 0;if("angleAxis"===n&&null!=i&&1e-6>=Math.abs(Math.abs(i[1]-i[0])-360))for(var u=0;u<l;u++){var c=u>0?r[u-1].coordinate:r[l-1].coordinate,s=r[u].coordinate,f=u>=l-1?r[0].coordinate:r[u+1].coordinate,h=void 0;if(td(s-c)!==td(f-s)){var d=[];if(td(f-s)===td(i[1]-i[0])){h=f;var p=s+i[1]-i[0];d[0]=Math.min(p,(p+c)/2),d[1]=Math.max(p,(p+c)/2)}else{h=c;var y=f+i[1]-i[0];d[0]=Math.min(s,(y+s)/2),d[1]=Math.max(s,(y+s)/2)}var v=[Math.min(s,(h+s)/2),Math.max(s,(h+s)/2)];if(e>v[0]&&e<=v[1]||e>=d[0]&&e<=d[1]){({index:o}=r[u]);break}}else{var g=Math.min(c,f),m=Math.max(c,f);if(e>(g+s)/2&&e<=(m+s)/2){({index:o}=r[u]);break}}}else if(t){for(var b=0;b<l;b++)if(0===b&&e<=(t[b].coordinate+t[b+1].coordinate)/2||b>0&&b<l-1&&e>(t[b].coordinate+t[b-1].coordinate)/2&&e<=(t[b].coordinate+t[b+1].coordinate)/2||b===l-1&&e>(t[b].coordinate+t[b-1].coordinate)/2){({index:o}=t[b]);break}}return o})(((e,t)=>"horizontal"===t?e.x:"vertical"===t?e.y:"centric"===t?e.angle:e.radius)(u,t),o,a,n,i),s=((e,t,r,n)=>{var i=t.find(e=>e&&e.index===r);if(i){if("horizontal"===e)return{x:i.coordinate,y:n.y};if("vertical"===e)return{x:n.x,y:i.coordinate};if("centric"===e){var a=i.coordinate,{radius:o}=n;return rP(rP(rP({},n),rx(n.cx,n.cy,o,a)),{},{angle:a,radius:o})}var l=i.coordinate,{angle:u}=n;return rP(rP(rP({},n),rx(n.cx,n.cy,l,u)),{},{angle:u,radius:l})}return{x:0,y:0}})(t,a,c,u);return{activeIndex:String(c),activeCoordinate:s}}}}),sk=e=>{var t=e.currentTarget.getBoundingClientRect(),r=t.width/e.currentTarget.offsetWidth,n=t.height/e.currentTarget.offsetHeight;return{chartX:Math.round((e.clientX-t.left)/r),chartY:Math.round((e.clientY-t.top)/n)}},sT=e_("mouseClick"),sC=ts();sC.startListening({actionCreator:sT,effect:(e,t)=>{var r=e.payload,n=sM(t.getState(),sk(r));(null==n?void 0:n.activeIndex)!=null&&t.dispatch(t$({activeIndex:n.activeIndex,activeDataKey:void 0,activeCoordinate:n.activeCoordinate}))}});var s_=e_("mouseMove"),sD=ts();function sN(e,t){return t instanceof HTMLElement?"HTMLElement <".concat(t.tagName,' class="').concat(t.className,'">'):t===window?"global.window":t}function sI(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function sL(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?sI(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):sI(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}sD.startListening({actionCreator:s_,effect:(e,t)=>{var r=e.payload,n=t.getState(),i=cw(n,n.tooltip.settings.shared),a=sM(n,sk(r));"axis"===i&&((null==a?void 0:a.activeIndex)!=null?t.dispatch(tX({activeIndex:a.activeIndex,activeDataKey:void 0,activeCoordinate:a.activeCoordinate})):t.dispatch(tY()))}});var sB=eK({name:"cartesianAxis",initialState:{xAxis:{},yAxis:{},zAxis:{}},reducers:{addXAxis(e,t){e.xAxis[t.payload.id]=t.payload},removeXAxis(e,t){delete e.xAxis[t.payload.id]},addYAxis(e,t){e.yAxis[t.payload.id]=t.payload},removeYAxis(e,t){delete e.yAxis[t.payload.id]},addZAxis(e,t){e.zAxis[t.payload.id]=t.payload},removeZAxis(e,t){delete e.zAxis[t.payload.id]},updateYAxisWidth(e,t){var{id:r,width:n}=t.payload;e.yAxis[r]&&(e.yAxis[r]=sL(sL({},e.yAxis[r]),{},{width:n}))}}}),{addXAxis:sR,removeXAxis:sz,addYAxis:sU,removeYAxis:sF,addZAxis:sK,removeZAxis:sq,updateYAxisWidth:sH}=sB.actions,sW=sB.reducer,sV=eK({name:"graphicalItems",initialState:{cartesianItems:[],polarItems:[]},reducers:{addCartesianGraphicalItem(e,t){e.cartesianItems.push(t.payload)},replaceCartesianGraphicalItem(e,t){var{prev:r,next:n}=t.payload,i=Q(e).cartesianItems.indexOf(r);i>-1&&(e.cartesianItems[i]=n)},removeCartesianGraphicalItem(e,t){var r=Q(e).cartesianItems.indexOf(t.payload);r>-1&&e.cartesianItems.splice(r,1)},addPolarGraphicalItem(e,t){e.polarItems.push(t.payload)},removePolarGraphicalItem(e,t){var r=Q(e).polarItems.indexOf(t.payload);r>-1&&e.polarItems.splice(r,1)}}}),{addCartesianGraphicalItem:sY,replaceCartesianGraphicalItem:sG,removeCartesianGraphicalItem:sX,addPolarGraphicalItem:s$,removePolarGraphicalItem:sZ}=sV.actions,sJ=sV.reducer,sQ=eK({name:"referenceElements",initialState:{dots:[],areas:[],lines:[]},reducers:{addDot:(e,t)=>{e.dots.push(t.payload)},removeDot:(e,t)=>{var r=Q(e).dots.findIndex(e=>e===t.payload);-1!==r&&e.dots.splice(r,1)},addArea:(e,t)=>{e.areas.push(t.payload)},removeArea:(e,t)=>{var r=Q(e).areas.findIndex(e=>e===t.payload);-1!==r&&e.areas.splice(r,1)},addLine:(e,t)=>{e.lines.push(t.payload)},removeLine:(e,t)=>{var r=Q(e).lines.findIndex(e=>e===t.payload);-1!==r&&e.lines.splice(r,1)}}}),{addDot:s0,removeDot:s1,addArea:s2,removeArea:s5,addLine:s3,removeLine:s6}=sQ.actions,s8=sQ.reducer,s4={x:0,y:0,width:0,height:0,padding:{top:0,right:0,bottom:0,left:0}},s7=eK({name:"brush",initialState:s4,reducers:{setBrushSettings:(e,t)=>null==t.payload?s4:t.payload}}),{setBrushSettings:s9}=s7.actions,fe=s7.reducer,ft=eK({name:"legend",initialState:{settings:{layout:"horizontal",align:"center",verticalAlign:"middle",itemSorter:"value"},size:{width:0,height:0},payload:[]},reducers:{setLegendSize(e,t){e.size.width=t.payload.width,e.size.height=t.payload.height},setLegendSettings(e,t){e.settings.align=t.payload.align,e.settings.layout=t.payload.layout,e.settings.verticalAlign=t.payload.verticalAlign,e.settings.itemSorter=t.payload.itemSorter},addLegendPayload(e,t){e.payload.push(t.payload)},removeLegendPayload(e,t){var r=Q(e).payload.indexOf(t.payload);r>-1&&e.payload.splice(r,1)}}}),{setLegendSize:fr,setLegendSettings:fn,addLegendPayload:fi,removeLegendPayload:fa}=ft.actions,fo=ft.reducer,fl={accessibilityLayer:!0,barCategoryGap:"10%",barGap:4,barSize:void 0,className:void 0,maxBarSize:void 0,stackOffset:"none",syncId:void 0,syncMethod:"index"},fu=eK({name:"rootProps",initialState:fl,reducers:{updateOptions:(e,t)=>{var r;e.accessibilityLayer=t.payload.accessibilityLayer,e.barCategoryGap=t.payload.barCategoryGap,e.barGap=null!=(r=t.payload.barGap)?r:fl.barGap,e.barSize=t.payload.barSize,e.maxBarSize=t.payload.maxBarSize,e.stackOffset=t.payload.stackOffset,e.syncId=t.payload.syncId,e.syncMethod=t.payload.syncMethod,e.className=t.payload.className}}}),fc=fu.reducer,{updateOptions:fs}=fu.actions,ff=eK({name:"polarAxis",initialState:{radiusAxis:{},angleAxis:{}},reducers:{addRadiusAxis(e,t){e.radiusAxis[t.payload.id]=t.payload},removeRadiusAxis(e,t){delete e.radiusAxis[t.payload.id]},addAngleAxis(e,t){e.angleAxis[t.payload.id]=t.payload},removeAngleAxis(e,t){delete e.angleAxis[t.payload.id]}}}),{addRadiusAxis:fh,removeRadiusAxis:fd,addAngleAxis:fp,removeAngleAxis:fy}=ff.actions,fv=ff.reducer,fg=eK({name:"polarOptions",initialState:null,reducers:{updatePolarOptions:(e,t)=>t.payload}}),{updatePolarOptions:fm}=fg.actions,fb=fg.reducer,fx=e_("keyDown"),fw=e_("focus"),fO=ts();fO.startListening({actionCreator:fx,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip,i=e.payload;if("ArrowRight"===i||"ArrowLeft"===i||"Enter"===i){var a=Number(cE(n,cU(r))),o=st(r);if("Enter"===i){var l=sj(r,"axis","hover",String(n.index));t.dispatch(tJ({active:!n.active,activeIndex:n.index,activeDataKey:n.dataKey,activeCoordinate:l}));return}var u=a+("ArrowRight"===i?1:-1)*("left-to-right"===cg(r)?1:-1);if(null!=o&&!(u>=o.length)&&!(u<0)){var c=sj(r,"axis","hover",String(u));t.dispatch(tJ({active:!0,activeIndex:u.toString(),activeDataKey:void 0,activeCoordinate:c}))}}}}}),fO.startListening({actionCreator:fw,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip;if(!n.active&&null==n.index){var i=sj(r,"axis","hover",String("0"));t.dispatch(tJ({activeDataKey:void 0,active:!0,activeIndex:"0",activeCoordinate:i}))}}}});var fj=e_("externalEvent"),fP=ts();fP.startListening({actionCreator:fj,effect:(e,t)=>{if(null!=e.payload.handler){var r=t.getState(),n={activeCoordinate:sf(r),activeDataKey:su(r),activeIndex:so(r),activeLabel:sl(r),activeTooltipIndex:so(r),isTooltipActive:sh(r)};e.payload.handler(n,e.payload.reactEvent)}}});var fA=eb([cT],e=>e.tooltipItemPayloads),fE=eb([fA,ck,(e,t,r)=>t,(e,t,r)=>r],(e,t,r,n)=>{var i=e.find(e=>e.settings.dataKey===n);if(null!=i){var{positions:a}=i;if(null!=a)return t(a,r)}}),fS=e_("touchMove"),fM=ts();fM.startListening({actionCreator:fS,effect:(e,t)=>{var r=e.payload,n=t.getState(),i=cw(n,n.tooltip.settings.shared);if("axis"===i){var a=sM(n,sk({clientX:r.touches[0].clientX,clientY:r.touches[0].clientY,currentTarget:r.currentTarget}));(null==a?void 0:a.activeIndex)!=null&&t.dispatch(tX({activeIndex:a.activeIndex,activeDataKey:void 0,activeCoordinate:a.activeCoordinate}))}else if("item"===i){var o,l=r.touches[0],u=document.elementFromPoint(l.clientX,l.clientY);if(!u||!u.getAttribute)return;var c=u.getAttribute(rH),s=null!=(o=u.getAttribute(rW))?o:void 0,f=fE(t.getState(),c,s);t.dispatch(tW({activeDataKey:s,activeIndex:c,activeCoordinate:f}))}}});var fk=eK({name:"errorBars",initialState:{},reducers:{addErrorBar:(e,t)=>{var{itemId:r,errorBar:n}=t.payload;e[r]||(e[r]=[]),e[r].push(n)},removeErrorBar:(e,t)=>{var{itemId:r,errorBar:n}=t.payload;e[r]&&(e[r]=e[r].filter(e=>e.dataKey!==n.dataKey||e.direction!==n.direction))}}}),{addErrorBar:fT,removeErrorBar:fC}=fk.actions,f_=eE({brush:fe,cartesianAxis:sW,chartData:t3,errorBars:fk.reducer,graphicalItems:sJ,layout:re,legend:fo,options:tM,polarAxis:fv,polarOptions:fb,referenceElements:s8,rootProps:fc,tooltip:tQ}),fD=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Chart";return function(e){let t,r,n,i=function(e){let{thunk:t=!0,immutableCheck:r=!0,serializableCheck:n=!0,actionCreatorCheck:i=!0}=null!=e?e:{},a=new eD;return t&&("boolean"==typeof t?a.push(eT):a.push(ek(t.extraArgument))),a},{reducer:a,middleware:o,devTools:l=!0,duplicateMiddlewareCheck:u=!0,preloadedState:c,enhancers:s}=e||{};if("function"==typeof a)t=a;else if(eA(a))t=eE(a);else throw Error(tf(1));r="function"==typeof o?o(i):i();let f=eS;l&&(f=eC({trace:!1,..."object"==typeof l&&l}));let h=(n=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return e=>(r,n)=>{let i=e(r,n),a=()=>{throw Error(ew(15))},o={getState:i.getState,dispatch:function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return a(e,...r)}};return a=eS(...t.map(e=>e(o)))(i.dispatch),{...i,dispatch:a}}}(...r),function(e){let{autoBatch:t=!0}=null!=e?e:{},r=new eD(n);return t&&r.push(eB("object"==typeof t?t:void 0)),r});return function e(t,r,n){if("function"!=typeof t)throw Error(ew(2));if("function"==typeof r&&"function"==typeof n||"function"==typeof n&&"function"==typeof arguments[3])throw Error(ew(0));if("function"==typeof r&&void 0===n&&(n=r,r=void 0),void 0!==n){if("function"!=typeof n)throw Error(ew(1));return n(e)(t,r)}let i=t,a=r,o=new Map,l=o,u=0,c=!1;function s(){l===o&&(l=new Map,o.forEach((e,t)=>{l.set(t,e)}))}function f(){if(c)throw Error(ew(3));return a}function h(e){if("function"!=typeof e)throw Error(ew(4));if(c)throw Error(ew(5));let t=!0;s();let r=u++;return l.set(r,e),function(){if(t){if(c)throw Error(ew(6));t=!1,s(),l.delete(r),o=null}}}function d(e){if(!eA(e))throw Error(ew(7));if(void 0===e.type)throw Error(ew(8));if("string"!=typeof e.type)throw Error(ew(17));if(c)throw Error(ew(9));try{c=!0,a=i(a,e)}finally{c=!1}return(o=l).forEach(e=>{e()}),e}return d({type:eP.INIT}),{dispatch:d,subscribe:h,getState:f,replaceReducer:function(e){if("function"!=typeof e)throw Error(ew(10));i=e,d({type:eP.REPLACE})},[eO]:function(){return{subscribe(e){if("object"!=typeof e||null===e)throw Error(ew(11));function t(){e.next&&e.next(f())}return t(),{unsubscribe:h(t)}},[eO](){return this}}}}}(t,c,f(..."function"==typeof s?s(h):h()))}({reducer:f_,preloadedState:e,middleware:e=>e({serializableCheck:!1}).concat([sC.middleware,sD.middleware,fO.middleware,fP.middleware,fM.middleware]),devTools:{serialize:{replacer:sN},name:"recharts-".concat(t)}})};function fN(e){var{preloadedState:t,children:r,reduxStoreName:n}=e,i=rJ(),a=(0,d.useRef)(null);return i?r:(null==a.current&&(a.current=fD(t,n)),d.createElement(tB,{context:rr,store:a.current},r))}var fI=e=>{var{chartData:t}=e,r=ri(),n=rJ();return(0,d.useEffect)(()=>n?()=>{}:(r(t1(t)),()=>{r(t1(void 0))}),[t,r,n]),null};function fL(e){var{layout:t,width:r,height:n,margin:i}=e,a=ri(),o=rJ();return(0,d.useEffect)(()=>{o||(a(t4(t)),a(t7({width:r,height:n})),a(t8(i)))},[a,o,t,r,n,i]),null}function fB(e){var t=ri();return(0,d.useEffect)(()=>{t(fs(e))},[t,e]),null}var fR=()=>ru(e=>e.rootProps.accessibilityLayer),fz=e.i(7670),fU=e.i(79684),fF=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"];function fK(e){return"string"==typeof e&&fF.includes(e)}var fq=["points","pathLength"],fH={svg:["viewBox","children"],polygon:fq,polyline:fq},fW=(e,t)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var r=e;if((0,d.isValidElement)(e)&&(r=e.props),"object"!=typeof r&&"function"!=typeof r)return null;var n={};return Object.keys(r).forEach(e=>{fK(e)&&(n[e]=t||(t=>r[e](r,t)))}),n},fV=(e,t,r)=>{if(null===e||"object"!=typeof e&&"function"!=typeof e)return null;var n=null;return Object.keys(e).forEach(i=>{var a=e[i];fK(i)&&"function"==typeof a&&(n||(n={}),n[i]=e=>(a(t,r,e),null))}),n},fY=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"];function fG(e){return"string"==typeof e&&fY.includes(e)}function fX(e){return Object.fromEntries(Object.entries(e).filter(e=>{var[t]=e;return fG(t)}))}var f$=e=>"string"==typeof e?e:e?e.displayName||e.name||"Component":"",fZ=null,fJ=null,fQ=e=>{if(e===fZ&&Array.isArray(fJ))return fJ;var t=[];return d.Children.forEach(e,e=>{null==e||((0,fU.isFragment)(e)?t=t.concat(fQ(e.props.children)):t.push(e))}),fJ=t,fZ=e,t};function f0(e,t){var r=[],n=[];return n=Array.isArray(t)?t.map(e=>f$(e)):[f$(t)],fQ(e).forEach(e=>{var t=(0,th.default)(e,"type.displayName")||(0,th.default)(e,"type.name");-1!==n.indexOf(t)&&r.push(e)}),r}var f1=e=>!e||"object"!=typeof e||!("clipDot"in e)||!!e.clipDot,f2=(e,t,r)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var n=e;if((0,d.isValidElement)(e)&&(n=e.props),"object"!=typeof n&&"function"!=typeof n)return null;var i={};return Object.keys(n).forEach(e=>{var a;((e,t,r,n)=>{if("symbol"==typeof t||"number"==typeof t)return!0;var i,a=null!=(i=n&&(null==fH?void 0:fH[n]))?i:[],o=t.startsWith("data-"),l="function"!=typeof e&&(!!n&&a.includes(t)||fG(t)),u=!!r&&fK(t);return o||l||u})(null==(a=n)?void 0:a[e],e,t,r)&&(i[e]=n[e])}),i},f5=["children","width","height","viewBox","className","style","title","desc"];function f3(){return(f3=Object.assign.bind()).apply(null,arguments)}var f6=(0,d.forwardRef)((e,t)=>{var{children:r,width:n,height:i,viewBox:a,className:o,style:l,title:u,desc:c}=e,s=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,f5),f=a||{width:n,height:i,x:0,y:0},h=(0,fz.clsx)("recharts-surface",o);return d.createElement("svg",f3({},f2(s,!0,"svg"),{className:h,width:n,height:i,style:l,viewBox:"".concat(f.x," ").concat(f.y," ").concat(f.width," ").concat(f.height),ref:t}),d.createElement("title",null,u),d.createElement("desc",null,c),r)}),f8=["children"];function f4(){return(f4=Object.assign.bind()).apply(null,arguments)}var f7={width:"100%",height:"100%"},f9=(0,d.forwardRef)((e,t)=>{var r,n,i=r3(),a=r6(),o=fR();if(!o8(i)||!o8(a))return null;var{children:l,otherAttributes:u,title:c,desc:s}=e;return r="number"==typeof u.tabIndex?u.tabIndex:o?0:void 0,n="string"==typeof u.role?u.role:o?"application":void 0,d.createElement(f6,f4({},u,{title:c,desc:s,role:n,tabIndex:r,width:i,height:a,style:f7,ref:t}),l)}),he=e=>{var{children:t}=e,r=ru(r0);if(!r)return null;var{width:n,height:i,y:a,x:o}=r;return d.createElement(f6,{width:n,height:i,x:o,y:a},t)},ht=(0,d.forwardRef)((e,t)=>{var{children:r}=e,n=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,f8);return rJ()?d.createElement(he,null,r):d.createElement(f9,f4({ref:t},n),r)}),hr=new(e.i(78492)).default,hn="recharts.syncEvent.tooltip",hi="recharts.syncEvent.brush";function ha(e){return e.tooltip.syncInteraction}var ho=()=>{},hl=(0,d.createContext)(null),hu=(0,d.createContext)(null);function hc(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var hs=(0,d.forwardRef)((e,t)=>{var{children:r,className:n,height:i,onClick:a,onContextMenu:o,onDoubleClick:l,onMouseDown:u,onMouseEnter:c,onMouseLeave:s,onMouseMove:f,onMouseUp:h,onTouchEnd:p,onTouchMove:y,onTouchStart:v,style:g,width:m}=e,b=ri(),[x,w]=(0,d.useState)(null),[O,j]=(0,d.useState)(null);!function(){var e,t,r,n,i,a,o,l,u,c,s,f=ri();(0,d.useEffect)(()=>{f(tk())},[f]),e=ru(lw),t=ru(lj),r=ri(),n=ru(lO),i=ru(st),a=r4(),o=r1(),l=ru(e=>e.rootProps.className),(0,d.useEffect)(()=>{if(null==e)return ho;var l=(l,u,c)=>{if(t!==c&&e===l){if("index"===n)return void r(u);if(null!=i){if("function"==typeof n){var s,f=n(i,{activeTooltipIndex:null==u.payload.index?void 0:Number(u.payload.index),isTooltipActive:u.payload.active,activeIndex:null==u.payload.index?void 0:Number(u.payload.index),activeLabel:u.payload.label,activeDataKey:u.payload.dataKey,activeCoordinate:u.payload.coordinate});s=i[f]}else"value"===n&&(s=i.find(e=>String(e.value)===u.payload.label));var{coordinate:h}=u.payload;if(null==s||!1===u.payload.active||null==h||null==o)return void r(tZ({active:!1,coordinate:void 0,dataKey:void 0,index:null,label:void 0}));var{x:d,y:p}=h,y=Math.min(d,o.x+o.width),v=Math.min(p,o.y+o.height),g={x:"horizontal"===a?s.coordinate:y,y:"horizontal"===a?v:s.coordinate};r(tZ({active:u.payload.active,coordinate:g,dataKey:u.payload.dataKey,index:String(s.index),label:u.payload.label}))}}};return hr.on(hn,l),()=>{hr.off(hn,l)}},[l,r,t,e,n,i,a,o]),u=ru(lw),c=ru(lj),s=ri(),(0,d.useEffect)(()=>{if(null==u)return ho;var e=(e,t,r)=>{c!==r&&u===e&&s(t2(t))};return hr.on(hi,e),()=>{hr.off(hi,e)}},[s,c,u])}();var P=function(){var e=ri(),[t,r]=(0,d.useState)(null),n=ru(rU);return(0,d.useEffect)(()=>{if(null!=t){var r=t.getBoundingClientRect().width/t.offsetWidth;o6(r)&&r!==n&&e(t9(r))}},[t,e,n]),r}(),A=(0,d.useCallback)(e=>{P(e),"function"==typeof t&&t(e),w(e),j(e)},[P,t,w,j]),E=(0,d.useCallback)(e=>{b(sT(e)),b(fj({handler:a,reactEvent:e}))},[b,a]),S=(0,d.useCallback)(e=>{b(s_(e)),b(fj({handler:c,reactEvent:e}))},[b,c]),M=(0,d.useCallback)(e=>{b(tY()),b(fj({handler:s,reactEvent:e}))},[b,s]),k=(0,d.useCallback)(e=>{b(s_(e)),b(fj({handler:f,reactEvent:e}))},[b,f]),T=(0,d.useCallback)(()=>{b(fw())},[b]),C=(0,d.useCallback)(e=>{b(fx(e.key))},[b]),_=(0,d.useCallback)(e=>{b(fj({handler:o,reactEvent:e}))},[b,o]),D=(0,d.useCallback)(e=>{b(fj({handler:l,reactEvent:e}))},[b,l]),N=(0,d.useCallback)(e=>{b(fj({handler:u,reactEvent:e}))},[b,u]),I=(0,d.useCallback)(e=>{b(fj({handler:h,reactEvent:e}))},[b,h]),L=(0,d.useCallback)(e=>{b(fj({handler:v,reactEvent:e}))},[b,v]),B=(0,d.useCallback)(e=>{b(fS(e)),b(fj({handler:y,reactEvent:e}))},[b,y]),R=(0,d.useCallback)(e=>{b(fj({handler:p,reactEvent:e}))},[b,p]);return d.createElement(hl.Provider,{value:x},d.createElement(hu.Provider,{value:O},d.createElement("div",{className:(0,fz.clsx)("recharts-wrapper",n),style:function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?hc(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hc(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({position:"relative",cursor:"default",width:m,height:i},g),onClick:E,onContextMenu:_,onDoubleClick:D,onFocus:T,onKeyDown:C,onMouseDown:N,onMouseEnter:S,onMouseLeave:M,onMouseMove:k,onMouseUp:I,onTouchEnd:R,onTouchMove:B,onTouchStart:L,ref:A},r)))}),hf=eb([rG],e=>{if(e)return{top:e.top,bottom:e.bottom,left:e.left,right:e.right}}),hh=eb([hf,rR,rz],(e,t,r)=>{if(e&&null!=t&&null!=r)return{x:e.left,y:e.top,width:Math.max(0,t-e.left-e.right),height:Math.max(0,r-e.top-e.bottom)}}),hd=()=>ru(hh),hp=(0,d.createContext)(void 0),hy=e=>{var{children:t}=e,[r]=(0,d.useState)("".concat(tb("recharts"),"-clip")),n=hd();if(null==n)return null;var{x:i,y:a,width:o,height:l}=n;return d.createElement(hp.Provider,{value:r},d.createElement("defs",null,d.createElement("clipPath",{id:r},d.createElement("rect",{x:i,y:a,height:l,width:o}))),t)},hv=["children","className","width","height","style","compact","title","desc"],hg=(0,d.forwardRef)((e,t)=>{var{children:r,className:n,width:i,height:a,style:o,compact:l,title:u,desc:c}=e,s=fX(function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,hv));return l?d.createElement(ht,{otherAttributes:s,title:u,desc:c},r):d.createElement(hs,{className:n,style:o,width:i,height:a,onClick:e.onClick,onMouseLeave:e.onMouseLeave,onMouseEnter:e.onMouseEnter,onMouseMove:e.onMouseMove,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onContextMenu:e.onContextMenu,onDoubleClick:e.onDoubleClick,onTouchStart:e.onTouchStart,onTouchMove:e.onTouchMove,onTouchEnd:e.onTouchEnd},d.createElement(ht,{otherAttributes:s,title:u,desc:c,ref:t},d.createElement(hy,null,r)))});function hm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function hb(e,t){var r=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?hm(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hm(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e);return Object.keys(t).reduce((e,r)=>(void 0===e[r]&&void 0!==t[r]&&(e[r]=t[r]),e),r)}var hx=["width","height"];function hw(){return(hw=Object.assign.bind()).apply(null,arguments)}var hO={accessibilityLayer:!0,layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},hj=(0,d.forwardRef)(function(e,t){var r,n=hb(e.categoricalChartProps,hO),{width:i,height:a}=n,o=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(n,hx);if(!o8(i)||!o8(a))return null;var{chartName:l,defaultTooltipEventType:u,validateTooltipEventTypes:c,tooltipPayloadSearcher:s,categoricalChartProps:f}=e;return d.createElement(fN,{preloadedState:{options:{chartName:l,defaultTooltipEventType:u,validateTooltipEventTypes:c,tooltipPayloadSearcher:s,eventEmitter:void 0}},reduxStoreName:null!=(r=f.id)?r:l},d.createElement(fI,{chartData:f.data}),d.createElement(fL,{width:i,height:a,layout:n.layout,margin:n.margin}),d.createElement(fB,{accessibilityLayer:n.accessibilityLayer,barCategoryGap:n.barCategoryGap,maxBarSize:n.maxBarSize,stackOffset:n.stackOffset,barGap:n.barGap,barSize:n.barSize,syncId:n.syncId,syncMethod:n.syncMethod,className:n.className}),d.createElement(hg,hw({},o,{width:i,height:a,ref:t})))}),hP=["axis","item"],hA=(0,d.forwardRef)((e,t)=>d.createElement(hj,{chartName:"BarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:hP,tooltipPayloadSearcher:tE,categoricalChartProps:e,ref:t})),hE=d,hS=["children","className"];function hM(){return(hM=Object.assign.bind()).apply(null,arguments)}var hk=d.forwardRef((e,t)=>{var{children:r,className:n}=e,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,hS),a=(0,fz.clsx)("recharts-layer",n);return d.createElement("g",hM({className:a},f2(i,!0),{ref:t}),r)}),hT=e=>null;hT.displayName="Cell";var hC=e.i(4178),h_={isSsr:!("undefined"!=typeof window&&window.document&&window.document.createElement&&window.setTimeout)};function hD(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var hN=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?hD(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hD(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},{cacheSize:2e3,enableCache:!0}),hI=new class{get(e){var t=this.cache.get(e);return void 0!==t&&(this.cache.delete(e),this.cache.set(e,t)),t}set(e,t){if(this.cache.has(e))this.cache.delete(e);else if(this.cache.size>=this.maxSize){var r=this.cache.keys().next().value;this.cache.delete(r)}this.cache.set(e,t)}clear(){this.cache.clear()}size(){return this.cache.size}constructor(e){!function(e,t,r){var n;(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(this,"cache",new Map),this.maxSize=e}}(hN.cacheSize),hL={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},hB="recharts_measurement_span",hR=(e,t)=>{try{var r=document.getElementById(hB);r||((r=document.createElement("span")).setAttribute("id",hB),r.setAttribute("aria-hidden","true"),document.body.appendChild(r)),Object.assign(r.style,hL,t),r.textContent="".concat(e);var n=r.getBoundingClientRect();return{width:n.width,height:n.height}}catch(e){return{width:0,height:0}}},hz=function(e){var t,r,n,i,a,o,l=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==e||h_.isSsr)return{width:0,height:0};if(!hN.enableCache)return hR(e,l);var u=(t=l.fontSize||"",r=l.fontFamily||"",n=l.fontWeight||"",i=l.fontStyle||"",a=l.letterSpacing||"",o=l.textTransform||"","".concat(e,"|").concat(t,"|").concat(r,"|").concat(n,"|").concat(i,"|").concat(a,"|").concat(o)),c=hI.get(u);if(c)return c;var s=hR(e,l);return hI.set(u,s),s},hU=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,hF=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,hK=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,hq=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,hH={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},hW=Object.keys(hH);class hV{static parse(e){var t,[,r,n]=null!=(t=hq.exec(e))?t:[];return new hV(parseFloat(r),null!=n?n:"")}add(e){return this.unit!==e.unit?new hV(NaN,""):new hV(this.num+e.num,this.unit)}subtract(e){return this.unit!==e.unit?new hV(NaN,""):new hV(this.num-e.num,this.unit)}multiply(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new hV(NaN,""):new hV(this.num*e.num,this.unit||e.unit)}divide(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new hV(NaN,""):new hV(this.num/e.num,this.unit||e.unit)}toString(){return"".concat(this.num).concat(this.unit)}isNaN(){return tp(this.num)}constructor(e,t){this.num=e,this.unit=t,this.num=e,this.unit=t,tp(e)&&(this.unit=""),""===t||hK.test(t)||(this.num=NaN,this.unit=""),hW.includes(t)&&(this.num=e*hH[t],this.unit="px")}}function hY(e){if(e.includes("NaN"))return"NaN";for(var t=e;t.includes("*")||t.includes("/");){var r,[,n,i,a]=null!=(r=hU.exec(t))?r:[],o=hV.parse(null!=n?n:""),l=hV.parse(null!=a?a:""),u="*"===i?o.multiply(l):o.divide(l);if(u.isNaN())return"NaN";t=t.replace(hU,u.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var c,[,s,f,h]=null!=(c=hF.exec(t))?c:[],d=hV.parse(null!=s?s:""),p=hV.parse(null!=h?h:""),y="+"===f?d.add(p):d.subtract(p);if(y.isNaN())return"NaN";t=t.replace(hF,y.toString())}return t}var hG=/\(([^()]*)\)/;function hX(e){var t=function(e){try{var t;return t=e.replace(/\s+/g,""),t=function(e){for(var t,r=e;null!=(t=hG.exec(r));){var[,n]=t;r=r.replace(hG,hY(n))}return r}(t),t=hY(t)}catch(e){return"NaN"}}(e.slice(5,-1));return"NaN"===t?"":t}var h$=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],hZ=["dx","dy","angle","className","breakAll"];function hJ(){return(hJ=Object.assign.bind()).apply(null,arguments)}function hQ(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var h0=/[ \f\n\r\t\v\u2028\u2029]+/,h1=e=>{var{children:t,breakAll:r,style:n}=e;try{let e;var i=[];e=t,null==e||(i=r?t.toString().split(""):t.toString().split(h0));var a=i.map(e=>({word:e,width:hz(e,n).width})),o=r?0:hz(" ",n).width;return{wordsWithComputedWidth:a,spaceWidth:o}}catch(e){return null}},h2=e=>[{words:null==e?[]:e.toString().split(h0)}],h5="#808080",h3=(0,d.forwardRef)((e,t)=>{var r,{x:n=0,y:i=0,lineHeight:a="1em",capHeight:o="0.71em",scaleToFit:l=!1,textAnchor:u="start",verticalAnchor:c="end",fill:s=h5}=e,f=hQ(e,h$),h=(0,d.useMemo)(()=>(e=>{var{width:t,scaleToFit:r,children:n,style:i,breakAll:a,maxLines:o}=e;if((t||r)&&!h_.isSsr){var l=h1({breakAll:a,children:n,style:i});if(!l)return h2(n);var{wordsWithComputedWidth:u,spaceWidth:c}=l;return((e,t,r,n,i)=>{var a,{maxLines:o,children:l,style:u,breakAll:c}=e,s=tv(o),f=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.reduce((e,t)=>{var{word:a,width:o}=t,l=e[e.length-1];return l&&(null==n||i||l.width+o+r<Number(n))?(l.words.push(a),l.width+=o+r):e.push({words:[a],width:o}),e},[])},h=f(t),d=e=>e.reduce((e,t)=>e.width>t.width?e:t);if(!s||i||!(h.length>o||d(h).width>Number(n)))return h;for(var p=e=>{var t=f(h1({breakAll:c,style:u,children:l.slice(0,e)+"…"}).wordsWithComputedWidth);return[t.length>o||d(t).width>Number(n),t]},y=0,v=l.length-1,g=0;y<=v&&g<=l.length-1;){var m=Math.floor((y+v)/2),[b,x]=p(m-1),[w]=p(m);if(b||w||(y=m+1),b&&w&&(v=m-1),!b&&w){a=x;break}g++}return a||h})({breakAll:a,children:n,maxLines:o,style:i},u,c,t,r)}return h2(n)})({breakAll:f.breakAll,children:f.children,maxLines:f.maxLines,scaleToFit:l,style:f.style,width:f.width}),[f.breakAll,f.children,f.maxLines,l,f.style,f.width]),{dx:p,dy:y,angle:v,className:g,breakAll:m}=f,b=hQ(f,hZ);if(!tg(n)||!tg(i))return null;var x=n+(tv(p)?p:0),w=i+(tv(y)?y:0);switch(c){case"start":r=hX("calc(".concat(o,")"));break;case"middle":r=hX("calc(".concat((h.length-1)/2," * -").concat(a," + (").concat(o," / 2))"));break;default:r=hX("calc(".concat(h.length-1," * -").concat(a,")"))}var O=[];if(l){var j=h[0].width,{width:P}=f;O.push("scale(".concat(tv(P)?P/j:1,")"))}return v&&O.push("rotate(".concat(v,", ").concat(x,", ").concat(w,")")),O.length&&(b.transform=O.join(" ")),d.createElement("text",hJ({},f2(b,!0),{ref:t,x:x,y:w,className:(0,fz.clsx)("recharts-text",g),textAnchor:u,fill:s.includes("url")?h5:s}),h.map((e,t)=>{var n=e.words.join(m?"":" ");return d.createElement("tspan",{x:x,dy:0===t?r:a,key:"".concat(n,"-").concat(t)},n)}))});h3.displayName="Text";var h6=["offset"],h8=["labelRef"];function h4(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function h7(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function h9(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?h7(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):h7(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function de(){return(de=Object.assign.bind()).apply(null,arguments)}var dt=e=>null!=e&&"function"==typeof e;function dr(e){var t,{offset:r=5}=e,n=h9({offset:r},h4(e,h6)),{viewBox:i,position:a,value:o,children:l,content:u,className:c="",textBreakAll:s,labelRef:f}=n,h=ru(lz),p=r1(),y=i||("center"===a?p:null!=h?h:p);if(!y||null==o&&null==l&&!(0,d.isValidElement)(u)&&"function"!=typeof u)return null;var v=h9(h9({},n),{},{viewBox:y});if((0,d.isValidElement)(u)){var{labelRef:g}=v,m=h4(v,h8);return(0,d.cloneElement)(u,m)}if("function"==typeof u){if(t=(0,d.createElement)(u,v),(0,d.isValidElement)(t))return t}else t=(e=>{var{value:t,formatter:r}=e,n=null==e.children?t:e.children;return"function"==typeof r?r(n):n})(n);var b="cx"in y&&tv(y.cx),x=f2(n,!0);if(b&&("insideStart"===a||"insideEnd"===a||"end"===a))return((e,t,r,n)=>{let i,a;var o,l,{position:u,offset:c,className:s}=e,{cx:f,cy:h,innerRadius:p,outerRadius:y,startAngle:v,endAngle:g,clockWise:m}=n,b=(p+y)/2,x=(i=v,td((a=g)-i)*Math.min(Math.abs(a-i),360)),w=x>=0?1:-1;"insideStart"===u?(o=v+w*c,l=m):"insideEnd"===u?(o=g-w*c,l=!m):"end"===u&&(o=g+w*c,l=m),l=x<=0?l:!l;var O=rx(f,h,b,o),j=rx(f,h,b,o+(l?1:-1)*359),P="M".concat(O.x,",").concat(O.y,"\n    A").concat(b,",").concat(b,",0,1,").concat(+!l,",\n    ").concat(j.x,",").concat(j.y),A=null==e.id?tb("recharts-radial-line-"):e.id;return d.createElement("text",de({},r,{dominantBaseline:"central",className:(0,fz.clsx)("recharts-radial-bar-label",s)}),d.createElement("defs",null,d.createElement("path",{id:A,d:P})),d.createElement("textPath",{xlinkHref:"#".concat(A)},t))})(n,t,x,y);var w=b?((e,t,r)=>{var{cx:n,cy:i,innerRadius:a,outerRadius:o,startAngle:l,endAngle:u}=e,c=(l+u)/2;if("outside"===r){var{x:s,y:f}=rx(n,i,o+t,c);return{x:s,y:f,textAnchor:s>=n?"start":"end",verticalAnchor:"middle"}}if("center"===r)return{x:n,y:i,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===r)return{x:n,y:i,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===r)return{x:n,y:i,textAnchor:"middle",verticalAnchor:"end"};var{x:h,y:d}=rx(n,i,(a+o)/2,c);return{x:h,y:d,textAnchor:"middle",verticalAnchor:"middle"}})(y,n.offset,n.position):((e,t)=>{var{parentViewBox:r,offset:n,position:i}=e,{x:a,y:o,width:l,height:u}=t,c=u>=0?1:-1,s=c*n,f=c>0?"end":"start",h=c>0?"start":"end",d=l>=0?1:-1,p=d*n,y=d>0?"end":"start",v=d>0?"start":"end";if("top"===i)return h9(h9({},{x:a+l/2,y:o-c*n,textAnchor:"middle",verticalAnchor:f}),r?{height:Math.max(o-r.y,0),width:l}:{});if("bottom"===i)return h9(h9({},{x:a+l/2,y:o+u+s,textAnchor:"middle",verticalAnchor:h}),r?{height:Math.max(r.y+r.height-(o+u),0),width:l}:{});if("left"===i){var g={x:a-p,y:o+u/2,textAnchor:y,verticalAnchor:"middle"};return h9(h9({},g),r?{width:Math.max(g.x-r.x,0),height:u}:{})}if("right"===i){var m={x:a+l+p,y:o+u/2,textAnchor:v,verticalAnchor:"middle"};return h9(h9({},m),r?{width:Math.max(r.x+r.width-m.x,0),height:u}:{})}var b=r?{width:l,height:u}:{};return"insideLeft"===i?h9({x:a+p,y:o+u/2,textAnchor:v,verticalAnchor:"middle"},b):"insideRight"===i?h9({x:a+l-p,y:o+u/2,textAnchor:y,verticalAnchor:"middle"},b):"insideTop"===i?h9({x:a+l/2,y:o+s,textAnchor:"middle",verticalAnchor:h},b):"insideBottom"===i?h9({x:a+l/2,y:o+u-s,textAnchor:"middle",verticalAnchor:f},b):"insideTopLeft"===i?h9({x:a+p,y:o+s,textAnchor:v,verticalAnchor:h},b):"insideTopRight"===i?h9({x:a+l-p,y:o+s,textAnchor:y,verticalAnchor:h},b):"insideBottomLeft"===i?h9({x:a+p,y:o+u-s,textAnchor:v,verticalAnchor:f},b):"insideBottomRight"===i?h9({x:a+l-p,y:o+u-s,textAnchor:y,verticalAnchor:f},b):i&&"object"==typeof i&&(tv(i.x)||ty(i.x))&&(tv(i.y)||ty(i.y))?h9({x:a+tx(i.x,l),y:o+tx(i.y,u),textAnchor:"end",verticalAnchor:"end"},b):h9({x:a+l/2,y:o+u/2,textAnchor:"middle",verticalAnchor:"middle"},b)})(n,y);return d.createElement(h3,de({ref:f,className:(0,fz.clsx)("recharts-label",c)},x,w,{breakAll:s}),t)}dr.displayName="Label";var dn=e=>{var{cx:t,cy:r,angle:n,startAngle:i,endAngle:a,r:o,radius:l,innerRadius:u,outerRadius:c,x:s,y:f,top:h,left:d,width:p,height:y,clockWise:v,labelViewBox:g}=e;if(g)return g;if(tv(p)&&tv(y)){if(tv(s)&&tv(f))return{x:s,y:f,width:p,height:y};if(tv(h)&&tv(d))return{x:h,y:d,width:p,height:y}}return tv(s)&&tv(f)?{x:s,y:f,width:0,height:0}:tv(t)&&tv(r)?{cx:t,cy:r,startAngle:i||n||0,endAngle:a||n||0,innerRadius:u||0,outerRadius:c||l||o||0,clockWise:v}:e.viewBox?e.viewBox:void 0};dr.parseViewBox=dn,dr.renderCallByParent=function(e,t){var r=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&r&&!e.label)return null;var{children:n,labelRef:i}=e,a=dn(e),o=f0(n,dr).map((e,r)=>(0,d.cloneElement)(e,{viewBox:t||a,key:"label-".concat(r)}));return r?[((e,t,r)=>{if(!e)return null;var n={viewBox:t,labelRef:r};return!0===e?d.createElement(dr,de({key:"label-implicit"},n)):tg(e)?d.createElement(dr,de({key:"label-implicit",value:e},n)):(0,d.isValidElement)(e)?e.type===dr?(0,d.cloneElement)(e,h9({key:"label-implicit"},n)):d.createElement(dr,de({key:"label-implicit",content:e},n)):dt(e)?d.createElement(dr,de({key:"label-implicit",content:e},n)):e&&"object"==typeof e?d.createElement(dr,de({},e,{key:"label-implicit"},n)):null})(e.label,t||a,i),...o]:o};var di=["valueAccessor"],da=["data","dataKey","clockWise","id","textBreakAll"];function dl(){return(dl=Object.assign.bind()).apply(null,arguments)}function du(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function dc(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?du(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):du(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ds(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var df=e=>Array.isArray(e.value)?(0,hC.default)(e.value):e.value;function dh(e){var{valueAccessor:t=df}=e,r=ds(e,di),{data:n,dataKey:i,clockWise:a,id:o,textBreakAll:l}=r,u=ds(r,da);return n&&n.length?d.createElement(hk,{className:"recharts-label-list"},n.map((e,r)=>{var n=null==i?t(e,r):rA(e&&e.payload,i),c=null==o?{}:{id:"".concat(o,"-").concat(r)};return d.createElement(dr,dl({},f2(e,!0),u,c,{parentViewBox:e.parentViewBox,value:n,textBreakAll:l,viewBox:dr.parseViewBox(null==a?e:dc(dc({},e),{},{clockWise:a})),key:"label-".concat(r),index:r}))})):null}dh.displayName="LabelList",dh.renderCallByParent=function(e,t){var r,n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&n&&!e.label)return null;var{children:i}=e,a=f0(i,dh).map((e,r)=>(0,d.cloneElement)(e,{data:t,key:"labelList-".concat(r)}));return n?[(r=e.label,r?!0===r?d.createElement(dh,{key:"labelList-implicit",data:t}):d.isValidElement(r)||dt(r)?d.createElement(dh,{key:"labelList-implicit",data:t,content:r}):"object"==typeof r?d.createElement(dh,dl({data:t},r,{key:"labelList-implicit"})):null:null),...a]:a};var dd=e.i(7328),dp=d,dy=e.i(13287),dv=(e,t)=>[0,3*e,3*t-6*e,3*e-3*t+1],dg=(e,t)=>e.map((e,r)=>e*t**r).reduce((e,t)=>e+t),dm=(e,t)=>r=>dg(dv(e,t),r),db=function(){let e,t;for(var r,n,i,a,o=arguments.length,l=Array(o),u=0;u<o;u++)l[u]=arguments[u];if(1===l.length)switch(l[0]){case"linear":[r,i,n,a]=[0,0,1,1];break;case"ease":[r,i,n,a]=[.25,.1,.25,1];break;case"ease-in":[r,i,n,a]=[.42,0,1,1];break;case"ease-out":[r,i,n,a]=[.42,0,.58,1];break;case"ease-in-out":[r,i,n,a]=[0,0,.58,1];break;default:var c=l[0].split("(");"cubic-bezier"===c[0]&&4===c[1].split(")")[0].split(",").length&&([r,i,n,a]=c[1].split(")")[0].split(",").map(e=>parseFloat(e)))}else 4===l.length&&([r,i,n,a]=l);var s=dm(r,n),f=dm(i,a),h=(e=r,t=n,r=>dg([...dv(e,t).map((e,t)=>e*t).slice(1),0],r)),d=e=>e>1?1:e<0?0:e,p=e=>{for(var t=e>1?1:e,r=t,n=0;n<8;++n){var i=s(r)-t,a=h(r);if(1e-4>Math.abs(i-t)||a<1e-4)break;r=d(r-i/a)}return f(r)};return p.isStepper=!1,p},dx=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{stiff:t=100,damping:r=8,dt:n=17}=e,i=(e,i,a)=>{var o=a+(-(e-i)*t-a*r)*n/1e3,l=a*n/1e3+e;return 1e-4>Math.abs(l-i)&&1e-4>Math.abs(o)?[i,0]:[l,o]};return i.isStepper=!0,i.dt=n,i},dw=e=>{if("string"==typeof e)switch(e){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return db(e);case"spring":return dx();default:if("cubic-bezier"===e.split("(")[0])return db(e)}return"function"==typeof e?e:null};function dO(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function dj(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?dO(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):dO(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var dP=(e,t)=>Object.keys(t).reduce((r,n)=>dj(dj({},r),{},{[n]:e(n,t[n])}),{});function dA(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function dE(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?dA(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):dA(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var dS=(e,t,r)=>e+(t-e)*r,dM=e=>{var{from:t,to:r}=e;return t!==r},dk=(e,t,r)=>{var n=dP((t,r)=>{if(dM(r)){var[n,i]=e(r.from,r.to,r.velocity);return dE(dE({},r),{},{from:n,velocity:i})}return r},t);return r<1?dP((e,t)=>dM(t)?dE(dE({},t),{},{velocity:dS(t.velocity,n[e].velocity,r),from:dS(t.from,n[e].from,r)}):t,t):dk(e,n,r-1)};let dT=(e,t,r,n,i,a)=>{var o=[Object.keys(e),Object.keys(t)].reduce((e,t)=>e.filter(e=>t.includes(e)));return!0===r.isStepper?function(e,t,r,n,i,a){var o,l=n.reduce((r,n)=>dE(dE({},r),{},{[n]:{from:e[n],velocity:0,to:t[n]}}),{}),u=null,c=n=>{o||(o=n);var s=(n-o)/r.dt;l=dk(r,l,s),i(dE(dE(dE({},e),t),dP((e,t)=>t.from,l))),o=n,Object.values(l).filter(dM).length&&(u=a.setTimeout(c))};return()=>(u=a.setTimeout(c),()=>{u()})}(e,t,r,o,i,a):function(e,t,r,n,i,a,o){var l,u=null,c=i.reduce((r,n)=>dE(dE({},r),{},{[n]:[e[n],t[n]]}),{}),s=i=>{l||(l=i);var f=(i-l)/n,h=dP((e,t)=>dS(...t,r(f)),c);if(a(dE(dE(dE({},e),t),h)),f<1)u=o.setTimeout(s);else{var d=dP((e,t)=>dS(...t,r(1)),c);a(dE(dE(dE({},e),t),d))}};return()=>(u=o.setTimeout(s),()=>{u()})}(e,t,r,n,o,i,a)};class dC{setTimeout(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=performance.now(),n=null,i=a=>{a-r>=t?e(a):"function"==typeof requestAnimationFrame&&(n=requestAnimationFrame(i))};return n=requestAnimationFrame(i),()=>{cancelAnimationFrame(n)}}}var d_=(0,d.createContext)(function(){var e,t,r,n,i;return e=new dC,t=()=>null,r=!1,n=null,i=a=>{if(!r){if(Array.isArray(a)){if(!a.length)return;var[o,...l]=a;if("number"==typeof o){n=e.setTimeout(i.bind(null,l),o);return}i(o),n=e.setTimeout(i.bind(null,l));return}"string"==typeof a&&t(a),"object"==typeof a&&t(a),"function"==typeof a&&a()}},{stop:()=>{r=!0},start:e=>{r=!1,n&&(n(),n=null),i(e)},subscribe:e=>(t=e,()=>{t=()=>null}),getTimeoutController:()=>e}});function dD(e,t){var r=(0,d.useContext)(d_);return(0,d.useMemo)(()=>null!=t?t:r(e),[e,t,r])}var dN=["children","begin","duration","attributeName","easing","isActive","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart","animationManager"];function dI(){return(dI=Object.assign.bind()).apply(null,arguments)}function dL(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function dB(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?dL(Object(r),!0).forEach(function(t){dR(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):dL(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function dR(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class dz extends dp.PureComponent{componentDidMount(){var{isActive:e,canBegin:t}=this.props;this.mounted=!0,e&&t&&this.runAnimation(this.props)}componentDidUpdate(e){var{isActive:t,canBegin:r,attributeName:n,shouldReAnimate:i,to:a,from:o}=this.props,{style:l}=this.state;if(r){if(!t){this.state&&l&&(n&&l[n]!==a||!n&&l!==a)&&this.setState({style:n?{[n]:a}:a});return}if(!(0,dy.default)(e.to,a)||!e.canBegin||!e.isActive){var u=!e.canBegin||!e.isActive;this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var c=u||i?o:e.to;this.state&&l&&(n&&l[n]!==c||!n&&l!==c)&&this.setState({style:n?{[n]:c}:c}),this.runAnimation(dB(dB({},this.props),{},{from:c,begin:0}))}}}componentWillUnmount(){this.mounted=!1;var{onAnimationEnd:e}=this.props;this.unSubscribe&&this.unSubscribe(),this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation(),e&&e()}handleStyleChange(e){this.changeStyle(e)}changeStyle(e){this.mounted&&this.setState({style:e})}runJSAnimation(e){var{from:t,to:r,duration:n,easing:i,begin:a,onAnimationEnd:o,onAnimationStart:l}=e,u=dT(t,r,dw(i),n,this.changeStyle,this.manager.getTimeoutController()),c=()=>{this.stopJSAnimation=u()};this.manager.start([l,a,c,n,o])}runAnimation(e){let t;var{begin:r,duration:n,attributeName:i,to:a,easing:o,onAnimationStart:l,onAnimationEnd:u,children:c}=e;if(this.unSubscribe=this.manager.subscribe(this.handleStyleChange),"function"==typeof o||"function"==typeof c||"spring"===o)return void this.runJSAnimation(e);var s=i?{[i]:a}:a,f=(t=Object.keys(s),t.map(e=>"".concat(e.replace(/([A-Z])/g,e=>"-".concat(e.toLowerCase()))," ").concat(n,"ms ").concat(o)).join(","));this.manager.start([l,r,dB(dB({},s),{},{transition:f}),n,u])}render(){var e=this.props,{children:t,begin:r,duration:n,attributeName:i,easing:a,isActive:o,from:l,to:u,canBegin:c,onAnimationEnd:s,shouldReAnimate:f,onAnimationReStart:h,animationManager:d}=e,p=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,dN),y=dp.Children.count(t),v=this.state.style;if("function"==typeof t)return t(v);if(!o||0===y||n<=0)return t;var g=e=>{var{style:t={},className:r}=e.props;return(0,dp.cloneElement)(e,dB(dB({},p),{},{style:dB(dB({},t),v),className:r}))};return 1===y?g(dp.Children.only(t)):dp.createElement("div",null,dp.Children.map(t,e=>g(e)))}constructor(e,t){super(e,t),dR(this,"mounted",!1),dR(this,"manager",void 0),dR(this,"stopJSAnimation",null),dR(this,"unSubscribe",null);var{isActive:r,attributeName:n,from:i,to:a,children:o,duration:l,animationManager:u}=this.props;if(this.manager=u,this.handleStyleChange=this.handleStyleChange.bind(this),this.changeStyle=this.changeStyle.bind(this),!r||l<=0){this.state={style:{}},"function"==typeof o&&(this.state={style:a});return}if(i){if("function"==typeof o){this.state={style:i};return}this.state={style:n?{[n]:i}:i}}else this.state={style:{}}}}function dU(e){var t,r=dD(null!=(t=e.attributeName)?t:Object.keys(e.to).join(","),e.animationManager);return dp.createElement(dz,dI({},e,{animationManager:r}))}function dF(){return(dF=Object.assign.bind()).apply(null,arguments)}dR(dz,"displayName","Animate"),dR(dz,"defaultProps",{begin:0,duration:1e3,attributeName:"",easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}});var dK=(e,t,r,n,i)=>{var a,o=Math.min(Math.abs(r)/2,Math.abs(n)/2),l=n>=0?1:-1,u=r>=0?1:-1,c=+(n>=0&&r>=0||n<0&&r<0);if(o>0&&i instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=i[f]>o?o:i[f];a="M".concat(e,",").concat(t+l*s[0]),s[0]>0&&(a+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(c,",").concat(e+u*s[0],",").concat(t)),a+="L ".concat(e+r-u*s[1],",").concat(t),s[1]>0&&(a+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(c,",\n        ").concat(e+r,",").concat(t+l*s[1])),a+="L ".concat(e+r,",").concat(t+n-l*s[2]),s[2]>0&&(a+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(c,",\n        ").concat(e+r-u*s[2],",").concat(t+n)),a+="L ".concat(e+u*s[3],",").concat(t+n),s[3]>0&&(a+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(c,",\n        ").concat(e,",").concat(t+n-l*s[3])),a+="Z"}else if(o>0&&i===+i&&i>0){var h=Math.min(o,i);a="M ".concat(e,",").concat(t+l*h,"\n            A ").concat(h,",").concat(h,",0,0,").concat(c,",").concat(e+u*h,",").concat(t,"\n            L ").concat(e+r-u*h,",").concat(t,"\n            A ").concat(h,",").concat(h,",0,0,").concat(c,",").concat(e+r,",").concat(t+l*h,"\n            L ").concat(e+r,",").concat(t+n-l*h,"\n            A ").concat(h,",").concat(h,",0,0,").concat(c,",").concat(e+r-u*h,",").concat(t+n,"\n            L ").concat(e+u*h,",").concat(t+n,"\n            A ").concat(h,",").concat(h,",0,0,").concat(c,",").concat(e,",").concat(t+n-l*h," Z")}else a="M ".concat(e,",").concat(t," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return a},dq={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},dH=e=>{var t=hb(e,dq),r=(0,d.useRef)(null),[n,i]=(0,d.useState)(-1);(0,d.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var e=r.current.getTotalLength();e&&i(e)}catch(e){}},[]);var{x:a,y:o,width:l,height:u,radius:c,className:s}=t,{animationEasing:f,animationDuration:h,animationBegin:p,isAnimationActive:y,isUpdateAnimationActive:v}=t;if(a!==+a||o!==+o||l!==+l||u!==+u||0===l||0===u)return null;var g=(0,fz.clsx)("recharts-rectangle",s);return v?d.createElement(dU,{canBegin:n>0,from:{width:l,height:u,x:a,y:o},to:{width:l,height:u,x:a,y:o},duration:h,animationEasing:f,isActive:v},e=>{var{width:i,height:a,x:o,y:l}=e;return d.createElement(dU,{canBegin:n>0,from:"0px ".concat(-1===n?1:n,"px"),to:"".concat(n,"px 0px"),attributeName:"strokeDasharray",begin:p,duration:h,isActive:y,easing:f},d.createElement("path",dF({},f2(t,!0),{className:g,d:dK(o,l,i,a,c),ref:r})))}):d.createElement("path",dF({},f2(t,!0),{className:g,d:dK(a,o,l,u,c)}))};function dW(){return(dW=Object.assign.bind()).apply(null,arguments)}var dV=(e,t,r,n,i)=>{var a=r-n;return"M ".concat(e,",").concat(t)+"L ".concat(e+r,",").concat(t)+"L ".concat(e+r-a/2,",").concat(t+i)+"L ".concat(e+r-a/2-n,",").concat(t+i)+"L ".concat(e,",").concat(t," Z")},dY={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},dG=e=>{var t=hb(e,dY),r=(0,d.useRef)(),[n,i]=(0,d.useState)(-1);(0,d.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var e=r.current.getTotalLength();e&&i(e)}catch(e){}},[]);var{x:a,y:o,upperWidth:l,lowerWidth:u,height:c,className:s}=t,{animationEasing:f,animationDuration:h,animationBegin:p,isUpdateAnimationActive:y}=t;if(a!==+a||o!==+o||l!==+l||u!==+u||c!==+c||0===l&&0===u||0===c)return null;var v=(0,fz.clsx)("recharts-trapezoid",s);return y?d.createElement(dU,{canBegin:n>0,from:{upperWidth:0,lowerWidth:0,height:c,x:a,y:o},to:{upperWidth:l,lowerWidth:u,height:c,x:a,y:o},duration:h,animationEasing:f,isActive:y},e=>{var{upperWidth:i,lowerWidth:a,height:o,x:l,y:u}=e;return d.createElement(dU,{canBegin:n>0,from:"0px ".concat(-1===n?1:n,"px"),to:"".concat(n,"px 0px"),attributeName:"strokeDasharray",begin:p,duration:h,easing:f},d.createElement("path",dW({},f2(t,!0),{className:v,d:dV(l,u,i,a,o),ref:r})))}):d.createElement("g",null,d.createElement("path",dW({},f2(t,!0),{className:v,d:dV(a,o,l,u,c)})))};function dX(){return(dX=Object.assign.bind()).apply(null,arguments)}var d$=e=>{var{cx:t,cy:r,radius:n,angle:i,sign:a,isExternal:o,cornerRadius:l,cornerIsExternal:u}=e,c=l*(o?1:-1)+n,s=Math.asin(l/c)/rb,f=u?i:i+a*s,h=rx(t,r,c,f);return{center:h,circleTangency:rx(t,r,n,f),lineTangency:rx(t,r,c*Math.cos(s*rb),u?i-a*s:i),theta:s}},dZ=e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:a,endAngle:o}=e,l=((e,t)=>td(t-e)*Math.min(Math.abs(t-e),359.999))(a,o),u=a+l,c=rx(t,r,i,a),s=rx(t,r,i,u),f="M ".concat(c.x,",").concat(c.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(l)>180),",").concat(+(a>u),",\n    ").concat(s.x,",").concat(s.y,"\n  ");if(n>0){var h=rx(t,r,n,a),d=rx(t,r,n,u);f+="L ".concat(d.x,",").concat(d.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(l)>180),",").concat(+(a<=u),",\n            ").concat(h.x,",").concat(h.y," Z")}else f+="L ".concat(t,",").concat(r," Z");return f},dJ={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},dQ=e=>{var t,r=hb(e,dJ),{cx:n,cy:i,innerRadius:a,outerRadius:o,cornerRadius:l,forceCornerRadius:u,cornerIsExternal:c,startAngle:s,endAngle:f,className:h}=r;if(o<a||s===f)return null;var p=(0,fz.clsx)("recharts-sector",h),y=o-a,v=tx(l,y,0,!0);return t=v>0&&360>Math.abs(s-f)?(e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,cornerRadius:a,forceCornerRadius:o,cornerIsExternal:l,startAngle:u,endAngle:c}=e,s=td(c-u),{circleTangency:f,lineTangency:h,theta:d}=d$({cx:t,cy:r,radius:i,angle:u,sign:s,cornerRadius:a,cornerIsExternal:l}),{circleTangency:p,lineTangency:y,theta:v}=d$({cx:t,cy:r,radius:i,angle:c,sign:-s,cornerRadius:a,cornerIsExternal:l}),g=l?Math.abs(u-c):Math.abs(u-c)-d-v;if(g<0)return o?"M ".concat(h.x,",").concat(h.y,"\n        a").concat(a,",").concat(a,",0,0,1,").concat(2*a,",0\n        a").concat(a,",").concat(a,",0,0,1,").concat(-(2*a),",0\n      "):dZ({cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:u,endAngle:c});var m="M ".concat(h.x,",").concat(h.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(s<0),",").concat(f.x,",").concat(f.y,"\n    A").concat(i,",").concat(i,",0,").concat(+(g>180),",").concat(+(s<0),",").concat(p.x,",").concat(p.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(s<0),",").concat(y.x,",").concat(y.y,"\n  ");if(n>0){var{circleTangency:b,lineTangency:x,theta:w}=d$({cx:t,cy:r,radius:n,angle:u,sign:s,isExternal:!0,cornerRadius:a,cornerIsExternal:l}),{circleTangency:O,lineTangency:j,theta:P}=d$({cx:t,cy:r,radius:n,angle:c,sign:-s,isExternal:!0,cornerRadius:a,cornerIsExternal:l}),A=l?Math.abs(u-c):Math.abs(u-c)-w-P;if(A<0&&0===a)return"".concat(m,"L").concat(t,",").concat(r,"Z");m+="L".concat(j.x,",").concat(j.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(s<0),",").concat(O.x,",").concat(O.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(A>180),",").concat(+(s>0),",").concat(b.x,",").concat(b.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(s<0),",").concat(x.x,",").concat(x.y,"Z")}else m+="L".concat(t,",").concat(r,"Z");return m})({cx:n,cy:i,innerRadius:a,outerRadius:o,cornerRadius:Math.min(v,y/2),forceCornerRadius:u,cornerIsExternal:c,startAngle:s,endAngle:f}):dZ({cx:n,cy:i,innerRadius:a,outerRadius:o,startAngle:s,endAngle:f}),d.createElement("path",dX({},f2(r,!0),{className:p,d:t}))};function d0(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}function d1(){let e=d0(["M",",",""]);return d1=function(){return e},e}function d2(){let e=d0(["Z"]);return d2=function(){return e},e}function d5(){let e=d0(["L",",",""]);return d5=function(){return e},e}function d3(){let e=d0(["Q",",",",",",",""]);return d3=function(){return e},e}function d6(){let e=d0(["C",",",",",",",",",",",""]);return d6=function(){return e},e}function d8(){let e=d0(["M",",",""]);return d8=function(){return e},e}function d4(){let e=d0(["L",",",""]);return d4=function(){return e},e}function d7(){let e=d0(["L",",",""]);return d7=function(){return e},e}function d9(){let e=d0(["A",",",",0,0,",",",",",""]);return d9=function(){return e},e}function pe(){let e=d0(["M",",",""]);return pe=function(){return e},e}function pt(){let e=d0(["L",",",""]);return pt=function(){return e},e}function pr(){let e=d0(["A",",",",0,1,",",",",","A",",",",0,1,",",",",",""]);return pr=function(){return e},e}function pn(){let e=d0(["A",",",",0,",",",",",",",""]);return pn=function(){return e},e}function pi(){let e=d0(["M",",","h","v","h","Z"]);return pi=function(){return e},e}let pa=Math.PI,po=2*pa,pl=po-1e-6;function pu(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}class pc{moveTo(e,t){this._append(d1(),this._x0=this._x1=+e,this._y0=this._y1=+t)}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append(d2()))}lineTo(e,t){this._append(d5(),this._x1=+e,this._y1=+t)}quadraticCurveTo(e,t,r,n){this._append(d3(),+e,+t,this._x1=+r,this._y1=+n)}bezierCurveTo(e,t,r,n,i,a){this._append(d6(),+e,+t,+r,+n,this._x1=+i,this._y1=+a)}arcTo(e,t,r,n,i){if(e*=1,t*=1,r*=1,n*=1,(i*=1)<0)throw Error("negative radius: ".concat(i));let a=this._x1,o=this._y1,l=r-e,u=n-t,c=a-e,s=o-t,f=c*c+s*s;if(null===this._x1)this._append(d8(),this._x1=e,this._y1=t);else if(f>1e-6)if(Math.abs(s*l-u*c)>1e-6&&i){let h=r-a,d=n-o,p=l*l+u*u,y=Math.sqrt(p),v=Math.sqrt(f),g=i*Math.tan((pa-Math.acos((p+f-(h*h+d*d))/(2*y*v)))/2),m=g/v,b=g/y;Math.abs(m-1)>1e-6&&this._append(d7(),e+m*c,t+m*s),this._append(d9(),i,i,+(s*h>c*d),this._x1=e+b*l,this._y1=t+b*u)}else this._append(d4(),this._x1=e,this._y1=t)}arc(e,t,r,n,i,a){if(e*=1,t*=1,r*=1,a=!!a,r<0)throw Error("negative radius: ".concat(r));let o=r*Math.cos(n),l=r*Math.sin(n),u=e+o,c=t+l,s=1^a,f=a?n-i:i-n;null===this._x1?this._append(pe(),u,c):(Math.abs(this._x1-u)>1e-6||Math.abs(this._y1-c)>1e-6)&&this._append(pt(),u,c),r&&(f<0&&(f=f%po+po),f>pl?this._append(pr(),r,r,s,e-o,t-l,r,r,s,this._x1=u,this._y1=c):f>1e-6&&this._append(pn(),r,r,+(f>=pa),s,this._x1=e+r*Math.cos(i),this._y1=t+r*Math.sin(i)))}rect(e,t,r,n){this._append(pi(),this._x0=this._x1=+e,this._y0=this._y1=+t,r*=1,+n,-r)}toString(){return this._}constructor(e){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==e?pu:function(e){let t=Math.floor(e);if(!(t>=0))throw Error("invalid digits: ".concat(e));if(t>15)return pu;let r=10**t;return function(e){this._+=e[0];for(let t=1,n=e.length;t<n;++t)this._+=Math.round(arguments[t]*r)/r+e[t]}}(e)}}function ps(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(null==r)t=null;else{let e=Math.floor(r);if(!(e>=0))throw RangeError("invalid digits: ".concat(r));t=e}return e},()=>new pc(t)}pc.prototype;let pf=Math.cos,ph=Math.sin,pd=Math.sqrt,pp=Math.PI,py=2*pp;pd(3);let pv={draw(e,t){let r=pd(t/pp);e.moveTo(r,0),e.arc(0,0,r,0,py)}},pg=pd(1/3),pm=2*pg,pb=ph(pp/10)/ph(7*pp/10),px=ph(py/10)*pb,pw=-pf(py/10)*pb,pO=pd(3);pd(3);let pj=pd(3)/2,pP=1/pd(12),pA=(pP/2+1)*3;var pE=["type","size","sizeType"];function pS(){return(pS=Object.assign.bind()).apply(null,arguments)}function pM(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function pk(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?pM(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pM(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var pT={symbolCircle:pv,symbolCross:{draw(e,t){let r=pd(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},symbolDiamond:{draw(e,t){let r=pd(t/pm),n=r*pg;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},symbolSquare:{draw(e,t){let r=pd(t),n=-r/2;e.rect(n,n,r,r)}},symbolStar:{draw(e,t){let r=pd(.8908130915292852*t),n=px*r,i=pw*r;e.moveTo(0,-r),e.lineTo(n,i);for(let t=1;t<5;++t){let a=py*t/5,o=pf(a),l=ph(a);e.lineTo(l*r,-o*r),e.lineTo(o*n-l*i,l*n+o*i)}e.closePath()}},symbolTriangle:{draw(e,t){let r=-pd(t/(3*pO));e.moveTo(0,2*r),e.lineTo(-pO*r,-r),e.lineTo(pO*r,-r),e.closePath()}},symbolWye:{draw(e,t){let r=pd(t/pA),n=r/2,i=r*pP,a=r*pP+r,o=-n;e.moveTo(n,i),e.lineTo(n,a),e.lineTo(o,a),e.lineTo(-.5*n-pj*i,pj*n+-.5*i),e.lineTo(-.5*n-pj*a,pj*n+-.5*a),e.lineTo(-.5*o-pj*a,pj*o+-.5*a),e.lineTo(-.5*n+pj*i,-.5*i-pj*n),e.lineTo(-.5*n+pj*a,-.5*a-pj*n),e.lineTo(-.5*o+pj*a,-.5*a-pj*o),e.closePath()}}},pC=Math.PI/180,p_=e=>{var{type:t="circle",size:r=64,sizeType:n="area"}=e,i=pk(pk({},function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,pE)),{},{type:t,size:r,sizeType:n}),{className:a,cx:o,cy:l}=i,u=f2(i,!0);return o===+o&&l===+l&&r===+r?d.createElement("path",pS({},u,{className:(0,fz.clsx)("recharts-symbols",a),transform:"translate(".concat(o,", ").concat(l,")"),d:(()=>{var e=pT["symbol".concat(tA(t))]||pv;return(function(e,t){let r=null,n=ps(i);function i(){let i;if(r||(r=i=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),i)return r=null,i+""||null}return e="function"==typeof e?e:rh(e||pv),t="function"==typeof t?t:rh(void 0===t?64:+t),i.type=function(t){return arguments.length?(e="function"==typeof t?t:rh(t),i):e},i.size=function(e){return arguments.length?(t="function"==typeof e?e:rh(+e),i):t},i.context=function(e){return arguments.length?(r=null==e?null:e,i):r},i})().type(e).size(((e,t,r)=>{if("area"===t)return e;switch(r){case"cross":return 5*e*e/9;case"diamond":return .5*e*e/Math.sqrt(3);case"square":return e*e;case"star":var n=18*pC;return 1.25*e*e*(Math.tan(n)-Math.tan(2*n)*Math.tan(n)**2);case"triangle":return Math.sqrt(3)*e*e/4;case"wye":return(21-10*Math.sqrt(3))*e*e/8;default:return Math.PI*e*e/4}})(r,n,t))()})()})):null};p_.registerSymbol=(e,t)=>{pT["symbol".concat(tA(e))]=t};var pD=["option","shapeType","propTransformer","activeClassName","isActive"];function pN(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function pI(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?pN(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pN(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function pL(e,t){return pI(pI({},t),e)}function pB(e){var{shapeType:t,elementProps:r}=e;switch(t){case"rectangle":return d.createElement(dH,r);case"trapezoid":return d.createElement(dG,r);case"sector":return d.createElement(dQ,r);case"symbols":if("symbols"===t)return d.createElement(p_,r);break;default:return null}}function pR(e){var t,{option:r,shapeType:n,propTransformer:i=pL,activeClassName:a="recharts-active-shape",isActive:o}=e,l=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,pD);if((0,d.isValidElement)(r))t=(0,d.cloneElement)(r,pI(pI({},l),(0,d.isValidElement)(r)?r.props:r));else if("function"==typeof r)t=r(l);else if((0,dd.default)(r)&&"boolean"!=typeof r){var u=i(r,l);t=d.createElement(pB,{shapeType:n,elementProps:u})}else t=d.createElement(pB,{shapeType:n,elementProps:l});return o?d.createElement(hk,{className:a},t):t}var pz=["x","y"];function pU(){return(pU=Object.assign.bind()).apply(null,arguments)}function pF(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function pK(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?pF(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pF(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function pq(e,t){var{x:r,y:n}=e,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,pz),a=parseInt("".concat(r),10),o=parseInt("".concat(n),10),l=parseInt("".concat(t.height||i.height),10),u=parseInt("".concat(t.width||i.width),10);return pK(pK(pK(pK(pK({},t),i),a?{x:a}:{}),o?{y:o}:{}),{},{height:l,width:u,name:t.name,radius:t.radius})}function pH(e){return d.createElement(pR,pU({shapeType:"rectangle",propTransformer:pq,activeClassName:"recharts-active-bar"},e))}var pW=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(r,n)=>{if(tv(e))return e;var i=tv(r)||null==r;return i?e(r,n):(i||function(e,t){if(!e)throw Error("Invariant failed")}(!1),t)}},pV=(e,t)=>{var r=ri();return(n,i)=>a=>{null==e||e(n,i,a),r(tW({activeIndex:String(i),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}},pY=e=>{var t=ri();return(r,n)=>i=>{null==e||e(r,n,i),t(tV())}},pG=(e,t)=>{var r=ri();return(n,i)=>a=>{null==e||e(n,i,a),r(tG({activeIndex:String(i),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}};function pX(e){var{fn:t,args:r}=e,n=ri(),i=rJ();return(0,d.useEffect)(()=>{if(!i){var e=t(r);return n(tK(e)),()=>{n(tq(e))}}},[t,r,n,i]),null}var p$=null!=(c=d["useId".toString()])?c:()=>{var[e]=d.useState(()=>tb("uid-"));return e},pZ=(0,d.createContext)(void 0),pJ=e=>{var{id:t,type:r,children:n}=e,i=function(e,t){var r=p$();return t||(e?"".concat(e,"-").concat(r):r)}("recharts-".concat(r),t);return d.createElement(pZ.Provider,{value:i},n(i))},pQ=["children"],p0=(0,d.createContext)({data:[],xAxisId:"xAxis-0",yAxisId:"yAxis-0",dataPointFormatter:()=>({x:0,y:0,value:0}),errorBarOffset:0});function p1(e){var{children:t}=e,r=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,pQ);return d.createElement(p0.Provider,{value:r},t)}function p2(e,t){var r,n,i=ru(t=>lJ(t,e)),a=ru(e=>l0(e,t)),o=null!=(r=null==i?void 0:i.allowDataOverflow)?r:lZ.allowDataOverflow,l=null!=(n=null==a?void 0:a.allowDataOverflow)?n:lQ.allowDataOverflow;return{needClip:o||l,needClipX:o,needClipY:l}}function p5(e){var{xAxisId:t,yAxisId:r,clipPathId:n}=e,i=hd(),{needClipX:a,needClipY:o,needClip:l}=p2(t,r);if(!l)return null;var{x:u,y:c,width:s,height:f}=i;return d.createElement("clipPath",{id:"clipPath-".concat(n)},d.createElement("rect",{x:a?u:u-s/2,y:o?c:c-f/2,width:a?s:2*s,height:o?f:2*f}))}function p3(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function p6(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p3(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p3(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var p8=eb([l4,(e,t,r,n,i)=>i],(e,t)=>e.filter(e=>"bar"===e.type).find(e=>e.id===t)),p4=eb([p8],e=>null==e?void 0:e.maxBarSize),p7=(e,t,r)=>{var n=null!=r?r:e;if(null!=n)return tx(n,t,0)},p9=eb([r8,l4,(e,t)=>t,(e,t,r)=>r,(e,t,r,n)=>n],(e,t,r,n,i)=>t.filter(t=>"horizontal"===e?t.xAxisId===r:t.yAxisId===n).filter(e=>e.isPanorama===i).filter(e=>!1===e.hide).filter(e=>"bar"===e.type)),ye=eb([p9,e=>e.rootProps.barSize,(e,t,r)=>"horizontal"===r8(e)?cl(e,"xAxis",t):cl(e,"yAxis",r)],(e,t,r)=>{var n=e.filter(lY),i=e.filter(e=>null==e.stackId);return[...Object.entries(n.reduce((e,t)=>(e[t.stackId]||(e[t.stackId]=[]),e[t.stackId].push(t),e),{})).map(e=>{var[n,i]=e;return{stackId:n,dataKeys:i.map(e=>e.dataKey),barSize:p7(t,r,i[0].barSize)}}),...i.map(e=>({stackId:void 0,dataKeys:[e.dataKey].filter(e=>null!=e),barSize:p7(t,r,e.barSize)}))]}),yt=(e,t,r,n)=>{var i,a;return"horizontal"===r8(e)?(i=cy(e,"xAxis",t,n),a=cp(e,"xAxis",t,n)):(i=cy(e,"yAxis",r,n),a=cp(e,"yAxis",r,n)),rI(i,a)},yr=eb([ye,lg,e=>e.rootProps.barGap,lm,(e,t,r,n,i)=>{var a,o,l,u,c=p8(e,t,r,n,i);if(null!=c){var s=r8(e),f=lg(e),{maxBarSize:h}=c,d=null==h?f:h;return"horizontal"===s?(l=cy(e,"xAxis",t,n),u=cp(e,"xAxis",t,n)):(l=cy(e,"yAxis",r,n),u=cp(e,"yAxis",r,n)),null!=(a=null!=(o=rI(l,u,!0))?o:d)?a:0}},yt,p4],(e,t,r,n,i,a,o)=>{var l=function(e,t,r,n,i){var a,o=n.length;if(!(o<1)){var l=tx(e,r,0,!0),u=[];if(o6(n[0].barSize)){var c=!1,s=r/o,f=n.reduce((e,t)=>e+(t.barSize||0),0);(f+=(o-1)*l)>=r&&(f-=(o-1)*l,l=0),f>=r&&s>0&&(c=!0,s*=.9,f=o*s);var h={offset:((r-f)/2|0)-l,size:0};a=n.reduce((e,t)=>{var r,n=[...e,{stackId:t.stackId,dataKeys:t.dataKeys,position:{offset:h.offset+h.size+l,size:c?s:null!=(r=t.barSize)?r:0}}];return h=n[n.length-1].position,n},u)}else{var d=tx(t,r,0,!0);r-2*d-(o-1)*l<=0&&(l=0);var p=(r-2*d-(o-1)*l)/o;p>1&&(p>>=0);var y=o6(i)?Math.min(p,i):p;a=n.reduce((e,t,r)=>[...e,{stackId:t.stackId,dataKeys:t.dataKeys,position:{offset:d+(p+l)*r+(p-y)/2,size:y}}],u)}return a}}(r,n,i!==a?i:a,e,null==o?t:o);return i!==a&&null!=l&&(l=l.map(e=>p6(p6({},e),{},{position:p6(p6({},e.position),{},{offset:e.position.offset-i/2})}))),l}),yn=eb([yr,p8],(e,t)=>{if(null!=e&&null!=t){var r=e.find(e=>e.stackId===t.stackId&&null!=t.dataKey&&e.dataKeys.includes(t.dataKey));if(null!=r)return r.position}}),yi=eb([(e,t,r,n)=>"horizontal"===r8(e)?up(e,"yAxis",r,n):up(e,"xAxis",t,n),p8],(e,t)=>{var r=lK(t);if(!e||null==r||null==t)return;var{stackId:n}=t;if(null!=n){var i=e[n];if(i){var{stackedData:a}=i;if(a)return a.find(e=>e.key===r)}}}),ya=eb([rG,(e,t,r,n)=>cy(e,"xAxis",t,n),(e,t,r,n)=>cy(e,"yAxis",r,n),(e,t,r,n)=>cp(e,"xAxis",t,n),(e,t,r,n)=>cp(e,"yAxis",r,n),yn,r8,o3,yt,yi,p8,(e,t,r,n,i,a)=>a],(e,t,r,n,i,a,o,l,u,c,s,f)=>{var h,{chartData:d,dataStartIndex:p,dataEndIndex:y}=l;if(null!=s&&null!=a&&("horizontal"===o||"vertical"===o)&&null!=t&&null!=r&&null!=n&&null!=i&&null!=u){var{data:v}=s;if(null!=(h=null!=v&&v.length>0?v:null==d?void 0:d.slice(p,y+1)))return function(e){var{layout:t,barSettings:{dataKey:r,minPointSize:n},pos:i,bandSize:a,xAxis:o,yAxis:l,xAxisTicks:u,yAxisTicks:c,stackedData:s,displayedData:f,offset:h,cells:d}=e,p="horizontal"===t?l:o,y=s?p.scale.domain():null,v=(e=>{var{numericAxis:t}=e,r=t.scale.domain();if("number"===t.type){var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]);return n<=0&&i>=0?0:i<0?i:n}return r[0]})({numericAxis:p});return f.map((e,f)=>{s?g=((e,t)=>{if(!t||2!==t.length||!tv(t[0])||!tv(t[1]))return e;var r=Math.min(t[0],t[1]),n=Math.max(t[0],t[1]),i=[e[0],e[1]];return(!tv(e[0])||e[0]<r)&&(i[0]=r),(!tv(e[1])||e[1]>n)&&(i[1]=n),i[0]>n&&(i[0]=n),i[1]<r&&(i[1]=r),i})(s[f],y):Array.isArray(g=rA(e,r))||(g=[v,g]);var p=pW(n,0)(g[1],f);if("horizontal"===t){var g,m,b,x,w,O,j,[P,A]=[l.scale(g[0]),l.scale(g[1])];m=r_({axis:o,ticks:u,bandSize:a,offset:i.offset,entry:e,index:f}),b=null!=(j=null!=A?A:P)?j:void 0,x=i.size;var E=P-A;if(w=tp(E)?0:E,O={x:m,y:h.top,width:x,height:h.height},Math.abs(p)>0&&Math.abs(w)<Math.abs(p)){var S=td(w||p)*(Math.abs(p)-Math.abs(w));b-=S,w+=S}}else{var[M,k]=[o.scale(g[0]),o.scale(g[1])];if(m=M,b=r_({axis:l,ticks:c,bandSize:a,offset:i.offset,entry:e,index:f}),x=k-M,w=i.size,O={x:h.left,y:b,width:h.width,height:w},Math.abs(p)>0&&Math.abs(x)<Math.abs(p)){var T=td(x||p)*(Math.abs(p)-Math.abs(x));x+=T}}return null==m||null==b||null==x||null==w?null:yj(yj({},e),{},{x:m,y:b,width:x,height:w,value:s?g:g[1],payload:e,background:O,tooltipPosition:{x:m+x/2,y:b+w/2}},d&&d[f]&&d[f].props)}).filter(Boolean)}({layout:o,barSettings:s,pos:a,bandSize:u,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:i,stackedData:c,displayedData:h,offset:e,cells:f})}}),yo=()=>{};function yl(e){var{legendPayload:t}=e,r=ri(),n=rJ();return(0,d.useEffect)(()=>n?yo:(r(fi(t)),()=>{r(fa(t))}),[r,n,t]),null}function yu(e){var{legendPayload:t}=e,r=ri(),n=ru(r8);return(0,d.useEffect)(()=>"centric"!==n&&"radial"!==n?yo:(r(fi(t)),()=>{r(fa(t))}),[r,n,t]),null}function yc(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"animation-",r=(0,d.useRef)(tb(t)),n=(0,d.useRef)(e);return n.current!==e&&(r.current=tb(t),n.current=e),r.current}function ys(e){var t=ri(),r=(0,d.useRef)(null);return(0,d.useEffect)(()=>{null===r.current?t(sY(e)):r.current!==e&&t(sG({prev:r.current,next:e})),r.current=e},[t,e]),(0,d.useEffect)(()=>()=>{r.current&&(t(sX(r.current)),r.current=null)},[t]),null}function yf(e){var t=ri();return(0,d.useEffect)(()=>(t(s$(e)),()=>{t(sZ(e))}),[t,e]),null}function yh(){}var yd={begin:0,duration:1e3,easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}},yp={t:0},yy={t:1};function yv(e){var t=hb(e,yd),{isActive:r,canBegin:n,duration:i,easing:a,begin:o,onAnimationEnd:l,onAnimationStart:u,children:c}=t,s=dD("JavascriptAnimate",t.animationManager),[f,h]=(0,d.useState)(r?yp:yy),p=(0,d.useRef)(null);return(0,d.useEffect)(()=>{r||h(yy)},[r]),(0,d.useEffect)(()=>{if(!r||!n)return yh;var e=dT(yp,yy,dw(a),i,h,s.getTimeoutController());return s.start([u,o,()=>{p.current=e()},i,l]),()=>{s.stop(),p.current&&p.current(),l()}},[r,n,i,a,o,u,l,s]),c(f.t)}var yg=["onMouseEnter","onMouseLeave","onClick"],ym=["value","background","tooltipPosition"],yb=["id"],yx=["onMouseEnter","onClick","onMouseLeave"];function yw(){return(yw=Object.assign.bind()).apply(null,arguments)}function yO(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function yj(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?yO(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yO(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function yP(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function yA(e){var{dataKey:t,stroke:r,strokeWidth:n,fill:i,name:a,hide:o,unit:l}=e;return{dataDefinedOnItem:void 0,positions:void 0,settings:{stroke:r,strokeWidth:n,fill:i,dataKey:t,nameKey:void 0,name:rB(a,t),hide:o,type:e.tooltipType,color:e.fill,unit:l}}}function yE(e){var t=ru(so),{data:r,dataKey:n,background:i,allOtherBarProps:a}=e,{onMouseEnter:o,onMouseLeave:l,onClick:u}=a,c=yP(a,yg),s=pV(o,n),f=pY(l),h=pG(u,n);if(!i||null==r)return null;var d=f2(i,!1);return hE.createElement(hE.Fragment,null,r.map((e,r)=>{var{value:a,background:o,tooltipPosition:l}=e,u=yP(e,ym);if(!o)return null;var p=s(e,r),y=f(e,r),v=h(e,r),g=yj(yj(yj(yj(yj({option:i,isActive:String(r)===t},u),{},{fill:"#eee"},o),d),fV(c,e,r)),{},{onMouseEnter:p,onMouseLeave:y,onClick:v,dataKey:n,index:r,className:"recharts-bar-background-rectangle"});return hE.createElement(pH,yw({key:"background-bar-".concat(r)},g))}))}function yS(e){var{data:t,props:r,showLabels:n}=e,i=fX(r),{id:a}=i,o=yP(i,yb),{shape:l,dataKey:u,activeBar:c}=r,s=ru(so),f=ru(su),{onMouseEnter:h,onClick:d,onMouseLeave:p}=r,y=yP(r,yx),v=pV(h,u),g=pY(p),m=pG(d,u);return t?hE.createElement(hE.Fragment,null,t.map((e,t)=>{var r=c&&String(t)===s&&(null==f||u===f),n=yj(yj(yj({},o),e),{},{isActive:r,option:r?c:l,index:t,dataKey:u});return hE.createElement(hk,yw({className:"recharts-bar-rectangle"},fV(y,e,t),{onMouseEnter:v(e,t),onMouseLeave:g(e,t),onClick:m(e,t),key:"rectangle-".concat(null==e?void 0:e.x,"-").concat(null==e?void 0:e.y,"-").concat(null==e?void 0:e.value,"-").concat(t)}),hE.createElement(pH,n))}),n&&dh.renderCallByParent(r,t)):null}function yM(e){var{props:t,previousRectanglesRef:r}=e,{data:n,layout:i,isAnimationActive:a,animationBegin:o,animationDuration:l,animationEasing:u,onAnimationEnd:c,onAnimationStart:s}=t,f=r.current,h=yc(t,"recharts-bar-"),[d,p]=(0,hE.useState)(!1),y=(0,hE.useCallback)(()=>{"function"==typeof c&&c(),p(!1)},[c]),v=(0,hE.useCallback)(()=>{"function"==typeof s&&s(),p(!0)},[s]);return hE.createElement(yv,{begin:o,duration:l,isActive:a,easing:u,onAnimationEnd:y,onAnimationStart:v,key:h},e=>{var a=1===e?n:null==n?void 0:n.map((t,r)=>{var n=f&&f[r];if(n)return yj(yj({},t),{},{x:tj(n.x,t.x,e),y:tj(n.y,t.y,e),width:tj(n.width,t.width,e),height:tj(n.height,t.height,e)});if("horizontal"===i){var a=tj(0,t.height,e);return yj(yj({},t),{},{y:t.y+t.height-a,height:a})}var o=tj(0,t.width,e);return yj(yj({},t),{},{width:o})});return(e>0&&(r.current=null!=a?a:null),null==a)?null:hE.createElement(hk,null,hE.createElement(yS,{props:t,data:a,showLabels:!d}))})}function yk(e){var{data:t,isAnimationActive:r}=e,n=(0,hE.useRef)(null);return r&&t&&t.length&&(null==n.current||n.current!==t)?hE.createElement(yM,{previousRectanglesRef:n,props:e}):hE.createElement(yS,{props:e,data:t,showLabels:!0})}var yT=(e,t)=>{var r=Array.isArray(e.value)?e.value[1]:e.value;return{x:e.x,y:e.y,value:r,errorVal:rA(e,t)}};class yC extends hE.PureComponent{render(){var{hide:e,data:t,dataKey:r,className:n,xAxisId:i,yAxisId:a,needClip:o,background:l,id:u}=this.props;if(e)return null;var c=(0,fz.clsx)("recharts-bar",n);return hE.createElement(hk,{className:c,id:u},o&&hE.createElement("defs",null,hE.createElement(p5,{clipPathId:u,xAxisId:i,yAxisId:a})),hE.createElement(hk,{className:"recharts-bar-rectangles",clipPath:o?"url(#clipPath-".concat(u,")"):void 0},hE.createElement(yE,{data:t,dataKey:r,background:l,allOtherBarProps:this.props}),hE.createElement(yk,this.props)),this.props.children)}}var y_={activeBar:!1,animationBegin:0,animationDuration:400,animationEasing:"ease",hide:!1,isAnimationActive:!h_.isSsr,legendType:"rect",minPointSize:0,xAxisId:0,yAxisId:0};function yD(e){var t,{xAxisId:r,yAxisId:n,hide:i,legendType:a,minPointSize:o,activeBar:l,animationBegin:u,animationDuration:c,animationEasing:s,isAnimationActive:f}=e,{needClip:h}=p2(r,n),d=r4(),p=rJ(),y=f0(e.children,hT),v=ru(t=>ya(t,r,n,p,e.id,y));if("vertical"!==d&&"horizontal"!==d)return null;var g=null==v?void 0:v[0];return t=null==g||null==g.height||null==g.width?0:"vertical"===d?g.height/2:g.width/2,hE.createElement(p1,{xAxisId:r,yAxisId:n,data:v,dataPointFormatter:yT,errorBarOffset:t},hE.createElement(yC,yw({},e,{layout:d,needClip:h,data:v,xAxisId:r,yAxisId:n,hide:i,legendType:a,minPointSize:o,activeBar:l,animationBegin:u,animationDuration:c,animationEasing:s,isAnimationActive:f})))}function yN(e){var t=hb(e,y_),r=rJ();return hE.createElement(pJ,{id:t.id,type:"bar"},e=>hE.createElement(hE.Fragment,null,hE.createElement(yl,{legendPayload:(e=>{var{dataKey:t,name:r,fill:n,legendType:i,hide:a}=e;return[{inactive:a,dataKey:t,type:i,color:n,value:rB(r,t),payload:e}]})(t)}),hE.createElement(pX,{fn:yA,args:t}),hE.createElement(ys,{type:"bar",id:e,data:void 0,xAxisId:t.xAxisId,yAxisId:t.yAxisId,zAxisId:0,dataKey:t.dataKey,stackId:rT(t.stackId),hide:t.hide,barSize:t.barSize,minPointSize:t.minPointSize,maxBarSize:t.maxBarSize,isPanorama:r}),hE.createElement(yD,yw({},t,{id:e}))))}yN.displayName="Bar";var yI=d,yL=d;function yB(e,t){for(var r in e)if(({}).hasOwnProperty.call(e,r)&&(!({}).hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if(({}).hasOwnProperty.call(t,n)&&!({}).hasOwnProperty.call(e,n))return!1;return!0}class yR{static create(e){return new yR(e)}get domain(){return this.scale.domain}get range(){return this.scale.range}get rangeMin(){return this.range()[0]}get rangeMax(){return this.range()[1]}get bandwidth(){return this.scale.bandwidth}apply(e){var{bandAware:t,position:r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(void 0!==e){if(r)switch(r){case"start":default:return this.scale(e);case"middle":var n=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+n;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(e)+i}if(t){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+a}return this.scale(e)}}isInRange(e){var t=this.range(),r=t[0],n=t[t.length-1];return r<=n?e>=r&&e<=n:e>=n&&e<=r}constructor(e){this.scale=e}}!function(e,t,r){var n;(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:1e-4,enumerable:!0,configurable:!0,writable:!0}):e[t]=1e-4}(yR,"EPS",1e-4);var yz=function(e){var{width:t,height:r}=e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=(n%180+180)%180*Math.PI/180,a=Math.atan(r/t);return Math.abs(i>a&&i<Math.PI-a?r/Math.sin(i):t/Math.cos(i))};function yU(e,t,r){if(t<1)return[];if(1===t&&void 0===r)return e;for(var n=[],i=0;i<e.length;i+=t)if(void 0!==r&&!0!==r(e[i]))return;else n.push(e[i]);return n}function yF(e,t,r,n,i){if(e*t<e*n||e*t>e*i)return!1;var a=r();return e*(t-e*a/2-n)>=0&&e*(t+e*a/2-i)<=0}function yK(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function yq(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?yK(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yK(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function yH(e,t,r){var n,{tick:i,ticks:a,viewBox:o,minTickGap:l,orientation:u,interval:c,tickFormatter:s,unit:f,angle:h}=e;if(!a||!a.length||!i)return[];if(tv(c)||h_.isSsr)return null!=(n=yU(a,(tv(c)?c:0)+1))?n:[];var d="top"===u||"bottom"===u?"width":"height",p=f&&"width"===d?hz(f,{fontSize:t,letterSpacing:r}):{width:0,height:0},y=(e,n)=>{var i,a="function"==typeof s?s(e.value,n):e.value;return"width"===d?(i=hz(a,{fontSize:t,letterSpacing:r}),yz({width:i.width+p.width,height:i.height+p.height},h)):hz(a,{fontSize:t,letterSpacing:r})[d]},v=a.length>=2?td(a[1].coordinate-a[0].coordinate):1,g=function(e,t,r){var n="width"===r,{x:i,y:a,width:o,height:l}=e;return 1===t?{start:n?i:a,end:n?i+o:a+l}:{start:n?i+o:a+l,end:n?i:a}}(o,v,d);return"equidistantPreserveStart"===c?function(e,t,r,n,i){for(var a,o=(n||[]).slice(),{start:l,end:u}=t,c=0,s=1,f=l;s<=o.length;)if(a=function(){var t,a=null==n?void 0:n[c];if(void 0===a)return{v:yU(n,s)};var o=c,h=()=>(void 0===t&&(t=r(a,o)),t),d=a.coordinate,p=0===c||yF(e,d,h,f,u);p||(c=0,f=l,s+=1),p&&(f=d+e*(h()/2+i),c+=s)}())return a.v;return[]}(v,g,y,a,l):("preserveStart"===c||"preserveStartEnd"===c?function(e,t,r,n,i,a){var o=(n||[]).slice(),l=o.length,{start:u,end:c}=t;if(a){var s=n[l-1],f=r(s,l-1),h=e*(s.coordinate+e*f/2-c);o[l-1]=s=yq(yq({},s),{},{tickCoord:h>0?s.coordinate-h*e:s.coordinate}),yF(e,s.tickCoord,()=>f,u,c)&&(c=s.tickCoord-e*(f/2+i),o[l-1]=yq(yq({},s),{},{isShow:!0}))}for(var d=a?l-1:l,p=function(t){var n,a=o[t],l=()=>(void 0===n&&(n=r(a,t)),n);if(0===t){var s=e*(a.coordinate-e*l()/2-u);o[t]=a=yq(yq({},a),{},{tickCoord:s<0?a.coordinate-s*e:a.coordinate})}else o[t]=a=yq(yq({},a),{},{tickCoord:a.coordinate});yF(e,a.tickCoord,l,u,c)&&(u=a.tickCoord+e*(l()/2+i),o[t]=yq(yq({},a),{},{isShow:!0}))},y=0;y<d;y++)p(y);return o}(v,g,y,a,l,"preserveStartEnd"===c):function(e,t,r,n,i){for(var a=(n||[]).slice(),o=a.length,{start:l}=t,{end:u}=t,c=function(t){var n,c=a[t],s=()=>(void 0===n&&(n=r(c,t)),n);if(t===o-1){var f=e*(c.coordinate+e*s()/2-u);a[t]=c=yq(yq({},c),{},{tickCoord:f>0?c.coordinate-f*e:c.coordinate})}else a[t]=c=yq(yq({},c),{},{tickCoord:c.coordinate});yF(e,c.tickCoord,s,l,u)&&(u=c.tickCoord-e*(s()/2+i),a[t]=yq(yq({},c),{},{isShow:!0}))},s=o-1;s>=0;s--)c(s);return a}(v,g,y,a,l)).filter(e=>e.isShow)}var yW=["viewBox"],yV=["viewBox"];function yY(){return(yY=Object.assign.bind()).apply(null,arguments)}function yG(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function yX(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?yG(Object(r),!0).forEach(function(t){yZ(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yG(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function y$(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function yZ(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class yJ extends yL.Component{shouldComponentUpdate(e,t){var{viewBox:r}=e,n=y$(e,yW),i=this.props,{viewBox:a}=i,o=y$(i,yV);return!yB(r,a)||!yB(n,o)||!yB(t,this.state)}getTickLineCoord(e){var t,r,n,i,a,o,{x:l,y:u,width:c,height:s,orientation:f,tickSize:h,mirror:d,tickMargin:p}=this.props,y=d?-1:1,v=e.tickSize||h,g=tv(e.tickCoord)?e.tickCoord:e.coordinate;switch(f){case"top":t=r=e.coordinate,o=(n=(i=u+!d*s)-y*v)-y*p,a=g;break;case"left":n=i=e.coordinate,a=(t=(r=l+!d*c)-y*v)-y*p,o=g;break;case"right":n=i=e.coordinate,a=(t=(r=l+d*c)+y*v)+y*p,o=g;break;default:t=r=e.coordinate,o=(n=(i=u+d*s)+y*v)+y*p,a=g}return{line:{x1:t,y1:n,x2:r,y2:i},tick:{x:a,y:o}}}getTickTextAnchor(){var e,{orientation:t,mirror:r}=this.props;switch(t){case"left":e=r?"start":"end";break;case"right":e=r?"end":"start";break;default:e="middle"}return e}getTickVerticalAnchor(){var{orientation:e,mirror:t}=this.props;switch(e){case"left":case"right":return"middle";case"top":return t?"start":"end";default:return t?"end":"start"}}renderAxisLine(){var{x:e,y:t,width:r,height:n,orientation:i,mirror:a,axisLine:o}=this.props,l=yX(yX(yX({},f2(this.props,!1)),f2(o,!1)),{},{fill:"none"});if("top"===i||"bottom"===i){var u=+("top"===i&&!a||"bottom"===i&&a);l=yX(yX({},l),{},{x1:e,y1:t+u*n,x2:e+r,y2:t+u*n})}else{var c=+("left"===i&&!a||"right"===i&&a);l=yX(yX({},l),{},{x1:e+c*r,y1:t,x2:e+c*r,y2:t+n})}return yL.createElement("line",yY({},l,{className:(0,fz.clsx)("recharts-cartesian-axis-line",(0,th.default)(o,"className"))}))}static renderTickItem(e,t,r){var n,i=(0,fz.clsx)(t.className,"recharts-cartesian-axis-tick-value");if(yL.isValidElement(e))n=yL.cloneElement(e,yX(yX({},t),{},{className:i}));else if("function"==typeof e)n=e(yX(yX({},t),{},{className:i}));else{var a="recharts-cartesian-axis-tick-value";"boolean"!=typeof e&&(a=(0,fz.clsx)(a,e.className)),n=yL.createElement(h3,yY({},t,{className:a}),r)}return n}renderTicks(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],{tickLine:n,stroke:i,tick:a,tickFormatter:o,unit:l,padding:u}=this.props,c=yH(yX(yX({},this.props),{},{ticks:r}),e,t),s=this.getTickTextAnchor(),f=this.getTickVerticalAnchor(),h=fX(this.props),d=f2(a,!1),p=yX(yX({},h),{},{fill:"none"},f2(n,!1)),y=c.map((e,t)=>{var{line:r,tick:y}=this.getTickLineCoord(e),v=yX(yX(yX(yX({textAnchor:s,verticalAnchor:f},h),{},{stroke:"none",fill:i},d),y),{},{index:t,payload:e,visibleTicksCount:c.length,tickFormatter:o,padding:u});return yL.createElement(hk,yY({className:"recharts-cartesian-axis-tick",key:"tick-".concat(e.value,"-").concat(e.coordinate,"-").concat(e.tickCoord)},fV(this.props,e,t)),n&&yL.createElement("line",yY({},p,r,{className:(0,fz.clsx)("recharts-cartesian-axis-tick-line",(0,th.default)(n,"className"))})),a&&yJ.renderTickItem(a,v,"".concat("function"==typeof o?o(e.value,t):e.value).concat(l||"")))});return y.length>0?yL.createElement("g",{className:"recharts-cartesian-axis-ticks"},y):null}render(){var{axisLine:e,width:t,height:r,className:n,hide:i}=this.props;if(i)return null;var{ticks:a}=this.props;return null!=t&&t<=0||null!=r&&r<=0?null:yL.createElement(hk,{className:(0,fz.clsx)("recharts-cartesian-axis",n),ref:e=>{if(e){var t=e.getElementsByClassName("recharts-cartesian-axis-tick-value");this.tickRefs.current=Array.from(t);var r=t[0];if(r){var n=window.getComputedStyle(r).fontSize,i=window.getComputedStyle(r).letterSpacing;(n!==this.state.fontSize||i!==this.state.letterSpacing)&&this.setState({fontSize:window.getComputedStyle(r).fontSize,letterSpacing:window.getComputedStyle(r).letterSpacing})}}}},e&&this.renderAxisLine(),this.renderTicks(this.state.fontSize,this.state.letterSpacing,a),dr.renderCallByParent(this.props))}constructor(e){super(e),this.tickRefs=yL.createRef(),this.tickRefs.current=[],this.state={fontSize:"",letterSpacing:""}}}yZ(yJ,"displayName","CartesianAxis"),yZ(yJ,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var yQ=["children"],y0=["dangerouslySetInnerHTML","ticks"];function y1(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function y2(){return(y2=Object.assign.bind()).apply(null,arguments)}function y5(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function y3(e){var t=ri(),r=(0,yI.useMemo)(()=>{var{children:t}=e;return y5(e,yQ)},[e]),n=ru(e=>lJ(e,r.id)),i=r===n;return((0,yI.useEffect)(()=>(t(sR(r)),()=>{t(sz(r))}),[r,t]),i)?e.children:null}var y6=e=>{var{xAxisId:t,className:r}=e,n=ru(r$),i=rJ(),a="xAxis",o=ru(e=>u8(e,a,t,i)),l=ru(e=>cd(e,a,t,i)),u=ru(e=>cn(e,t)),c=ru(e=>((e,t)=>{var r=rG(e),n=lJ(e,t);if(null!=n){var i=ci(e,n.orientation,n.mirror)[t];return null==i?{x:r.left,y:0}:{x:r.left,y:i}}})(e,t));if(null==u||null==c)return null;var{dangerouslySetInnerHTML:s,ticks:f}=e,h=y5(e,y0);return yI.createElement(yJ,y2({},h,{scale:o,x:c.x,y:c.y,width:u.width,height:u.height,className:(0,fz.clsx)("recharts-".concat(a," ").concat(a),r),viewBox:n,ticks:l}))},y8=e=>{var t,r,n,i,a;return yI.createElement(y3,{interval:null!=(t=e.interval)?t:"preserveEnd",id:e.xAxisId,scale:e.scale,type:e.type,padding:e.padding,allowDataOverflow:e.allowDataOverflow,domain:e.domain,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,includeHidden:null!=(r=e.includeHidden)&&r,reversed:e.reversed,ticks:e.ticks,height:e.height,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!=(n=e.angle)?n:0,minTickGap:null!=(i=e.minTickGap)?i:5,tick:null==(a=e.tick)||a,tickFormatter:e.tickFormatter},yI.createElement(y6,e))};class y4 extends yI.Component{render(){return yI.createElement(y8,this.props)}}y1(y4,"displayName","XAxis"),y1(y4,"defaultProps",{allowDataOverflow:lZ.allowDataOverflow,allowDecimals:lZ.allowDecimals,allowDuplicatedCategory:lZ.allowDuplicatedCategory,height:lZ.height,hide:!1,mirror:lZ.mirror,orientation:lZ.orientation,padding:lZ.padding,reversed:lZ.reversed,scale:lZ.scale,tickCount:lZ.tickCount,type:lZ.type,xAxisId:0});var y7=d,y9=["dangerouslySetInnerHTML","ticks"];function ve(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function vt(){return(vt=Object.assign.bind()).apply(null,arguments)}function vr(e){var t=ri();return(0,y7.useEffect)(()=>(t(sU(e)),()=>{t(sF(e))}),[e,t]),null}var vn=e=>{var t,{yAxisId:r,className:n,width:i,label:a}=e,o=(0,y7.useRef)(null),l=(0,y7.useRef)(null),u=ru(r$),c=rJ(),s=ri(),f="yAxis",h=ru(e=>u8(e,f,r,c)),d=ru(e=>co(e,r)),p=ru(e=>((e,t)=>{var r=rG(e),n=l0(e,t);if(null!=n){var i=ca(e,n.orientation,n.mirror)[t];return null==i?{x:0,y:r.top}:{x:i,y:r.top}}})(e,r)),y=ru(e=>cd(e,f,r,c));if((0,y7.useLayoutEffect)(()=>{if(!("auto"!==i||!d||dt(a)||(0,y7.isValidElement)(a))){var e,t=o.current,n=null==t||null==(e=t.tickRefs)?void 0:e.current,{tickSize:u,tickMargin:c}=t.props,f=(e=>{var{ticks:t,label:r,labelGapWithTick:n=5,tickSize:i=0,tickMargin:a=0}=e,o=0;if(t){t.forEach(e=>{if(e){var t=e.getBoundingClientRect();t.width>o&&(o=t.width)}});var l=r?r.getBoundingClientRect().width:0;return Math.round(o+(i+a)+l+(r?n:0))}return 0})({ticks:n,label:l.current,labelGapWithTick:5,tickSize:u,tickMargin:c});Math.round(d.width)!==Math.round(f)&&s(sH({id:r,width:f}))}},[o,null==o||null==(t=o.current)||null==(t=t.tickRefs)?void 0:t.current,null==d?void 0:d.width,d,s,a,r,i]),null==d||null==p)return null;var{dangerouslySetInnerHTML:v,ticks:g}=e,m=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,y9);return y7.createElement(yJ,vt({},m,{ref:o,labelRef:l,scale:h,x:p.x,y:p.y,width:d.width,height:d.height,className:(0,fz.clsx)("recharts-".concat(f," ").concat(f),n),viewBox:u,ticks:y}))},vi=e=>{var t,r,n,i,a;return y7.createElement(y7.Fragment,null,y7.createElement(vr,{interval:null!=(t=e.interval)?t:"preserveEnd",id:e.yAxisId,scale:e.scale,type:e.type,domain:e.domain,allowDataOverflow:e.allowDataOverflow,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,padding:e.padding,includeHidden:null!=(r=e.includeHidden)&&r,reversed:e.reversed,ticks:e.ticks,width:e.width,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!=(n=e.angle)?n:0,minTickGap:null!=(i=e.minTickGap)?i:5,tick:null==(a=e.tick)||a,tickFormatter:e.tickFormatter}),y7.createElement(vn,e))},va={allowDataOverflow:lQ.allowDataOverflow,allowDecimals:lQ.allowDecimals,allowDuplicatedCategory:lQ.allowDuplicatedCategory,hide:!1,mirror:lQ.mirror,orientation:lQ.orientation,padding:lQ.padding,reversed:lQ.reversed,scale:lQ.scale,tickCount:lQ.tickCount,type:lQ.type,width:lQ.width,yAxisId:0};class vo extends y7.Component{render(){return y7.createElement(vi,this.props)}}ve(vo,"displayName","YAxis"),ve(vo,"defaultProps",va);var vl=function(e,t){for(var r=arguments.length,n=Array(r>2?r-2:0),i=2;i<r;i++)n[i-2]=arguments[i]},vu=["x1","y1","x2","y2","key"],vc=["offset"],vs=["xAxisId","yAxisId"],vf=["xAxisId","yAxisId"];function vh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function vd(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?vh(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vh(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function vp(){return(vp=Object.assign.bind()).apply(null,arguments)}function vy(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var vv=e=>{var{fill:t}=e;if(!t||"none"===t)return null;var{fillOpacity:r,x:n,y:i,width:a,height:o,ry:l}=e;return d.createElement("rect",{x:n,y:i,ry:l,width:a,height:o,stroke:"none",fill:t,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function vg(e,t){var r;if(d.isValidElement(e))r=d.cloneElement(e,t);else if("function"==typeof e)r=e(t);else{var{x1:n,y1:i,x2:a,y2:o,key:l}=t,u=fX(vy(t,vu)),{offset:c}=u,s=vy(u,vc);r=d.createElement("line",vp({},s,{x1:n,y1:i,x2:a,y2:o,fill:"none",key:l}))}return r}function vm(e){var{x:t,width:r,horizontal:n=!0,horizontalPoints:i}=e;if(!n||!i||!i.length)return null;var{xAxisId:a,yAxisId:o}=e,l=vy(e,vs),u=i.map((e,i)=>vg(n,vd(vd({},l),{},{x1:t,y1:e,x2:t+r,y2:e,key:"line-".concat(i),index:i})));return d.createElement("g",{className:"recharts-cartesian-grid-horizontal"},u)}function vb(e){var{y:t,height:r,vertical:n=!0,verticalPoints:i}=e;if(!n||!i||!i.length)return null;var{xAxisId:a,yAxisId:o}=e,l=vy(e,vf),u=i.map((e,i)=>vg(n,vd(vd({},l),{},{x1:e,y1:t,x2:e,y2:t+r,key:"line-".concat(i),index:i})));return d.createElement("g",{className:"recharts-cartesian-grid-vertical"},u)}function vx(e){var{horizontalFill:t,fillOpacity:r,x:n,y:i,width:a,height:o,horizontalPoints:l,horizontal:u=!0}=e;if(!u||!t||!t.length)return null;var c=l.map(e=>Math.round(e+i-i)).sort((e,t)=>e-t);i!==c[0]&&c.unshift(0);var s=c.map((e,l)=>{var u=c[l+1]?c[l+1]-e:i+o-e;if(u<=0)return null;var s=l%t.length;return d.createElement("rect",{key:"react-".concat(l),y:e,x:n,height:u,width:a,stroke:"none",fill:t[s],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return d.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},s)}function vw(e){var{vertical:t=!0,verticalFill:r,fillOpacity:n,x:i,y:a,width:o,height:l,verticalPoints:u}=e;if(!t||!r||!r.length)return null;var c=u.map(e=>Math.round(e+i-i)).sort((e,t)=>e-t);i!==c[0]&&c.unshift(0);var s=c.map((e,t)=>{var u=c[t+1]?c[t+1]-e:i+o-e;if(u<=0)return null;var s=t%r.length;return d.createElement("rect",{key:"react-".concat(t),x:e,y:a,width:u,height:l,stroke:"none",fill:r[s],fillOpacity:n,className:"recharts-cartesian-grid-bg"})});return d.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},s)}var vO=(e,t)=>{var{xAxis:r,width:n,height:i,offset:a}=e;return rS(yH(vd(vd(vd({},yJ.defaultProps),r),{},{ticks:rM(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),a.left,a.left+a.width,t)},vj=(e,t)=>{var{yAxis:r,width:n,height:i,offset:a}=e;return rS(yH(vd(vd(vd({},yJ.defaultProps),r),{},{ticks:rM(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),a.top,a.top+a.height,t)},vP={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[],xAxisId:0,yAxisId:0};function vA(e){var t=r3(),r=r6(),n=r5(),i=vd(vd({},hb(e,vP)),{},{x:tv(e.x)?e.x:n.left,y:tv(e.y)?e.y:n.top,width:tv(e.width)?e.width:n.width,height:tv(e.height)?e.height:n.height}),{xAxisId:a,yAxisId:o,x:l,y:u,width:c,height:s,syncWithTicks:f,horizontalValues:h,verticalValues:p}=i,y=rJ(),v=ru(e=>ch(e,"xAxis",a,y)),g=ru(e=>ch(e,"yAxis",o,y));if(!tv(c)||c<=0||!tv(s)||s<=0||!tv(l)||l!==+l||!tv(u)||u!==+u)return null;var m=i.verticalCoordinatesGenerator||vO,b=i.horizontalCoordinatesGenerator||vj,{horizontalPoints:x,verticalPoints:w}=i;if((!x||!x.length)&&"function"==typeof b){var O=h&&h.length,j=b({yAxis:g?vd(vd({},g),{},{ticks:O?h:g.ticks}):void 0,width:t,height:r,offset:n},!!O||f);vl(Array.isArray(j),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(typeof j,"]")),Array.isArray(j)&&(x=j)}if((!w||!w.length)&&"function"==typeof m){var P=p&&p.length,A=m({xAxis:v?vd(vd({},v),{},{ticks:P?p:v.ticks}):void 0,width:t,height:r,offset:n},!!P||f);vl(Array.isArray(A),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(typeof A,"]")),Array.isArray(A)&&(w=A)}return d.createElement("g",{className:"recharts-cartesian-grid"},d.createElement(vv,{fill:i.fill,fillOpacity:i.fillOpacity,x:i.x,y:i.y,width:i.width,height:i.height,ry:i.ry}),d.createElement(vx,vp({},i,{horizontalPoints:x})),d.createElement(vw,vp({},i,{verticalPoints:w})),d.createElement(vm,vp({},i,{offset:n,horizontalPoints:x,xAxis:v,yAxis:g})),d.createElement(vb,vp({},i,{offset:n,verticalPoints:w,xAxis:v,yAxis:g})))}vA.displayName="CartesianGrid";var vE=e.i(74080);function vS(){return(vS=Object.assign.bind()).apply(null,arguments)}function vM(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function vk(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?vM(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vM(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function vT(e){return Array.isArray(e)&&tg(e[0])&&tg(e[1])?e.join(" ~ "):e}var vC=e=>{var{separator:t=" : ",contentStyle:r={},itemStyle:n={},labelStyle:i={},payload:a,formatter:o,itemSorter:l,wrapperClassName:u,labelClassName:c,label:s,labelFormatter:f,accessibilityLayer:h=!1}=e,p=vk({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},r),y=vk({margin:0},i),v=null!=s,g=v?s:"",m=(0,fz.clsx)("recharts-default-tooltip",u),b=(0,fz.clsx)("recharts-tooltip-label",c);return v&&f&&null!=a&&(g=f(s,a)),d.createElement("div",vS({className:m,style:p},h?{role:"status","aria-live":"assertive"}:{}),d.createElement("p",{className:b,style:y},d.isValidElement(g)?g:"".concat(g)),(()=>{if(a&&a.length){var e=(l?(0,rc.default)(a,l):a).map((e,r)=>{if("none"===e.type)return null;var i=e.formatter||o||vT,{value:l,name:u}=e,c=l,s=u;if(i){var f=i(l,u,e,r,a);if(Array.isArray(f))[c,s]=f;else{if(null==f)return null;c=f}}var h=vk({display:"block",paddingTop:4,paddingBottom:4,color:e.color||"#000"},n);return d.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(r),style:h},tg(s)?d.createElement("span",{className:"recharts-tooltip-item-name"},s):null,tg(s)?d.createElement("span",{className:"recharts-tooltip-item-separator"},t):null,d.createElement("span",{className:"recharts-tooltip-item-value"},c),d.createElement("span",{className:"recharts-tooltip-item-unit"},e.unit||""))});return d.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},e)}return null})())},v_=d,vD="recharts-tooltip-wrapper",vN={visibility:"hidden"};function vI(e){var{allowEscapeViewBox:t,coordinate:r,key:n,offsetTopLeft:i,position:a,reverseDirection:o,tooltipDimension:l,viewBox:u,viewBoxDimension:c}=e;if(a&&tv(a[n]))return a[n];var s=r[n]-l-(i>0?i:0),f=r[n]+i;if(t[n])return o[n]?s:f;var h=u[n];return null==h?0:o[n]?s<h?Math.max(f,h):Math.max(s,h):null==c?0:f+l>h+c?Math.max(s,h):Math.max(f,h)}function vL(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function vB(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?vL(Object(r),!0).forEach(function(t){vR(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vL(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function vR(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class vz extends v_.PureComponent{componentDidMount(){document.addEventListener("keydown",this.handleKeyDown)}componentWillUnmount(){document.removeEventListener("keydown",this.handleKeyDown)}componentDidUpdate(){var e,t;this.state.dismissed&&((null==(e=this.props.coordinate)?void 0:e.x)!==this.state.dismissedAtCoordinate.x||(null==(t=this.props.coordinate)?void 0:t.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}render(){var{active:e,allowEscapeViewBox:t,animationDuration:r,animationEasing:n,children:i,coordinate:a,hasPayload:o,isAnimationActive:l,offset:u,position:c,reverseDirection:s,useTranslate3d:f,viewBox:h,wrapperStyle:d,lastBoundingBox:p,innerRef:y,hasPortalFromProps:v}=this.props,{cssClasses:g,cssProperties:m}=function(e){var t,r,n,{allowEscapeViewBox:i,coordinate:a,offsetTopLeft:o,position:l,reverseDirection:u,tooltipBox:c,useTranslate3d:s,viewBox:f}=e;return{cssProperties:t=c.height>0&&c.width>0&&a?function(e){var{translateX:t,translateY:r,useTranslate3d:n}=e;return{transform:n?"translate3d(".concat(t,"px, ").concat(r,"px, 0)"):"translate(".concat(t,"px, ").concat(r,"px)")}}({translateX:r=vI({allowEscapeViewBox:i,coordinate:a,key:"x",offsetTopLeft:o,position:l,reverseDirection:u,tooltipDimension:c.width,viewBox:f,viewBoxDimension:f.width}),translateY:n=vI({allowEscapeViewBox:i,coordinate:a,key:"y",offsetTopLeft:o,position:l,reverseDirection:u,tooltipDimension:c.height,viewBox:f,viewBoxDimension:f.height}),useTranslate3d:s}):vN,cssClasses:function(e){var{coordinate:t,translateX:r,translateY:n}=e;return(0,fz.clsx)(vD,{["".concat(vD,"-right")]:tv(r)&&t&&tv(t.x)&&r>=t.x,["".concat(vD,"-left")]:tv(r)&&t&&tv(t.x)&&r<t.x,["".concat(vD,"-bottom")]:tv(n)&&t&&tv(t.y)&&n>=t.y,["".concat(vD,"-top")]:tv(n)&&t&&tv(t.y)&&n<t.y})}({translateX:r,translateY:n,coordinate:a})}}({allowEscapeViewBox:t,coordinate:a,offsetTopLeft:u,position:c,reverseDirection:s,tooltipBox:{height:p.height,width:p.width},useTranslate3d:f,viewBox:h}),b=v?{}:vB(vB({transition:l&&e?"transform ".concat(r,"ms ").concat(n):void 0},m),{},{pointerEvents:"none",visibility:!this.state.dismissed&&e&&o?"visible":"hidden",position:"absolute",top:0,left:0}),x=vB(vB({},b),{},{visibility:!this.state.dismissed&&e&&o?"visible":"hidden"},d);return v_.createElement("div",{xmlns:"http://www.w3.org/1999/xhtml",tabIndex:-1,className:g,style:x,ref:y},i)}constructor(){super(...arguments),vR(this,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0}}),vR(this,"handleKeyDown",e=>{if("Escape"===e.key){var t,r,n,i;this.setState({dismissed:!0,dismissedAtCoordinate:{x:null!=(t=null==(r=this.props.coordinate)?void 0:r.x)?t:0,y:null!=(n=null==(i=this.props.coordinate)?void 0:i.y)?n:0}})}})}}var vU=e.i(92809);function vF(e){this._context=e}function vK(e){return new vF(e)}function vq(e){return e[0]}function vH(e){return e[1]}function vW(e,t){var r=rh(!0),n=null,i=vK,a=null,o=ps(l);function l(l){var u,c,s,f=(l=rf(l)).length,h=!1;for(null==n&&(a=i(s=o())),u=0;u<=f;++u)!(u<f&&r(c=l[u],u,l))===h&&((h=!h)?a.lineStart():a.lineEnd()),h&&a.point(+e(c,u,l),+t(c,u,l));if(s)return a=null,s+""||null}return e="function"==typeof e?e:void 0===e?vq:rh(e),t="function"==typeof t?t:void 0===t?vH:rh(t),l.x=function(t){return arguments.length?(e="function"==typeof t?t:rh(+t),l):e},l.y=function(e){return arguments.length?(t="function"==typeof e?e:rh(+e),l):t},l.defined=function(e){return arguments.length?(r="function"==typeof e?e:rh(!!e),l):r},l.curve=function(e){return arguments.length?(i=e,null!=n&&(a=i(n)),l):i},l.context=function(e){return arguments.length?(null==e?n=a=null:a=i(n=e),l):n},l}function vV(e,t,r){var n=null,i=rh(!0),a=null,o=vK,l=null,u=ps(c);function c(c){var s,f,h,d,p,y=(c=rf(c)).length,v=!1,g=Array(y),m=Array(y);for(null==a&&(l=o(p=u())),s=0;s<=y;++s){if(!(s<y&&i(d=c[s],s,c))===v)if(v=!v)f=s,l.areaStart(),l.lineStart();else{for(l.lineEnd(),l.lineStart(),h=s-1;h>=f;--h)l.point(g[h],m[h]);l.lineEnd(),l.areaEnd()}v&&(g[s]=+e(d,s,c),m[s]=+t(d,s,c),l.point(n?+n(d,s,c):g[s],r?+r(d,s,c):m[s]))}if(p)return l=null,p+""||null}function s(){return vW().defined(i).curve(o).context(a)}return e="function"==typeof e?e:void 0===e?vq:rh(+e),t="function"==typeof t?t:void 0===t?rh(0):rh(+t),r="function"==typeof r?r:void 0===r?vH:rh(+r),c.x=function(t){return arguments.length?(e="function"==typeof t?t:rh(+t),n=null,c):e},c.x0=function(t){return arguments.length?(e="function"==typeof t?t:rh(+t),c):e},c.x1=function(e){return arguments.length?(n=null==e?null:"function"==typeof e?e:rh(+e),c):n},c.y=function(e){return arguments.length?(t="function"==typeof e?e:rh(+e),r=null,c):t},c.y0=function(e){return arguments.length?(t="function"==typeof e?e:rh(+e),c):t},c.y1=function(e){return arguments.length?(r=null==e?null:"function"==typeof e?e:rh(+e),c):r},c.lineX0=c.lineY0=function(){return s().x(e).y(t)},c.lineY1=function(){return s().x(e).y(r)},c.lineX1=function(){return s().x(n).y(t)},c.defined=function(e){return arguments.length?(i="function"==typeof e?e:rh(!!e),c):i},c.curve=function(e){return arguments.length?(o=e,null!=a&&(l=o(a)),c):o},c.context=function(e){return arguments.length?(null==e?a=l=null:l=o(a=e),c):a},c}function vY(){}function vG(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function vX(e){this._context=e}function v$(e){this._context=e}function vZ(e){this._context=e}vF.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t)}}},vX.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:vG(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:vG(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},v$.prototype={areaStart:vY,areaEnd:vY,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:vG(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},vZ.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:vG(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};class vJ{areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+e)/2,this._y0,this._x0,t,e,t):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+t)/2,e,this._y0,e,t)}this._x0=e,this._y0=t}constructor(e,t){this._context=e,this._x=t}}function vQ(e){this._context=e}vQ.prototype={areaStart:vY,areaEnd:vY,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e*=1,t*=1,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}};function v0(e,t,r){var n=e._x1-e._x0,i=t-e._x1,a=(e._y1-e._y0)/(n||i<0&&-0),o=(r-e._y1)/(i||n<0&&-0);return((a<0?-1:1)+(o<0?-1:1))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs((a*i+o*n)/(n+i)))||0}function v1(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function v2(e,t,r){var n=e._x0,i=e._y0,a=e._x1,o=e._y1,l=(a-n)/3;e._context.bezierCurveTo(n+l,i+l*t,a-l,o-l*r,a,o)}function v5(e){this._context=e}function v3(e){this._context=new v6(e)}function v6(e){this._context=e}function v8(e){this._context=e}function v4(e){var t,r,n=e.length-1,i=Array(n),a=Array(n),o=Array(n);for(i[0]=0,a[0]=2,o[0]=e[0]+2*e[1],t=1;t<n-1;++t)i[t]=1,a[t]=4,o[t]=4*e[t]+2*e[t+1];for(i[n-1]=2,a[n-1]=7,o[n-1]=8*e[n-1]+e[n],t=1;t<n;++t)r=i[t]/a[t-1],a[t]-=r,o[t]-=r*o[t-1];for(i[n-1]=o[n-1]/a[n-1],t=n-2;t>=0;--t)i[t]=(o[t]-i[t+1])/a[t];for(t=0,a[n-1]=(e[n]+i[n-1])/2;t<n-1;++t)a[t]=2*e[t+1]-i[t+1];return[i,a]}function v7(e,t){this._context=e,this._t=t}function v9(){return(v9=Object.assign.bind()).apply(null,arguments)}function ge(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function gt(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ge(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ge(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}v5.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:v2(this,this._t0,v1(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(t*=1,(e*=1)!==this._x1||t!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,v2(this,v1(this,r=v0(this,e,t)),r);break;default:v2(this,this._t0,r=v0(this,e,t))}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}},(v3.prototype=Object.create(v5.prototype)).point=function(e,t){v5.prototype.point.call(this,t,e)},v6.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,i,a){this._context.bezierCurveTo(t,e,n,r,a,i)}},v8.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),2===r)this._context.lineTo(e[1],t[1]);else for(var n=v4(e),i=v4(t),a=0,o=1;o<r;++a,++o)this._context.bezierCurveTo(n[0][a],i[0][a],n[1][a],i[1][a],e[o],t[o]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}},v7.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}}this._x=e,this._y=t}};var gr={curveBasisClosed:function(e){return new v$(e)},curveBasisOpen:function(e){return new vZ(e)},curveBasis:function(e){return new vX(e)},curveBumpX:function(e){return new vJ(e,!0)},curveBumpY:function(e){return new vJ(e,!1)},curveLinearClosed:function(e){return new vQ(e)},curveLinear:vK,curveMonotoneX:function(e){return new v5(e)},curveMonotoneY:function(e){return new v3(e)},curveNatural:function(e){return new v8(e)},curveStep:function(e){return new v7(e,.5)},curveStepAfter:function(e){return new v7(e,1)},curveStepBefore:function(e){return new v7(e,0)}},gn=e=>o6(e.x)&&o6(e.y),gi=e=>e.x,ga=e=>e.y,go=e=>{var{className:t,points:r,path:n,pathRef:i}=e;if((!r||!r.length)&&!n)return null;var a=r&&r.length?(e=>{var t,{type:r="linear",points:n=[],baseLine:i,layout:a,connectNulls:o=!1}=e,l=((e,t)=>{if("function"==typeof e)return e;var r="curve".concat(tA(e));return("curveMonotone"===r||"curveBump"===r)&&t?gr["".concat(r).concat("vertical"===t?"Y":"X")]:gr[r]||vK})(r,a),u=o?n.filter(gn):n;if(Array.isArray(i)){var c=o?i.filter(e=>gn(e)):i,s=u.map((e,t)=>gt(gt({},e),{},{base:c[t]}));return(t="vertical"===a?vV().y(ga).x1(gi).x0(e=>e.base.x):vV().x(gi).y1(ga).y0(e=>e.base.y)).defined(gn).curve(l),t(s)}return(t="vertical"===a&&tv(i)?vV().y(ga).x1(gi).x0(i):tv(i)?vV().x(gi).y1(ga).y0(i):vW().x(gi).y(ga)).defined(gn).curve(l),t(u)})(e):n;return d.createElement("path",v9({},fX(e),fW(e),{className:(0,fz.clsx)("recharts-curve",t),d:null===a?void 0:a,ref:i}))},gl=["x","y","top","left","width","height","className"];function gu(){return(gu=Object.assign.bind()).apply(null,arguments)}function gc(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var gs=e=>{var{x:t=0,y:r=0,top:n=0,left:i=0,width:a=0,height:o=0,className:l}=e,u=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?gc(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gc(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({x:t,y:r,top:n,left:i,width:a,height:o},function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,gl));return tv(t)&&tv(r)&&tv(a)&&tv(o)&&tv(n)&&tv(i)?d.createElement("path",gu({},f2(u,!0),{className:(0,fz.clsx)("recharts-cross",l),d:"M".concat(t,",").concat(n,"v").concat(o,"M").concat(i,",").concat(r,"h").concat(a)})):null};function gf(e){var{cx:t,cy:r,radius:n,startAngle:i,endAngle:a}=e;return{points:[rx(t,r,n,i),rx(t,r,n,a)],cx:t,cy:r,radius:n,startAngle:i,endAngle:a}}function gh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function gd(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?gh(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gh(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function gp(){return(gp=Object.assign.bind()).apply(null,arguments)}function gy(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function gv(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?gy(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gy(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function gg(e){var t,r,n,{coordinate:i,payload:a,index:o,offset:l,tooltipAxisBandSize:u,layout:c,cursor:s,tooltipEventType:f,chartName:h}=e;if(!s||!i||"ScatterChart"!==h&&"axis"!==f)return null;if("ScatterChart"===h)r=i,n=gs;else if("BarChart"===h)t=u/2,r={stroke:"none",fill:"#ccc",x:"horizontal"===c?i.x-t:l.left+.5,y:"horizontal"===c?l.top+.5:i.y-t,width:"horizontal"===c?u:l.width-1,height:"horizontal"===c?l.height-1:u},n=dH;else if("radial"===c){var{cx:p,cy:y,radius:v,startAngle:g,endAngle:m}=gf(i);r={cx:p,cy:y,startAngle:g,endAngle:m,innerRadius:v,outerRadius:v},n=dQ}else r={points:function(e,t,r){var n,i,a,o;if("horizontal"===e)a=n=t.x,i=r.top,o=r.top+r.height;else if("vertical"===e)o=i=t.y,n=r.left,a=r.left+r.width;else if(null!=t.cx&&null!=t.cy)if("centric"!==e)return gf(t);else{var{cx:l,cy:u,innerRadius:c,outerRadius:s,angle:f}=t,h=rx(l,u,c,f),d=rx(l,u,s,f);n=h.x,i=h.y,a=d.x,o=d.y}return[{x:n,y:i},{x:a,y:o}]}(c,i,l)},n=go;var b="object"==typeof s&&"className"in s?s.className:void 0,x=gv(gv(gv(gv({stroke:"#ccc",pointerEvents:"none"},l),r),f2(s,!1)),{},{payload:a,payloadIndex:o,className:(0,fz.clsx)("recharts-tooltip-cursor",b)});return(0,d.isValidElement)(s)?(0,d.cloneElement)(s,x):(0,d.createElement)(n,x)}function gm(e){var t,r,n,i=(t=ru(lW),r=ru(st),n=ru(c7),rI(gd(gd({},t),{},{scale:n}),r)),a=r5(),o=r4(),l=sy();return d.createElement(gg,gp({},e,{coordinate:e.coordinate,index:e.index,payload:e.payload,offset:a,layout:o,tooltipAxisBandSize:i,chartName:l}))}function gb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function gx(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?gb(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gb(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function gw(e){return e.dataKey}var gO=[],gj={allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",axisId:0,contentStyle:{},cursor:!0,filterNull:!0,isAnimationActive:!h_.isSsr,itemSorter:"name",itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,wrapperStyle:{}};function gP(e){var t,r,n,i,a,o,l,u,c=hb(e,gj),{active:s,allowEscapeViewBox:f,animationDuration:h,animationEasing:p,content:y,filterNull:v,isAnimationActive:g,offset:m,payloadUniqBy:b,position:x,reverseDirection:w,useTranslate3d:O,wrapperStyle:j,cursor:P,shared:A,trigger:E,defaultIndex:S,portal:M,axisId:k}=c,T=ri(),C="number"==typeof S?String(S):S;(0,d.useEffect)(()=>{T(tH({shared:A,trigger:E,axisId:k,active:s,defaultIndex:C}))},[T,A,E,k,s,C]);var _=r1(),D=fR(),N=ru(e=>cw(e,A)),{activeIndex:I,isActive:L}=ru(e=>sS(e,N,E,C)),B=ru(e=>sE(e,N,E,C)),R=ru(e=>sA(e,N,E,C)),z=ru(e=>sP(e,N,E,C)),U=(0,d.useContext)(hl),F=null!=s?s:L,[K,q]=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],[t,r]=(0,d.useState)({height:0,left:0,top:0,width:0}),n=(0,d.useCallback)(e=>{if(null!=e){var n=e.getBoundingClientRect(),i={height:n.height,left:n.left,top:n.top,width:n.width};(Math.abs(i.height-t.height)>1||Math.abs(i.left-t.left)>1||Math.abs(i.top-t.top)>1||Math.abs(i.width-t.width)>1)&&r({height:i.height,left:i.left,top:i.top,width:i.width})}},[t.width,t.height,t.top,t.left,...e]);return[t,n]}([B,F]),H="axis"===N?R:void 0;t=ru(e=>((e,t,r)=>{if(null!=t){var n=cT(e);return"axis"===t?"hover"===r?n.axisInteraction.hover.dataKey:n.axisInteraction.click.dataKey:"hover"===r?n.itemInteraction.hover.dataKey:n.itemInteraction.click.dataKey}})(e,N,E)),r=ru(lj),n=ru(lw),i=ru(lO),o=null==(a=ru(ha))?void 0:a.active,(0,d.useEffect)(()=>{if(!o&&null!=n&&null!=r){var e=tZ({active:F,coordinate:z,dataKey:t,index:I,label:"number"==typeof H?String(H):H});hr.emit(hn,n,e,r)}},[o,z,t,I,H,r,n,i,F]);var W=null!=M?M:U;if(null==W)return null;var V=null!=B?B:gO;F||(V=gO),v&&V.length&&(l=B.filter(e=>null!=e.value&&(!0!==e.hide||c.includeHidden)),V=!0===b?(0,vU.default)(l,gw):"function"==typeof b?(0,vU.default)(l,b):l);var Y=V.length>0,G=d.createElement(vz,{allowEscapeViewBox:f,animationDuration:h,animationEasing:p,isAnimationActive:g,active:F,coordinate:z,hasPayload:Y,offset:m,position:x,reverseDirection:w,useTranslate3d:O,viewBox:_,wrapperStyle:j,lastBoundingBox:K,innerRef:q,hasPortalFromProps:!!M},(u=gx(gx({},c),{},{payload:V,label:H,active:F,coordinate:z,accessibilityLayer:D}),d.isValidElement(y)?d.cloneElement(y,u):"function"==typeof y?d.createElement(y,u):d.createElement(vC,u)));return d.createElement(d.Fragment,null,(0,vE.createPortal)(G,W),F&&d.createElement(gm,{cursor:P,tooltipEventType:N,coordinate:z,payload:B,index:I}))}var gA=e.i(58723);function gE(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function gS(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?gE(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gE(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var gM=(0,d.forwardRef)((e,t)=>{var{aspect:r,initialDimension:n={width:-1,height:-1},width:i="100%",height:a="100%",minWidth:o=0,minHeight:l,maxHeight:u,children:c,debounce:s=0,id:f,className:h,onResize:p,style:y={}}=e,v=(0,d.useRef)(null),g=(0,d.useRef)();g.current=p,(0,d.useImperativeHandle)(t,()=>v.current);var[m,b]=(0,d.useState)({containerWidth:n.width,containerHeight:n.height}),x=(0,d.useCallback)((e,t)=>{b(r=>{var n=Math.round(e),i=Math.round(t);return r.containerWidth===n&&r.containerHeight===i?r:{containerWidth:n,containerHeight:i}})},[]);(0,d.useEffect)(()=>{var e=e=>{var t,{width:r,height:n}=e[0].contentRect;x(r,n),null==(t=g.current)||t.call(g,r,n)};s>0&&(e=(0,gA.default)(e,s,{trailing:!0,leading:!1}));var t=new ResizeObserver(e),{width:r,height:n}=v.current.getBoundingClientRect();return x(r,n),t.observe(v.current),()=>{t.disconnect()}},[x,s]);var w=(0,d.useMemo)(()=>{var{containerWidth:e,containerHeight:t}=m;if(e<0||t<0)return null;vl(ty(i)||ty(a),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",i,a),vl(!r||r>0,"The aspect(%s) must be greater than zero.",r);var n=ty(i)?e:i,s=ty(a)?t:a;return r&&r>0&&(n?s=n/r:s&&(n=s*r),u&&s>u&&(s=u)),vl(n>0||s>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",n,s,i,a,o,l,r),d.Children.map(c,e=>(0,d.cloneElement)(e,{width:n,height:s,style:gS({width:n,height:s},e.props.style)}))},[r,c,a,u,l,o,m,i]);return d.createElement("div",{id:f?"".concat(f):void 0,className:(0,fz.clsx)("recharts-responsive-container",h),style:gS(gS({},y),{},{width:i,height:a,minWidth:o,minHeight:l,maxHeight:u}),ref:v},d.createElement("div",{style:{width:0,height:0,overflow:"visible"}},w))});function gk(e){var t=ri();return(0,d.useEffect)(()=>{t(fm(e))},[t,e]),null}var gT=["width","height","layout"];function gC(){return(gC=Object.assign.bind()).apply(null,arguments)}var g_={accessibilityLayer:!0,stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index",layout:"radial"},gD=(0,d.forwardRef)(function(e,t){var r,n=hb(e.categoricalChartProps,g_),{width:i,height:a,layout:o}=n,l=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(n,gT);if(!o8(i)||!o8(a))return null;var{chartName:u,defaultTooltipEventType:c,validateTooltipEventTypes:s,tooltipPayloadSearcher:f}=e;return d.createElement(fN,{preloadedState:{options:{chartName:u,defaultTooltipEventType:c,validateTooltipEventTypes:s,tooltipPayloadSearcher:f,eventEmitter:void 0}},reduxStoreName:null!=(r=n.id)?r:u},d.createElement(fI,{chartData:n.data}),d.createElement(fL,{width:i,height:a,layout:o,margin:n.margin}),d.createElement(fB,{accessibilityLayer:n.accessibilityLayer,barCategoryGap:n.barCategoryGap,maxBarSize:n.maxBarSize,stackOffset:n.stackOffset,barGap:n.barGap,barSize:n.barSize,syncId:n.syncId,syncMethod:n.syncMethod,className:n.className}),d.createElement(gk,{cx:n.cx,cy:n.cy,startAngle:n.startAngle,endAngle:n.endAngle,innerRadius:n.innerRadius,outerRadius:n.outerRadius}),d.createElement(hg,gC({width:i,height:a},l,{ref:t})))}),gN=["item"],gI={layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},gL=(0,d.forwardRef)((e,t)=>{var r=hb(e,gI);return d.createElement(gD,{chartName:"PieChart",defaultTooltipEventType:"item",validateTooltipEventTypes:gN,tooltipPayloadSearcher:tE,categoricalChartProps:r,ref:t})}),gB=e=>e.graphicalItems.polarItems,gR=eb([lU,lF],l8),gz=eb([gB,l5,gR],l9),gU=eb([gz],ui),gF=eb([gU,o5],uo),gK=eb([gF,l5,gz],uu),gq=eb([gF,l5,gz],(e,t,r)=>r.length>0?e.flatMap(e=>r.flatMap(r=>{var n;return{value:rA(e,null!=(n=t.dataKey)?n:r.dataKey),errorDomain:[]}})).filter(Boolean):(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:rA(e,t.dataKey),errorDomain:[]})):e.map(e=>({value:e,errorDomain:[]}))),gH=()=>void 0,gW=eb([l5,uz,gH,gq,gH,r8,lU],uU),gV=eb([l5,r8,gF,gK,lb,lU,gW],uq),gY=eb([gV,l5,uV],uG);function gG(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function gX(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?gG(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gG(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}eb([l5,gV,gY,lU],u$);var g$=eb([gB,(e,t)=>t],(e,t)=>e.filter(e=>"pie"===e.type).find(e=>e.id===t)),gZ=[],gJ=(e,t,r)=>(null==r?void 0:r.length)===0?gZ:r,gQ=eb([o5,g$,gJ],(e,t,r)=>{var n,{chartData:i}=e;if(null!=t&&((n=(null==t?void 0:t.data)!=null&&t.data.length>0?t.data:i)&&n.length||null==r||(n=r.map(e=>gX(gX({},t.presentationProps),e.props))),null!=n))return n}),g0=eb([gQ,g$,gJ],(e,t,r)=>{if(null!=e&&null!=t)return e.map((e,n)=>{var i,a,o=rA(e,t.nameKey,t.name);return a=null!=r&&null!=(i=r[n])&&null!=(i=i.props)&&i.fill?r[n].props.fill:"object"==typeof e&&null!=e&&"fill"in e?e.fill:t.fill,{value:rB(o,t.dataKey),color:a,payload:e,type:t.legendType}})}),g1=eb([gQ,g$,gJ,rG],(e,t,r,n)=>{if(null!=t&&null!=e)return function(e){var t,r,n,{pieSettings:i,displayedData:a,cells:o,offset:l}=e,{cornerRadius:u,startAngle:c,endAngle:s,dataKey:f,nameKey:h,tooltipType:d}=i,p=Math.abs(i.minAngle),y=td(s-c)*Math.min(Math.abs(s-c),360),v=Math.abs(y),g=a.length<=1?0:null!=(t=i.paddingAngle)?t:0,m=a.filter(e=>0!==rA(e,f,0)).length,b=v-m*p-(v>=360?m:m-1)*g,x=a.reduce((e,t)=>{var r=rA(t,f,0);return e+(tv(r)?r:0)},0);return x>0&&(r=a.map((e,t)=>{var r,a=rA(e,f,0),s=rA(e,h,t),v=((e,t,r)=>{let n,i,a;var{top:o,left:l,width:u,height:c}=t,s=rw(u,c),f=l+tx(e.cx,u,u/2),h=o+tx(e.cy,c,c/2),d=tx(e.innerRadius,s,0);return{cx:f,cy:h,innerRadius:d,outerRadius:(n=r,i=e.outerRadius,a=s,"function"==typeof i?i(n):tx(i,a,.8*a)),maxRadius:e.maxRadius||Math.sqrt(u*u+c*c)/2}})(i,l,e),m=(tv(a)?a:0)/x,w=g4(g4({},e),o&&o[t]&&o[t].props),O=(r=t?n.endAngle+td(y)*g*(0!==a):c)+td(y)*((0!==a?p:0)+m*b),j=(r+O)/2,P=(v.innerRadius+v.outerRadius)/2,A=[{name:s,value:a,payload:w,dataKey:f,type:d}],E=rx(v.cx,v.cy,P,j);return n=g4(g4(g4(g4({},i.presentationProps),{},{percent:m,cornerRadius:u,name:s,tooltipPayload:A,midAngle:j,middleRadius:P,tooltipPosition:E},w),v),{},{value:rA(e,f),startAngle:r,endAngle:O,payload:w,paddingAngle:td(y)*g})})),r}({offset:n,pieSettings:t,displayedData:e,cells:r})}),g2=["onMouseEnter","onClick","onMouseLeave"],g5=["id"],g3=["id"];function g6(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function g8(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function g4(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?g8(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):g8(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function g7(){return(g7=Object.assign.bind()).apply(null,arguments)}function g9(e){var t=(0,d.useMemo)(()=>f0(e.children,hT),[e.children]),r=ru(r=>g0(r,e.id,t));return null==r?null:d.createElement(yu,{legendPayload:r})}function me(e){var{dataKey:t,nameKey:r,sectors:n,stroke:i,strokeWidth:a,fill:o,name:l,hide:u,tooltipType:c}=e;return{dataDefinedOnItem:null==n?void 0:n.map(e=>e.tooltipPayload),positions:null==n?void 0:n.map(e=>e.tooltipPosition),settings:{stroke:i,strokeWidth:a,fill:o,dataKey:t,nameKey:r,name:rB(l,t),hide:u,type:c,color:o,unit:""}}}function mt(e){var{sectors:t,props:r,showLabels:n}=e,{label:i,labelLine:a,dataKey:o}=r;if(!n||!i||!t)return null;var l=fX(r),u=f2(i,!1),c=f2(a,!1),s="object"==typeof i&&"offsetRadius"in i&&i.offsetRadius||20,f=t.map((e,t)=>{var r,n,f=(e.startAngle+e.endAngle)/2,h=rx(e.cx,e.cy,e.outerRadius+s,f),p=g4(g4(g4(g4({},l),e),{},{stroke:"none"},u),{},{index:t,textAnchor:(r=h.x)>(n=e.cx)?"start":r<n?"end":"middle"},h),y=g4(g4(g4(g4({},l),e),{},{fill:"none",stroke:e.fill},c),{},{index:t,points:[rx(e.cx,e.cy,e.outerRadius,f),h],key:"line"});return d.createElement(hk,{key:"label-".concat(e.startAngle,"-").concat(e.endAngle,"-").concat(e.midAngle,"-").concat(t)},a&&((e,t)=>{if(d.isValidElement(e))return d.cloneElement(e,t);if("function"==typeof e)return e(t);var r=(0,fz.clsx)("recharts-pie-label-line","boolean"!=typeof e?e.className:"");return d.createElement(go,g7({},t,{type:"linear",className:r}))})(a,y),((e,t,r)=>{if(d.isValidElement(e))return d.cloneElement(e,t);var n=r;if("function"==typeof e&&(n=e(t),d.isValidElement(n)))return n;var i=(0,fz.clsx)("recharts-pie-label-text","boolean"!=typeof e&&"function"!=typeof e?e.className:"");return d.createElement(h3,g7({},t,{alignmentBaseline:"middle",className:i}),n)})(i,p,rA(e,o)))});return d.createElement(hk,{className:"recharts-pie-labels"},f)}function mr(e){var{sectors:t,activeShape:r,inactiveShape:n,allOtherPieProps:i,showLabels:a}=e,o=ru(so),{onMouseEnter:l,onClick:u,onMouseLeave:c}=i,s=g6(i,g2),f=pV(l,i.dataKey),h=pY(c),p=pG(u,i.dataKey);return null==t?null:d.createElement(d.Fragment,null,t.map((e,a)=>{if((null==e?void 0:e.startAngle)===0&&(null==e?void 0:e.endAngle)===0&&1!==t.length)return null;var l=r&&String(a)===o,u=l?r:o?n:null,c=g4(g4({},e),{},{stroke:e.stroke,tabIndex:-1,[rH]:a,[rW]:i.dataKey});return d.createElement(hk,g7({tabIndex:-1,className:"recharts-pie-sector"},fV(s,e,a),{onMouseEnter:f(e,a),onMouseLeave:h(e,a),onClick:p(e,a),key:"sector-".concat(null==e?void 0:e.startAngle,"-").concat(null==e?void 0:e.endAngle,"-").concat(e.midAngle,"-").concat(a)}),d.createElement(pR,g7({option:u,isActive:l,shapeType:"sector"},c)))}),d.createElement(mt,{sectors:t,props:i,showLabels:a}))}function mn(e){var{props:t,previousSectorsRef:r}=e,{sectors:n,isAnimationActive:i,animationBegin:a,animationDuration:o,animationEasing:l,activeShape:u,inactiveShape:c,onAnimationStart:s,onAnimationEnd:f}=t,h=yc(t,"recharts-pie-"),p=r.current,[y,v]=(0,d.useState)(!0),g=(0,d.useCallback)(()=>{"function"==typeof f&&f(),v(!1)},[f]),m=(0,d.useCallback)(()=>{"function"==typeof s&&s(),v(!0)},[s]);return d.createElement(yv,{begin:a,duration:o,isActive:i,easing:l,onAnimationStart:m,onAnimationEnd:g,key:h},e=>{var i=[],a=(n&&n[0]).startAngle;return n.forEach((t,r)=>{var n=p&&p[r],o=r>0?(0,th.default)(t,"paddingAngle",0):0;if(n){var l=tO(n.endAngle-n.startAngle,t.endAngle-t.startAngle),u=g4(g4({},t),{},{startAngle:a+o,endAngle:a+l(e)+o});i.push(u),a=u.endAngle}else{var{endAngle:c,startAngle:s}=t,f=tO(0,c-s)(e),h=g4(g4({},t),{},{startAngle:a+o,endAngle:a+f+o});i.push(h),a=h.endAngle}}),r.current=i,d.createElement(hk,null,d.createElement(mr,{sectors:i,activeShape:u,inactiveShape:c,allOtherPieProps:t,showLabels:!y}))})}function mi(e){var{sectors:t,isAnimationActive:r,activeShape:n,inactiveShape:i}=e,a=(0,d.useRef)(null),o=a.current;return r&&t&&t.length&&(!o||o!==t)?d.createElement(mn,{props:e,previousSectorsRef:a}):d.createElement(mr,{sectors:t,activeShape:n,inactiveShape:i,allOtherPieProps:e,showLabels:!0})}function ma(e){var{hide:t,className:r,rootTabIndex:n}=e,i=(0,fz.clsx)("recharts-pie",r);return t?null:d.createElement(hk,{tabIndex:n,className:i},d.createElement(mi,e))}var mo={animationBegin:400,animationDuration:1500,animationEasing:"ease",cx:"50%",cy:"50%",dataKey:"value",endAngle:360,fill:"#808080",hide:!1,innerRadius:0,isAnimationActive:!h_.isSsr,labelLine:!0,legendType:"rect",minAngle:0,nameKey:"name",outerRadius:"80%",paddingAngle:0,rootTabIndex:0,startAngle:0,stroke:"#fff"};function ml(e){var{id:t}=e,r=g6(e,g5),n=(0,d.useMemo)(()=>f0(e.children,hT),[e.children]),i=ru(e=>g1(e,t,n));return d.createElement(d.Fragment,null,d.createElement(pX,{fn:me,args:g4(g4({},e),{},{sectors:i})}),d.createElement(ma,g7({},r,{sectors:i})))}function mu(e){var t=hb(e,mo),{id:r}=t,n=g6(t,g3),i=fX(n);return d.createElement(pJ,{id:r,type:"pie"},e=>d.createElement(d.Fragment,null,d.createElement(yf,{type:"pie",id:e,data:n.data,dataKey:n.dataKey,hide:n.hide,angleAxisId:0,radiusAxisId:0,name:n.name,nameKey:n.nameKey,tooltipType:n.tooltipType,legendType:n.legendType,fill:n.fill,cx:n.cx,cy:n.cy,startAngle:n.startAngle,endAngle:n.endAngle,paddingAngle:n.paddingAngle,minAngle:n.minAngle,innerRadius:n.innerRadius,outerRadius:n.outerRadius,cornerRadius:n.cornerRadius,presentationProps:i}),d.createElement(g9,g7({},n,{id:e})),d.createElement(ml,g7({},n,{id:e})),n.children))}mu.displayName="Pie";var mc=["axis"],ms=(0,d.forwardRef)((e,t)=>d.createElement(hj,{chartName:"LineChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:mc,tooltipPayloadSearcher:tE,categoricalChartProps:e,ref:t})),mf=d;function mh(){return(mh=Object.assign.bind()).apply(null,arguments)}var md=e=>{var{cx:t,cy:r,r:n,className:i}=e,a=(0,fz.clsx)("recharts-dot",i);return t===+t&&r===+r&&n===+n?d.createElement("circle",mh({},fX(e),fW(e),{className:a,cx:t,cy:r,r:n})):null};function mp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function my(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?mp(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):mp(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function mv(e){var{points:t,mainColor:r,activeDot:n,itemDataKey:i}=e,a=ru(so),o=ru(sp);if(null==t||null==o)return null;var l=t.find(e=>o.includes(e.payload));return null==l?null:(e=>{var t,{point:r,childIndex:n,mainColor:i,activeDot:a,dataKey:o}=e;if(!1===a||null==r.x||null==r.y)return null;var l=my(my({index:n,dataKey:o,cx:r.x,cy:r.y,r:4,fill:null!=i?i:"none",strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},f2(a,!1)),fW(a));return t=(0,d.isValidElement)(a)?(0,d.cloneElement)(a,l):"function"==typeof a?a(l):d.createElement(md,l),d.createElement(hk,{className:"recharts-active-dot"},t)})({point:l,childIndex:Number(a),mainColor:r,dataKey:i,activeDot:n})}var mg=(e,t,r,n)=>cy(e,"xAxis",t,n),mm=(e,t,r,n)=>cp(e,"xAxis",t,n),mb=(e,t,r,n)=>cy(e,"yAxis",r,n),mx=(e,t,r,n)=>cp(e,"yAxis",r,n),mw=eb([r8,mg,mb,mm,mx],(e,t,r,n,i)=>rE(e,"xAxis")?rI(t,n,!1):rI(r,i,!1));function mO(e){return"line"===e.type}var mj=eb([l4,(e,t,r,n,i)=>i],(e,t)=>e.filter(mO).find(e=>e.id===t)),mP=eb([r8,mg,mb,mm,mx,mj,mw,o3],(e,t,r,n,i,a,o,l)=>{var u,{chartData:c,dataStartIndex:s,dataEndIndex:f}=l;if(null!=a&&null!=t&&null!=r&&null!=n&&null!=i&&0!==n.length&&0!==i.length&&null!=o){var{dataKey:h,data:d}=a;if(null!=(u=null!=d&&d.length>0?d:null==c?void 0:c.slice(s,f+1)))return function(e){var{layout:t,xAxis:r,yAxis:n,xAxisTicks:i,yAxisTicks:a,dataKey:o,bandSize:l,displayedData:u}=e;return u.map((e,u)=>{var c=rA(e,o);if("horizontal"===t)return{x:rC({axis:r,ticks:i,bandSize:l,entry:e,index:u}),y:null==c?null:n.scale(c),value:c,payload:e};var s=null==c?null:r.scale(c),f=rC({axis:n,ticks:a,bandSize:l,entry:e,index:u});return null==s||null==f?null:{x:s,y:f,value:c,payload:e}}).filter(Boolean)}({layout:e,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:i,dataKey:h,bandSize:o,displayedData:u})}}),mA=["id"],mE=["type","layout","connectNulls","needClip"],mS=["activeDot","animateNewValues","animationBegin","animationDuration","animationEasing","connectNulls","dot","hide","isAnimationActive","label","legendType","xAxisId","yAxisId","id"];function mM(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function mk(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?mM(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):mM(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function mT(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function mC(){return(mC=Object.assign.bind()).apply(null,arguments)}function m_(e){var{dataKey:t,data:r,stroke:n,strokeWidth:i,fill:a,name:o,hide:l,unit:u}=e;return{dataDefinedOnItem:r,positions:void 0,settings:{stroke:n,strokeWidth:i,fill:a,dataKey:t,nameKey:void 0,name:rB(o,t),hide:l,type:e.tooltipType,color:e.stroke,unit:u}}}var mD=(e,t)=>"".concat(t,"px ").concat(e-t,"px");function mN(e){var{clipPathId:t,points:r,props:n}=e,{dot:i,dataKey:a,needClip:o}=n;if(null==r||!i&&1!==r.length)return null;var{id:l}=n,u=mT(n,mA),c=f1(i),s=fX(u),f=f2(i,!0),h=r.map((e,t)=>{var n,o=mk(mk(mk({key:"dot-".concat(t),r:3},s),f),{},{index:t,cx:e.x,cy:e.y,dataKey:a,value:e.value,payload:e.payload,points:r});if(mf.isValidElement(i))n=mf.cloneElement(i,o);else if("function"==typeof i)n=i(o);else{var l=(0,fz.clsx)("recharts-line-dot","boolean"!=typeof i?i.className:"");n=mf.createElement(md,mC({},o,{className:l}))}return n}),d={clipPath:o?"url(#clipPath-".concat(c?"":"dots-").concat(t,")"):void 0};return mf.createElement(hk,mC({className:"recharts-line-dots",key:"dots"},d),h)}function mI(e){var{clipPathId:t,pathRef:r,points:n,strokeDasharray:i,props:a,showLabels:o}=e,{type:l,layout:u,connectNulls:c,needClip:s}=a,f=mk(mk({},f2(mT(a,mE),!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:s?"url(#clipPath-".concat(t,")"):void 0,points:n,type:l,layout:u,connectNulls:c,strokeDasharray:null!=i?i:a.strokeDasharray});return mf.createElement(mf.Fragment,null,(null==n?void 0:n.length)>1&&mf.createElement(go,mC({},f,{pathRef:r})),mf.createElement(mN,{points:n,clipPathId:t,props:a}),o&&dh.renderCallByParent(a,n))}function mL(e){var{clipPathId:t,props:r,pathRef:n,previousPointsRef:i,longestAnimatedLengthRef:a}=e,{points:o,strokeDasharray:l,isAnimationActive:u,animationBegin:c,animationDuration:s,animationEasing:f,animateNewValues:h,width:d,height:p,onAnimationEnd:y,onAnimationStart:v}=r,g=i.current,m=yc(r,"recharts-line-"),[b,x]=(0,mf.useState)(!1),w=(0,mf.useCallback)(()=>{"function"==typeof y&&y(),x(!1)},[y]),O=(0,mf.useCallback)(()=>{"function"==typeof v&&v(),x(!0)},[v]),j=function(e){try{return e&&e.getTotalLength&&e.getTotalLength()||0}catch(e){return 0}}(n.current),P=a.current;return mf.createElement(yv,{begin:c,duration:s,isActive:u,easing:f,onAnimationEnd:w,onAnimationStart:O,key:m},e=>{var u,c=Math.min(tj(P,j+P,e),j);if(u=l?((e,t,r)=>{var n=r.reduce((e,t)=>e+t);if(!n)return mD(t,e);for(var i=Math.floor(e/n),a=e%n,o=t-e,l=[],u=0,c=0;u<r.length;c+=r[u],++u)if(c+r[u]>a){l=[...r.slice(0,u),a-c];break}var s=l.length%2==0?[0,o]:[o];return[...function(e,t){for(var r=e.length%2!=0?[...e,0]:e,n=[],i=0;i<t;++i)n=[...n,...r];return n}(r,i),...l,...s].map(e=>"".concat(e,"px")).join(", ")})(c,j,"".concat(l).split(/[,\s]+/gim).map(e=>parseFloat(e))):mD(j,c),g){var s=g.length/o.length,f=1===e?o:o.map((t,r)=>{var n=Math.floor(r*s);if(g[n]){var i=g[n];return mk(mk({},t),{},{x:tj(i.x,t.x,e),y:tj(i.y,t.y,e)})}return h?mk(mk({},t),{},{x:tj(2*d,t.x,e),y:tj(p/2,t.y,e)}):mk(mk({},t),{},{x:t.x,y:t.y})});return i.current=f,mf.createElement(mI,{props:r,points:f,clipPathId:t,pathRef:n,showLabels:!b,strokeDasharray:u})}return e>0&&j>0&&(i.current=o,a.current=c),mf.createElement(mI,{props:r,points:o,clipPathId:t,pathRef:n,showLabels:!b,strokeDasharray:u})})}function mB(e){var{clipPathId:t,props:r}=e,{points:n,isAnimationActive:i}=r,a=(0,mf.useRef)(null),o=(0,mf.useRef)(0),l=(0,mf.useRef)(null),u=a.current;return i&&n&&n.length&&u!==n?mf.createElement(mL,{props:r,clipPathId:t,previousPointsRef:a,longestAnimatedLengthRef:o,pathRef:l}):mf.createElement(mI,{props:r,points:n,clipPathId:t,pathRef:l,showLabels:!0})}var mR=(e,t)=>({x:e.x,y:e.y,value:e.value,errorVal:rA(e.payload,t)});class mz extends mf.Component{render(){var e,{hide:t,dot:r,points:n,className:i,xAxisId:a,yAxisId:o,top:l,left:u,width:c,height:s,id:f,needClip:h}=this.props;if(t)return null;var d=(0,fz.clsx)("recharts-line",i),{r:p=3,strokeWidth:y=2}=null!=(e=f2(r,!1))?e:{r:3,strokeWidth:2},v=f1(r),g=2*p+y;return mf.createElement(mf.Fragment,null,mf.createElement(hk,{className:d},h&&mf.createElement("defs",null,mf.createElement(p5,{clipPathId:f,xAxisId:a,yAxisId:o}),!v&&mf.createElement("clipPath",{id:"clipPath-dots-".concat(f)},mf.createElement("rect",{x:u-g/2,y:l-g/2,width:c+g,height:s+g}))),mf.createElement(mB,{props:this.props,clipPathId:f}),mf.createElement(p1,{xAxisId:a,yAxisId:o,data:n,dataPointFormatter:mR,errorBarOffset:0},this.props.children)),mf.createElement(mv,{activeDot:this.props.activeDot,points:n,mainColor:this.props.stroke,itemDataKey:this.props.dataKey}))}}var mU={activeDot:!0,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",connectNulls:!1,dot:!0,fill:"#fff",hide:!1,isAnimationActive:!h_.isSsr,label:!1,legendType:"line",stroke:"#3182bd",strokeWidth:1,xAxisId:0,yAxisId:0};function mF(e){var t=hb(e,mU),{activeDot:r,animateNewValues:n,animationBegin:i,animationDuration:a,animationEasing:o,connectNulls:l,dot:u,hide:c,isAnimationActive:s,label:f,legendType:h,xAxisId:d,yAxisId:p,id:y}=t,v=mT(t,mS),{needClip:g}=p2(d,p),m=hd(),b=r4(),x=rJ(),w=ru(e=>mP(e,d,p,x,y));if("horizontal"!==b&&"vertical"!==b||null==w||null==m)return null;var{height:O,width:j,x:P,y:A}=m;return mf.createElement(mz,mC({},v,{id:y,connectNulls:l,dot:u,activeDot:r,animateNewValues:n,animationBegin:i,animationDuration:a,animationEasing:o,isAnimationActive:s,hide:c,label:f,legendType:h,xAxisId:d,yAxisId:p,points:w,layout:b,height:O,width:j,left:P,top:A,needClip:g}))}function mK(e){var t=hb(e,mU),r=rJ();return mf.createElement(pJ,{id:t.id,type:"line"},e=>mf.createElement(mf.Fragment,null,mf.createElement(yl,{legendPayload:(e=>{var{dataKey:t,name:r,stroke:n,legendType:i,hide:a}=e;return[{inactive:a,dataKey:t,type:i,color:n,value:rB(r,t),payload:e}]})(t)}),mf.createElement(pX,{fn:m_,args:t}),mf.createElement(ys,{type:"line",id:e,data:t.data,xAxisId:t.xAxisId,yAxisId:t.yAxisId,zAxisId:0,dataKey:t.dataKey,hide:t.hide,isPanorama:r}),mf.createElement(mF,mC({},t,{id:e}))))}mK.displayName="Line";var mq=d,mH=(e,t,r,n)=>cy(e,"xAxis",t,n),mW=(e,t,r,n)=>cp(e,"xAxis",t,n),mV=(e,t,r,n)=>cy(e,"yAxis",r,n),mY=(e,t,r,n)=>cp(e,"yAxis",r,n),mG=eb([r8,mH,mV,mW,mY],(e,t,r,n,i)=>rE(e,"xAxis")?rI(t,n,!1):rI(r,i,!1)),mX=eb([l4,(e,t,r,n,i)=>i],(e,t)=>e.filter(e=>"area"===e.type).find(e=>e.id===t)),m$=eb([r8,mH,mV,mW,mY,(e,t,r,n,i)=>{var a,o,l=mX(e,t,r,n,i);if(null!=l&&null!=(o=rE(r8(e),"xAxis")?up(e,"yAxis",r,n):up(e,"xAxis",t,n))){var{stackId:u}=l,c=lK(l);if(null!=u&&null!=c){var s=null==(a=o[u])?void 0:a.stackedData;return null==s?void 0:s.find(e=>e.key===c)}}},o3,mG,mX],(e,t,r,n,i,a,o,l,u)=>{var c,{chartData:s,dataStartIndex:f,dataEndIndex:h}=o;if(null!=u&&("horizontal"===e||"vertical"===e)&&null!=t&&null!=r&&null!=n&&null!=i&&0!==n.length&&0!==i.length&&null!=l){var{data:d}=u;if(null!=(c=d&&d.length>0?d:null==s?void 0:s.slice(f,h+1)))return function(e){var t,{areaSettings:{connectNulls:r,baseValue:n,dataKey:i},stackedData:a,layout:o,chartBaseValue:l,xAxis:u,yAxis:c,displayedData:s,dataStartIndex:f,xAxisTicks:h,yAxisTicks:d,bandSize:p}=e,y=a&&a.length,v=((e,t,r,n,i)=>{var a=null!=r?r:t;if(tv(a))return a;var o="horizontal"===e?i:n,l=o.scale.domain();if("number"===o.type){var u=Math.max(l[0],l[1]),c=Math.min(l[0],l[1]);return"dataMin"===a?c:"dataMax"===a||u<0?u:Math.max(Math.min(l[0],l[1]),0)}return"dataMin"===a?l[0]:"dataMax"===a?l[1]:l[0]})(o,l,n,u,c),g="horizontal"===o,m=!1,b=s.map((e,t)=>{y?n=a[f+t]:Array.isArray(n=rA(e,i))?m=!0:n=[v,n];var n,o=null==n[1]||y&&!r&&null==rA(e,i);return g?{x:rC({axis:u,ticks:h,bandSize:p,entry:e,index:t}),y:o?null:c.scale(n[1]),value:n,payload:e}:{x:o?null:u.scale(n[1]),y:rC({axis:c,ticks:d,bandSize:p,entry:e,index:t}),value:n,payload:e}});return t=y||m?b.map(e=>{var t=Array.isArray(e.value)?e.value[0]:null;return g?{x:e.x,y:null!=t&&null!=e.y?c.scale(t):null,payload:e.payload}:{x:null!=t?u.scale(t):null,y:e.y,payload:e.payload}}):g?c.scale(v):u.scale(v),{points:b,baseLine:t,isRange:m}}({layout:e,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:i,dataStartIndex:f,areaSettings:u,stackedData:a,displayedData:c,chartBaseValue:void 0,bandSize:l})}}),mZ=["id"],mJ=["activeDot","animationBegin","animationDuration","animationEasing","connectNulls","dot","fill","fillOpacity","hide","isAnimationActive","legendType","stroke","xAxisId","yAxisId"];function mQ(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function m0(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function m1(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?m0(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):m0(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function m2(){return(m2=Object.assign.bind()).apply(null,arguments)}function m5(e,t){return e&&"none"!==e?e:t}function m3(e){var{dataKey:t,data:r,stroke:n,strokeWidth:i,fill:a,name:o,hide:l,unit:u}=e;return{dataDefinedOnItem:r,positions:void 0,settings:{stroke:n,strokeWidth:i,fill:a,dataKey:t,nameKey:void 0,name:rB(o,t),hide:l,type:e.tooltipType,color:m5(n,a),unit:u}}}function m6(e){var{clipPathId:t,points:r,props:n}=e,{needClip:i,dot:a,dataKey:o}=n;if(null==r||!a&&1!==r.length)return null;var l=f1(a),u=fX(n),c=f2(a,!0),s=r.map((e,t)=>{var n,i=m1(m1(m1({key:"dot-".concat(t),r:3},u),c),{},{index:t,cx:e.x,cy:e.y,dataKey:o,value:e.value,payload:e.payload,points:r});if(mq.isValidElement(a))n=mq.cloneElement(a,i);else if("function"==typeof a)n=a(i);else{var l=(0,fz.clsx)("recharts-area-dot","boolean"!=typeof a?a.className:"");n=mq.createElement(md,m2({},i,{className:l}))}return n}),f={clipPath:i?"url(#clipPath-".concat(l?"":"dots-").concat(t,")"):void 0};return mq.createElement(hk,m2({className:"recharts-area-dots"},f),s)}function m8(e){var{points:t,baseLine:r,needClip:n,clipPathId:i,props:a,showLabels:o}=e,{layout:l,type:u,stroke:c,connectNulls:s,isRange:f}=a,{id:h}=a,d=mQ(a,mZ),p=fX(d);return mq.createElement(mq.Fragment,null,(null==t?void 0:t.length)>1&&mq.createElement(hk,{clipPath:n?"url(#clipPath-".concat(i,")"):void 0},mq.createElement(go,m2({},p,{id:h,points:t,connectNulls:s,type:u,baseLine:r,layout:l,stroke:"none",className:"recharts-area-area"})),"none"!==c&&mq.createElement(go,m2({},p,{className:"recharts-area-curve",layout:l,type:u,connectNulls:s,fill:"none",points:t})),"none"!==c&&f&&mq.createElement(go,m2({},p,{className:"recharts-area-curve",layout:l,type:u,connectNulls:s,fill:"none",points:r}))),mq.createElement(m6,{points:t,props:d,clipPathId:i}),o&&dh.renderCallByParent(d,t))}function m4(e){var{alpha:t,baseLine:r,points:n,strokeWidth:i}=e,a=n[0].y,o=n[n.length-1].y;if(!o6(a)||!o6(o))return null;var l=t*Math.abs(a-o),u=Math.max(...n.map(e=>e.x||0));return(tv(r)?u=Math.max(r,u):r&&Array.isArray(r)&&r.length&&(u=Math.max(...r.map(e=>e.x||0),u)),tv(u))?mq.createElement("rect",{x:0,y:a<o?a:a-l,width:u+(i?parseInt("".concat(i),10):1),height:Math.floor(l)}):null}function m7(e){var{alpha:t,baseLine:r,points:n,strokeWidth:i}=e,a=n[0].x,o=n[n.length-1].x;if(!o6(a)||!o6(o))return null;var l=t*Math.abs(a-o),u=Math.max(...n.map(e=>e.y||0));return(tv(r)?u=Math.max(r,u):r&&Array.isArray(r)&&r.length&&(u=Math.max(...r.map(e=>e.y||0),u)),tv(u))?mq.createElement("rect",{x:a<o?a:a-l,y:0,width:l,height:Math.floor(u+(i?parseInt("".concat(i),10):1))}):null}function m9(e){var{alpha:t,layout:r,points:n,baseLine:i,strokeWidth:a}=e;return"vertical"===r?mq.createElement(m4,{alpha:t,points:n,baseLine:i,strokeWidth:a}):mq.createElement(m7,{alpha:t,points:n,baseLine:i,strokeWidth:a})}function be(e){var{needClip:t,clipPathId:r,props:n,previousPointsRef:i,previousBaselineRef:a}=e,{points:o,baseLine:l,isAnimationActive:u,animationBegin:c,animationDuration:s,animationEasing:f,onAnimationStart:h,onAnimationEnd:d}=n,p=yc(n,"recharts-area-"),[y,v]=(0,mq.useState)(!0),g=(0,mq.useCallback)(()=>{"function"==typeof d&&d(),v(!1)},[d]),m=(0,mq.useCallback)(()=>{"function"==typeof h&&h(),v(!0)},[h]),b=i.current,x=a.current;return mq.createElement(yv,{begin:c,duration:s,isActive:u,easing:f,onAnimationEnd:g,onAnimationStart:m,key:p},e=>{if(b){var u,c=b.length/o.length,s=1===e?o:o.map((t,r)=>{var n=Math.floor(r*c);if(b[n]){var i=b[n];return m1(m1({},t),{},{x:tj(i.x,t.x,e),y:tj(i.y,t.y,e)})}return t});if(tv(l))u=tj(x,l,e);else u=null==l||tp(l)?tj(x,0,e):l.map((t,r)=>{var n=Math.floor(r*c);if(Array.isArray(x)&&x[n]){var i=x[n];return m1(m1({},t),{},{x:tj(i.x,t.x,e),y:tj(i.y,t.y,e)})}return t});return e>0&&(i.current=s,a.current=u),mq.createElement(m8,{points:s,baseLine:u,needClip:t,clipPathId:r,props:n,showLabels:!y})}return e>0&&(i.current=o,a.current=l),mq.createElement(hk,null,mq.createElement("defs",null,mq.createElement("clipPath",{id:"animationClipPath-".concat(r)},mq.createElement(m9,{alpha:e,points:o,baseLine:l,layout:n.layout,strokeWidth:n.strokeWidth}))),mq.createElement(hk,{clipPath:"url(#animationClipPath-".concat(r,")")},mq.createElement(m8,{points:o,baseLine:l,needClip:t,clipPathId:r,props:n,showLabels:!0})))})}function bt(e){var{needClip:t,clipPathId:r,props:n}=e,{points:i,baseLine:a,isAnimationActive:o}=n,l=(0,mq.useRef)(null),u=(0,mq.useRef)(),c=l.current,s=u.current;return o&&i&&i.length&&(c!==i||s!==a)?mq.createElement(be,{needClip:t,clipPathId:r,props:n,previousPointsRef:l,previousBaselineRef:u}):mq.createElement(m8,{points:i,baseLine:a,needClip:t,clipPathId:r,props:n,showLabels:!0})}class br extends mq.PureComponent{render(){var e,{hide:t,dot:r,points:n,className:i,top:a,left:o,needClip:l,xAxisId:u,yAxisId:c,width:s,height:f,id:h,baseLine:d}=this.props;if(t)return null;var p=(0,fz.clsx)("recharts-area",i),{r:y=3,strokeWidth:v=2}=null!=(e=f2(r,!1))?e:{r:3,strokeWidth:2},g=f1(r),m=2*y+v;return mq.createElement(mq.Fragment,null,mq.createElement(hk,{className:p},l&&mq.createElement("defs",null,mq.createElement(p5,{clipPathId:h,xAxisId:u,yAxisId:c}),!g&&mq.createElement("clipPath",{id:"clipPath-dots-".concat(h)},mq.createElement("rect",{x:o-m/2,y:a-m/2,width:s+m,height:f+m}))),mq.createElement(bt,{needClip:l,clipPathId:h,props:this.props})),mq.createElement(mv,{points:n,mainColor:m5(this.props.stroke,this.props.fill),itemDataKey:this.props.dataKey,activeDot:this.props.activeDot}),this.props.isRange&&Array.isArray(d)&&mq.createElement(mv,{points:d,mainColor:m5(this.props.stroke,this.props.fill),itemDataKey:this.props.dataKey,activeDot:this.props.activeDot}))}}var bn={activeDot:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",connectNulls:!1,dot:!1,fill:"#3182bd",fillOpacity:.6,hide:!1,isAnimationActive:!h_.isSsr,legendType:"line",stroke:"#3182bd",xAxisId:0,yAxisId:0};function bi(e){var t,r=hb(e,bn),{activeDot:n,animationBegin:i,animationDuration:a,animationEasing:o,connectNulls:l,dot:u,fill:c,fillOpacity:s,hide:f,isAnimationActive:h,legendType:d,stroke:p,xAxisId:y,yAxisId:v}=r,g=mQ(r,mJ),m=r4(),b=sy(),{needClip:x}=p2(y,v),w=rJ(),{points:O,isRange:j,baseLine:P}=null!=(t=ru(t=>m$(t,y,v,w,e.id)))?t:{},A=hd();if("horizontal"!==m&&"vertical"!==m||null==A||"AreaChart"!==b&&"ComposedChart"!==b)return null;var{height:E,width:S,x:M,y:k}=A;return O&&O.length?mq.createElement(br,m2({},g,{activeDot:n,animationBegin:i,animationDuration:a,animationEasing:o,baseLine:P,connectNulls:l,dot:u,fill:c,fillOpacity:s,height:E,hide:f,layout:m,isAnimationActive:h,isRange:j,legendType:d,needClip:x,points:O,stroke:p,width:S,left:M,top:k,xAxisId:y,yAxisId:v})):null}function ba(e){var t=hb(e,bn),r=rJ();return mq.createElement(pJ,{id:t.id,type:"area"},e=>mq.createElement(mq.Fragment,null,mq.createElement(yl,{legendPayload:(e=>{var{dataKey:t,name:r,stroke:n,fill:i,legendType:a,hide:o}=e;return[{inactive:o,dataKey:t,type:a,color:m5(n,i),value:rB(r,t),payload:e}]})(t)}),mq.createElement(pX,{fn:m3,args:t}),mq.createElement(ys,{type:"area",id:e,data:t.data,dataKey:t.dataKey,xAxisId:t.xAxisId,yAxisId:t.yAxisId,zAxisId:0,stackId:rT(t.stackId),hide:t.hide,barSize:void 0,baseValue:t.baseValue,isPanorama:r,connectNulls:t.connectNulls}),mq.createElement(bi,m2({},t,{id:e}))))}ba.displayName="Area";var bo=["axis"],bl=(0,d.forwardRef)((e,t)=>d.createElement(hj,{chartName:"AreaChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:bo,tooltipPayloadSearcher:tE,categoricalChartProps:e,ref:t})),bu=e.i(25652),bc=e.i(17923),bs=e.i(75254);let bf=(0,bs.default)("chart-pie",[["path",{d:"M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z",key:"pzmjnu"}],["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}]]),bh=(0,bs.default)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]);var bd=e.i(16973);let bp=["#0088FE","#00C49F","#FFBB28","#FF8042","#8884D8"];function by(){let{data:e,isLoading:t,error:r}=(0,f.useQuery)({queryKey:["alert-stats"],queryFn:()=>bd.apiService.getAlertStats(),refetchInterval:6e4});if(t)return(0,s.jsx)("div",{className:"grid gap-4 md:grid-cols-2",children:[void 0,void 0,void 0,void 0].map((e,t)=>(0,s.jsxs)(h.Card,{children:[(0,s.jsx)(h.CardHeader,{children:(0,s.jsx)("div",{className:"h-6 bg-muted rounded w-1/3 animate-pulse"})}),(0,s.jsx)(h.CardContent,{children:(0,s.jsx)("div",{className:"h-64 bg-muted rounded animate-pulse"})})]},t))});if(r||!e)return(0,s.jsx)(h.Card,{children:(0,s.jsx)(h.CardContent,{className:"flex items-center justify-center h-64",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(bc.BarChart3,{className:"h-8 w-8 text-muted-foreground mx-auto mb-2"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Failed to load analytics data"})]})})});let n=Object.entries(e.detection_counts||{}).map(e=>{let[t,r]=e;return{name:t.charAt(0).toUpperCase()+t.slice(1),value:r}}),i=e.hourly_distribution||[],a=e.daily_distribution||[];return(0,s.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,s.jsxs)(h.Card,{children:[(0,s.jsx)(h.CardHeader,{children:(0,s.jsxs)(h.CardTitle,{className:"flex items-center gap-2",children:[(0,s.jsx)(bf,{className:"h-5 w-5"}),"Detection Types"]})}),(0,s.jsx)(h.CardContent,{children:(0,s.jsx)(gM,{width:"100%",height:300,children:(0,s.jsxs)(gL,{children:[(0,s.jsx)(mu,{data:n,cx:"50%",cy:"50%",labelLine:!1,label:e=>{let{name:t,percent:r}=e;return"".concat(t," ").concat((100*(r||0)).toFixed(0),"%")},outerRadius:80,fill:"#8884d8",dataKey:"value",children:n.map((e,t)=>(0,s.jsx)(hT,{fill:bp[t%bp.length]},"cell-".concat(t)))}),(0,s.jsx)(gP,{})]})})})]}),(0,s.jsxs)(h.Card,{children:[(0,s.jsx)(h.CardHeader,{children:(0,s.jsxs)(h.CardTitle,{className:"flex items-center gap-2",children:[(0,s.jsx)(bc.BarChart3,{className:"h-5 w-5"}),"Hourly Distribution"]})}),(0,s.jsx)(h.CardContent,{children:(0,s.jsx)(gM,{width:"100%",height:300,children:(0,s.jsxs)(hA,{data:i,children:[(0,s.jsx)(vA,{strokeDasharray:"3 3"}),(0,s.jsx)(y4,{dataKey:"hour",tickFormatter:e=>"".concat(e,":00")}),(0,s.jsx)(vo,{}),(0,s.jsx)(gP,{labelFormatter:e=>"".concat(e,":00"),formatter:e=>[e,"Alerts"]}),(0,s.jsx)(yN,{dataKey:"count",fill:"#0088FE"})]})})})]}),(0,s.jsxs)(h.Card,{children:[(0,s.jsx)(h.CardHeader,{children:(0,s.jsxs)(h.CardTitle,{className:"flex items-center gap-2",children:[(0,s.jsx)(bu.TrendingUp,{className:"h-5 w-5"}),"Daily Trend"]})}),(0,s.jsx)(h.CardContent,{children:(0,s.jsx)(gM,{width:"100%",height:300,children:(0,s.jsxs)(ms,{data:a,children:[(0,s.jsx)(vA,{strokeDasharray:"3 3"}),(0,s.jsx)(y4,{dataKey:"date",tickFormatter:e=>new Date(e).toLocaleDateString()}),(0,s.jsx)(vo,{}),(0,s.jsx)(gP,{labelFormatter:e=>new Date(e).toLocaleDateString(),formatter:e=>[e,"Alerts"]}),(0,s.jsx)(mK,{type:"monotone",dataKey:"count",stroke:"#00C49F",strokeWidth:2,dot:{fill:"#00C49F"}})]})})})]}),(0,s.jsxs)(h.Card,{children:[(0,s.jsx)(h.CardHeader,{children:(0,s.jsxs)(h.CardTitle,{className:"flex items-center gap-2",children:[(0,s.jsx)(bh,{className:"h-5 w-5"}),"Activity Overview"]})}),(0,s.jsx)(h.CardContent,{children:(0,s.jsx)(gM,{width:"100%",height:300,children:(0,s.jsxs)(bl,{data:a,children:[(0,s.jsx)(vA,{strokeDasharray:"3 3"}),(0,s.jsx)(y4,{dataKey:"date",tickFormatter:e=>new Date(e).toLocaleDateString()}),(0,s.jsx)(vo,{}),(0,s.jsx)(gP,{labelFormatter:e=>new Date(e).toLocaleDateString(),formatter:e=>[e,"Alerts"]}),(0,s.jsx)(ba,{type:"monotone",dataKey:"count",stroke:"#FFBB28",fill:"#FFBB28",fillOpacity:.3})]})})})]})]})}}]);