import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiService } from '@/services/api';
import { Alert } from '@/types';

// Health Status Hook
export function useHealthStatus() {
  return useQuery({
    queryKey: ['health-status'],
    queryFn: () => apiService.getHealthStatus(),
    refetchInterval: 15000,
    retry: 3,
  });
}

// Alerts Hooks
export function useAlerts(limit?: number) {
  return useQuery({
    queryKey: ['alerts', limit],
    queryFn: () => apiService.getAlerts(limit),
    refetchInterval: 10000,
  });
}

export function useAlert(id: string) {
  return useQuery({
    queryKey: ['alert', id],
    queryFn: () => apiService.getAlert(id),
    enabled: !!id,
  });
}

export function useUpdateAlertStatus() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, status }: { id: string; status: Alert['status'] }) =>
      apiService.updateAlertStatus(id, status),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['alerts'] });
      queryClient.invalidateQueries({ queryKey: ['alert-stats'] });
    },
  });
}

// Analytics Hook
export function useAlertStats() {
  return useQuery({
    queryKey: ['alert-stats'],
    queryFn: () => apiService.getAlertStats(),
    refetchInterval: 60000,
  });
}

// Camera Stream Hook
export function useCameraStream() {
  return {
    streamUrl: apiService.getCameraStreamUrl(),
    wsUrl: apiService.getWebSocketUrl(),
  };
}
