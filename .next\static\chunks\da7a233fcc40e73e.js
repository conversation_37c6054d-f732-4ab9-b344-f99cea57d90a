(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,33525,(e,t,s)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),Object.defineProperty(s,"warnOnce",{enumerable:!0,get:function(){return i}});let i=e=>{}},66027,e=>{"use strict";e.s(["useQuery",()=>et],66027);var t,s,i,a,r,l,n,o,c,d,h,u,p,v,f,m,x,b,g,_,y,w,j,k,C=e.i(39946),N=e.i(70292),S=e.i(62351),R=e.i(37696),E=e.i(88245),M=e.i(75555),O=e.i(40143),T=e.i(86491),A=e.i(15823),W=e.i(93803),L=e.i(19273),F=(t=new WeakMap,s=new WeakMap,i=new WeakMap,a=new WeakMap,r=new WeakMap,l=new WeakMap,n=new WeakMap,o=new WeakMap,c=new WeakMap,d=new WeakMap,h=new WeakMap,u=new WeakMap,p=new WeakMap,v=new WeakMap,f=new WeakMap,m=new WeakSet,x=new WeakSet,b=new WeakSet,g=new WeakSet,_=new WeakSet,y=new WeakSet,w=new WeakSet,j=new WeakSet,k=new WeakSet,class extends A.Subscribable{bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&((0,C._)(this,s).addObserver(this),U((0,C._)(this,s),this.options)?(0,R._)(this,m,P).call(this):this.updateResult(),(0,R._)(this,_,H).call(this))}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return q((0,C._)(this,s),this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return q((0,C._)(this,s),this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,(0,R._)(this,y,K).call(this),(0,R._)(this,w,V).call(this),(0,C._)(this,s).removeObserver(this)}setOptions(e){let i=this.options,a=(0,C._)(this,s);if(this.options=(0,C._)(this,t).defaultQueryOptions(e),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof(0,L.resolveEnabled)(this.options.enabled,(0,C._)(this,s)))throw Error("Expected enabled to be a boolean or a callback that returns a boolean");(0,R._)(this,j,X).call(this),(0,C._)(this,s).setOptions(this.options),i._defaulted&&!(0,L.shallowEqualObjects)(this.options,i)&&(0,C._)(this,t).getQueryCache().notify({type:"observerOptionsUpdated",query:(0,C._)(this,s),observer:this});let r=this.hasListeners();r&&z((0,C._)(this,s),a,this.options,i)&&(0,R._)(this,m,P).call(this),this.updateResult(),r&&((0,C._)(this,s)!==a||(0,L.resolveEnabled)(this.options.enabled,(0,C._)(this,s))!==(0,L.resolveEnabled)(i.enabled,(0,C._)(this,s))||(0,L.resolveStaleTime)(this.options.staleTime,(0,C._)(this,s))!==(0,L.resolveStaleTime)(i.staleTime,(0,C._)(this,s)))&&(0,R._)(this,x,D).call(this);let l=(0,R._)(this,b,I).call(this);r&&((0,C._)(this,s)!==a||(0,L.resolveEnabled)(this.options.enabled,(0,C._)(this,s))!==(0,L.resolveEnabled)(i.enabled,(0,C._)(this,s))||l!==(0,C._)(this,v))&&(0,R._)(this,g,B).call(this,l)}getOptimisticResult(e){var i,n;let o=(0,C._)(this,t).getQueryCache().build((0,C._)(this,t),e),c=this.createResult(o,e);return i=this,n=c,(0,L.shallowEqualObjects)(i.getCurrentResult(),n)||((0,S._)(this,a,c),(0,S._)(this,l,this.options),(0,S._)(this,r,(0,C._)(this,s).state)),c}getCurrentResult(){return(0,C._)(this,a)}trackResult(e,t){return new Proxy(e,{get:(e,s)=>(this.trackProp(s),null==t||t(s),"promise"!==s||this.options.experimental_prefetchInRender||"pending"!==(0,C._)(this,n).status||(0,C._)(this,n).reject(Error("experimental_prefetchInRender feature flag is not enabled")),Reflect.get(e,s))})}trackProp(e){(0,C._)(this,f).add(e)}getCurrentQuery(){return(0,C._)(this,s)}refetch(){let{...e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.fetch({...e})}fetchOptimistic(e){let s=(0,C._)(this,t).defaultQueryOptions(e),i=(0,C._)(this,t).getQueryCache().build((0,C._)(this,t),s);return i.fetch().then(()=>this.createResult(i,s))}fetch(e){var t;return(0,R._)(this,m,P).call(this,{...e,cancelRefetch:null==(t=e.cancelRefetch)||t}).then(()=>(this.updateResult(),(0,C._)(this,a)))}createResult(e,t){let u,p=(0,C._)(this,s),v=this.options,f=(0,C._)(this,a),m=(0,C._)(this,r),x=(0,C._)(this,l),b=e!==p?e.state:(0,C._)(this,i),{state:g}=e,_={...g},y=!1;if(t._optimisticResults){let s=this.hasListeners(),i=!s&&U(e,t),a=s&&z(e,p,t,v);(i||a)&&(_={..._,...(0,T.fetchState)(g.data,e.options)}),"isRestoring"===t._optimisticResults&&(_.fetchStatus="idle")}let{error:w,errorUpdatedAt:j,status:k}=_;u=_.data;let N=!1;if(void 0!==t.placeholderData&&void 0===u&&"pending"===k){let e;if((null==f?void 0:f.isPlaceholderData)&&t.placeholderData===(null==x?void 0:x.placeholderData))e=f.data,N=!0;else{var R;e="function"==typeof t.placeholderData?t.placeholderData(null==(R=(0,C._)(this,h))?void 0:R.state.data,(0,C._)(this,h)):t.placeholderData}void 0!==e&&(k="success",u=(0,L.replaceData)(null==f?void 0:f.data,e,t),y=!0)}if(t.select&&void 0!==u&&!N)if(f&&u===(null==m?void 0:m.data)&&t.select===(0,C._)(this,c))u=(0,C._)(this,d);else try{(0,S._)(this,c,t.select),u=t.select(u),u=(0,L.replaceData)(null==f?void 0:f.data,u,t),(0,S._)(this,d,u),(0,S._)(this,o,null)}catch(e){(0,S._)(this,o,e)}(0,C._)(this,o)&&(w=(0,C._)(this,o),u=(0,C._)(this,d),j=Date.now(),k="error");let E="fetching"===_.fetchStatus,M="pending"===k,O="error"===k,A=M&&E,F=void 0!==u,q={status:k,fetchStatus:_.fetchStatus,isPending:M,isSuccess:"success"===k,isError:O,isInitialLoading:A,isLoading:A,data:u,dataUpdatedAt:_.dataUpdatedAt,error:w,errorUpdatedAt:j,failureCount:_.fetchFailureCount,failureReason:_.fetchFailureReason,errorUpdateCount:_.errorUpdateCount,isFetched:_.dataUpdateCount>0||_.errorUpdateCount>0,isFetchedAfterMount:_.dataUpdateCount>b.dataUpdateCount||_.errorUpdateCount>b.errorUpdateCount,isFetching:E,isRefetching:E&&!M,isLoadingError:O&&!F,isPaused:"paused"===_.fetchStatus,isPlaceholderData:y,isRefetchError:O&&F,isStale:Q(e,t),refetch:this.refetch,promise:(0,C._)(this,n),isEnabled:!1!==(0,L.resolveEnabled)(t.enabled,e)};if(this.options.experimental_prefetchInRender){let t=e=>{"error"===q.status?e.reject(q.error):void 0!==q.data&&e.resolve(q.data)},s=()=>{t((0,S._)(this,n,q.promise=(0,W.pendingThenable)()))},i=(0,C._)(this,n);switch(i.status){case"pending":e.queryHash===p.queryHash&&t(i);break;case"fulfilled":("error"===q.status||q.data!==i.value)&&s();break;case"rejected":("error"!==q.status||q.error!==i.reason)&&s()}}return q}updateResult(){let e=(0,C._)(this,a),t=this.createResult((0,C._)(this,s),this.options);if((0,S._)(this,r,(0,C._)(this,s).state),(0,S._)(this,l,this.options),void 0!==(0,C._)(this,r).data&&(0,S._)(this,h,(0,C._)(this,s)),(0,L.shallowEqualObjects)(t,e))return;(0,S._)(this,a,t);let i=()=>{if(!e)return!0;let{notifyOnChangeProps:t}=this.options,s="function"==typeof t?t():t;if("all"===s||!s&&!(0,C._)(this,f).size)return!0;let i=new Set(null!=s?s:(0,C._)(this,f));return this.options.throwOnError&&i.add("error"),Object.keys((0,C._)(this,a)).some(t=>(0,C._)(this,a)[t]!==e[t]&&i.has(t))};(0,R._)(this,k,Y).call(this,{listeners:i()})}onQueryUpdate(){this.updateResult(),this.hasListeners()&&(0,R._)(this,_,H).call(this)}constructor(e,C){super(),(0,E._)(this,m),(0,E._)(this,x),(0,E._)(this,b),(0,E._)(this,g),(0,E._)(this,_),(0,E._)(this,y),(0,E._)(this,w),(0,E._)(this,j),(0,E._)(this,k),(0,N._)(this,t,{writable:!0,value:void 0}),(0,N._)(this,s,{writable:!0,value:void 0}),(0,N._)(this,i,{writable:!0,value:void 0}),(0,N._)(this,a,{writable:!0,value:void 0}),(0,N._)(this,r,{writable:!0,value:void 0}),(0,N._)(this,l,{writable:!0,value:void 0}),(0,N._)(this,n,{writable:!0,value:void 0}),(0,N._)(this,o,{writable:!0,value:void 0}),(0,N._)(this,c,{writable:!0,value:void 0}),(0,N._)(this,d,{writable:!0,value:void 0}),(0,N._)(this,h,{writable:!0,value:void 0}),(0,N._)(this,u,{writable:!0,value:void 0}),(0,N._)(this,p,{writable:!0,value:void 0}),(0,N._)(this,v,{writable:!0,value:void 0}),(0,N._)(this,f,{writable:!0,value:new Set}),this.options=C,(0,S._)(this,t,e),(0,S._)(this,o,null),(0,S._)(this,n,(0,W.pendingThenable)()),this.bindMethods(),this.setOptions(C)}});function U(e,t){return!1!==(0,L.resolveEnabled)(t.enabled,e)&&void 0===e.state.data&&("error"!==e.state.status||!1!==t.retryOnMount)||void 0!==e.state.data&&q(e,t,t.refetchOnMount)}function q(e,t,s){if(!1!==(0,L.resolveEnabled)(t.enabled,e)&&"static"!==(0,L.resolveStaleTime)(t.staleTime,e)){let i="function"==typeof s?s(e):s;return"always"===i||!1!==i&&Q(e,t)}return!1}function z(e,t,s,i){return(e!==t||!1===(0,L.resolveEnabled)(i.enabled,e))&&(!s.suspense||"error"!==e.state.status)&&Q(e,s)}function Q(e,t){return!1!==(0,L.resolveEnabled)(t.enabled,e)&&e.isStaleByTime((0,L.resolveStaleTime)(t.staleTime,e))}function P(e){(0,R._)(this,j,X).call(this);let t=(0,C._)(this,s).fetch(this.options,e);return(null==e?void 0:e.throwOnError)||(t=t.catch(L.noop)),t}function D(){(0,R._)(this,y,K).call(this);let e=(0,L.resolveStaleTime)(this.options.staleTime,(0,C._)(this,s));if(L.isServer||(0,C._)(this,a).isStale||!(0,L.isValidTimeout)(e))return;let t=(0,L.timeUntilStale)((0,C._)(this,a).dataUpdatedAt,e);(0,S._)(this,u,setTimeout(()=>{(0,C._)(this,a).isStale||this.updateResult()},t+1))}function I(){var e;return null!=(e="function"==typeof this.options.refetchInterval?this.options.refetchInterval((0,C._)(this,s)):this.options.refetchInterval)&&e}function B(e){(0,R._)(this,w,V).call(this),(0,S._)(this,v,e),!L.isServer&&!1!==(0,L.resolveEnabled)(this.options.enabled,(0,C._)(this,s))&&(0,L.isValidTimeout)((0,C._)(this,v))&&0!==(0,C._)(this,v)&&(0,S._)(this,p,setInterval(()=>{(this.options.refetchIntervalInBackground||M.focusManager.isFocused())&&(0,R._)(this,m,P).call(this)},(0,C._)(this,v)))}function H(){(0,R._)(this,x,D).call(this),(0,R._)(this,g,B).call(this,(0,R._)(this,b,I).call(this))}function K(){(0,C._)(this,u)&&(clearTimeout((0,C._)(this,u)),(0,S._)(this,u,void 0))}function V(){(0,C._)(this,p)&&(clearInterval((0,C._)(this,p)),(0,S._)(this,p,void 0))}function X(){let e=(0,C._)(this,t).getQueryCache().build((0,C._)(this,t),this.options);if(e===(0,C._)(this,s))return;let a=(0,C._)(this,s);(0,S._)(this,s,e),(0,S._)(this,i,e.state),this.hasListeners()&&(null==a||a.removeObserver(this),e.addObserver(this))}function Y(e){O.notifyManager.batch(()=>{e.listeners&&this.listeners.forEach(e=>{e((0,C._)(this,a))}),(0,C._)(this,t).getQueryCache().notify({query:(0,C._)(this,s),type:"observerResultsUpdated"})})}e.i(47167);var G=e.i(71645),J=e.i(12598);e.i(43476);var Z=G.createContext(function(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}()),$=G.createContext(!1);$.Provider;var ee=(e,t,s)=>t.fetchOptimistic(e).catch(()=>{s.clearReset()});function et(e,t){return function(e,t,s){var i,a,r,l,n;let o=G.useContext($),c=G.useContext(Z),d=(0,J.useQueryClient)(s),h=d.defaultQueryOptions(e);if(null==(a=d.getDefaultOptions().queries)||null==(i=a._experimental_beforeQuery)||i.call(a,h),h._optimisticResults=o?"isRestoring":"optimistic",h.suspense){let e=e=>"static"===e?e:Math.max(null!=e?e:1e3,1e3),t=h.staleTime;h.staleTime="function"==typeof t?function(){for(var s=arguments.length,i=Array(s),a=0;a<s;a++)i[a]=arguments[a];return e(t(...i))}:e(t),"number"==typeof h.gcTime&&(h.gcTime=Math.max(h.gcTime,1e3))}(h.suspense||h.throwOnError||h.experimental_prefetchInRender)&&!c.isReset()&&(h.retryOnMount=!1),G.useEffect(()=>{c.clearReset()},[c]);let u=!d.getQueryCache().get(h.queryHash),[p]=G.useState(()=>new t(d,h)),v=p.getOptimisticResult(h),f=!o&&!1!==e.subscribed;if(G.useSyncExternalStore(G.useCallback(e=>{let t=f?p.subscribe(O.notifyManager.batchCalls(e)):L.noop;return p.updateResult(),t},[p,f]),()=>p.getCurrentResult(),()=>p.getCurrentResult()),G.useEffect(()=>{p.setOptions(h)},[h,p]),(null==h?void 0:h.suspense)&&v.isPending)throw ee(h,p,c);if((e=>{let{result:t,errorResetBoundary:s,throwOnError:i,query:a,suspense:r}=e;return t.isError&&!s.isReset()&&!t.isFetching&&a&&(r&&void 0===t.data||(0,L.shouldThrowError)(i,[t.error,a]))})({result:v,errorResetBoundary:c,throwOnError:h.throwOnError,query:d.getQueryCache().get(h.queryHash),suspense:h.suspense}))throw v.error;if(null==(l=d.getDefaultOptions().queries)||null==(r=l._experimental_afterQuery)||r.call(l,h,v),h.experimental_prefetchInRender&&!L.isServer&&v.isLoading&&v.isFetching&&!o){let e=u?ee(h,p,c):null==(n=d.getQueryCache().get(h.queryHash))?void 0:n.promise;null==e||e.catch(L.noop).finally(()=>{p.updateResult()})}return h.notifyOnChangeProps?v:p.trackResult(v)}(e,F,t)}},15288,e=>{"use strict";e.s(["Card",()=>i,"CardContent",()=>l,"CardHeader",()=>a,"CardTitle",()=>r]);var t=e.i(43476),s=e.i(75157);function i(e){let{className:i,...a}=e;return(0,t.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",i),...a})}function a(e){let{className:i,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",i),...a})}function r(e){let{className:i,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",i),...a})}function l(e){let{className:i,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",i),...a})}},19455,e=>{"use strict";e.s(["Button",()=>l]);var t=e.i(43476),s=e.i(91918),i=e.i(25913),a=e.i(75157);let r=(0,i.cva)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:i,variant:l,size:n,asChild:o=!1,...c}=e,d=o?s.Slot:"button";return(0,t.jsx)(d,{"data-slot":"button",className:(0,a.cn)(r({variant:l,size:n,className:i})),...c})}},46897,e=>{"use strict";e.s(["MapPin",()=>t],46897);let t=(0,e.i(75254).default)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},69638,73884,e=>{"use strict";e.s(["CheckCircle",()=>s],69638);var t=e.i(75254);let s=(0,t.default)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);e.s(["XCircle",()=>i],73884);let i=(0,t.default)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},63209,e=>{"use strict";e.s(["AlertCircle",()=>t],63209);let t=(0,e.i(75254).default)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},29592,e=>{"use strict";e.s(["Alert",()=>r,"AlertDescription",()=>l]);var t=e.i(43476),s=e.i(25913),i=e.i(75157);let a=(0,s.cva)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function r(e){let{className:s,variant:r,...l}=e;return(0,t.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,i.cn)(a({variant:r}),s),...l})}function l(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"alert-description",className:(0,i.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",s),...a})}},68553,e=>{"use strict";e.s(["Camera",()=>t],68553);let t=(0,e.i(75254).default)("camera",[["path",{d:"M13.997 4a2 2 0 0 1 1.76 1.05l.486.9A2 2 0 0 0 18.003 7H20a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V9a2 2 0 0 1 2-2h1.997a2 2 0 0 0 1.759-1.048l.489-.904A2 2 0 0 1 10.004 4z",key:"18u6gg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},84771,e=>{"use strict";e.s(["CameraFeed",()=>x],84771);var t=e.i(43476),s=e.i(71645),i=e.i(15288),a=e.i(19455),r=e.i(87486),l=e.i(29592),n=e.i(68553),o=e.i(75254);let c=(0,o.default)("play",[["path",{d:"M5 5a2 2 0 0 1 3.008-1.728l11.997 6.998a2 2 0 0 1 .003 3.458l-12 7A2 2 0 0 1 5 19z",key:"10ikf1"}]]),d=(0,o.default)("pause",[["rect",{x:"14",y:"3",width:"5",height:"18",rx:"1",key:"kaeet6"}],["rect",{x:"5",y:"3",width:"5",height:"18",rx:"1",key:"1wsw3u"}]]),h=(0,o.default)("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]),u=(0,o.default)("maximize",[["path",{d:"M8 3H5a2 2 0 0 0-2 2v3",key:"1dcmit"}],["path",{d:"M21 8V5a2 2 0 0 0-2-2h-3",key:"1e4gt3"}],["path",{d:"M3 16v3a2 2 0 0 0 2 2h3",key:"wsl5sc"}],["path",{d:"M16 21h3a2 2 0 0 0 2-2v-3",key:"18trek"}]]);var p=e.i(63209),v=e.i(53475),f=e.i(16973),m=e.i(75157);function x(e){let{className:o,autoPlay:x=!0}=e,b=(0,s.useRef)(null),[g,_]=(0,s.useState)(!1),[y,w]=(0,s.useState)(!0),[j,k]=(0,s.useState)(null),C=f.apiService.getCameraStreamUrl();return(0,s.useEffect)(()=>{let e=b.current;if(!e)return;let t=()=>{w(!0),k(null)},s=()=>{w(!1),x&&e.play().catch(e=>{console.error("Auto-play failed:",e),k("Auto-play failed. Click play to start the stream.")})},i=()=>{_(!0),k(null)},a=()=>{_(!1)},r=()=>{w(!1),_(!1),k("Failed to load camera stream. Please check your connection.")};return e.addEventListener("loadstart",t),e.addEventListener("canplay",s),e.addEventListener("play",i),e.addEventListener("pause",a),e.addEventListener("error",r),()=>{e.removeEventListener("loadstart",t),e.removeEventListener("canplay",s),e.removeEventListener("play",i),e.removeEventListener("pause",a),e.removeEventListener("error",r)}},[x]),(0,t.jsxs)(i.Card,{className:(0,m.cn)("overflow-hidden",o),children:[(0,t.jsx)(i.CardHeader,{children:(0,t.jsxs)(i.CardTitle,{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(n.Camera,{className:"h-5 w-5"}),"Camera Feed",y?(0,t.jsx)(r.Badge,{variant:"secondary",children:"Connecting..."}):j?(0,t.jsx)(r.Badge,{className:"bg-red-500/10 text-red-500 border-red-500/20",children:"Offline"}):g?(0,t.jsx)(r.Badge,{className:"bg-green-500/10 text-green-500 border-green-500/20",children:"Live"}):(0,t.jsx)(r.Badge,{variant:"secondary",children:"Paused"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(a.Button,{variant:"outline",size:"sm",onClick:()=>{let e=b.current;e&&(k(null),w(!0),e.load())},disabled:y,children:(0,t.jsx)(h,{className:"h-4 w-4"})}),(0,t.jsx)(a.Button,{variant:"outline",size:"sm",onClick:()=>{let e=b.current;e&&(document.fullscreenElement?document.exitFullscreen():e.requestFullscreen().catch(e=>{console.error("Fullscreen failed:",e)}))},children:(0,t.jsx)(u,{className:"h-4 w-4"})})]})]})}),(0,t.jsx)(i.CardContent,{className:"p-0",children:(0,t.jsx)("div",{className:"relative bg-black aspect-video",children:j?(0,t.jsxs)(l.Alert,{className:"m-4 border-red-500/20 bg-red-500/10",children:[(0,t.jsx)(p.AlertCircle,{className:"h-4 w-4 text-red-500"}),(0,t.jsx)(l.AlertDescription,{className:"text-red-500",children:j})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("video",{ref:b,className:"w-full h-full object-cover",controls:!1,muted:!0,playsInline:!0,children:[(0,t.jsx)("source",{src:C,type:"video/mp4"}),(0,t.jsx)("source",{src:C,type:"application/x-mpegURL"}),"Your browser does not support the video tag."]}),y&&(0,t.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-black/50",children:(0,t.jsxs)("div",{className:"text-white text-center",children:[(0,t.jsx)(v.Wifi,{className:"h-8 w-8 mx-auto mb-2 animate-pulse"}),(0,t.jsx)("p",{children:"Connecting to camera..."})]})}),!y&&!j&&(0,t.jsx)("div",{className:"absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity bg-black/20",children:(0,t.jsx)(a.Button,{variant:"secondary",size:"lg",onClick:()=>{let e=b.current;e&&(g?e.pause():e.play().catch(e=>{console.error("Play failed:",e),k("Failed to play stream. Please try again.")}))},className:"bg-black/50 hover:bg-black/70",children:g?(0,t.jsx)(d,{className:"h-6 w-6"}):(0,t.jsx)(c,{className:"h-6 w-6"})})})]})})})]})}},50345,e=>{"use strict";e.s(["AlertsTable",()=>P],50345);var t,s,i,a,r,l,n=e.i(43476),o=e.i(66027),c=e.i(71645),d=e.i(39946),h=e.i(70292),u=e.i(62351),p=e.i(37696),v=e.i(88245),f=e.i(14272),m=e.i(40143),x=e.i(15823),b=e.i(19273),g=(t=new WeakMap,s=new WeakMap,i=new WeakMap,a=new WeakMap,r=new WeakSet,l=new WeakSet,class extends x.Subscribable{bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){var s;let a=this.options;this.options=(0,d._)(this,t).defaultMutationOptions(e),(0,b.shallowEqualObjects)(this.options,a)||(0,d._)(this,t).getMutationCache().notify({type:"observerOptionsUpdated",mutation:(0,d._)(this,i),observer:this}),(null==a?void 0:a.mutationKey)&&this.options.mutationKey&&(0,b.hashKey)(a.mutationKey)!==(0,b.hashKey)(this.options.mutationKey)?this.reset():(null==(s=(0,d._)(this,i))?void 0:s.state.status)==="pending"&&(0,d._)(this,i).setOptions(this.options)}onUnsubscribe(){if(!this.hasListeners()){var e;null==(e=(0,d._)(this,i))||e.removeObserver(this)}}onMutationUpdate(e){(0,p._)(this,r,_).call(this),(0,p._)(this,l,y).call(this,e)}getCurrentResult(){return(0,d._)(this,s)}reset(){var e;null==(e=(0,d._)(this,i))||e.removeObserver(this),(0,u._)(this,i,void 0),(0,p._)(this,r,_).call(this),(0,p._)(this,l,y).call(this)}mutate(e,s){var r;return(0,u._)(this,a,s),null==(r=(0,d._)(this,i))||r.removeObserver(this),(0,u._)(this,i,(0,d._)(this,t).getMutationCache().build((0,d._)(this,t),this.options)),(0,d._)(this,i).addObserver(this),(0,d._)(this,i).execute(e)}constructor(e,n){super(),(0,v._)(this,r),(0,v._)(this,l),(0,h._)(this,t,{writable:!0,value:void 0}),(0,h._)(this,s,{writable:!0,value:void 0}),(0,h._)(this,i,{writable:!0,value:void 0}),(0,h._)(this,a,{writable:!0,value:void 0}),(0,u._)(this,t,e),this.setOptions(n),this.bindMethods(),(0,p._)(this,r,_).call(this)}});function _(){var e,t;let a=null!=(t=null==(e=(0,d._)(this,i))?void 0:e.state)?t:(0,f.getDefaultState)();(0,u._)(this,s,{...a,isPending:"pending"===a.status,isSuccess:"success"===a.status,isError:"error"===a.status,isIdle:"idle"===a.status,mutate:this.mutate,reset:this.reset})}function y(e){m.notifyManager.batch(()=>{if((0,d._)(this,a)&&this.hasListeners()){var t,i,r,l,n,o,c,h;let u=(0,d._)(this,s).variables,p=(0,d._)(this,s).context;(null==e?void 0:e.type)==="success"?(null==(t=(i=(0,d._)(this,a)).onSuccess)||t.call(i,e.data,u,p),null==(r=(l=(0,d._)(this,a)).onSettled)||r.call(l,e.data,null,u,p)):(null==e?void 0:e.type)==="error"&&(null==(n=(o=(0,d._)(this,a)).onError)||n.call(o,e.error,u,p),null==(c=(h=(0,d._)(this,a)).onSettled)||c.call(h,void 0,e.error,u,p))}this.listeners.forEach(e=>{e((0,d._)(this,s))})})}var w=e.i(12598),j=e.i(75157);function k(e){let{className:t,...s}=e;return(0,n.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,n.jsx)("table",{"data-slot":"table",className:(0,j.cn)("w-full caption-bottom text-sm",t),...s})})}function C(e){let{className:t,...s}=e;return(0,n.jsx)("thead",{"data-slot":"table-header",className:(0,j.cn)("[&_tr]:border-b",t),...s})}function N(e){let{className:t,...s}=e;return(0,n.jsx)("tbody",{"data-slot":"table-body",className:(0,j.cn)("[&_tr:last-child]:border-0",t),...s})}function S(e){let{className:t,...s}=e;return(0,n.jsx)("tr",{"data-slot":"table-row",className:(0,j.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...s})}function R(e){let{className:t,...s}=e;return(0,n.jsx)("th",{"data-slot":"table-head",className:(0,j.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...s})}function E(e){let{className:t,...s}=e;return(0,n.jsx)("td",{"data-slot":"table-cell",className:(0,j.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...s})}var M=e.i(87486),O=e.i(19455),T=e.i(15288),A=e.i(78894),W=e.i(46897);let L=(0,e.i(75254).default)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var F=e.i(69638),U=e.i(73884),q=e.i(63209),z=e.i(16973),Q=e.i(46696);function P(e){let{limit:t,showActions:s=!0}=e,i=(0,w.useQueryClient)(),{data:a,isLoading:r,error:l}=(0,o.useQuery)({queryKey:["alerts",t],queryFn:()=>z.apiService.getAlerts(t),refetchInterval:1e4}),d=function(e,t){let s=(0,w.useQueryClient)(void 0),[i]=c.useState(()=>new g(s,e));c.useEffect(()=>{i.setOptions(e)},[i,e]);let a=c.useSyncExternalStore(c.useCallback(e=>i.subscribe(m.notifyManager.batchCalls(e)),[i]),()=>i.getCurrentResult(),()=>i.getCurrentResult()),r=c.useCallback((e,t)=>{i.mutate(e,t).catch(b.noop)},[i]);if(a.error&&(0,b.shouldThrowError)(i.options.throwOnError,[a.error]))throw a.error;return{...a,mutate:r,mutateAsync:a.mutate}}({mutationFn:e=>{let{id:t,status:s}=e;return z.apiService.updateAlertStatus(t,s)},onSuccess:()=>{i.invalidateQueries({queryKey:["alerts"]}),i.invalidateQueries({queryKey:["alert-stats"]}),Q.toast.success("Alert status updated successfully")},onError:e=>{Q.toast.error("Failed to update alert status"),console.error("Update failed:",e)}}),h=(e,t)=>{d.mutate({id:e.id,status:t})};return r?(0,n.jsxs)(T.Card,{children:[(0,n.jsx)(T.CardHeader,{children:(0,n.jsx)(T.CardTitle,{children:"Alerts"})}),(0,n.jsx)(T.CardContent,{children:(0,n.jsx)("div",{className:"space-y-3",children:[void 0,void 0,void 0,void 0,void 0].map((e,t)=>(0,n.jsx)("div",{className:"animate-pulse",children:(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsx)("div",{className:"h-4 w-4 bg-muted rounded"}),(0,n.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,n.jsx)("div",{className:"h-4 bg-muted rounded w-3/4"}),(0,n.jsx)("div",{className:"h-3 bg-muted rounded w-1/2"})]})]})},t))})})]}):l?(0,n.jsxs)(T.Card,{children:[(0,n.jsx)(T.CardHeader,{children:(0,n.jsx)(T.CardTitle,{children:"Alerts"})}),(0,n.jsx)(T.CardContent,{children:(0,n.jsxs)("div",{className:"text-center py-6",children:[(0,n.jsx)(U.XCircle,{className:"h-8 w-8 text-red-500 mx-auto mb-2"}),(0,n.jsx)("p",{className:"text-sm text-muted-foreground",children:"Failed to load alerts"})]})})]}):a&&0!==a.length?(0,n.jsxs)(T.Card,{children:[(0,n.jsx)(T.CardHeader,{children:(0,n.jsxs)(T.CardTitle,{className:"flex items-center gap-2",children:[(0,n.jsx)(A.AlertTriangle,{className:"h-5 w-5"}),"Alerts (",a.length,")"]})}),(0,n.jsx)(T.CardContent,{children:(0,n.jsx)("div",{className:"rounded-md border",children:(0,n.jsxs)(k,{children:[(0,n.jsx)(C,{children:(0,n.jsxs)(S,{children:[(0,n.jsx)(R,{children:"Status"}),(0,n.jsx)(R,{children:"Detection"}),(0,n.jsx)(R,{children:"Confidence"}),(0,n.jsx)(R,{children:"Time"}),(0,n.jsx)(R,{children:"Location"}),s&&(0,n.jsx)(R,{children:"Actions"})]})}),(0,n.jsx)(N,{children:a.map(e=>(0,n.jsxs)(S,{children:[(0,n.jsx)(E,{children:(0,n.jsxs)(M.Badge,{className:(0,j.cn)("flex items-center gap-1 w-fit",(e=>{switch(e){case"active":return"bg-red-500/10 text-red-500 border-red-500/20";case"investigating":return"bg-yellow-500/10 text-yellow-500 border-yellow-500/20";case"resolved":return"bg-green-500/10 text-green-500 border-green-500/20";default:return"bg-gray-500/10 text-gray-500 border-gray-500/20"}})(e.status)),children:[(e=>{switch(e){case"active":default:return(0,n.jsx)(A.AlertTriangle,{className:"h-4 w-4"});case"investigating":return(0,n.jsx)(q.AlertCircle,{className:"h-4 w-4"});case"resolved":return(0,n.jsx)(F.CheckCircle,{className:"h-4 w-4"})}})(e.status),e.status]})}),(0,n.jsx)(E,{children:(0,n.jsx)("div",{className:"font-medium",children:e.objects.join(", ")})}),(0,n.jsx)(E,{children:(0,n.jsxs)("div",{className:"flex items-center gap-1",children:[(0,n.jsx)("div",{className:(0,j.cn)("h-2 w-2 rounded-full",e.confidence>.8?"bg-green-500":e.confidence>.6?"bg-yellow-500":"bg-red-500")}),Math.round(100*e.confidence),"%"]})}),(0,n.jsx)(E,{children:(0,n.jsx)("div",{className:"text-sm",children:new Date(e.timestamp).toLocaleString()})}),(0,n.jsx)(E,{children:e.location?(0,n.jsxs)("div",{className:"flex items-center gap-1 text-sm",children:[(0,n.jsx)(W.MapPin,{className:"h-3 w-3"}),e.location.latitude.toFixed(4),","," ",e.location.longitude.toFixed(4)]}):(0,n.jsx)("span",{className:"text-muted-foreground text-sm",children:"-"})}),s&&(0,n.jsx)(E,{children:(0,n.jsxs)("div",{className:"flex items-center gap-1",children:["active"===e.status&&(0,n.jsx)(O.Button,{variant:"outline",size:"sm",onClick:()=>h(e,"investigating"),disabled:d.isPending,children:"Investigate"}),"investigating"===e.status&&(0,n.jsx)(O.Button,{variant:"outline",size:"sm",onClick:()=>h(e,"resolved"),disabled:d.isPending,children:"Resolve"}),e.image_url&&(0,n.jsx)(O.Button,{variant:"ghost",size:"sm",onClick:()=>window.open(e.image_url,"_blank"),children:(0,n.jsx)(L,{className:"h-4 w-4"})})]})})]},e.id))})]})})})]}):(0,n.jsxs)(T.Card,{children:[(0,n.jsx)(T.CardHeader,{children:(0,n.jsx)(T.CardTitle,{children:"Alerts"})}),(0,n.jsx)(T.CardContent,{children:(0,n.jsxs)("div",{className:"text-center py-6",children:[(0,n.jsx)(A.AlertTriangle,{className:"h-8 w-8 text-muted-foreground mx-auto mb-2"}),(0,n.jsx)("p",{className:"text-sm text-muted-foreground",children:"No alerts found"})]})})]})}}]);