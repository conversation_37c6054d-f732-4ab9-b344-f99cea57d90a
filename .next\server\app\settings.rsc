1:"$Sreact.fragment"
2:I[44636,["/_next/static/chunks/76b7c01eed2d4531.js","/_next/static/chunks/09319e66ace203ff.js"],"Providers"]
3:I[39756,["/_next/static/chunks/ff1a16fafef87110.js","/_next/static/chunks/6a7993b07cb97bb3.js"],"default"]
4:I[37457,["/_next/static/chunks/ff1a16fafef87110.js","/_next/static/chunks/6a7993b07cb97bb3.js"],"default"]
5:I[13354,["/_next/static/chunks/76b7c01eed2d4531.js","/_next/static/chunks/09319e66ace203ff.js"],"Toaster"]
6:I[42252,["/_next/static/chunks/76b7c01eed2d4531.js","/_next/static/chunks/09319e66ace203ff.js","/_next/static/chunks/ff1a16fafef87110.js","/_next/static/chunks/ebe113b3fedbedc6.js"],"Layout"]
f:I[68027,["/_next/static/chunks/76b7c01eed2d4531.js","/_next/static/chunks/09319e66ace203ff.js"],"default"]
:HL["/_next/static/chunks/840063f96249edb5.css","style"]
:HL["/_next/static/media/797e433ab948586e-s.p.dbea232f.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/_next/static/media/caa3a2e1cccd8315-s.p.6435ea53.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
0:{"P":null,"b":"AO-JRiXT-b8VjxP_Sjq1D","p":"","c":["","settings"],"i":false,"f":[[["",{"children":["settings",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/chunks/840063f96249edb5.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}],["$","script","script-0",{"src":"/_next/static/chunks/76b7c01eed2d4531.js","async":true,"nonce":"$undefined"}],["$","script","script-1",{"src":"/_next/static/chunks/09319e66ace203ff.js","async":true,"nonce":"$undefined"}]],["$","html",null,{"lang":"en","className":"dark","children":["$","body",null,{"className":"geist_a71539c9-module__T19VSG__variable geist_mono_8d43a2aa-module__8Li5zG__variable antialiased","children":["$","$L2",null,{"children":[["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}],["$","$L5",null,{}]]}]}]}]]}],{"children":["settings",["$","$1","c",{"children":[null,["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","$L6",null,{"children":["$","div",null,{"className":"space-y-6","children":[["$","div",null,{"children":[["$","h1",null,{"className":"text-3xl font-bold text-foreground","children":"Settings"}],["$","p",null,{"className":"text-muted-foreground","children":"System configuration and preferences"}]]}],["$","div",null,{"className":"grid gap-6 md:grid-cols-2","children":[["$","div",null,{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6","children":["$","div",null,{"data-slot":"card-title","className":"leading-none font-semibold flex items-center gap-2","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-server h-5 w-5","aria-hidden":"true","children":[["$","rect","ngkwjq",{"width":"20","height":"8","x":"2","y":"2","rx":"2","ry":"2"}],["$","rect","iecqi9",{"width":"20","height":"8","x":"2","y":"14","rx":"2","ry":"2"}],["$","line","16zg32",{"x1":"6","x2":"6.01","y1":"6","y2":"6"}],["$","line","nzw8ys",{"x1":"6","x2":"6.01","y1":"18","y2":"18"}],"$undefined"]}],"Backend Configuration"]}]}],["$","div",null,{"data-slot":"card-content","className":"px-6 space-y-4","children":[["$","div",null,{"className":"flex items-center justify-between","children":[["$","span",null,{"className":"text-sm font-medium","children":"API URL"}],["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90","children":"http://localhost:8000"}]]}],"$L7"]}]]}],"$L8","$L9","$La"]}]]}]}],["$Lb","$Lc"],"$Ld"]}],{},null,false]},null,false]},null,false],"$Le",false]],"m":"$undefined","G":["$f",["$L10"]],"s":false,"S":true}
11:I[97367,["/_next/static/chunks/ff1a16fafef87110.js","/_next/static/chunks/6a7993b07cb97bb3.js"],"OutletBoundary"]
13:I[11533,["/_next/static/chunks/ff1a16fafef87110.js","/_next/static/chunks/6a7993b07cb97bb3.js"],"AsyncMetadataOutlet"]
15:I[97367,["/_next/static/chunks/ff1a16fafef87110.js","/_next/static/chunks/6a7993b07cb97bb3.js"],"ViewportBoundary"]
17:I[97367,["/_next/static/chunks/ff1a16fafef87110.js","/_next/static/chunks/6a7993b07cb97bb3.js"],"MetadataBoundary"]
18:"$Sreact.suspense"
7:["$","div",null,{"className":"flex items-center justify-between","children":[["$","span",null,{"className":"text-sm font-medium","children":"WebSocket URL"}],["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90","children":"ws://localhost:8000"}]]}]
8:["$","div",null,{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6","children":["$","div",null,{"data-slot":"card-title","className":"leading-none font-semibold flex items-center gap-2","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-camera h-5 w-5","aria-hidden":"true","children":[["$","path","18u6gg",{"d":"M13.997 4a2 2 0 0 1 1.76 1.05l.486.9A2 2 0 0 0 18.003 7H20a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V9a2 2 0 0 1 2-2h1.997a2 2 0 0 0 1.759-1.048l.489-.904A2 2 0 0 1 10.004 4z"}],["$","circle","1vg3eu",{"cx":"12","cy":"13","r":"3"}],"$undefined"]}],"Camera Settings"]}]}],["$","div",null,{"data-slot":"card-content","className":"px-6 space-y-4","children":[["$","div",null,{"className":"flex items-center justify-between","children":[["$","span",null,{"className":"text-sm font-medium","children":"Auto-play Stream"}],["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden [a&]:hover:bg-primary/90 bg-green-500/10 text-green-500 border-green-500/20","children":"Enabled"}]]}],["$","div",null,{"className":"flex items-center justify-between","children":[["$","span",null,{"className":"text-sm font-medium","children":"Stream Quality"}],["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90","children":"Auto"}]]}]]}]]}]
9:["$","div",null,{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6","children":["$","div",null,{"data-slot":"card-title","className":"leading-none font-semibold flex items-center gap-2","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-bell h-5 w-5","aria-hidden":"true","children":[["$","path","vwvbt9",{"d":"M10.268 21a2 2 0 0 0 3.464 0"}],["$","path","11g9vi",{"d":"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326"}],"$undefined"]}],"Notifications"]}]}],["$","div",null,{"data-slot":"card-content","className":"px-6 space-y-4","children":[["$","div",null,{"className":"flex items-center justify-between","children":[["$","span",null,{"className":"text-sm font-medium","children":"Real-time Alerts"}],["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden [a&]:hover:bg-primary/90 bg-green-500/10 text-green-500 border-green-500/20","children":"Enabled"}]]}],["$","div",null,{"className":"flex items-center justify-between","children":[["$","span",null,{"className":"text-sm font-medium","children":"Sound Notifications"}],["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90","children":"Disabled"}]]}]]}]]}]
a:["$","div",null,{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6","children":["$","div",null,{"data-slot":"card-title","className":"leading-none font-semibold flex items-center gap-2","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-shield h-5 w-5","aria-hidden":"true","children":[["$","path","oel41y",{"d":"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z"}],"$undefined"]}],"Security"]}]}],["$","div",null,{"data-slot":"card-content","className":"px-6 space-y-4","children":[["$","div",null,{"className":"flex items-center justify-between","children":[["$","span",null,{"className":"text-sm font-medium","children":"Authentication"}],["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90","children":"Not configured"}]]}],["$","div",null,{"className":"flex items-center justify-between","children":[["$","span",null,{"className":"text-sm font-medium","children":"SSL/TLS"}],["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90","children":"Not configured"}]]}]]}]]}]
b:["$","script","script-0",{"src":"/_next/static/chunks/ff1a16fafef87110.js","async":true,"nonce":"$undefined"}]
c:["$","script","script-1",{"src":"/_next/static/chunks/ebe113b3fedbedc6.js","async":true,"nonce":"$undefined"}]
d:["$","$L11",null,{"children":["$L12",["$","$L13",null,{"promise":"$@14"}]]}]
e:["$","$1","h",{"children":[null,[["$","$L15",null,{"children":"$L16"}],["$","meta",null,{"name":"next-size-adjust","content":""}]],["$","$L17",null,{"children":["$","div",null,{"hidden":true,"children":["$","$18",null,{"fallback":null,"children":"$L19"}]}]}]]}]
10:["$","link","0",{"rel":"stylesheet","href":"/_next/static/chunks/840063f96249edb5.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]
16:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
12:null
1a:I[27201,["/_next/static/chunks/ff1a16fafef87110.js","/_next/static/chunks/6a7993b07cb97bb3.js"],"IconMark"]
14:{"metadata":[["$","title","0",{"children":"Wildlife Guardian - Animal Poaching Alert System"}],["$","meta","1",{"name":"description","content":"Real-time wildlife monitoring and poaching detection system"}],["$","link","2",{"rel":"icon","href":"/favicon.ico?favicon.0b3bf435.ico","sizes":"256x256","type":"image/x-icon"}],["$","$L1a","3",{}]],"error":null,"digest":"$undefined"}
19:"$14:metadata"
