"use client";

// import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  AlertTriangle,
  MapPin,
  Eye,
  CheckCircle,
  XCircle,
  AlertCircle,
} from "lucide-react";
import { apiService } from "@/services/api";
import { Alert } from "@/types";
import { cn } from "@/lib/utils";
import { toast } from "sonner";

interface AlertsTableProps {
  limit?: number;
  showActions?: boolean;
}

export function AlertsTable({ limit, showActions = true }: AlertsTableProps) {
  // const [selectedAlert, setSelectedAlert] = useState<Alert | null>(null);
  const queryClient = useQueryClient();

  const {
    data: alerts,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["alerts", limit],
    queryFn: () => apiService.getAlerts(limit),
    refetchInterval: 10000, // Refetch every 10 seconds
  });

  const updateStatusMutation = useMutation({
    mutationFn: ({ id, status }: { id: string; status: Alert["status"] }) =>
      apiService.updateAlertStatus(id, status),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["alerts"] });
      queryClient.invalidateQueries({ queryKey: ["alert-stats"] });
      toast.success("Alert status updated successfully");
    },
    onError: (error) => {
      toast.error("Failed to update alert status");
      console.error("Update failed:", error);
    },
  });

  const getStatusColor = (status: Alert["status"]) => {
    switch (status) {
      case "active":
        return "bg-red-500/10 text-red-500 border-red-500/20";
      case "investigating":
        return "bg-yellow-500/10 text-yellow-500 border-yellow-500/20";
      case "resolved":
        return "bg-green-500/10 text-green-500 border-green-500/20";
      default:
        return "bg-gray-500/10 text-gray-500 border-gray-500/20";
    }
  };

  const getStatusIcon = (status: Alert["status"]) => {
    switch (status) {
      case "active":
        return <AlertTriangle className="h-4 w-4" />;
      case "investigating":
        return <AlertCircle className="h-4 w-4" />;
      case "resolved":
        return <CheckCircle className="h-4 w-4" />;
      default:
        return <AlertTriangle className="h-4 w-4" />;
    }
  };

  const formatDateTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  const handleStatusUpdate = (alert: Alert, newStatus: Alert["status"]) => {
    updateStatusMutation.mutate({ id: alert.id, status: newStatus });
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Alerts</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="flex items-center space-x-4">
                  <div className="h-4 w-4 bg-muted rounded"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-muted rounded w-3/4"></div>
                    <div className="h-3 bg-muted rounded w-1/2"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Alerts</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <XCircle className="h-8 w-8 text-red-500 mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">
              Failed to load alerts
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!alerts || alerts.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Alerts</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <AlertTriangle className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">No alerts found</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <AlertTriangle className="h-5 w-5" />
          Alerts ({alerts.length})
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Status</TableHead>
                <TableHead>Detection</TableHead>
                <TableHead>Confidence</TableHead>
                <TableHead>Time</TableHead>
                <TableHead>Location</TableHead>
                {showActions && <TableHead>Actions</TableHead>}
              </TableRow>
            </TableHeader>
            <TableBody>
              {alerts.map((alert) => (
                <TableRow key={alert.id}>
                  <TableCell>
                    <Badge
                      className={cn(
                        "flex items-center gap-1 w-fit",
                        getStatusColor(alert.status)
                      )}
                    >
                      {getStatusIcon(alert.status)}
                      {alert.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="font-medium">
                      {alert.objects.join(", ")}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <div
                        className={cn(
                          "h-2 w-2 rounded-full",
                          alert.confidence > 0.8
                            ? "bg-green-500"
                            : alert.confidence > 0.6
                            ? "bg-yellow-500"
                            : "bg-red-500"
                        )}
                      />
                      {Math.round(alert.confidence * 100)}%
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {formatDateTime(alert.timestamp)}
                    </div>
                  </TableCell>
                  <TableCell>
                    {alert.location ? (
                      <div className="flex items-center gap-1 text-sm">
                        <MapPin className="h-3 w-3" />
                        {alert.location.latitude.toFixed(4)},{" "}
                        {alert.location.longitude.toFixed(4)}
                      </div>
                    ) : (
                      <span className="text-muted-foreground text-sm">-</span>
                    )}
                  </TableCell>
                  {showActions && (
                    <TableCell>
                      <div className="flex items-center gap-1">
                        {alert.status === "active" && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              handleStatusUpdate(alert, "investigating")
                            }
                            disabled={updateStatusMutation.isPending}
                          >
                            Investigate
                          </Button>
                        )}
                        {alert.status === "investigating" && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              handleStatusUpdate(alert, "resolved")
                            }
                            disabled={updateStatusMutation.isPending}
                          >
                            Resolve
                          </Button>
                        )}
                        {alert.image_url && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() =>
                              window.open(alert.image_url, "_blank")
                            }
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  )}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}
