(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,98183,(e,r,t)=>{"use strict";function o(e){let r={};for(let[t,o]of e.entries()){let e=r[t];void 0===e?r[t]=o:Array.isArray(e)?e.push(o):r[t]=[e,o]}return r}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function a(e){let r=new URLSearchParams;for(let[t,o]of Object.entries(e))if(Array.isArray(o))for(let e of o)r.append(t,n(e));else r.set(t,n(o));return r}function l(e){for(var r=arguments.length,t=Array(r>1?r-1:0),o=1;o<r;o++)t[o-1]=arguments[o];for(let r of t){for(let t of r.keys())e.delete(t);for(let[t,o]of r.entries())e.append(t,o)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(t,{assign:function(){return l},searchParamsToUrlQuery:function(){return o},urlQueryToSearchParams:function(){return a}})},95057,(e,r,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(t,{formatUrl:function(){return a},formatWithValidation:function(){return s},urlObjectKeys:function(){return l}});let o=e.r(90809)._(e.r(98183)),n=/https?|ftp|gopher|file/;function a(e){let{auth:r,hostname:t}=e,a=e.protocol||"",l=e.pathname||"",s=e.hash||"",i=e.query||"",c=!1;r=r?encodeURIComponent(r).replace(/%3A/i,":")+"@":"",e.host?c=r+e.host:t&&(c=r+(~t.indexOf(":")?"["+t+"]":t),e.port&&(c+=":"+e.port)),i&&"object"==typeof i&&(i=String(o.urlQueryToSearchParams(i)));let d=e.search||i&&"?"+i||"";return a&&!a.endsWith(":")&&(a+=":"),e.slashes||(!a||n.test(a))&&!1!==c?(c="//"+(c||""),l&&"/"!==l[0]&&(l="/"+l)):c||(c=""),s&&"#"!==s[0]&&(s="#"+s),d&&"?"!==d[0]&&(d="?"+d),""+a+c+(l=l.replace(/[?#]/g,encodeURIComponent))+(d=d.replace("#","%23"))+s}let l=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function s(e){return a(e)}},18581,(e,r,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return n}});let o=e.r(71645);function n(e,r){let t=(0,o.useRef)(null),n=(0,o.useRef)(null);return(0,o.useCallback)(o=>{if(null===o){let e=t.current;e&&(t.current=null,e());let r=n.current;r&&(n.current=null,r())}else e&&(t.current=a(e,o)),r&&(n.current=a(r,o))},[e,r])}function a(e,r){if("function"!=typeof e)return e.current=r,()=>{e.current=null};{let t=e(r);return"function"==typeof t?t:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),r.exports=t.default)},18967,(e,r,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(t,{DecodeError:function(){return g},MiddlewareNotFoundError:function(){return x},MissingStaticPage:function(){return y},NormalizeError:function(){return b},PageNotFoundError:function(){return h},SP:function(){return p},ST:function(){return m},WEB_VITALS:function(){return o},execOnce:function(){return n},getDisplayName:function(){return c},getLocationOrigin:function(){return s},getURL:function(){return i},isAbsoluteUrl:function(){return l},isResSent:function(){return d},loadGetInitialProps:function(){return f},normalizeRepeatedSlashes:function(){return u},stringifyError:function(){return v}});let o=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let r,t=!1;return function(){for(var o=arguments.length,n=Array(o),a=0;a<o;a++)n[a]=arguments[a];return t||(t=!0,r=e(...n)),r}}let a=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,l=e=>a.test(e);function s(){let{protocol:e,hostname:r,port:t}=window.location;return e+"//"+r+(t?":"+t:"")}function i(){let{href:e}=window.location,r=s();return e.substring(r.length)}function c(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function d(e){return e.finished||e.headersSent}function u(e){let r=e.split("?");return r[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(r[1]?"?"+r.slice(1).join("?"):"")}async function f(e,r){let t=r.res||r.ctx&&r.ctx.res;if(!e.getInitialProps)return r.ctx&&r.Component?{pageProps:await f(r.Component,r.ctx)}:{};let o=await e.getInitialProps(r);if(t&&d(t))return o;if(!o)throw Object.defineProperty(Error('"'+c(e)+'.getInitialProps()" should resolve to an object. But found "'+o+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return o}let p="undefined"!=typeof performance,m=p&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class g extends Error{}class b extends Error{}class h extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class y extends Error{constructor(e,r){super(),this.message="Failed to load static file for page: "+e+" "+r}}class x extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function v(e){return JSON.stringify({message:e.message,stack:e.stack})}},73668,(e,r,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return a}});let o=e.r(18967),n=e.r(52817);function a(e){if(!(0,o.isAbsoluteUrl)(e))return!0;try{let r=(0,o.getLocationOrigin)(),t=new URL(e,r);return t.origin===r&&(0,n.hasBasePath)(t.pathname)}catch(e){return!1}}},84508,(e,r,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return o}});let o=e=>{}},22016,(e,r,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(t,{default:function(){return b},useLinkStatus:function(){return y}});let o=e.r(90809),n=e.r(43476),a=o._(e.r(71645)),l=e.r(95057),s=e.r(8372),i=e.r(18581),c=e.r(18967),d=e.r(5550);e.r(33525);let u=e.r(91949),f=e.r(73668),p=e.r(99781);e.r(84508);let m=e.r(65165);function g(e){return"string"==typeof e?e:(0,l.formatUrl)(e)}function b(e){var r;let t,o,l,[b,y]=(0,a.useOptimistic)(u.IDLE_LINK_STATUS),x=(0,a.useRef)(null),{href:v,as:k,children:w,prefetch:j=null,passHref:z,replace:N,shallow:P,scroll:M,onClick:C,onMouseEnter:O,onTouchStart:E,legacyBehavior:A=!1,onNavigate:_,ref:S,unstable_dynamicOnHover:T,...R}=e;t=w,A&&("string"==typeof t||"number"==typeof t)&&(t=(0,n.jsx)("a",{children:t}));let I=a.default.useContext(s.AppRouterContext),L=!1!==j,U=!1!==j?null===(r=j)||"auto"===r?m.FetchStrategy.PPR:m.FetchStrategy.Full:m.FetchStrategy.PPR,{href:W,as:B}=a.default.useMemo(()=>{let e=g(v);return{href:e,as:k?g(k):e}},[v,k]);A&&(o=a.default.Children.only(t));let F=A?o&&"object"==typeof o&&o.ref:S,D=a.default.useCallback(e=>(null!==I&&(x.current=(0,u.mountLinkInstance)(e,W,I,U,L,y)),()=>{x.current&&((0,u.unmountLinkForCurrentNavigation)(x.current),x.current=null),(0,u.unmountPrefetchableInstance)(e)}),[L,W,I,U,y]),G={ref:(0,i.useMergedRef)(D,F),onClick(e){A||"function"!=typeof C||C(e),A&&o.props&&"function"==typeof o.props.onClick&&o.props.onClick(e),I&&(e.defaultPrevented||function(e,r,t,o,n,l,s){let{nodeName:i}=e.currentTarget;if(!("A"===i.toUpperCase()&&function(e){let r=e.currentTarget.getAttribute("target");return r&&"_self"!==r||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,f.isLocalURL)(r)){n&&(e.preventDefault(),location.replace(r));return}if(e.preventDefault(),s){let e=!1;if(s({preventDefault:()=>{e=!0}}),e)return}a.default.startTransition(()=>{(0,p.dispatchNavigateAction)(t||r,n?"replace":"push",null==l||l,o.current)})}}(e,W,B,x,N,M,_))},onMouseEnter(e){A||"function"!=typeof O||O(e),A&&o.props&&"function"==typeof o.props.onMouseEnter&&o.props.onMouseEnter(e),I&&L&&(0,u.onNavigationIntent)(e.currentTarget,!0===T)},onTouchStart:function(e){A||"function"!=typeof E||E(e),A&&o.props&&"function"==typeof o.props.onTouchStart&&o.props.onTouchStart(e),I&&L&&(0,u.onNavigationIntent)(e.currentTarget,!0===T)}};return(0,c.isAbsoluteUrl)(B)?G.href=B:A&&!z&&("a"!==o.type||"href"in o.props)||(G.href=(0,d.addBasePath)(B)),l=A?a.default.cloneElement(o,G):(0,n.jsx)("a",{...R,...G,children:t}),(0,n.jsx)(h.Provider,{value:b,children:l})}let h=(0,a.createContext)(u.IDLE_LINK_STATUS),y=()=>(0,a.useContext)(h);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),r.exports=t.default)},18566,(e,r,t)=>{r.exports=e.r(76562)},75157,7670,75254,e=>{"use strict";function r(){for(var e,r,t=0,o="",n=arguments.length;t<n;t++)(e=arguments[t])&&(r=function e(r){var t,o,n="";if("string"==typeof r||"number"==typeof r)n+=r;else if("object"==typeof r)if(Array.isArray(r)){var a=r.length;for(t=0;t<a;t++)r[t]&&(o=e(r[t]))&&(n&&(n+=" "),n+=o)}else for(o in r)r[o]&&(n&&(n+=" "),n+=o);return n}(e))&&(o&&(o+=" "),o+=r);return o}e.s(["cn",()=>ee],75157),e.s(["clsx",()=>r],7670);let t=(e,r)=>{var o;if(0===e.length)return r.classGroupId;let n=e[0],a=r.nextPart.get(n),l=a?t(e.slice(1),a):void 0;if(l)return l;if(0===r.validators.length)return;let s=e.join("-");return null==(o=r.validators.find(e=>{let{validator:r}=e;return r(s)}))?void 0:o.classGroupId},o=/^\[(.+)\]$/,n=(e,r,t,o)=>{e.forEach(e=>{if("string"==typeof e){(""===e?r:a(r,e)).classGroupId=t;return}if("function"==typeof e)return l(e)?void n(e(o),r,t,o):void r.validators.push({validator:e,classGroupId:t});Object.entries(e).forEach(e=>{let[l,s]=e;n(s,a(r,l),t,o)})})},a=(e,r)=>{let t=e;return r.split("-").forEach(e=>{t.nextPart.has(e)||t.nextPart.set(e,{nextPart:new Map,validators:[]}),t=t.nextPart.get(e)}),t},l=e=>e.isThemeGetter,s=/\s+/;function i(){let e,r,t=0,o="";for(;t<arguments.length;)(e=arguments[t++])&&(r=c(e))&&(o&&(o+=" "),o+=r);return o}let c=e=>{let r;if("string"==typeof e)return e;let t="";for(let o=0;o<e.length;o++)e[o]&&(r=c(e[o]))&&(t&&(t+=" "),t+=r);return t},d=e=>{let r=r=>r[e]||[];return r.isThemeGetter=!0,r},u=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,f=/^\((?:(\w[\w-]*):)?(.+)\)$/i,p=/^\d+\/\d+$/,m=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,g=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,b=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,h=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,y=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,x=e=>p.test(e),v=e=>!!e&&!Number.isNaN(Number(e)),k=e=>!!e&&Number.isInteger(Number(e)),w=e=>e.endsWith("%")&&v(e.slice(0,-1)),j=e=>m.test(e),z=()=>!0,N=e=>g.test(e)&&!b.test(e),P=()=>!1,M=e=>h.test(e),C=e=>y.test(e),O=e=>!A(e)&&!L(e),E=e=>V(e,Z,P),A=e=>u.test(e),_=e=>V(e,Q,N),S=e=>V(e,J,v),T=e=>V(e,K,P),R=e=>V(e,q,C),I=e=>V(e,H,M),L=e=>f.test(e),U=e=>$(e,Q),W=e=>$(e,X),B=e=>$(e,K),F=e=>$(e,Z),D=e=>$(e,q),G=e=>$(e,H,!0),V=(e,r,t)=>{let o=u.exec(e);return!!o&&(o[1]?r(o[1]):t(o[2]))},$=function(e,r){let t=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=f.exec(e);return!!o&&(o[1]?r(o[1]):t)},K=e=>"position"===e||"percentage"===e,q=e=>"image"===e||"url"===e,Z=e=>"length"===e||"size"===e||"bg-size"===e,Q=e=>"length"===e,J=e=>"number"===e,X=e=>"family-name"===e,H=e=>"shadow"===e;Symbol.toStringTag;let Y=function(e){let r,a,l;for(var c=arguments.length,d=Array(c>1?c-1:0),u=1;u<c;u++)d[u-1]=arguments[u];let f=function(s){let i;return a=(r={cache:(e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let r=0,t=new Map,o=new Map,n=(n,a)=>{t.set(n,a),++r>e&&(r=0,o=t,t=new Map)};return{get(e){let r=t.get(e);return void 0!==r?r:void 0!==(r=o.get(e))?(n(e,r),r):void 0},set(e,r){t.has(e)?t.set(e,r):n(e,r)}}})((i=d.reduce((e,r)=>r(e),e())).cacheSize),parseClassName:(e=>{let{prefix:r,experimentalParseClassName:t}=e,o=e=>{let r,t,o=[],n=0,a=0,l=0;for(let t=0;t<e.length;t++){let s=e[t];if(0===n&&0===a){if(":"===s){o.push(e.slice(l,t)),l=t+1;continue}if("/"===s){r=t;continue}}"["===s?n++:"]"===s?n--:"("===s?a++:")"===s&&a--}let s=0===o.length?e:e.substring(l),i=(t=s).endsWith("!")?t.substring(0,t.length-1):t.startsWith("!")?t.substring(1):t;return{modifiers:o,hasImportantModifier:i!==s,baseClassName:i,maybePostfixModifierPosition:r&&r>l?r-l:void 0}};if(r){let e=r+":",t=o;o=r=>r.startsWith(e)?t(r.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:r,maybePostfixModifierPosition:void 0}}if(t){let e=o;o=r=>t({className:r,parseClassName:e})}return o})(i),sortModifiers:(e=>{let r=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let t=[],o=[];return e.forEach(e=>{"["===e[0]||r[e]?(t.push(...o.sort(),e),o=[]):o.push(e)}),t.push(...o.sort()),t}})(i),...(e=>{let r=(e=>{let{theme:r,classGroups:t}=e,o={nextPart:new Map,validators:[]};for(let e in t)n(t[e],o,e,r);return o})(e),{conflictingClassGroups:a,conflictingClassGroupModifiers:l}=e;return{getClassGroupId:e=>{let n=e.split("-");return""===n[0]&&1!==n.length&&n.shift(),t(n,r)||(e=>{if(o.test(e)){let r=o.exec(e)[1],t=null==r?void 0:r.substring(0,r.indexOf(":"));if(t)return"arbitrary.."+t}})(e)},getConflictingClassGroupIds:(e,r)=>{let t=a[e]||[];return r&&l[e]?[...t,...l[e]]:t}}})(i)}).cache.get,l=r.cache.set,f=p,p(s)};function p(e){let t=a(e);if(t)return t;let o=((e,r)=>{let{parseClassName:t,getClassGroupId:o,getConflictingClassGroupIds:n,sortModifiers:a}=r,l=[],i=e.trim().split(s),c="";for(let e=i.length-1;e>=0;e-=1){let r=i[e],{isExternal:s,modifiers:d,hasImportantModifier:u,baseClassName:f,maybePostfixModifierPosition:p}=t(r);if(s){c=r+(c.length>0?" "+c:c);continue}let m=!!p,g=o(m?f.substring(0,p):f);if(!g){if(!m||!(g=o(f))){c=r+(c.length>0?" "+c:c);continue}m=!1}let b=a(d).join(":"),h=u?b+"!":b,y=h+g;if(l.includes(y))continue;l.push(y);let x=n(g,m);for(let e=0;e<x.length;++e){let r=x[e];l.push(h+r)}c=r+(c.length>0?" "+c:c)}return c})(e,r);return l(e,o),o}return function(){return f(i.apply(null,arguments))}}(()=>{let e=d("color"),r=d("font"),t=d("text"),o=d("font-weight"),n=d("tracking"),a=d("leading"),l=d("breakpoint"),s=d("container"),i=d("spacing"),c=d("radius"),u=d("shadow"),f=d("inset-shadow"),p=d("text-shadow"),m=d("drop-shadow"),g=d("blur"),b=d("perspective"),h=d("aspect"),y=d("ease"),N=d("animate"),P=()=>["auto","avoid","all","avoid-page","page","left","right","column"],M=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],C=()=>[...M(),L,A],V=()=>["auto","hidden","clip","visible","scroll"],$=()=>["auto","contain","none"],K=()=>[L,A,i],q=()=>[x,"full","auto",...K()],Z=()=>[k,"none","subgrid",L,A],Q=()=>["auto",{span:["full",k,L,A]},k,L,A],J=()=>[k,"auto",L,A],X=()=>["auto","min","max","fr",L,A],H=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],Y=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...K()],er=()=>[x,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...K()],et=()=>[e,L,A],eo=()=>[...M(),B,T,{position:[L,A]}],en=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ea=()=>["auto","cover","contain",F,E,{size:[L,A]}],el=()=>[w,U,_],es=()=>["","none","full",c,L,A],ei=()=>["",v,U,_],ec=()=>["solid","dashed","dotted","double"],ed=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],eu=()=>[v,w,B,T],ef=()=>["","none",g,L,A],ep=()=>["none",v,L,A],em=()=>["none",v,L,A],eg=()=>[v,L,A],eb=()=>[x,"full",...K()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[j],breakpoint:[j],color:[z],container:[j],"drop-shadow":[j],ease:["in","out","in-out"],font:[O],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[j],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[j],shadow:[j],spacing:["px",v],text:[j],"text-shadow":[j],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",x,A,L,h]}],container:["container"],columns:[{columns:[v,A,L,s]}],"break-after":[{"break-after":P()}],"break-before":[{"break-before":P()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:C()}],overflow:[{overflow:V()}],"overflow-x":[{"overflow-x":V()}],"overflow-y":[{"overflow-y":V()}],overscroll:[{overscroll:$()}],"overscroll-x":[{"overscroll-x":$()}],"overscroll-y":[{"overscroll-y":$()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:q()}],"inset-x":[{"inset-x":q()}],"inset-y":[{"inset-y":q()}],start:[{start:q()}],end:[{end:q()}],top:[{top:q()}],right:[{right:q()}],bottom:[{bottom:q()}],left:[{left:q()}],visibility:["visible","invisible","collapse"],z:[{z:[k,"auto",L,A]}],basis:[{basis:[x,"full","auto",s,...K()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[v,x,"auto","initial","none",A]}],grow:[{grow:["",v,L,A]}],shrink:[{shrink:["",v,L,A]}],order:[{order:[k,"first","last","none",L,A]}],"grid-cols":[{"grid-cols":Z()}],"col-start-end":[{col:Q()}],"col-start":[{"col-start":J()}],"col-end":[{"col-end":J()}],"grid-rows":[{"grid-rows":Z()}],"row-start-end":[{row:Q()}],"row-start":[{"row-start":J()}],"row-end":[{"row-end":J()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":X()}],"auto-rows":[{"auto-rows":X()}],gap:[{gap:K()}],"gap-x":[{"gap-x":K()}],"gap-y":[{"gap-y":K()}],"justify-content":[{justify:[...H(),"normal"]}],"justify-items":[{"justify-items":[...Y(),"normal"]}],"justify-self":[{"justify-self":["auto",...Y()]}],"align-content":[{content:["normal",...H()]}],"align-items":[{items:[...Y(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...Y(),{baseline:["","last"]}]}],"place-content":[{"place-content":H()}],"place-items":[{"place-items":[...Y(),"baseline"]}],"place-self":[{"place-self":["auto",...Y()]}],p:[{p:K()}],px:[{px:K()}],py:[{py:K()}],ps:[{ps:K()}],pe:[{pe:K()}],pt:[{pt:K()}],pr:[{pr:K()}],pb:[{pb:K()}],pl:[{pl:K()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":K()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":K()}],"space-y-reverse":["space-y-reverse"],size:[{size:er()}],w:[{w:[s,"screen",...er()]}],"min-w":[{"min-w":[s,"screen","none",...er()]}],"max-w":[{"max-w":[s,"screen","none","prose",{screen:[l]},...er()]}],h:[{h:["screen","lh",...er()]}],"min-h":[{"min-h":["screen","lh","none",...er()]}],"max-h":[{"max-h":["screen","lh",...er()]}],"font-size":[{text:["base",t,U,_]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[o,L,S]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",w,A]}],"font-family":[{font:[W,A,r]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[n,L,A]}],"line-clamp":[{"line-clamp":[v,"none",L,S]}],leading:[{leading:[a,...K()]}],"list-image":[{"list-image":["none",L,A]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",L,A]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:et()}],"text-color":[{text:et()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ec(),"wavy"]}],"text-decoration-thickness":[{decoration:[v,"from-font","auto",L,_]}],"text-decoration-color":[{decoration:et()}],"underline-offset":[{"underline-offset":[v,"auto",L,A]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:K()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",L,A]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",L,A]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:eo()}],"bg-repeat":[{bg:en()}],"bg-size":[{bg:ea()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},k,L,A],radial:["",L,A],conic:[k,L,A]},D,R]}],"bg-color":[{bg:et()}],"gradient-from-pos":[{from:el()}],"gradient-via-pos":[{via:el()}],"gradient-to-pos":[{to:el()}],"gradient-from":[{from:et()}],"gradient-via":[{via:et()}],"gradient-to":[{to:et()}],rounded:[{rounded:es()}],"rounded-s":[{"rounded-s":es()}],"rounded-e":[{"rounded-e":es()}],"rounded-t":[{"rounded-t":es()}],"rounded-r":[{"rounded-r":es()}],"rounded-b":[{"rounded-b":es()}],"rounded-l":[{"rounded-l":es()}],"rounded-ss":[{"rounded-ss":es()}],"rounded-se":[{"rounded-se":es()}],"rounded-ee":[{"rounded-ee":es()}],"rounded-es":[{"rounded-es":es()}],"rounded-tl":[{"rounded-tl":es()}],"rounded-tr":[{"rounded-tr":es()}],"rounded-br":[{"rounded-br":es()}],"rounded-bl":[{"rounded-bl":es()}],"border-w":[{border:ei()}],"border-w-x":[{"border-x":ei()}],"border-w-y":[{"border-y":ei()}],"border-w-s":[{"border-s":ei()}],"border-w-e":[{"border-e":ei()}],"border-w-t":[{"border-t":ei()}],"border-w-r":[{"border-r":ei()}],"border-w-b":[{"border-b":ei()}],"border-w-l":[{"border-l":ei()}],"divide-x":[{"divide-x":ei()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":ei()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ec(),"hidden","none"]}],"divide-style":[{divide:[...ec(),"hidden","none"]}],"border-color":[{border:et()}],"border-color-x":[{"border-x":et()}],"border-color-y":[{"border-y":et()}],"border-color-s":[{"border-s":et()}],"border-color-e":[{"border-e":et()}],"border-color-t":[{"border-t":et()}],"border-color-r":[{"border-r":et()}],"border-color-b":[{"border-b":et()}],"border-color-l":[{"border-l":et()}],"divide-color":[{divide:et()}],"outline-style":[{outline:[...ec(),"none","hidden"]}],"outline-offset":[{"outline-offset":[v,L,A]}],"outline-w":[{outline:["",v,U,_]}],"outline-color":[{outline:et()}],shadow:[{shadow:["","none",u,G,I]}],"shadow-color":[{shadow:et()}],"inset-shadow":[{"inset-shadow":["none",f,G,I]}],"inset-shadow-color":[{"inset-shadow":et()}],"ring-w":[{ring:ei()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:et()}],"ring-offset-w":[{"ring-offset":[v,_]}],"ring-offset-color":[{"ring-offset":et()}],"inset-ring-w":[{"inset-ring":ei()}],"inset-ring-color":[{"inset-ring":et()}],"text-shadow":[{"text-shadow":["none",p,G,I]}],"text-shadow-color":[{"text-shadow":et()}],opacity:[{opacity:[v,L,A]}],"mix-blend":[{"mix-blend":[...ed(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ed()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[v]}],"mask-image-linear-from-pos":[{"mask-linear-from":eu()}],"mask-image-linear-to-pos":[{"mask-linear-to":eu()}],"mask-image-linear-from-color":[{"mask-linear-from":et()}],"mask-image-linear-to-color":[{"mask-linear-to":et()}],"mask-image-t-from-pos":[{"mask-t-from":eu()}],"mask-image-t-to-pos":[{"mask-t-to":eu()}],"mask-image-t-from-color":[{"mask-t-from":et()}],"mask-image-t-to-color":[{"mask-t-to":et()}],"mask-image-r-from-pos":[{"mask-r-from":eu()}],"mask-image-r-to-pos":[{"mask-r-to":eu()}],"mask-image-r-from-color":[{"mask-r-from":et()}],"mask-image-r-to-color":[{"mask-r-to":et()}],"mask-image-b-from-pos":[{"mask-b-from":eu()}],"mask-image-b-to-pos":[{"mask-b-to":eu()}],"mask-image-b-from-color":[{"mask-b-from":et()}],"mask-image-b-to-color":[{"mask-b-to":et()}],"mask-image-l-from-pos":[{"mask-l-from":eu()}],"mask-image-l-to-pos":[{"mask-l-to":eu()}],"mask-image-l-from-color":[{"mask-l-from":et()}],"mask-image-l-to-color":[{"mask-l-to":et()}],"mask-image-x-from-pos":[{"mask-x-from":eu()}],"mask-image-x-to-pos":[{"mask-x-to":eu()}],"mask-image-x-from-color":[{"mask-x-from":et()}],"mask-image-x-to-color":[{"mask-x-to":et()}],"mask-image-y-from-pos":[{"mask-y-from":eu()}],"mask-image-y-to-pos":[{"mask-y-to":eu()}],"mask-image-y-from-color":[{"mask-y-from":et()}],"mask-image-y-to-color":[{"mask-y-to":et()}],"mask-image-radial":[{"mask-radial":[L,A]}],"mask-image-radial-from-pos":[{"mask-radial-from":eu()}],"mask-image-radial-to-pos":[{"mask-radial-to":eu()}],"mask-image-radial-from-color":[{"mask-radial-from":et()}],"mask-image-radial-to-color":[{"mask-radial-to":et()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":M()}],"mask-image-conic-pos":[{"mask-conic":[v]}],"mask-image-conic-from-pos":[{"mask-conic-from":eu()}],"mask-image-conic-to-pos":[{"mask-conic-to":eu()}],"mask-image-conic-from-color":[{"mask-conic-from":et()}],"mask-image-conic-to-color":[{"mask-conic-to":et()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:eo()}],"mask-repeat":[{mask:en()}],"mask-size":[{mask:ea()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",L,A]}],filter:[{filter:["","none",L,A]}],blur:[{blur:ef()}],brightness:[{brightness:[v,L,A]}],contrast:[{contrast:[v,L,A]}],"drop-shadow":[{"drop-shadow":["","none",m,G,I]}],"drop-shadow-color":[{"drop-shadow":et()}],grayscale:[{grayscale:["",v,L,A]}],"hue-rotate":[{"hue-rotate":[v,L,A]}],invert:[{invert:["",v,L,A]}],saturate:[{saturate:[v,L,A]}],sepia:[{sepia:["",v,L,A]}],"backdrop-filter":[{"backdrop-filter":["","none",L,A]}],"backdrop-blur":[{"backdrop-blur":ef()}],"backdrop-brightness":[{"backdrop-brightness":[v,L,A]}],"backdrop-contrast":[{"backdrop-contrast":[v,L,A]}],"backdrop-grayscale":[{"backdrop-grayscale":["",v,L,A]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[v,L,A]}],"backdrop-invert":[{"backdrop-invert":["",v,L,A]}],"backdrop-opacity":[{"backdrop-opacity":[v,L,A]}],"backdrop-saturate":[{"backdrop-saturate":[v,L,A]}],"backdrop-sepia":[{"backdrop-sepia":["",v,L,A]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":K()}],"border-spacing-x":[{"border-spacing-x":K()}],"border-spacing-y":[{"border-spacing-y":K()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",L,A]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[v,"initial",L,A]}],ease:[{ease:["linear","initial",y,L,A]}],delay:[{delay:[v,L,A]}],animate:[{animate:["none",N,L,A]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[b,L,A]}],"perspective-origin":[{"perspective-origin":C()}],rotate:[{rotate:ep()}],"rotate-x":[{"rotate-x":ep()}],"rotate-y":[{"rotate-y":ep()}],"rotate-z":[{"rotate-z":ep()}],scale:[{scale:em()}],"scale-x":[{"scale-x":em()}],"scale-y":[{"scale-y":em()}],"scale-z":[{"scale-z":em()}],"scale-3d":["scale-3d"],skew:[{skew:eg()}],"skew-x":[{"skew-x":eg()}],"skew-y":[{"skew-y":eg()}],transform:[{transform:[L,A,"","none","gpu","cpu"]}],"transform-origin":[{origin:C()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eb()}],"translate-x":[{"translate-x":eb()}],"translate-y":[{"translate-y":eb()}],"translate-z":[{"translate-z":eb()}],"translate-none":["translate-none"],accent:[{accent:et()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:et()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",L,A]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":K()}],"scroll-mx":[{"scroll-mx":K()}],"scroll-my":[{"scroll-my":K()}],"scroll-ms":[{"scroll-ms":K()}],"scroll-me":[{"scroll-me":K()}],"scroll-mt":[{"scroll-mt":K()}],"scroll-mr":[{"scroll-mr":K()}],"scroll-mb":[{"scroll-mb":K()}],"scroll-ml":[{"scroll-ml":K()}],"scroll-p":[{"scroll-p":K()}],"scroll-px":[{"scroll-px":K()}],"scroll-py":[{"scroll-py":K()}],"scroll-ps":[{"scroll-ps":K()}],"scroll-pe":[{"scroll-pe":K()}],"scroll-pt":[{"scroll-pt":K()}],"scroll-pr":[{"scroll-pr":K()}],"scroll-pb":[{"scroll-pb":K()}],"scroll-pl":[{"scroll-pl":K()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",L,A]}],fill:[{fill:["none",...et()]}],"stroke-w":[{stroke:[v,U,_,S]}],stroke:[{stroke:["none",...et()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}});function ee(){for(var e=arguments.length,t=Array(e),o=0;o<e;o++)t[o]=arguments[o];return Y(r(t))}e.s(["default",()=>el],75254);var er=e.i(71645);let et=e=>{let r=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase());return r.charAt(0).toUpperCase()+r.slice(1)},eo=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim()};var en={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let ea=(0,er.forwardRef)((e,r)=>{let{color:t="currentColor",size:o=24,strokeWidth:n=2,absoluteStrokeWidth:a,className:l="",children:s,iconNode:i,...c}=e;return(0,er.createElement)("svg",{ref:r,...en,width:o,height:o,stroke:t,strokeWidth:a?24*Number(n)/Number(o):n,className:eo("lucide",l),...!s&&!(e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0})(c)&&{"aria-hidden":"true"},...c},[...i.map(e=>{let[r,t]=e;return(0,er.createElement)(r,t)}),...Array.isArray(s)?s:[s]])}),el=(e,r)=>{let t=(0,er.forwardRef)((t,o)=>{let{className:n,...a}=t;return(0,er.createElement)(ea,{ref:o,iconNode:r,className:eo("lucide-".concat(et(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),n),...a})});return t.displayName=et(e),t}},78894,e=>{"use strict";e.s(["AlertTriangle",()=>r],78894);let r=(0,e.i(75254).default)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},17923,e=>{"use strict";e.s(["BarChart3",()=>r],17923);let r=(0,e.i(75254).default)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},53475,e=>{"use strict";e.s(["Wifi",()=>r],53475);let r=(0,e.i(75254).default)("wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]])},57212,e=>{"use strict";e.s(["WifiOff",()=>r],57212);let r=(0,e.i(75254).default)("wifi-off",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},87486,91918,25913,e=>{"use strict";e.s(["Badge",()=>p],87486);var r=e.i(43476);e.s(["Slot",()=>n],91918);var t=e.i(71645);function o(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}var n=function(e){let n=function(e){let r=t.forwardRef((e,r)=>{let{children:n,...a}=e;if(t.isValidElement(n)){var l,s,i;let e,c,d=(c=(e=null==(s=Object.getOwnPropertyDescriptor((l=n).props,"ref"))?void 0:s.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(c=(e=null==(i=Object.getOwnPropertyDescriptor(l,"ref"))?void 0:i.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref,u=function(e,r){let t={...r};for(let o in r){let n=e[o],a=r[o];/^on[A-Z]/.test(o)?n&&a?t[o]=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];let o=a(...r);return n(...r),o}:n&&(t[o]=n):"style"===o?t[o]={...n,...a}:"className"===o&&(t[o]=[n,a].filter(Boolean).join(" "))}return{...e,...t}}(a,n.props);return n.type!==t.Fragment&&(u.ref=r?function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return e=>{let t=!1,n=r.map(r=>{let n=o(r,e);return t||"function"!=typeof n||(t=!0),n});if(t)return()=>{for(let e=0;e<n.length;e++){let t=n[e];"function"==typeof t?t():o(r[e],null)}}}}(r,d):d),t.cloneElement(n,u)}return t.Children.count(n)>1?t.Children.only(null):null});return r.displayName="".concat(e,".SlotClone"),r}(e),a=t.forwardRef((e,o)=>{let{children:a,...s}=e,i=t.Children.toArray(a),c=i.find(l);if(c){let e=c.props.children,a=i.map(r=>r!==c?r:t.Children.count(e)>1?t.Children.only(null):t.isValidElement(e)?e.props.children:null);return(0,r.jsx)(n,{...s,ref:o,children:t.isValidElement(e)?t.cloneElement(e,void 0,a):null})}return(0,r.jsx)(n,{...s,ref:o,children:a})});return a.displayName="".concat(e,".Slot"),a}("Slot"),a=Symbol("radix.slottable");function l(e){return t.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}e.s(["cva",()=>d],25913);var s=e.i(7670);let i=e=>"boolean"==typeof e?"".concat(e):0===e?"0":e,c=s.clsx,d=(e,r)=>t=>{var o;if((null==r?void 0:r.variants)==null)return c(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:n,defaultVariants:a}=r,l=Object.keys(n).map(e=>{let r=null==t?void 0:t[e],o=null==a?void 0:a[e];if(null===r)return null;let l=i(r)||i(o);return n[e][l]}),s=t&&Object.entries(t).reduce((e,r)=>{let[t,o]=r;return void 0===o||(e[t]=o),e},{});return c(e,l,null==r||null==(o=r.compoundVariants)?void 0:o.reduce((e,r)=>{let{class:t,className:o,...n}=r;return Object.entries(n).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...a,...s}[r]):({...a,...s})[r]===t})?[...e,t,o]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)};var u=e.i(75157);let f=d("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function p(e){let{className:t,variant:o,asChild:a=!1,...l}=e;return(0,r.jsx)(a?n:"span",{"data-slot":"badge",className:(0,u.cn)(f({variant:o}),t),...l})}},42252,e=>{"use strict";e.s(["Layout",()=>h],42252);var r=e.i(43476),t=e.i(22016),o=e.i(18566),n=e.i(75157),a=e.i(75254);let l=(0,a.default)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]]);var s=e.i(78894),i=e.i(17923);let c=(0,a.default)("settings",[["path",{d:"M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915",key:"1i5ecw"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),d=(0,a.default)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);var u=e.i(53475),f=e.i(57212),p=e.i(99346),m=e.i(87486);let g=[{name:"Dashboard",href:"/",icon:l},{name:"Alerts",href:"/alerts",icon:s.AlertTriangle},{name:"Analytics",href:"/analytics",icon:i.BarChart3},{name:"Settings",href:"/settings",icon:c}];function b(){let e=(0,o.usePathname)(),{isConnected:a,connectionStatus:l}=(0,p.useWebSocketContext)();return(0,r.jsxs)("nav",{className:"bg-card border-b border-border",children:[(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between h-16",children:[(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsxs)("div",{className:"flex-shrink-0 flex items-center",children:[(0,r.jsx)(d,{className:"h-8 w-8 text-primary"}),(0,r.jsx)("span",{className:"ml-2 text-xl font-bold text-foreground",children:"Wildlife Guardian"})]}),(0,r.jsx)("div",{className:"hidden sm:ml-6 sm:flex sm:space-x-8",children:g.map(o=>{let a=e===o.href;return(0,r.jsxs)(t.default,{href:o.href,className:(0,n.cn)("inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-colors",a?"border-primary text-primary":"border-transparent text-muted-foreground hover:text-foreground hover:border-border"),children:[(0,r.jsx)(o.icon,{className:"h-4 w-4 mr-2"}),o.name]},o.name)})})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsxs)("div",{className:"hidden sm:flex items-center gap-2",children:[a?(0,r.jsx)(u.Wifi,{className:"h-4 w-4 text-green-500"}):(0,r.jsx)(f.WifiOff,{className:"h-4 w-4 text-red-500"}),(0,r.jsx)(m.Badge,{className:(0,n.cn)("text-xs",a?"bg-green-500/10 text-green-500 border-green-500/20":"bg-red-500/10 text-red-500 border-red-500/20"),children:l})]}),(0,r.jsx)("div",{className:"sm:hidden flex items-center",children:(0,r.jsxs)("button",{type:"button",className:"inline-flex items-center justify-center p-2 rounded-md text-muted-foreground hover:text-foreground hover:bg-accent focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary","aria-controls":"mobile-menu","aria-expanded":"false",children:[(0,r.jsx)("span",{className:"sr-only",children:"Open main menu"}),(0,r.jsx)("svg",{className:"block h-6 w-6",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","aria-hidden":"true",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})]})})]})]})}),(0,r.jsx)("div",{className:"sm:hidden",id:"mobile-menu",children:(0,r.jsx)("div",{className:"pt-2 pb-3 space-y-1",children:g.map(o=>{let a=e===o.href;return(0,r.jsxs)(t.default,{href:o.href,className:(0,n.cn)("flex items-center pl-3 pr-4 py-2 border-l-4 text-base font-medium transition-colors",a?"bg-primary/10 border-primary text-primary":"border-transparent text-muted-foreground hover:text-foreground hover:bg-accent hover:border-border"),children:[(0,r.jsx)(o.icon,{className:"h-5 w-5 mr-3"}),o.name]},o.name)})})})]})}function h(e){let{children:t}=e;return(0,r.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,r.jsx)(b,{}),(0,r.jsx)("main",{className:"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8",children:t})]})}}]);