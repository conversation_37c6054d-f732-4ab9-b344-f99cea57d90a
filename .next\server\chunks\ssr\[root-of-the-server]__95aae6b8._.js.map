{"version": 3, "sources": ["turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/contexts/app-router-context.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-server-dom-turbopack-client.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/contexts/hooks-client-context.ts", "turbopack:///[project]/node_modules/next/src/server/route-modules/app-page/vendored/contexts/server-inserted-html.ts", "turbopack:///[project]/node_modules/next/src/client/components/handle-isr-error.tsx", "turbopack:///[project]/node_modules/next/src/client/components/builtin/global-error.tsx", "turbopack:///[project]/node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js", "turbopack:///[project]/node_modules/@tanstack/react-query/build/modern/errorBoundaryUtils.js", "turbopack:///[project]/node_modules/@tanstack/react-query/build/modern/IsRestoringProvider.js", "turbopack:///[project]/node_modules/@tanstack/react-query/build/modern/suspense.js", "turbopack:///[project]/node_modules/@tanstack/react-query/build/modern/useBaseQuery.js", "turbopack:///[project]/node_modules/@tanstack/query-core/build/modern/queryObserver.js", "turbopack:///[project]/node_modules/@tanstack/react-query/build/modern/useQuery.js", "turbopack:///[project]/src/components/ui/card.tsx"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].AppRouterContext\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactServerDOMTurbopackClient\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].HooksClientContext\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].ServerInsertedHtml\n", "const workAsyncStorage =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n      ).workAsyncStorage\n    : undefined\n\n// if we are revalidating we want to re-throw the error so the\n// function crashes so we can maintain our previous cache\n// instead of caching the error page\nexport function HandleISRError({ error }: { error: any }) {\n  if (workAsyncStorage) {\n    const store = workAsyncStorage.getStore()\n    if (store?.isRevalidate || store?.isStaticGeneration) {\n      console.error(error)\n      throw error\n    }\n  }\n\n  return null\n}\n", "'use client'\n\nimport { HandleISRError } from '../handle-isr-error'\n\nconst styles = {\n  error: {\n    // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n    fontFamily:\n      'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n    height: '100vh',\n    textAlign: 'center',\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  text: {\n    fontSize: '14px',\n    fontWeight: 400,\n    lineHeight: '28px',\n    margin: '0 8px',\n  },\n} as const\n\nexport type GlobalErrorComponent = React.ComponentType<{\n  error: any\n}>\nfunction DefaultGlobalError({ error }: { error: any }) {\n  const digest: string | undefined = error?.digest\n  return (\n    <html id=\"__next_error__\">\n      <head></head>\n      <body>\n        <HandleISRError error={error} />\n        <div style={styles.error}>\n          <div>\n            <h2 style={styles.text}>\n              Application error: a {digest ? 'server' : 'client'}-side exception\n              has occurred while loading {window.location.hostname} (see the{' '}\n              {digest ? 'server logs' : 'browser console'} for more\n              information).\n            </h2>\n            {digest ? <p style={styles.text}>{`Digest: ${digest}`}</p> : null}\n          </div>\n        </div>\n      </body>\n    </html>\n  )\n}\n\n// Exported so that the import signature in the loaders can be identical to user\n// supplied custom global error signatures.\nexport default DefaultGlobalError\n", "\"use client\";\n\n// src/QueryErrorResetBoundary.tsx\nimport * as React from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nfunction createValue() {\n  let isReset = false;\n  return {\n    clearReset: () => {\n      isReset = false;\n    },\n    reset: () => {\n      isReset = true;\n    },\n    isReset: () => {\n      return isReset;\n    }\n  };\n}\nvar QueryErrorResetBoundaryContext = React.createContext(createValue());\nvar useQueryErrorResetBoundary = () => React.useContext(QueryErrorResetBoundaryContext);\nvar QueryErrorResetBoundary = ({\n  children\n}) => {\n  const [value] = React.useState(() => createValue());\n  return /* @__PURE__ */ jsx(QueryErrorResetBoundaryContext.Provider, { value, children: typeof children === \"function\" ? children(value) : children });\n};\nexport {\n  QueryErrorResetBoundary,\n  useQueryErrorResetBoundary\n};\n//# sourceMappingURL=QueryErrorResetBoundary.js.map", "\"use client\";\n\n// src/errorBoundaryUtils.ts\nimport * as React from \"react\";\nimport { shouldThrowError } from \"@tanstack/query-core\";\nvar ensurePreventErrorBoundaryRetry = (options, errorResetBoundary) => {\n  if (options.suspense || options.throwOnError || options.experimental_prefetchInRender) {\n    if (!errorResetBoundary.isReset()) {\n      options.retryOnMount = false;\n    }\n  }\n};\nvar useClearResetErrorBoundary = (errorResetBoundary) => {\n  React.useEffect(() => {\n    errorResetBoundary.clearReset();\n  }, [errorResetBoundary]);\n};\nvar getHasError = ({\n  result,\n  errorResetBoundary,\n  throwOnError,\n  query,\n  suspense\n}) => {\n  return result.isError && !errorResetBoundary.isReset() && !result.isFetching && query && (suspense && result.data === void 0 || shouldThrowError(throwOnError, [result.error, query]));\n};\nexport {\n  ensurePreventErrorBoundaryRetry,\n  getHasError,\n  useClearResetErrorBoundary\n};\n//# sourceMappingURL=errorBoundaryUtils.js.map", "\"use client\";\n\n// src/IsRestoringProvider.ts\nimport * as React from \"react\";\nvar IsRestoringContext = React.createContext(false);\nvar useIsRestoring = () => React.useContext(IsRestoringContext);\nvar IsRestoringProvider = IsRestoringContext.Provider;\nexport {\n  IsRestoringProvider,\n  useIsRestoring\n};\n//# sourceMappingURL=IsRestoringProvider.js.map", "// src/suspense.ts\nvar defaultThrowOnError = (_error, query) => query.state.data === void 0;\nvar ensureSuspenseTimers = (defaultedOptions) => {\n  if (defaultedOptions.suspense) {\n    const clamp = (value) => value === \"static\" ? value : Math.max(value ?? 1e3, 1e3);\n    const originalStaleTime = defaultedOptions.staleTime;\n    defaultedOptions.staleTime = typeof originalStaleTime === \"function\" ? (...args) => clamp(originalStaleTime(...args)) : clamp(originalStaleTime);\n    if (typeof defaultedOptions.gcTime === \"number\") {\n      defaultedOptions.gcTime = Math.max(defaultedOptions.gcTime, 1e3);\n    }\n  }\n};\nvar willFetch = (result, isRestoring) => result.isLoading && result.isFetching && !isRestoring;\nvar shouldSuspend = (defaultedOptions, result) => defaultedOptions?.suspense && result.isPending;\nvar fetchOptimistic = (defaultedOptions, observer, errorResetBoundary) => observer.fetchOptimistic(defaultedOptions).catch(() => {\n  errorResetBoundary.clearReset();\n});\nexport {\n  defaultThrowOnError,\n  ensureSuspenseTimers,\n  fetchOptimistic,\n  shouldSuspend,\n  willFetch\n};\n//# sourceMappingURL=suspense.js.map", "\"use client\";\n\n// src/useBaseQuery.ts\nimport * as React from \"react\";\nimport { isServer, noop, notify<PERSON>anager } from \"@tanstack/query-core\";\nimport { useQueryClient } from \"./QueryClientProvider.js\";\nimport { useQueryErrorResetBoundary } from \"./QueryErrorResetBoundary.js\";\nimport {\n  ensurePreventErrorBoundaryRetry,\n  getHasError,\n  useClearResetErrorBoundary\n} from \"./errorBoundaryUtils.js\";\nimport { useIsRestoring } from \"./IsRestoringProvider.js\";\nimport {\n  ensureSuspenseTimers,\n  fetchOptimistic,\n  shouldSuspend,\n  willFetch\n} from \"./suspense.js\";\nfunction useBaseQuery(options, Observer, queryClient) {\n  if (process.env.NODE_ENV !== \"production\") {\n    if (typeof options !== \"object\" || Array.isArray(options)) {\n      throw new Error(\n        'Bad argument type. Starting with v5, only the \"Object\" form is allowed when calling query related functions. Please use the error stack to find the culprit call. More info here: https://tanstack.com/query/latest/docs/react/guides/migrating-to-v5#supports-a-single-signature-one-object'\n      );\n    }\n  }\n  const isRestoring = useIsRestoring();\n  const errorResetBoundary = useQueryErrorResetBoundary();\n  const client = useQueryClient(queryClient);\n  const defaultedOptions = client.defaultQueryOptions(options);\n  client.getDefaultOptions().queries?._experimental_beforeQuery?.(\n    defaultedOptions\n  );\n  if (process.env.NODE_ENV !== \"production\") {\n    if (!defaultedOptions.queryFn) {\n      console.error(\n        `[${defaultedOptions.queryHash}]: No queryFn was passed as an option, and no default queryFn was found. The queryFn parameter is only optional when using a default queryFn. More info here: https://tanstack.com/query/latest/docs/framework/react/guides/default-query-function`\n      );\n    }\n  }\n  defaultedOptions._optimisticResults = isRestoring ? \"isRestoring\" : \"optimistic\";\n  ensureSuspenseTimers(defaultedOptions);\n  ensurePreventErrorBoundaryRetry(defaultedOptions, errorResetBoundary);\n  useClearResetErrorBoundary(errorResetBoundary);\n  const isNewCacheEntry = !client.getQueryCache().get(defaultedOptions.queryHash);\n  const [observer] = React.useState(\n    () => new Observer(\n      client,\n      defaultedOptions\n    )\n  );\n  const result = observer.getOptimisticResult(defaultedOptions);\n  const shouldSubscribe = !isRestoring && options.subscribed !== false;\n  React.useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) => {\n        const unsubscribe = shouldSubscribe ? observer.subscribe(notifyManager.batchCalls(onStoreChange)) : noop;\n        observer.updateResult();\n        return unsubscribe;\n      },\n      [observer, shouldSubscribe]\n    ),\n    () => observer.getCurrentResult(),\n    () => observer.getCurrentResult()\n  );\n  React.useEffect(() => {\n    observer.setOptions(defaultedOptions);\n  }, [defaultedOptions, observer]);\n  if (shouldSuspend(defaultedOptions, result)) {\n    throw fetchOptimistic(defaultedOptions, observer, errorResetBoundary);\n  }\n  if (getHasError({\n    result,\n    errorResetBoundary,\n    throwOnError: defaultedOptions.throwOnError,\n    query: client.getQueryCache().get(defaultedOptions.queryHash),\n    suspense: defaultedOptions.suspense\n  })) {\n    throw result.error;\n  }\n  ;\n  client.getDefaultOptions().queries?._experimental_afterQuery?.(\n    defaultedOptions,\n    result\n  );\n  if (defaultedOptions.experimental_prefetchInRender && !isServer && willFetch(result, isRestoring)) {\n    const promise = isNewCacheEntry ? (\n      // Fetch immediately on render in order to ensure `.promise` is resolved even if the component is unmounted\n      fetchOptimistic(defaultedOptions, observer, errorResetBoundary)\n    ) : (\n      // subscribe to the \"cache promise\" so that we can finalize the currentThenable once data comes in\n      client.getQueryCache().get(defaultedOptions.queryHash)?.promise\n    );\n    promise?.catch(noop).finally(() => {\n      observer.updateResult();\n    });\n  }\n  return !defaultedOptions.notifyOnChangeProps ? observer.trackResult(result) : result;\n}\nexport {\n  useBaseQuery\n};\n//# sourceMappingURL=useBaseQuery.js.map", "// src/queryObserver.ts\nimport { focusManager } from \"./focusManager.js\";\nimport { notifyManager } from \"./notifyManager.js\";\nimport { fetchState } from \"./query.js\";\nimport { Subscribable } from \"./subscribable.js\";\nimport { pendingThenable } from \"./thenable.js\";\nimport {\n  isServer,\n  isValidTimeout,\n  noop,\n  replaceData,\n  resolveEnabled,\n  resolveStaleTime,\n  shallowEqualObjects,\n  timeUntilStale\n} from \"./utils.js\";\nvar QueryObserver = class extends Subscribable {\n  constructor(client, options) {\n    super();\n    this.options = options;\n    this.#client = client;\n    this.#selectError = null;\n    this.#currentThenable = pendingThenable();\n    this.bindMethods();\n    this.setOptions(options);\n  }\n  #client;\n  #currentQuery = void 0;\n  #currentQueryInitialState = void 0;\n  #currentResult = void 0;\n  #currentResultState;\n  #currentResultOptions;\n  #currentThenable;\n  #selectError;\n  #selectFn;\n  #selectResult;\n  // This property keeps track of the last query with defined data.\n  // It will be used to pass the previous data and query to the placeholder function between renders.\n  #lastQueryWithDefinedData;\n  #staleTimeoutId;\n  #refetchIntervalId;\n  #currentRefetchInterval;\n  #trackedProps = /* @__PURE__ */ new Set();\n  bindMethods() {\n    this.refetch = this.refetch.bind(this);\n  }\n  onSubscribe() {\n    if (this.listeners.size === 1) {\n      this.#currentQuery.addObserver(this);\n      if (shouldFetchOnMount(this.#currentQuery, this.options)) {\n        this.#executeFetch();\n      } else {\n        this.updateResult();\n      }\n      this.#updateTimers();\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.destroy();\n    }\n  }\n  shouldFetchOnReconnect() {\n    return shouldFetchOn(\n      this.#currentQuery,\n      this.options,\n      this.options.refetchOnReconnect\n    );\n  }\n  shouldFetchOnWindowFocus() {\n    return shouldFetchOn(\n      this.#currentQuery,\n      this.options,\n      this.options.refetchOnWindowFocus\n    );\n  }\n  destroy() {\n    this.listeners = /* @__PURE__ */ new Set();\n    this.#clearStaleTimeout();\n    this.#clearRefetchInterval();\n    this.#currentQuery.removeObserver(this);\n  }\n  setOptions(options) {\n    const prevOptions = this.options;\n    const prevQuery = this.#currentQuery;\n    this.options = this.#client.defaultQueryOptions(options);\n    if (this.options.enabled !== void 0 && typeof this.options.enabled !== \"boolean\" && typeof this.options.enabled !== \"function\" && typeof resolveEnabled(this.options.enabled, this.#currentQuery) !== \"boolean\") {\n      throw new Error(\n        \"Expected enabled to be a boolean or a callback that returns a boolean\"\n      );\n    }\n    this.#updateQuery();\n    this.#currentQuery.setOptions(this.options);\n    if (prevOptions._defaulted && !shallowEqualObjects(this.options, prevOptions)) {\n      this.#client.getQueryCache().notify({\n        type: \"observerOptionsUpdated\",\n        query: this.#currentQuery,\n        observer: this\n      });\n    }\n    const mounted = this.hasListeners();\n    if (mounted && shouldFetchOptionally(\n      this.#currentQuery,\n      prevQuery,\n      this.options,\n      prevOptions\n    )) {\n      this.#executeFetch();\n    }\n    this.updateResult();\n    if (mounted && (this.#currentQuery !== prevQuery || resolveEnabled(this.options.enabled, this.#currentQuery) !== resolveEnabled(prevOptions.enabled, this.#currentQuery) || resolveStaleTime(this.options.staleTime, this.#currentQuery) !== resolveStaleTime(prevOptions.staleTime, this.#currentQuery))) {\n      this.#updateStaleTimeout();\n    }\n    const nextRefetchInterval = this.#computeRefetchInterval();\n    if (mounted && (this.#currentQuery !== prevQuery || resolveEnabled(this.options.enabled, this.#currentQuery) !== resolveEnabled(prevOptions.enabled, this.#currentQuery) || nextRefetchInterval !== this.#currentRefetchInterval)) {\n      this.#updateRefetchInterval(nextRefetchInterval);\n    }\n  }\n  getOptimisticResult(options) {\n    const query = this.#client.getQueryCache().build(this.#client, options);\n    const result = this.createResult(query, options);\n    if (shouldAssignObserverCurrentProperties(this, result)) {\n      this.#currentResult = result;\n      this.#currentResultOptions = this.options;\n      this.#currentResultState = this.#currentQuery.state;\n    }\n    return result;\n  }\n  getCurrentResult() {\n    return this.#currentResult;\n  }\n  trackResult(result, onPropTracked) {\n    return new Proxy(result, {\n      get: (target, key) => {\n        this.trackProp(key);\n        onPropTracked?.(key);\n        if (key === \"promise\" && !this.options.experimental_prefetchInRender && this.#currentThenable.status === \"pending\") {\n          this.#currentThenable.reject(\n            new Error(\n              \"experimental_prefetchInRender feature flag is not enabled\"\n            )\n          );\n        }\n        return Reflect.get(target, key);\n      }\n    });\n  }\n  trackProp(key) {\n    this.#trackedProps.add(key);\n  }\n  getCurrentQuery() {\n    return this.#currentQuery;\n  }\n  refetch({ ...options } = {}) {\n    return this.fetch({\n      ...options\n    });\n  }\n  fetchOptimistic(options) {\n    const defaultedOptions = this.#client.defaultQueryOptions(options);\n    const query = this.#client.getQueryCache().build(this.#client, defaultedOptions);\n    return query.fetch().then(() => this.createResult(query, defaultedOptions));\n  }\n  fetch(fetchOptions) {\n    return this.#executeFetch({\n      ...fetchOptions,\n      cancelRefetch: fetchOptions.cancelRefetch ?? true\n    }).then(() => {\n      this.updateResult();\n      return this.#currentResult;\n    });\n  }\n  #executeFetch(fetchOptions) {\n    this.#updateQuery();\n    let promise = this.#currentQuery.fetch(\n      this.options,\n      fetchOptions\n    );\n    if (!fetchOptions?.throwOnError) {\n      promise = promise.catch(noop);\n    }\n    return promise;\n  }\n  #updateStaleTimeout() {\n    this.#clearStaleTimeout();\n    const staleTime = resolveStaleTime(\n      this.options.staleTime,\n      this.#currentQuery\n    );\n    if (isServer || this.#currentResult.isStale || !isValidTimeout(staleTime)) {\n      return;\n    }\n    const time = timeUntilStale(this.#currentResult.dataUpdatedAt, staleTime);\n    const timeout = time + 1;\n    this.#staleTimeoutId = setTimeout(() => {\n      if (!this.#currentResult.isStale) {\n        this.updateResult();\n      }\n    }, timeout);\n  }\n  #computeRefetchInterval() {\n    return (typeof this.options.refetchInterval === \"function\" ? this.options.refetchInterval(this.#currentQuery) : this.options.refetchInterval) ?? false;\n  }\n  #updateRefetchInterval(nextInterval) {\n    this.#clearRefetchInterval();\n    this.#currentRefetchInterval = nextInterval;\n    if (isServer || resolveEnabled(this.options.enabled, this.#currentQuery) === false || !isValidTimeout(this.#currentRefetchInterval) || this.#currentRefetchInterval === 0) {\n      return;\n    }\n    this.#refetchIntervalId = setInterval(() => {\n      if (this.options.refetchIntervalInBackground || focusManager.isFocused()) {\n        this.#executeFetch();\n      }\n    }, this.#currentRefetchInterval);\n  }\n  #updateTimers() {\n    this.#updateStaleTimeout();\n    this.#updateRefetchInterval(this.#computeRefetchInterval());\n  }\n  #clearStaleTimeout() {\n    if (this.#staleTimeoutId) {\n      clearTimeout(this.#staleTimeoutId);\n      this.#staleTimeoutId = void 0;\n    }\n  }\n  #clearRefetchInterval() {\n    if (this.#refetchIntervalId) {\n      clearInterval(this.#refetchIntervalId);\n      this.#refetchIntervalId = void 0;\n    }\n  }\n  createResult(query, options) {\n    const prevQuery = this.#currentQuery;\n    const prevOptions = this.options;\n    const prevResult = this.#currentResult;\n    const prevResultState = this.#currentResultState;\n    const prevResultOptions = this.#currentResultOptions;\n    const queryChange = query !== prevQuery;\n    const queryInitialState = queryChange ? query.state : this.#currentQueryInitialState;\n    const { state } = query;\n    let newState = { ...state };\n    let isPlaceholderData = false;\n    let data;\n    if (options._optimisticResults) {\n      const mounted = this.hasListeners();\n      const fetchOnMount = !mounted && shouldFetchOnMount(query, options);\n      const fetchOptionally = mounted && shouldFetchOptionally(query, prevQuery, options, prevOptions);\n      if (fetchOnMount || fetchOptionally) {\n        newState = {\n          ...newState,\n          ...fetchState(state.data, query.options)\n        };\n      }\n      if (options._optimisticResults === \"isRestoring\") {\n        newState.fetchStatus = \"idle\";\n      }\n    }\n    let { error, errorUpdatedAt, status } = newState;\n    data = newState.data;\n    let skipSelect = false;\n    if (options.placeholderData !== void 0 && data === void 0 && status === \"pending\") {\n      let placeholderData;\n      if (prevResult?.isPlaceholderData && options.placeholderData === prevResultOptions?.placeholderData) {\n        placeholderData = prevResult.data;\n        skipSelect = true;\n      } else {\n        placeholderData = typeof options.placeholderData === \"function\" ? options.placeholderData(\n          this.#lastQueryWithDefinedData?.state.data,\n          this.#lastQueryWithDefinedData\n        ) : options.placeholderData;\n      }\n      if (placeholderData !== void 0) {\n        status = \"success\";\n        data = replaceData(\n          prevResult?.data,\n          placeholderData,\n          options\n        );\n        isPlaceholderData = true;\n      }\n    }\n    if (options.select && data !== void 0 && !skipSelect) {\n      if (prevResult && data === prevResultState?.data && options.select === this.#selectFn) {\n        data = this.#selectResult;\n      } else {\n        try {\n          this.#selectFn = options.select;\n          data = options.select(data);\n          data = replaceData(prevResult?.data, data, options);\n          this.#selectResult = data;\n          this.#selectError = null;\n        } catch (selectError) {\n          this.#selectError = selectError;\n        }\n      }\n    }\n    if (this.#selectError) {\n      error = this.#selectError;\n      data = this.#selectResult;\n      errorUpdatedAt = Date.now();\n      status = \"error\";\n    }\n    const isFetching = newState.fetchStatus === \"fetching\";\n    const isPending = status === \"pending\";\n    const isError = status === \"error\";\n    const isLoading = isPending && isFetching;\n    const hasData = data !== void 0;\n    const result = {\n      status,\n      fetchStatus: newState.fetchStatus,\n      isPending,\n      isSuccess: status === \"success\",\n      isError,\n      isInitialLoading: isLoading,\n      isLoading,\n      data,\n      dataUpdatedAt: newState.dataUpdatedAt,\n      error,\n      errorUpdatedAt,\n      failureCount: newState.fetchFailureCount,\n      failureReason: newState.fetchFailureReason,\n      errorUpdateCount: newState.errorUpdateCount,\n      isFetched: newState.dataUpdateCount > 0 || newState.errorUpdateCount > 0,\n      isFetchedAfterMount: newState.dataUpdateCount > queryInitialState.dataUpdateCount || newState.errorUpdateCount > queryInitialState.errorUpdateCount,\n      isFetching,\n      isRefetching: isFetching && !isPending,\n      isLoadingError: isError && !hasData,\n      isPaused: newState.fetchStatus === \"paused\",\n      isPlaceholderData,\n      isRefetchError: isError && hasData,\n      isStale: isStale(query, options),\n      refetch: this.refetch,\n      promise: this.#currentThenable,\n      isEnabled: resolveEnabled(options.enabled, query) !== false\n    };\n    const nextResult = result;\n    if (this.options.experimental_prefetchInRender) {\n      const finalizeThenableIfPossible = (thenable) => {\n        if (nextResult.status === \"error\") {\n          thenable.reject(nextResult.error);\n        } else if (nextResult.data !== void 0) {\n          thenable.resolve(nextResult.data);\n        }\n      };\n      const recreateThenable = () => {\n        const pending = this.#currentThenable = nextResult.promise = pendingThenable();\n        finalizeThenableIfPossible(pending);\n      };\n      const prevThenable = this.#currentThenable;\n      switch (prevThenable.status) {\n        case \"pending\":\n          if (query.queryHash === prevQuery.queryHash) {\n            finalizeThenableIfPossible(prevThenable);\n          }\n          break;\n        case \"fulfilled\":\n          if (nextResult.status === \"error\" || nextResult.data !== prevThenable.value) {\n            recreateThenable();\n          }\n          break;\n        case \"rejected\":\n          if (nextResult.status !== \"error\" || nextResult.error !== prevThenable.reason) {\n            recreateThenable();\n          }\n          break;\n      }\n    }\n    return nextResult;\n  }\n  updateResult() {\n    const prevResult = this.#currentResult;\n    const nextResult = this.createResult(this.#currentQuery, this.options);\n    this.#currentResultState = this.#currentQuery.state;\n    this.#currentResultOptions = this.options;\n    if (this.#currentResultState.data !== void 0) {\n      this.#lastQueryWithDefinedData = this.#currentQuery;\n    }\n    if (shallowEqualObjects(nextResult, prevResult)) {\n      return;\n    }\n    this.#currentResult = nextResult;\n    const shouldNotifyListeners = () => {\n      if (!prevResult) {\n        return true;\n      }\n      const { notifyOnChangeProps } = this.options;\n      const notifyOnChangePropsValue = typeof notifyOnChangeProps === \"function\" ? notifyOnChangeProps() : notifyOnChangeProps;\n      if (notifyOnChangePropsValue === \"all\" || !notifyOnChangePropsValue && !this.#trackedProps.size) {\n        return true;\n      }\n      const includedProps = new Set(\n        notifyOnChangePropsValue ?? this.#trackedProps\n      );\n      if (this.options.throwOnError) {\n        includedProps.add(\"error\");\n      }\n      return Object.keys(this.#currentResult).some((key) => {\n        const typedKey = key;\n        const changed = this.#currentResult[typedKey] !== prevResult[typedKey];\n        return changed && includedProps.has(typedKey);\n      });\n    };\n    this.#notify({ listeners: shouldNotifyListeners() });\n  }\n  #updateQuery() {\n    const query = this.#client.getQueryCache().build(this.#client, this.options);\n    if (query === this.#currentQuery) {\n      return;\n    }\n    const prevQuery = this.#currentQuery;\n    this.#currentQuery = query;\n    this.#currentQueryInitialState = query.state;\n    if (this.hasListeners()) {\n      prevQuery?.removeObserver(this);\n      query.addObserver(this);\n    }\n  }\n  onQueryUpdate() {\n    this.updateResult();\n    if (this.hasListeners()) {\n      this.#updateTimers();\n    }\n  }\n  #notify(notifyOptions) {\n    notifyManager.batch(() => {\n      if (notifyOptions.listeners) {\n        this.listeners.forEach((listener) => {\n          listener(this.#currentResult);\n        });\n      }\n      this.#client.getQueryCache().notify({\n        query: this.#currentQuery,\n        type: \"observerResultsUpdated\"\n      });\n    });\n  }\n};\nfunction shouldLoadOnMount(query, options) {\n  return resolveEnabled(options.enabled, query) !== false && query.state.data === void 0 && !(query.state.status === \"error\" && options.retryOnMount === false);\n}\nfunction shouldFetchOnMount(query, options) {\n  return shouldLoadOnMount(query, options) || query.state.data !== void 0 && shouldFetchOn(query, options, options.refetchOnMount);\n}\nfunction shouldFetchOn(query, options, field) {\n  if (resolveEnabled(options.enabled, query) !== false && resolveStaleTime(options.staleTime, query) !== \"static\") {\n    const value = typeof field === \"function\" ? field(query) : field;\n    return value === \"always\" || value !== false && isStale(query, options);\n  }\n  return false;\n}\nfunction shouldFetchOptionally(query, prevQuery, options, prevOptions) {\n  return (query !== prevQuery || resolveEnabled(prevOptions.enabled, query) === false) && (!options.suspense || query.state.status !== \"error\") && isStale(query, options);\n}\nfunction isStale(query, options) {\n  return resolveEnabled(options.enabled, query) !== false && query.isStaleByTime(resolveStaleTime(options.staleTime, query));\n}\nfunction shouldAssignObserverCurrentProperties(observer, optimisticResult) {\n  if (!shallowEqualObjects(observer.getCurrentResult(), optimisticResult)) {\n    return true;\n  }\n  return false;\n}\nexport {\n  QueryObserver\n};\n//# sourceMappingURL=queryObserver.js.map", "\"use client\";\n\n// src/useQuery.ts\nimport { QueryObserver } from \"@tanstack/query-core\";\nimport { useBaseQuery } from \"./useBaseQuery.js\";\nfunction useQuery(options, queryClient) {\n  return useBaseQuery(options, QueryObserver, queryClient);\n}\nexport {\n  useQuery\n};\n//# sourceMappingURL=useQuery.js.map", "import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": ["module", "exports", "require", "vendored", "AppRouterContext", "ReactServerDOMTurbopackClient", "HooksClientContext", "ServerInsertedHtml", "HandleISRError", "workAsyncStorage", "window", "undefined", "error", "store", "getStore", "isRevalidate", "isStaticGeneration", "console", "styles", "fontFamily", "height", "textAlign", "display", "flexDirection", "alignItems", "justifyContent", "text", "fontSize", "fontWeight", "lineHeight", "margin", "DefaultGlobalError", "digest", "html", "id", "head", "body", "div", "style", "h2", "location", "hostname", "p"], "mappings": "+iBAAAA,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,QAAW,CAACC,gBAAgB,+BCFvCJ,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,YAAY,CAAEE,6BAA6B,+BCFtDL,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,QAAW,CAACG,kBAAkB,+BCFzCN,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,QAAW,CAACI,kBAAkB,wGCQzBC,iBAAAA,qCAAAA,KAVhB,IAAMC,EAGEP,EAAQ,CAAA,CAAA,IAAA,GACRO,MAHN,OAAOC,GAGe,CAMjB,EALDC,KAJc,EASJH,EAAe,CAAyB,EAAzB,GAAA,OAAEI,CAAK,CAAkB,CAAzB,EAC7B,GAAIH,EAAkB,CACpB,IAAMI,EAAQJ,EAAiBK,QAAQ,GACvC,GAAID,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAOE,YAAY,AAAZA,IAAgBF,CAAJ,KAAIA,EAAAA,KAAAA,EAAAA,EAAOG,kBAAAA,AAAkB,EAElD,CAFoD,KACpDC,QAAQL,KAAK,CAACA,GACRA,CAEV,CAEA,OAAO,IACT,+TCgCA,OAFA,AACA,GACA,qCAAA,GAD2C,uBAjDZ,CAAA,CAAA,IAAA,GAEzBM,EAAS,CACbN,EA6C8E,IA7CvE,CAELO,WACE,8FACFC,OAAQ,QACRC,UAAW,SACXC,QAAS,OACTC,cAAe,SACfC,WAAY,SACZC,eAAgB,QAClB,EACAC,KAAM,CACJC,SAAU,OACVC,WAAY,IACZC,WAAY,OACZC,OAAQ,OACV,CACF,EA8BA,EAzBA,SAyBeC,AAzBNA,AAAmB,CAAyB,EAAzB,GAAA,OAAEnB,CAAK,CAAkB,CAAzB,EACpBoB,EAA6BpB,MAAAA,EAAAA,KAAAA,EAAAA,EAAOoB,MAAM,CAChD,MACE,CADF,AACE,EAAA,EAAA,IAAA,EAACC,CADH,MACGA,CAAKC,GAAG,2BACP,CAAA,EAAA,EAAA,GAAA,EAACC,OAAAA,CAAAA,GACD,CAAA,EAAA,EAAA,IAAA,EAACC,OAAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC5B,EAAAA,cAAc,CAAA,CAACI,MAAOA,IACvB,CAAA,EAAA,EAAA,GAAA,EAACyB,MAAAA,CAAIC,MAAOpB,EAAON,KAAK,UACtB,CAAA,EAAA,EAAA,IAAA,EAACyB,CAAD,KAACA,WACC,CAAA,EAAA,EAAA,IAAA,EAACE,KAAAA,CAAGD,MAAOpB,EAAOQ,IAAI,WAAE,wBACAM,EAAS,SAAW,SAAS,8CACvBtB,OAAO8B,QAAQ,CAACC,QAAQ,CAAC,YAAU,IAC9DT,EAAS,cAAgB,kBAAkB,6BAG7CA,EAAS,CAAA,EAAA,EAAA,EAATA,CAAS,EAACU,IAAAA,CAAEJ,GAAZN,GAAmBd,EAAOQ,IAAI,UAAI,WAAUM,IAAgB,eAMzE,+QM/CA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAUI,EAAgB,cAAc,EAAA,YAAY,CAC5C,YAAY,CAAM,CAAE,CAAO,CAAE,CAC3B,KAAK,GACL,IAAI,CAAC,OAAO,CAAG,EACf,IAAI,EAAC,CAAO,AAAP,CAAU,EACf,IAAI,EAAC,CAAA,AAAY,CAAG,KACpB,IAAI,EAAC,CAAA,AAAgB,CAAG,CAAA,EAAA,EAAA,eAAA,AAAe,IACvC,IAAI,CAAC,WAAW,GAChB,IAAI,CAAC,UAAU,CAAC,EAClB,EACA,CAAA,AAAO,AAAC,EACR,CAAA,AAAa,CAAG,KAAK,CAAE,EACvB,CAAA,AAAyB,CAAG,KAAK,CAAE,EACnC,CAAA,AAAc,CAAG,KAAK,CAAE,EACxB,CAAA,AAAmB,AAAC,EACpB,CAAA,AAAqB,AAAC,EACtB,CAAA,AAAgB,AAAC,EACjB,CAAA,AAAY,AAAC,EACb,CAAA,AAAS,AAAC,EACV,CAAa,AAAb,AAAc,EAGd,CAA0B,AAC1B,AADyB,AAAzB,CACA,EAAe,AAAC,EAChB,CAAA,AAAkB,AAAC,EACnB,CAAA,AAAuB,AAAC,EACxB,CAAA,AAAa,CAAmB,EAAhB,EAAoB,GAAM,CAC1C,OAD6B,MACf,CACZ,IAAI,CAAC,OAAO,CAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CACvC,CACA,aAAc,CACgB,GAAG,CAA3B,IAAI,CAAC,SAAS,CAAC,IAAI,GACrB,IAAI,EAAC,CAAA,AAAa,CAAC,WAAW,CAAC,IAAI,EAC/B,EAAmB,IAAI,EAAC,CAAa,AAAb,CAAe,IAAI,CAAC,OAAO,EACrD,CADwD,GACpD,EAAC,CAAA,AAAa,GAElB,IAAI,CAAC,YAAY,GAEnB,IAAI,EAAC,CAAA,AAAa,GAEtB,CACA,eAAgB,CACV,AAAC,IAAI,CAAC,YAAY,IAAI,AACxB,IAAI,CAAC,OAAO,EAEhB,CACA,wBAAyB,CACvB,OAAO,EACL,IAAI,EAAC,CAAA,AAAa,CAClB,IAAI,CAAC,OAAO,CACZ,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAEnC,CACA,0BAA2B,CACzB,OAAO,EACL,IAAI,EAAC,CAAa,AAAb,CACL,IAAI,CAAC,OAAO,CACZ,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAErC,CACA,SAAU,CACR,IAAI,CAAC,SAAS,CAAmB,EAAhB,EAAoB,IACrC,IAAI,EAAC,CADyB,AACzB,AAAkB,GACvB,IAAI,EAAC,CAAA,AAAqB,GAC1B,IAAI,EAAC,CAAA,AAAa,CAAC,cAAc,CAAC,IAAI,CACxC,CACA,WAAW,CAAO,CAAE,CAClB,IAAM,EAAc,IAAI,CAAC,OAAO,CAC1B,EAAY,IAAI,EAAC,CAAA,AAAa,CAEpC,GADA,IAAI,CAAC,OAAO,CAAG,IAAI,EAAC,CAAA,AAAO,CAAC,mBAAmB,CAAC,GAC5C,AAAyB,KAAK,QAA1B,CAAC,OAAO,CAAC,OAAO,EAA+C,WAAhC,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,EAAkD,YAAhC,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,EAAuF,WAApE,AAA+E,MAAxE,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAE,IAAI,EAAC,CAAA,AAAa,EAC9L,MAAM,AAAI,MACR,yEAGJ,IAAI,EAAC,CAAA,AAAY,GACjB,IAAI,EAAC,CAAA,AAAa,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EACtC,EAAY,UAAU,EAAI,CAAC,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,IAAI,CAAC,OAAO,CAAE,IAC/D,IAAI,EAAC,CAAA,AAAO,CAAC,EADgE,WACnD,GAAG,MAAM,CAAC,CAClC,KAAM,yBACN,MAAO,IAAI,EAAC,CAAA,AAAa,CACzB,SAAU,IAAI,AAChB,GAEF,IAAM,EAAU,IAAI,CAAC,YAAY,GAC7B,GAAW,EACb,IAAI,EAAC,CAAa,AAAb,CACL,EACA,IAAI,CAAC,OAAO,CACZ,IAEA,IAAI,CAAC,CAAA,CAAa,GADjB,AAGH,IAAI,CAAC,YAAY,GACb,IAAY,IAAI,EAAC,CAAN,AAAM,AAAa,GAAK,GAAa,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAE,IAAI,EAAC,CAAA,AAAa,IAAM,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAY,OAAO,CAAE,IAAI,EAAC,CAAA,AAAa,GAAK,CAAA,EAAA,EAAA,gBAAA,AAAgB,EAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAE,IAAI,EAAC,CAAA,AAAa,IAAM,CAAA,EAAA,EAAA,gBAAA,AAAgB,EAAC,EAAY,SAAS,CAAE,IAAI,CAAC,CAAA,EAAa,CAAC,EACtS,CADyS,GACrS,EAAC,CAAA,AAAmB,GAE1B,IAAM,EAAsB,IAAI,EAAC,CAAA,AAAuB,GACpD,IAAY,IAAI,EAAC,CAAN,AAAM,AAAa,GAAK,GAAa,CAAA,EAAA,EAAA,cAAc,AAAd,EAAe,IAAI,CAAC,OAAO,CAAC,OAAO,CAAE,IAAI,EAAC,CAAA,AAAa,IAAM,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAY,OAAO,CAAE,IAAI,EAAC,CAAa,AAAb,GAAkB,IAAwB,IAAI,EAAC,CAAA,AAAuB,GAAG,AACjO,IAAI,EAAC,CAAA,AAAsB,CAAC,EAEhC,CACA,oBAAoB,CAAO,CAAE,OAkV0B,EAjVrD,IAAM,EAAQ,IAAI,EAAC,CAAA,AAAO,CAiV2C,AAjV1C,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,CAAA,CAAO,CAAE,GACzD,EAAS,IAAI,CAAC,YAAY,CAAC,EAAO,GAMxC,OA0U2C,EA/UD,IAAI,EA+UK,CA/UH,EAgV7C,CAAA,EAAA,EAAA,EAhVsD,iBAgVnC,AAAnB,EAAoB,EAAS,gBAAgB,GAAI,KA/UlD,IAAI,EAAC,CAAA,AAAc,CAAG,EACtB,IAAI,AA8UiE,EA9UhE,CAAA,AAAqB,CAAG,IAAI,CAAC,OAAO,CACzC,IAAI,EAAC,CAAmB,AAAnB,CAAsB,IAAI,EAAC,CAAA,AAAa,CAAC,KAAK,EAE9C,CACT,CACA,kBAAmB,CACjB,OAAO,IAAI,EAAC,CACd,AAD4B,AAAd,CAEd,YAAY,CAAM,CAAE,CAAa,CAAE,CACjC,OAAO,IAAI,MAAM,EAAQ,CACvB,IAAK,CAAC,EAAQ,KACZ,IAAI,CAAC,SAAS,CAAC,GACf,IAAgB,GACJ,YAAR,CAAqB,EAAC,IAAI,CAAC,OAAO,CAAC,6BAA6B,EAAqC,WAAW,CAA5C,IAAI,EAAC,CAAgB,AAAhB,CAAiB,MAAM,EAClG,IAAI,EAAC,CAAA,AAAgB,CAAC,MAAM,CAC1B,AAAI,MACF,8DAIC,QAAQ,GAAG,CAAC,EAAQ,GAE/B,EACF,CACA,UAAU,CAAG,CAAE,CACb,IAAI,EAAC,CAAA,AAAa,CAAC,GAAG,CAAC,EACzB,CACA,iBAAkB,CAChB,OAAO,IAAI,EAAC,CAAA,AAAa,AAC3B,CACA,QAAQ,CAAE,GAAG,EAAS,CAAG,CAAC,CAAC,CAAE,CAC3B,OAAO,IAAI,CAAC,KAAK,CAAC,CAChB,GAAG,CAAO,AACZ,EACF,CACA,gBAAgB,CAAO,CAAE,CACvB,IAAM,EAAmB,IAAI,EAAC,CAAA,AAAO,CAAC,mBAAmB,CAAC,GACpD,EAAQ,IAAI,EAAC,CAAA,AAAO,CAAC,aAAa,GAAG,KAAK,CAAC,IAAI,EAAC,CAAA,AAAO,CAAE,GAC/D,OAAO,EAAM,KAAK,GAAG,IAAI,CAAC,IAAM,IAAI,CAAC,YAAY,CAAC,EAAO,GAC3D,CACA,MAAM,CAAY,CAAE,CAClB,OAAO,IAAI,EAAC,CAAA,AAAa,CAAC,CACxB,GAAG,CAAY,CACf,cAAe,EAAa,aAAa,GAAI,CAC/C,GAAG,IAAI,CAAC,KACN,IAAI,CAAC,YAAY,GACV,IAAI,CAAC,CAAA,CAAc,EAE9B,EACA,CAAA,AAAa,CAAC,CAAY,EACxB,IAAI,EAAC,CAAA,AAAY,GACjB,IAAI,EAAU,IAAI,EAAC,CAAa,AAAb,CAAc,KAAK,CACpC,IAAI,CAAC,OAAO,CACZ,GAKF,OAHI,AAAC,GAAc,cAAc,CAC/B,EAAU,EAAQ,KAAK,CAAC,EAAA,KAAI,EAEvB,CACT,EACA,CAAA,AAAmB,GACjB,IAAI,EAAC,CAAA,AAAkB,GACvB,IAAM,EAAY,CAAA,EAAA,EAAA,gBAAA,AAAgB,EAChC,IAAI,CAAC,OAAO,CAAC,SAAS,CACtB,IAAI,CAAC,CAAA,CAAa,EAEpB,GAAI,EAAA,QAAQ,EAAI,IAAI,EAAC,CAAA,AAAc,CAAC,OAAO,EAAI,CAAC,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,GAC7D,OAEF,EAH2E,EAGrE,EAAO,CAAA,EAAA,EAAA,cAAc,AAAd,EAAe,IAAI,EAAC,CAAA,AAAc,CAAC,aAAa,CAAE,GAE/D,IAAI,EAAC,CAAA,AAAe,CAAG,WAAW,KAC5B,AAAC,IAAI,CAAC,CAAA,CAAc,CAAC,OAAO,EAAE,AAChC,IAAI,CAAC,YAAY,EAErB,EALgB,CAKb,CALoB,EAMzB,EACA,CAAA,AAAuB,GACrB,MAAO,CAAyC,YAAxC,OAAO,IAAI,CAAC,OAAO,CAAC,eAAe,CAAkB,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,EAAC,CAAA,AAAa,EAAI,IAAI,CAAC,OAAO,CAAC,eAAA,AAAe,IAAK,CACnJ,EACA,CAAA,AAAsB,CAAC,CAAY,EACjC,IAAI,EAAC,CAAA,AAAqB,GAC1B,IAAI,EAAC,CAAA,AAAuB,CAAG,GAC3B,EAAA,QAAQ,EAAI,CAA6D,IAA7D,CAAA,EAAA,EAAsE,AAAtE,cAAA,AAAc,EAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAE,IAAI,EAAC,CAAA,AAAa,GAAgB,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,IAAI,EAAC,CAAuB,AAAvB,GAA6D,GAAG,CAApC,IAAI,EAAC,CAAuB,AAAvB,GAG5I,IAAI,EAAC,CAAA,AAAkB,CAAG,YAAY,MAChC,IAAI,CAAC,OAAO,CAAC,2BAA2B,EAAI,EAAA,YAAY,CAAC,SAAS,EAAA,GAAI,AACxE,IAAI,EAAC,CAAA,AAAa,EAEtB,EAAG,IAAI,EAAC,CAAA,CAAuB,CACjC,EACA,CAAA,AAAa,GACX,IAAI,EAAC,CAAA,AAAmB,GACxB,IAAI,EAAC,CAAsB,AAAtB,CAAuB,IAAI,CAAC,CAAA,CAAuB,GAC1D,EACA,CAAA,AAAkB,GACZ,IAAI,EAAC,CAAA,AAAe,EAAE,CACxB,aAAa,IAAI,EAAC,CAAA,AAAe,EACjC,IAAI,EAAC,CAAA,AAAe,CAAG,KAAK,EAEhC,EACA,CAAA,AAAqB,GACf,IAAI,EAAC,CAAA,AAAkB,EAAE,CAC3B,cAAc,IAAI,EAAC,CAAA,AAAkB,EACrC,IAAI,EAAC,CAAA,AAAkB,CAAG,KAAK,EAEnC,CACA,aAAa,CAAK,CAAE,CAAO,CAAE,CAC3B,IAUI,EAVE,EAAY,IAAI,EAAC,CAAA,AAAa,CAC9B,EAAc,IAAI,CAAC,OAAO,CAC1B,EAAa,IAAI,EAAC,CAAA,AAAc,CAChC,EAAkB,IAAI,EAAC,CAAA,AAAmB,CAC1C,EAAoB,IAAI,CAAC,CAAA,CAAqB,CAE9C,EAAoB,AADN,IAAU,EACU,EAAM,KAAK,CAAG,IAAI,EAAC,CAAA,AAAyB,CAC9E,OAAE,CAAK,CAAE,CAAG,EACd,EAAW,CAAE,GAAG,CAAK,AAAC,EACtB,GAAoB,EAExB,GAAI,EAAQ,kBAAkB,CAAE,CAC9B,IAAM,EAAU,IAAI,CAAC,YAAY,GAC3B,EAAe,CAAC,GAAW,EAAmB,EAAO,GACrD,EAAkB,GAAW,EAAsB,EAAO,EAAW,EAAS,IAChF,GAAgB,CAAA,GAAiB,CACnC,EAAW,CACT,GAAG,CAAQ,CACX,GAAG,CAAA,EAAA,EAAA,UAAA,AAAU,EAAC,EAAM,IAAI,CAAE,EAAM,OAAO,CAAC,CAC1C,EAEiC,eAAe,CAA9C,EAAQ,kBAAkB,GAC5B,EAAS,WAAW,CAAG,MAAA,CAE3B,CACA,GAAI,OAAE,CAAK,gBAAE,CAAc,QAAE,CAAM,CAAE,CAAG,EACxC,EAAO,EAAS,IAAI,CACpB,IAAI,GAAa,EACjB,GAAgC,KAAK,IAAjC,EAAQ,eAAe,EAAwB,KAAK,IAAd,GAA8B,YAAX,EAAsB,CACjF,IAAI,EACA,GAAY,mBAAqB,EAAQ,eAAe,GAAK,GAAmB,iBAAiB,AACnG,EAAkB,EAAW,IAAI,CACjC,GAAa,GAEb,EAAqD,YAAnC,OAAO,EAAQ,eAAe,CAAkB,EAAQ,eAAe,CACvF,IAAI,EAAC,CAAA,AAAyB,EAAE,MAAM,KACtC,IAAI,EAAC,CAAA,AAAyB,EAC5B,EAAQ,eAAe,CAEL,KAAK,GAAG,CAA5B,IACF,EAAS,UACT,EAAO,CAAA,EAAA,EAAA,WAAA,AAAW,EAChB,GAAY,KACZ,EACA,GAEF,GAAoB,EAExB,CACA,GAAI,EAAQ,MAAM,EAAI,AAAS,KAAK,OAAK,CAAC,EACxC,GAAI,GAAc,IAAS,AADyB,GACR,MAAQ,EAAQ,MAAM,GAAK,IAAI,EAAC,CAAA,AAAS,CACnF,CADqF,CAC9E,IAAI,EAAC,CAAA,AAAa,MAEzB,GAAI,CACF,IAAI,EAAC,CAAA,AAAS,CAAG,EAAQ,MAAM,CAC/B,EAAO,EAAQ,MAAM,CAAC,GACtB,EAAO,CAAA,EAAA,EAAA,WAAA,AAAW,EAAC,GAAY,KAAM,EAAM,GAC3C,IAAI,EAAC,CAAA,AAAa,CAAG,EACrB,IAAI,CAAC,CAAA,CAAY,CAAG,IACtB,CAAE,MAAO,EAAa,CACpB,IAAI,EAAC,CAAY,AAAZ,CAAe,CACtB,CAGA,IAAI,EAAC,CAAA,AAAY,EAAE,CACrB,EAAQ,IAAI,CAAC,CAAA,CAAY,CACzB,EAAO,IAAI,EAAC,CAAa,AAAb,CACZ,EAAiB,KAAK,GAAG,GACzB,EAAS,SAEX,IAAM,EAAa,AAAyB,eAAhB,WAAW,CACjC,EAAuB,YAAX,EACZ,EAAqB,UAAX,EACV,EAAY,GAAa,EACzB,EAAU,AAAS,KAAK,MACxB,EAAS,QACb,EACA,YAAa,EAAS,WAAW,WACjC,EACA,UAAW,AAAW,sBACtB,EACA,iBAAkB,YAClB,OACA,EACA,cAAe,EAAS,aAAa,OACrC,iBACA,EACA,aAAc,EAAS,iBAAiB,CACxC,cAAe,EAAS,kBAAkB,CAC1C,iBAAkB,EAAS,gBAAgB,CAC3C,UAAW,EAAS,eAAe,CAAG,GAAK,EAAS,gBAAgB,CAAG,EACvE,oBAAqB,EAAS,eAAe,CAAG,EAAkB,eAAe,EAAI,EAAS,gBAAgB,CAAG,EAAkB,gBAAgB,CACnJ,aACA,aAAc,GAAc,CAAC,EAC7B,eAAgB,GAAW,CAAC,EAC5B,SAAmC,WAAzB,EAAS,WAAW,mBAC9B,EACA,eAAgB,GAAW,EAC3B,QAAS,EAAQ,EAAO,GACxB,QAAS,IAAI,CAAC,OAAO,CACrB,QAAS,IAAI,EAAC,CAAA,AAAgB,CAC9B,UAAsD,KAA3C,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAQ,OAAO,CAAE,EAC7C,EAEA,GAAI,IAAI,CAAC,OAAO,CAAC,6BAA6B,CAAE,CAC9C,IAAM,EAA6B,AAAC,IACR,SAAS,CAA/B,EAAW,MAAM,CACnB,EAAS,MAAM,CAAC,EAAW,KAAK,EACH,KAAK,GAAG,CAA5B,EAAW,IAAI,EACxB,EAAS,OAAO,CAAC,EAAW,IAAI,CAEpC,EACM,EAAmB,KAEvB,EADgB,IAAI,EAAC,CAAA,AAAgB,CAAG,EAAW,OAAO,CAAG,CAAA,EAAA,EAAA,EAClC,aADkC,AAAe,IAE9E,EACM,EAAe,IAAI,CAAC,CAAA,CAAgB,CAC1C,OAAQ,EAAa,MAAM,EACzB,IAAK,UACC,EAAM,SAAS,GAAK,EAAU,SAAS,EAAE,AAC3C,EAA2B,GAE7B,KACF,KAAK,aACuB,UAAtB,EAAW,MAAM,EAAgB,EAAW,IAAI,GAAK,EAAa,KAAA,AAAK,EAAE,CAC3E,IAEF,KACF,KAAK,YACuB,AAAtB,YAAW,MAAM,EAAgB,EAAW,KAAK,GAAK,EAAa,MAAA,AAAM,EAAE,CAC7E,GAGN,CACF,CACA,OAhCmB,AAgCZ,CACT,CACA,cAAe,CACb,IAAM,EAAa,IAAI,CAAC,CAAA,CAAc,CAChC,EAAa,IAAI,CAAC,YAAY,CAAC,IAAI,EAAC,CAAA,AAAa,CAAE,IAAI,CAAC,OAAO,EAMrE,GALA,IAAI,EAAC,CAAA,AAAmB,CAAG,IAAI,EAAC,CAAa,AAAb,CAAc,KAAK,CACnD,IAAI,EAAC,CAAA,AAAqB,CAAG,IAAI,CAAC,OAAO,CACH,KAAK,GAAG,CAA1C,IAAI,EAAC,CAAA,AAAmB,CAAC,IAAI,GAC/B,IAAI,EAAC,CAAA,AAAyB,CAAG,IAAI,EAAC,CAAa,AAAb,EAEpC,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,EAAY,GAClC,OAEF,GAHiD,CAG7C,CAAC,CAAA,CAAc,CAAG,EACtB,IAAM,EAAwB,KAC5B,GAAI,CAAC,EACH,OAAO,EAET,CAHiB,EAGX,qBAAE,CAAmB,CAAE,CAAG,IAAI,CAAC,OAAO,CACtC,EAA0D,YAA/B,OAAO,EAAqC,IAAwB,EACrG,GAAiC,QAA7B,GAAsC,CAAC,GAA4B,CAAC,IAAI,EAAC,CAAA,AAAa,CAAC,IAAI,CAC7F,CAD+F,MACxF,EAET,IAAM,EAAgB,IAAI,IACxB,GAA4B,IAAI,EAAC,CAAa,AAAb,EAKnC,OAHI,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,AAC7B,EAAc,GAAG,CAAC,SAEb,OAAO,IAAI,CAAC,IAAI,EAAC,CAAA,AAAc,EAAE,IAAI,CAAC,AAAC,GAE5B,AACT,IADa,EAAC,CAAA,AAAc,CAAC,EAAS,GAAK,CAAU,CAAC,EAAS,EACpD,EAAc,GAAG,CAFlB,AAEmB,GAExC,EACA,IAAI,EAAC,CAAA,AAAO,CAAC,CAAE,UAAW,GAAwB,EACpD,CACA,CAAA,CAAY,GACV,IAAM,EAAQ,IAAI,EAAC,CAAA,AAAO,CAAC,aAAa,GAAG,KAAK,CAAC,IAAI,EAAC,CAAA,AAAO,CAAE,IAAI,CAAC,OAAO,EAC3E,GAAI,IAAU,IAAI,CAAC,CAAA,CAAa,CAC9B,CADgC,MAGlC,IAAM,EAAY,IAAI,EAAC,CAAA,AAAa,CACpC,IAAI,EAAC,CAAA,AAAa,CAAG,EACrB,IAAI,EAAC,CAAA,AAAyB,CAAG,EAAM,KAAK,CACxC,IAAI,CAAC,YAAY,IAAI,CACvB,GAAW,eAAe,IAAI,EAC9B,EAAM,WAAW,CAAC,IAAI,EAE1B,CACA,eAAgB,CACd,IAAI,CAAC,YAAY,GACb,IAAI,CAAC,YAAY,IAAI,AACvB,IAAI,EAAC,CAAA,AAAa,EAEtB,EACA,CAAA,AAAO,CAAC,CAAa,EACnB,EAAA,aAAa,CAAC,KAAK,CAAC,KACd,EAAc,SAAS,EAAE,AAC3B,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,AAAC,IACtB,EAAS,IAAI,EAAC,CAAc,AAAd,CAChB,GAEF,IAAI,EAAC,CAAA,AAAO,CAAC,aAAa,GAAG,MAAM,CAAC,CAClC,MAAO,IAAI,EAAC,CAAA,AAAa,CACzB,KAAM,wBACR,EACF,EACF,CACF,EAIA,SAAS,EAAmB,CAAK,CAAE,CAAO,EACxC,OAHkD,AAG3C,IAHA,CAAA,EAAA,EAAA,cAAA,AAAc,EAGW,AAHV,EAAQ,OAAO,CAGZ,CAHc,GAAyC,KAAK,IAA1B,CAA+B,CAAC,AAA1B,KAAK,CAAC,IAAI,GAAwC,UAAvB,EAAM,KAAK,CAAC,MAAM,EAAgB,CAAyB,MAAjB,YAAY,AAAK,CAAK,EAG3F,KAAK,IAA1B,EAAM,KAAK,CAAC,IAAI,EAAe,EAAc,EAAO,EAAS,EAAQ,cAAc,CACjI,CACA,SAAS,EAAc,CAAK,CAAE,CAAO,CAAE,CAAK,EAC1C,GAA+C,KAA3C,CAAA,EAAA,EAAA,cAAc,AAAd,EAAe,EAAQ,OAAO,CAAE,IAAmE,WAA/C,CAAA,EAAA,EAAA,gBAAA,AAAgB,EAAC,EAAQ,SAAS,CAAE,GAAqB,CAC/G,IAAM,EAAyB,YAAjB,OAAO,EAAuB,EAAM,GAAS,EAC3D,MAAiB,WAAV,IAAgC,IAAV,GAAmB,EAAQ,EAAO,EACjE,CACA,MAAO,EACT,CACA,SAAS,EAAsB,CAAK,CAAE,CAAS,CAAE,CAAO,CAAE,CAAW,EACnE,MAAO,CAAC,IAAU,GAA4D,KAA/C,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAY,OAAO,CAAE,EAAW,CAAK,GAAM,CAAC,CAAF,CAAU,QAAQ,EAA2B,UAAvB,EAAM,KAAK,CAAC,MAAM,AAAK,CAAO,EAAK,EAAQ,EAAO,EAClK,CACA,SAAS,EAAQ,CAAK,CAAE,CAAO,EAC7B,OAAkD,IAA3C,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAQ,OAAO,CAAE,IAAoB,EAAM,aAAa,CAAC,CAAA,EAAA,EAAA,gBAAA,AAAgB,EAAC,EAAQ,SAAS,CAAE,GACrH,CDpcA,IAAA,EAAA,EAAA,CAAA,CAAA,OAEA,EAAA,EAAA,CAAA,CAAA,OJDA,EAAA,CAAA,CAAA,OAeA,IAAI,EAAiC,EAAA,aAAmB,CAdxD,AAcyD,SAdhD,EACP,IAAI,GAAU,EACd,MAAO,CACL,WAAY,KACV,GAAU,CACZ,EACA,MAAO,KACL,EAAU,EACZ,EACA,QAAS,IACA,CAEX,CACF,KEdI,EAAqB,EAAA,aAAmB,EAAC,EAEnB,GAAmB,QAAQ,CCQrD,IAAI,EAAkB,CAAC,EAAkB,EAAU,IAAuB,EAAS,eAAe,CAAC,GAAkB,KAAK,CAAC,KACzH,EAAmB,UAAU,EAC/B,GGXA,SAAS,EAAS,CAAO,CAAE,CAAW,EACpC,OFaF,AEbS,SFaA,AAAa,CAAO,CAAE,CAAQ,CAAE,CAAW,EAQlD,IAAM,EFtBmB,EAAA,UEsBL,AFtBqB,CAAC,GEuBpC,EJR+B,EAAA,UAAgB,CAAC,GIShD,EAAS,CADY,AACZ,EAAA,EAAA,cAAA,AAAc,EAAC,GACxB,EAAmB,EAAO,mBAAmB,CAAC,GD3BpD,GC4BA,EAAO,iBAAiB,GAAG,OAAO,EAAE,4BAClC,GASF,EAAiB,kBAAkB,CAAG,EAAc,cAAgB,aDtChE,EAAiB,QAAQ,CAAE,CAC7B,IAAM,EAAS,AAAD,GAAqB,WAAV,EAAqB,EAAQ,KAAK,GAAG,CAAC,GAAS,IAAK,KACvE,EAAoB,EAAiB,SAAS,AACpD,CCoCmB,EDpCF,SAAS,CAAgC,YAA7B,OAAO,EAAmC,CAAC,GAAG,IAAS,EAAM,KAAqB,IAAS,EAAM,GACvF,UAAnC,AAA6C,OAAtC,EAAiB,MAAM,GAChC,EAAiB,MAAM,CAAG,KAAK,GAAG,CAAC,EAAiB,MAAM,CAAE,IAAA,CAEhE,EFJI,EAAQ,QAAQ,EAAI,EAAQ,YAAY,EAAI,EAAQ,6BAA6B,AAA7B,EAA+B,CACjF,CGoC4C,AHpC3C,EAAmB,OAAO,IAAI,CGoCL,AHnC5B,EAAQ,YAAY,EAAG,CAAA,EAK3B,EAAA,SAAe,CAAC,KACd,EAAmB,UAAU,EAC/B,EAAG,CG6BwB,EH7BJ,EG8BvB,IAAM,EAAkB,CAAC,EAAO,aAAa,GAAG,GAAG,CAAC,EAAiB,SAAS,EACxE,CAAC,EAAS,CAAG,EAAA,QAAc,CAC/B,IAAM,IAAI,EACR,EACA,IAGE,EAAS,EAAS,mBAAmB,CAAC,GACtC,EAAkB,CAAC,GAAe,CAAuB,MAAf,UAAU,CAgB1D,GAfA,CAeI,CAfJ,oBAA0B,CACxB,EAAA,WAAiB,CACf,AAAC,IACC,IAAM,EAAc,EAAkB,EAAS,SAAS,CAAC,EAAA,aAAa,CAAC,UAAU,CAAC,IAAkB,EAAA,IAAI,CAExG,OADA,EAAS,YAAY,GACd,CACT,EACA,CAAC,EAAU,EAAgB,EAE7B,IAAM,EAAS,gBAAgB,GAC/B,IAAM,EAAS,gBAAgB,IAEjC,EAAA,SAAe,CAAC,KACd,EAAS,UAAU,CAAC,EACtB,EAAG,CAAC,EAAkB,EAAS,EACb,ADxD8B,GAAkB,UCwD9B,ADxD0C,EAAO,OCwDxC,EDxDiD,CCyD5F,MAAM,EAAgB,EAAkB,EAAU,GAEpD,GAAI,CHvDY,CAAC,QACjB,CAAM,oBACN,CAAkB,cAClB,CAAY,OACZ,CAAK,UACL,CAAQ,CACT,GACQ,EAAO,OAAO,EAAI,CAAC,EAAmB,OAAO,IAAM,CAAC,EAAO,UAAU,EAAI,IAAU,GAA4B,EAA7B,GAAkC,IAArB,EAAO,IAAI,EAAe,CAAA,EAAA,EAAA,gBAAA,AAAgB,EAAC,EAAc,CAAC,EAAO,KAAK,CAAE,GAAM,CAAC,AACvL,EG+CkB,QACd,qBACA,EACA,aAAc,EAAiB,YAAY,CAC3C,MAAO,EAAO,aAAa,GAAG,GAAG,CAAC,EAAiB,SAAS,EAC5D,SAAU,EAAiB,QAAQ,AACrC,GACE,CADE,KACI,EAAO,KAAK,CAOpB,GAJA,EAAO,iBAAiB,GAAG,OAAO,EAAE,2BAClC,EACA,GAEE,EAAiB,6BAA6B,EAAI,CAAC,EAAA,QAAQ,EAAc,AD1EtC,EC0E4B,AD1ErB,SAAS,EAAI,EAAO,UAAU,EAAI,CC0EK,AD1EJ,EC0EkB,CACjG,IAAM,EAAU,EAEd,EAAgB,EAAkB,EAAU,GAG5C,EAAO,KAJP,QAIoB,GAAG,CADvB,EAC0B,CAAC,EAAiB,SAAS,GAAG,QAE1D,GAAS,MAAM,EAAA,IAAI,EAAE,QAAQ,KAC3B,EAAS,YAAY,EACvB,EACF,CACA,OAAO,AAAC,EAAiB,YAVsF,GAGT,IAO1D,CAAkC,EAA/B,EAAS,WAAW,CAAC,EACtE,EE7FsB,EAAS,EAAe,EAC9C,wHCLA,EAAA,EAAA,CAAA,CAAA,OAEA,SAAS,EAAK,WAAE,CAAS,CAAE,GAAG,EAAoC,EAChE,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CACC,YAAU,OACV,UAAW,CAAA,EAAA,EAAA,EAAE,AAAF,EACT,oFACA,GAED,GAAG,CAAK,EAGf,CAEA,SAAS,EAAW,WAAE,CAAS,CAAE,GAAG,EAAoC,EACtE,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CACC,YAAU,cACV,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EACX,6JACA,GAED,GAAG,CAAK,EAGf,CAEA,SAAS,EAAU,WAAE,CAAS,CAAE,GAAG,EAAoC,EACrE,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CACC,YAAU,aACV,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EAAC,6BAA8B,GAC3C,GAAG,CAAK,EAGf,CAyBA,SAAS,EAAY,WAAE,CAAS,CAAE,GAAG,EAAoC,EACvE,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CACC,YAAU,eACV,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EAAC,OAAQ,GACrB,GAAG,CAAK,EAGf", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]}