(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,33525,(e,t,s)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),Object.defineProperty(s,"warnOnce",{enumerable:!0,get:function(){return a}});let a=e=>{}},68553,e=>{"use strict";e.s(["Camera",()=>t],68553);let t=(0,e.i(75254).default)("camera",[["path",{d:"M13.997 4a2 2 0 0 1 1.76 1.05l.486.9A2 2 0 0 0 18.003 7H20a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V9a2 2 0 0 1 2-2h1.997a2 2 0 0 0 1.759-1.048l.489-.904A2 2 0 0 1 10.004 4z",key:"18u6gg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},29592,e=>{"use strict";e.s(["Alert",()=>i,"AlertDescription",()=>l]);var t=e.i(43476),s=e.i(25913),a=e.i(75157);let r=(0,s.cva)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function i(e){let{className:s,variant:i,...l}=e;return(0,t.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,a.cn)(r({variant:i}),s),...l})}function l(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"alert-description",className:(0,a.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",s),...r})}},69638,73884,e=>{"use strict";e.s(["CheckCircle",()=>s],69638);var t=e.i(75254);let s=(0,t.default)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);e.s(["XCircle",()=>a],73884);let a=(0,t.default)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},63209,e=>{"use strict";e.s(["AlertCircle",()=>t],63209);let t=(0,e.i(75254).default)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},46897,e=>{"use strict";e.s(["MapPin",()=>t],46897);let t=(0,e.i(75254).default)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},19455,e=>{"use strict";e.s(["Button",()=>l]);var t=e.i(43476),s=e.i(91918),a=e.i(25913),r=e.i(75157);let i=(0,a.cva)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:a,variant:l,size:n,asChild:c=!1,...d}=e,o=c?s.Slot:"button";return(0,t.jsx)(o,{"data-slot":"button",className:(0,r.cn)(i({variant:l,size:n,className:a})),...d})}},84771,e=>{"use strict";e.s(["CameraFeed",()=>f],84771);var t=e.i(43476),s=e.i(71645),a=e.i(15288),r=e.i(19455),i=e.i(87486),l=e.i(29592),n=e.i(68553),c=e.i(75254);let d=(0,c.default)("play",[["path",{d:"M5 5a2 2 0 0 1 3.008-1.728l11.997 6.998a2 2 0 0 1 .003 3.458l-12 7A2 2 0 0 1 5 19z",key:"10ikf1"}]]),o=(0,c.default)("pause",[["rect",{x:"14",y:"3",width:"5",height:"18",rx:"1",key:"kaeet6"}],["rect",{x:"5",y:"3",width:"5",height:"18",rx:"1",key:"1wsw3u"}]]),h=(0,c.default)("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]),u=(0,c.default)("maximize",[["path",{d:"M8 3H5a2 2 0 0 0-2 2v3",key:"1dcmit"}],["path",{d:"M21 8V5a2 2 0 0 0-2-2h-3",key:"1e4gt3"}],["path",{d:"M3 16v3a2 2 0 0 0 2 2h3",key:"wsl5sc"}],["path",{d:"M16 21h3a2 2 0 0 0 2-2v-3",key:"18trek"}]]);var m=e.i(63209),x=e.i(53475),p=e.i(16973),v=e.i(75157);function f(e){let{className:c,autoPlay:f=!0}=e,g=(0,s.useRef)(null),[y,b]=(0,s.useState)(!1),[_,j]=(0,s.useState)(!0),[w,k]=(0,s.useState)(null),N=p.apiService.getCameraStreamUrl();return(0,s.useEffect)(()=>{let e=g.current;if(!e)return;let t=()=>{j(!0),k(null)},s=()=>{j(!1),f&&e.play().catch(e=>{console.error("Auto-play failed:",e),k("Auto-play failed. Click play to start the stream.")})},a=()=>{b(!0),k(null)},r=()=>{b(!1)},i=()=>{j(!1),b(!1),k("Failed to load camera stream. Please check your connection.")};return e.addEventListener("loadstart",t),e.addEventListener("canplay",s),e.addEventListener("play",a),e.addEventListener("pause",r),e.addEventListener("error",i),()=>{e.removeEventListener("loadstart",t),e.removeEventListener("canplay",s),e.removeEventListener("play",a),e.removeEventListener("pause",r),e.removeEventListener("error",i)}},[f]),(0,t.jsxs)(a.Card,{className:(0,v.cn)("overflow-hidden",c),children:[(0,t.jsx)(a.CardHeader,{children:(0,t.jsxs)(a.CardTitle,{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(n.Camera,{className:"h-5 w-5"}),"Camera Feed",_?(0,t.jsx)(i.Badge,{variant:"secondary",children:"Connecting..."}):w?(0,t.jsx)(i.Badge,{className:"bg-red-500/10 text-red-500 border-red-500/20",children:"Offline"}):y?(0,t.jsx)(i.Badge,{className:"bg-green-500/10 text-green-500 border-green-500/20",children:"Live"}):(0,t.jsx)(i.Badge,{variant:"secondary",children:"Paused"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(r.Button,{variant:"outline",size:"sm",onClick:()=>{let e=g.current;e&&(k(null),j(!0),e.load())},disabled:_,children:(0,t.jsx)(h,{className:"h-4 w-4"})}),(0,t.jsx)(r.Button,{variant:"outline",size:"sm",onClick:()=>{let e=g.current;e&&(document.fullscreenElement?document.exitFullscreen():e.requestFullscreen().catch(e=>{console.error("Fullscreen failed:",e)}))},children:(0,t.jsx)(u,{className:"h-4 w-4"})})]})]})}),(0,t.jsx)(a.CardContent,{className:"p-0",children:(0,t.jsx)("div",{className:"relative bg-black aspect-video",children:w?(0,t.jsxs)(l.Alert,{className:"m-4 border-red-500/20 bg-red-500/10",children:[(0,t.jsx)(m.AlertCircle,{className:"h-4 w-4 text-red-500"}),(0,t.jsx)(l.AlertDescription,{className:"text-red-500",children:w})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("video",{ref:g,className:"w-full h-full object-cover",controls:!1,muted:!0,playsInline:!0,children:[(0,t.jsx)("source",{src:N,type:"video/mp4"}),(0,t.jsx)("source",{src:N,type:"application/x-mpegURL"}),"Your browser does not support the video tag."]}),_&&(0,t.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-black/50",children:(0,t.jsxs)("div",{className:"text-white text-center",children:[(0,t.jsx)(x.Wifi,{className:"h-8 w-8 mx-auto mb-2 animate-pulse"}),(0,t.jsx)("p",{children:"Connecting to camera..."})]})}),!_&&!w&&(0,t.jsx)("div",{className:"absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity bg-black/20",children:(0,t.jsx)(r.Button,{variant:"secondary",size:"lg",onClick:()=>{let e=g.current;e&&(y?e.pause():e.play().catch(e=>{console.error("Play failed:",e),k("Failed to play stream. Please try again.")}))},className:"bg-black/50 hover:bg-black/70",children:y?(0,t.jsx)(o,{className:"h-6 w-6"}):(0,t.jsx)(d,{className:"h-6 w-6"})})})]})})})]})}},66027,e=>{"use strict";e.s(["useQuery",()=>et],66027);var t,s,a,r,i,l,n,c,d,o,h,u,m,x,p,v,f,g,y,b,_,j,w,k,N=e.i(39946),C=e.i(70292),M=e.i(62351),S=e.i(37696),R=e.i(88245),O=e.i(75555),T=e.i(40143),E=e.i(86491),A=e.i(15823),W=e.i(93803),L=e.i(19273),q=(t=new WeakMap,s=new WeakMap,a=new WeakMap,r=new WeakMap,i=new WeakMap,l=new WeakMap,n=new WeakMap,c=new WeakMap,d=new WeakMap,o=new WeakMap,h=new WeakMap,u=new WeakMap,m=new WeakMap,x=new WeakMap,p=new WeakMap,v=new WeakSet,f=new WeakSet,g=new WeakSet,y=new WeakSet,b=new WeakSet,_=new WeakSet,j=new WeakSet,w=new WeakSet,k=new WeakSet,class extends A.Subscribable{bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&((0,N._)(this,s).addObserver(this),U((0,N._)(this,s),this.options)?(0,S._)(this,v,Q).call(this):this.updateResult(),(0,S._)(this,b,I).call(this))}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return F((0,N._)(this,s),this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return F((0,N._)(this,s),this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,(0,S._)(this,_,V).call(this),(0,S._)(this,j,K).call(this),(0,N._)(this,s).removeObserver(this)}setOptions(e){let a=this.options,r=(0,N._)(this,s);if(this.options=(0,N._)(this,t).defaultQueryOptions(e),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof(0,L.resolveEnabled)(this.options.enabled,(0,N._)(this,s)))throw Error("Expected enabled to be a boolean or a callback that returns a boolean");(0,S._)(this,w,X).call(this),(0,N._)(this,s).setOptions(this.options),a._defaulted&&!(0,L.shallowEqualObjects)(this.options,a)&&(0,N._)(this,t).getQueryCache().notify({type:"observerOptionsUpdated",query:(0,N._)(this,s),observer:this});let i=this.hasListeners();i&&D((0,N._)(this,s),r,this.options,a)&&(0,S._)(this,v,Q).call(this),this.updateResult(),i&&((0,N._)(this,s)!==r||(0,L.resolveEnabled)(this.options.enabled,(0,N._)(this,s))!==(0,L.resolveEnabled)(a.enabled,(0,N._)(this,s))||(0,L.resolveStaleTime)(this.options.staleTime,(0,N._)(this,s))!==(0,L.resolveStaleTime)(a.staleTime,(0,N._)(this,s)))&&(0,S._)(this,f,H).call(this);let l=(0,S._)(this,g,P).call(this);i&&((0,N._)(this,s)!==r||(0,L.resolveEnabled)(this.options.enabled,(0,N._)(this,s))!==(0,L.resolveEnabled)(a.enabled,(0,N._)(this,s))||l!==(0,N._)(this,x))&&(0,S._)(this,y,B).call(this,l)}getOptimisticResult(e){var a,n;let c=(0,N._)(this,t).getQueryCache().build((0,N._)(this,t),e),d=this.createResult(c,e);return a=this,n=d,(0,L.shallowEqualObjects)(a.getCurrentResult(),n)||((0,M._)(this,r,d),(0,M._)(this,l,this.options),(0,M._)(this,i,(0,N._)(this,s).state)),d}getCurrentResult(){return(0,N._)(this,r)}trackResult(e,t){return new Proxy(e,{get:(e,s)=>(this.trackProp(s),null==t||t(s),"promise"!==s||this.options.experimental_prefetchInRender||"pending"!==(0,N._)(this,n).status||(0,N._)(this,n).reject(Error("experimental_prefetchInRender feature flag is not enabled")),Reflect.get(e,s))})}trackProp(e){(0,N._)(this,p).add(e)}getCurrentQuery(){return(0,N._)(this,s)}refetch(){let{...e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.fetch({...e})}fetchOptimistic(e){let s=(0,N._)(this,t).defaultQueryOptions(e),a=(0,N._)(this,t).getQueryCache().build((0,N._)(this,t),s);return a.fetch().then(()=>this.createResult(a,s))}fetch(e){var t;return(0,S._)(this,v,Q).call(this,{...e,cancelRefetch:null==(t=e.cancelRefetch)||t}).then(()=>(this.updateResult(),(0,N._)(this,r)))}createResult(e,t){let u,m=(0,N._)(this,s),x=this.options,p=(0,N._)(this,r),v=(0,N._)(this,i),f=(0,N._)(this,l),g=e!==m?e.state:(0,N._)(this,a),{state:y}=e,b={...y},_=!1;if(t._optimisticResults){let s=this.hasListeners(),a=!s&&U(e,t),r=s&&D(e,m,t,x);(a||r)&&(b={...b,...(0,E.fetchState)(y.data,e.options)}),"isRestoring"===t._optimisticResults&&(b.fetchStatus="idle")}let{error:j,errorUpdatedAt:w,status:k}=b;u=b.data;let C=!1;if(void 0!==t.placeholderData&&void 0===u&&"pending"===k){let e;if((null==p?void 0:p.isPlaceholderData)&&t.placeholderData===(null==f?void 0:f.placeholderData))e=p.data,C=!0;else{var S;e="function"==typeof t.placeholderData?t.placeholderData(null==(S=(0,N._)(this,h))?void 0:S.state.data,(0,N._)(this,h)):t.placeholderData}void 0!==e&&(k="success",u=(0,L.replaceData)(null==p?void 0:p.data,e,t),_=!0)}if(t.select&&void 0!==u&&!C)if(p&&u===(null==v?void 0:v.data)&&t.select===(0,N._)(this,d))u=(0,N._)(this,o);else try{(0,M._)(this,d,t.select),u=t.select(u),u=(0,L.replaceData)(null==p?void 0:p.data,u,t),(0,M._)(this,o,u),(0,M._)(this,c,null)}catch(e){(0,M._)(this,c,e)}(0,N._)(this,c)&&(j=(0,N._)(this,c),u=(0,N._)(this,o),w=Date.now(),k="error");let R="fetching"===b.fetchStatus,O="pending"===k,T="error"===k,A=O&&R,q=void 0!==u,F={status:k,fetchStatus:b.fetchStatus,isPending:O,isSuccess:"success"===k,isError:T,isInitialLoading:A,isLoading:A,data:u,dataUpdatedAt:b.dataUpdatedAt,error:j,errorUpdatedAt:w,failureCount:b.fetchFailureCount,failureReason:b.fetchFailureReason,errorUpdateCount:b.errorUpdateCount,isFetched:b.dataUpdateCount>0||b.errorUpdateCount>0,isFetchedAfterMount:b.dataUpdateCount>g.dataUpdateCount||b.errorUpdateCount>g.errorUpdateCount,isFetching:R,isRefetching:R&&!O,isLoadingError:T&&!q,isPaused:"paused"===b.fetchStatus,isPlaceholderData:_,isRefetchError:T&&q,isStale:z(e,t),refetch:this.refetch,promise:(0,N._)(this,n),isEnabled:!1!==(0,L.resolveEnabled)(t.enabled,e)};if(this.options.experimental_prefetchInRender){let t=e=>{"error"===F.status?e.reject(F.error):void 0!==F.data&&e.resolve(F.data)},s=()=>{t((0,M._)(this,n,F.promise=(0,W.pendingThenable)()))},a=(0,N._)(this,n);switch(a.status){case"pending":e.queryHash===m.queryHash&&t(a);break;case"fulfilled":("error"===F.status||F.data!==a.value)&&s();break;case"rejected":("error"!==F.status||F.error!==a.reason)&&s()}}return F}updateResult(){let e=(0,N._)(this,r),t=this.createResult((0,N._)(this,s),this.options);if((0,M._)(this,i,(0,N._)(this,s).state),(0,M._)(this,l,this.options),void 0!==(0,N._)(this,i).data&&(0,M._)(this,h,(0,N._)(this,s)),(0,L.shallowEqualObjects)(t,e))return;(0,M._)(this,r,t);let a=()=>{if(!e)return!0;let{notifyOnChangeProps:t}=this.options,s="function"==typeof t?t():t;if("all"===s||!s&&!(0,N._)(this,p).size)return!0;let a=new Set(null!=s?s:(0,N._)(this,p));return this.options.throwOnError&&a.add("error"),Object.keys((0,N._)(this,r)).some(t=>(0,N._)(this,r)[t]!==e[t]&&a.has(t))};(0,S._)(this,k,J).call(this,{listeners:a()})}onQueryUpdate(){this.updateResult(),this.hasListeners()&&(0,S._)(this,b,I).call(this)}constructor(e,N){super(),(0,R._)(this,v),(0,R._)(this,f),(0,R._)(this,g),(0,R._)(this,y),(0,R._)(this,b),(0,R._)(this,_),(0,R._)(this,j),(0,R._)(this,w),(0,R._)(this,k),(0,C._)(this,t,{writable:!0,value:void 0}),(0,C._)(this,s,{writable:!0,value:void 0}),(0,C._)(this,a,{writable:!0,value:void 0}),(0,C._)(this,r,{writable:!0,value:void 0}),(0,C._)(this,i,{writable:!0,value:void 0}),(0,C._)(this,l,{writable:!0,value:void 0}),(0,C._)(this,n,{writable:!0,value:void 0}),(0,C._)(this,c,{writable:!0,value:void 0}),(0,C._)(this,d,{writable:!0,value:void 0}),(0,C._)(this,o,{writable:!0,value:void 0}),(0,C._)(this,h,{writable:!0,value:void 0}),(0,C._)(this,u,{writable:!0,value:void 0}),(0,C._)(this,m,{writable:!0,value:void 0}),(0,C._)(this,x,{writable:!0,value:void 0}),(0,C._)(this,p,{writable:!0,value:new Set}),this.options=N,(0,M._)(this,t,e),(0,M._)(this,c,null),(0,M._)(this,n,(0,W.pendingThenable)()),this.bindMethods(),this.setOptions(N)}});function U(e,t){return!1!==(0,L.resolveEnabled)(t.enabled,e)&&void 0===e.state.data&&("error"!==e.state.status||!1!==t.retryOnMount)||void 0!==e.state.data&&F(e,t,t.refetchOnMount)}function F(e,t,s){if(!1!==(0,L.resolveEnabled)(t.enabled,e)&&"static"!==(0,L.resolveStaleTime)(t.staleTime,e)){let a="function"==typeof s?s(e):s;return"always"===a||!1!==a&&z(e,t)}return!1}function D(e,t,s,a){return(e!==t||!1===(0,L.resolveEnabled)(a.enabled,e))&&(!s.suspense||"error"!==e.state.status)&&z(e,s)}function z(e,t){return!1!==(0,L.resolveEnabled)(t.enabled,e)&&e.isStaleByTime((0,L.resolveStaleTime)(t.staleTime,e))}function Q(e){(0,S._)(this,w,X).call(this);let t=(0,N._)(this,s).fetch(this.options,e);return(null==e?void 0:e.throwOnError)||(t=t.catch(L.noop)),t}function H(){(0,S._)(this,_,V).call(this);let e=(0,L.resolveStaleTime)(this.options.staleTime,(0,N._)(this,s));if(L.isServer||(0,N._)(this,r).isStale||!(0,L.isValidTimeout)(e))return;let t=(0,L.timeUntilStale)((0,N._)(this,r).dataUpdatedAt,e);(0,M._)(this,u,setTimeout(()=>{(0,N._)(this,r).isStale||this.updateResult()},t+1))}function P(){var e;return null!=(e="function"==typeof this.options.refetchInterval?this.options.refetchInterval((0,N._)(this,s)):this.options.refetchInterval)&&e}function B(e){(0,S._)(this,j,K).call(this),(0,M._)(this,x,e),!L.isServer&&!1!==(0,L.resolveEnabled)(this.options.enabled,(0,N._)(this,s))&&(0,L.isValidTimeout)((0,N._)(this,x))&&0!==(0,N._)(this,x)&&(0,M._)(this,m,setInterval(()=>{(this.options.refetchIntervalInBackground||O.focusManager.isFocused())&&(0,S._)(this,v,Q).call(this)},(0,N._)(this,x)))}function I(){(0,S._)(this,f,H).call(this),(0,S._)(this,y,B).call(this,(0,S._)(this,g,P).call(this))}function V(){(0,N._)(this,u)&&(clearTimeout((0,N._)(this,u)),(0,M._)(this,u,void 0))}function K(){(0,N._)(this,m)&&(clearInterval((0,N._)(this,m)),(0,M._)(this,m,void 0))}function X(){let e=(0,N._)(this,t).getQueryCache().build((0,N._)(this,t),this.options);if(e===(0,N._)(this,s))return;let r=(0,N._)(this,s);(0,M._)(this,s,e),(0,M._)(this,a,e.state),this.hasListeners()&&(null==r||r.removeObserver(this),e.addObserver(this))}function J(e){T.notifyManager.batch(()=>{e.listeners&&this.listeners.forEach(e=>{e((0,N._)(this,r))}),(0,N._)(this,t).getQueryCache().notify({query:(0,N._)(this,s),type:"observerResultsUpdated"})})}e.i(47167);var Y=e.i(71645),G=e.i(12598);e.i(43476);var Z=Y.createContext(function(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}()),$=Y.createContext(!1);$.Provider;var ee=(e,t,s)=>t.fetchOptimistic(e).catch(()=>{s.clearReset()});function et(e,t){return function(e,t,s){var a,r,i,l,n;let c=Y.useContext($),d=Y.useContext(Z),o=(0,G.useQueryClient)(s),h=o.defaultQueryOptions(e);if(null==(r=o.getDefaultOptions().queries)||null==(a=r._experimental_beforeQuery)||a.call(r,h),h._optimisticResults=c?"isRestoring":"optimistic",h.suspense){let e=e=>"static"===e?e:Math.max(null!=e?e:1e3,1e3),t=h.staleTime;h.staleTime="function"==typeof t?function(){for(var s=arguments.length,a=Array(s),r=0;r<s;r++)a[r]=arguments[r];return e(t(...a))}:e(t),"number"==typeof h.gcTime&&(h.gcTime=Math.max(h.gcTime,1e3))}(h.suspense||h.throwOnError||h.experimental_prefetchInRender)&&!d.isReset()&&(h.retryOnMount=!1),Y.useEffect(()=>{d.clearReset()},[d]);let u=!o.getQueryCache().get(h.queryHash),[m]=Y.useState(()=>new t(o,h)),x=m.getOptimisticResult(h),p=!c&&!1!==e.subscribed;if(Y.useSyncExternalStore(Y.useCallback(e=>{let t=p?m.subscribe(T.notifyManager.batchCalls(e)):L.noop;return m.updateResult(),t},[m,p]),()=>m.getCurrentResult(),()=>m.getCurrentResult()),Y.useEffect(()=>{m.setOptions(h)},[h,m]),(null==h?void 0:h.suspense)&&x.isPending)throw ee(h,m,d);if((e=>{let{result:t,errorResetBoundary:s,throwOnError:a,query:r,suspense:i}=e;return t.isError&&!s.isReset()&&!t.isFetching&&r&&(i&&void 0===t.data||(0,L.shouldThrowError)(a,[t.error,r]))})({result:x,errorResetBoundary:d,throwOnError:h.throwOnError,query:o.getQueryCache().get(h.queryHash),suspense:h.suspense}))throw x.error;if(null==(l=o.getDefaultOptions().queries)||null==(i=l._experimental_afterQuery)||i.call(l,h,x),h.experimental_prefetchInRender&&!L.isServer&&x.isLoading&&x.isFetching&&!c){let e=u?ee(h,m,d):null==(n=o.getQueryCache().get(h.queryHash))?void 0:n.promise;null==e||e.catch(L.noop).finally(()=>{m.updateResult()})}return h.notifyOnChangeProps?x:m.trackResult(x)}(e,q,t)}},15288,e=>{"use strict";e.s(["Card",()=>a,"CardContent",()=>l,"CardHeader",()=>r,"CardTitle",()=>i]);var t=e.i(43476),s=e.i(75157);function a(e){let{className:a,...r}=e;return(0,t.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...r})}function r(e){let{className:a,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...r})}function i(e){let{className:a,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",a),...r})}function l(e){let{className:a,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",a),...r})}},25652,e=>{"use strict";e.s(["TrendingUp",()=>t],25652);let t=(0,e.i(75254).default)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},34663,e=>{"use strict";e.s(["DashboardOverview",()=>k],34663);var t=e.i(43476),s=e.i(66027),a=e.i(15288),r=e.i(87486),i=e.i(78894),l=e.i(75254);let n=(0,l.default)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);var c=e.i(25652),d=e.i(68553),o=e.i(16973),h=e.i(29592),u=e.i(69638),m=e.i(73884),x=e.i(63209);let p=(0,l.default)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]),v=(0,l.default)("brain",[["path",{d:"M12 18V5",key:"adv99a"}],["path",{d:"M15 13a4.17 4.17 0 0 1-3-4 4.17 4.17 0 0 1-3 4",key:"1e3is1"}],["path",{d:"M17.598 6.5A3 3 0 1 0 12 5a3 3 0 1 0-5.598 1.5",key:"1gqd8o"}],["path",{d:"M17.997 5.125a4 4 0 0 1 2.526 5.77",key:"iwvgf7"}],["path",{d:"M18 18a4 4 0 0 0 2-7.464",key:"efp6ie"}],["path",{d:"M19.967 17.483A4 4 0 1 1 12 18a4 4 0 1 1-7.967-.517",key:"1gq6am"}],["path",{d:"M6 18a4 4 0 0 1-2-7.464",key:"k1g0md"}],["path",{d:"M6.003 5.125a4 4 0 0 0-2.526 5.77",key:"q97ue3"}]]);var f=e.i(53475),g=e.i(57212),y=e.i(75157);function b(){let{data:e,isLoading:i,error:l}=(0,s.useQuery)({queryKey:["health-status"],queryFn:()=>o.apiService.getHealthStatus(),refetchInterval:15e3,retry:3}),n=e=>{switch(e){case"healthy":case"up":return"text-green-500";case"degraded":return"text-yellow-500";case"unhealthy":case"down":return"text-red-500";default:return"text-gray-500"}},c=e=>{switch(e){case"healthy":case"up":return(0,t.jsx)(r.Badge,{className:"bg-green-500/10 text-green-500 border-green-500/20",children:"Online"});case"degraded":return(0,t.jsx)(r.Badge,{className:"bg-yellow-500/10 text-yellow-500 border-yellow-500/20",children:"Degraded"});case"unhealthy":case"down":return(0,t.jsx)(r.Badge,{className:"bg-red-500/10 text-red-500 border-red-500/20",children:"Offline"});default:return(0,t.jsx)(r.Badge,{variant:"secondary",children:"Unknown"})}};return l?(0,t.jsxs)(h.Alert,{className:"border-red-500/20 bg-red-500/10",children:[(0,t.jsx)(m.XCircle,{className:"h-4 w-4 text-red-500"}),(0,t.jsx)(h.AlertDescription,{className:"text-red-500",children:"Unable to connect to backend services. Please check your connection."})]}):(0,t.jsxs)(a.Card,{children:[(0,t.jsx)(a.CardHeader,{children:(0,t.jsxs)(a.CardTitle,{className:"flex items-center gap-2",children:[i?(0,t.jsx)(f.Wifi,{className:"h-5 w-5 text-muted-foreground animate-pulse"}):l?(0,t.jsx)(g.WifiOff,{className:"h-5 w-5 text-red-500"}):(0,t.jsx)("div",{className:(0,y.cn)("h-5 w-5",n((null==e?void 0:e.status)||"unknown")),children:(e=>{switch(e){case"healthy":case"up":return(0,t.jsx)(u.CheckCircle,{className:"h-4 w-4"});case"degraded":default:return(0,t.jsx)(x.AlertCircle,{className:"h-4 w-4"});case"unhealthy":case"down":return(0,t.jsx)(m.XCircle,{className:"h-4 w-4"})}})((null==e?void 0:e.status)||"unknown")}),"System Health",e&&c(e.status)]})}),(0,t.jsx)(a.CardContent,{children:i?(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:"Checking system status..."}):e?(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(p,{className:(0,y.cn)("h-4 w-4",n(e.services.database))}),(0,t.jsx)("span",{className:"text-sm",children:"Database"}),c(e.services.database)]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(d.Camera,{className:(0,y.cn)("h-4 w-4",n(e.services.camera))}),(0,t.jsx)("span",{className:"text-sm",children:"Camera"}),c(e.services.camera)]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(v,{className:(0,y.cn)("h-4 w-4",n(e.services.ai_model))}),(0,t.jsx)("span",{className:"text-sm",children:"AI Model"}),c(e.services.ai_model)]})]}),(0,t.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Last updated: ",new Date(e.timestamp).toLocaleString(),e.uptime&&(0,t.jsxs)("span",{className:"ml-2",children:["• Uptime: ",Math.floor(e.uptime/3600),"h ",Math.floor(e.uptime%3600/60),"m"]})]})]}):(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:"No health data available"})})]})}var _=e.i(46897);function j(e){let{alerts:s,loading:a}=e;return a?(0,t.jsx)("div",{className:"space-y-3",children:[void 0,void 0,void 0].map((e,s)=>(0,t.jsx)("div",{className:"animate-pulse",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"h-8 w-8 bg-muted rounded-full"}),(0,t.jsxs)("div",{className:"flex-1 space-y-1",children:[(0,t.jsx)("div",{className:"h-4 bg-muted rounded w-3/4"}),(0,t.jsx)("div",{className:"h-3 bg-muted rounded w-1/2"})]})]})},s))}):s&&0!==s.length?(0,t.jsx)("div",{className:"space-y-3",children:s.map(e=>(0,t.jsxs)("div",{className:"flex items-start space-x-3 p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)(i.AlertTriangle,{className:"h-5 w-5 text-orange-500 mt-0.5"})}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,t.jsxs)("p",{className:"text-sm font-medium text-foreground truncate",children:[e.objects.join(", ")," detected"]}),(0,t.jsx)(r.Badge,{className:(0,y.cn)("text-xs",(e=>{switch(e){case"active":return"bg-red-500/10 text-red-500 border-red-500/20";case"investigating":return"bg-yellow-500/10 text-yellow-500 border-yellow-500/20";case"resolved":return"bg-green-500/10 text-green-500 border-green-500/20";default:return"bg-gray-500/10 text-gray-500 border-gray-500/20"}})(e.status)),children:e.status})]}),(0,t.jsxs)("div",{className:"flex items-center text-xs text-muted-foreground space-x-3",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(n,{className:"h-3 w-3 mr-1"}),(e=>{let t=new Date,s=new Date(e),a=Math.floor((t.getTime()-s.getTime())/6e4);return a<1?"Just now":a<60?"".concat(a,"m ago"):a<1440?"".concat(Math.floor(a/60),"h ago"):"".concat(Math.floor(a/1440),"d ago")})(e.timestamp)]}),(0,t.jsx)("div",{className:"flex items-center",children:(0,t.jsxs)("span",{children:["Confidence: ",Math.round(100*e.confidence),"%"]})}),e.location&&(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(_.MapPin,{className:"h-3 w-3 mr-1"}),(0,t.jsx)("span",{children:"Location"})]})]})]})]},e.id))}):(0,t.jsxs)("div",{className:"text-center py-6",children:[(0,t.jsx)(i.AlertTriangle,{className:"h-8 w-8 text-muted-foreground mx-auto mb-2"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"No recent alerts"})]})}var w=e.i(84771);function k(){let{data:e,isLoading:l}=(0,s.useQuery)({queryKey:["alert-stats"],queryFn:()=>o.apiService.getAlertStats(),refetchInterval:3e4}),{data:h,isLoading:u}=(0,s.useQuery)({queryKey:["recent-alerts"],queryFn:()=>o.apiService.getAlerts(5),refetchInterval:1e4});return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(b,{}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,t.jsxs)(a.Card,{children:[(0,t.jsxs)(a.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.CardTitle,{className:"text-sm font-medium",children:"Total Alerts"}),(0,t.jsx)(i.AlertTriangle,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsxs)(a.CardContent,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:l?"...":(null==e?void 0:e.total_alerts)||0}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"All time detections"})]})]}),(0,t.jsxs)(a.Card,{children:[(0,t.jsxs)(a.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.CardTitle,{className:"text-sm font-medium",children:"Today"}),(0,t.jsx)(n,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsxs)(a.CardContent,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:l?"...":(null==e?void 0:e.alerts_today)||0}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Alerts in last 24h"})]})]}),(0,t.jsxs)(a.Card,{children:[(0,t.jsxs)(a.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.CardTitle,{className:"text-sm font-medium",children:"This Week"}),(0,t.jsx)(c.TrendingUp,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsxs)(a.CardContent,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:l?"...":(null==e?void 0:e.alerts_this_week)||0}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Weekly detections"})]})]}),(0,t.jsxs)(a.Card,{children:[(0,t.jsxs)(a.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.CardTitle,{className:"text-sm font-medium",children:"Active Cameras"}),(0,t.jsx)(d.Camera,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsxs)(a.CardContent,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"1"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Monitoring zones"})]})]})]}),(0,t.jsxs)("div",{className:"grid gap-4 lg:grid-cols-3",children:[(0,t.jsxs)(a.Card,{children:[(0,t.jsx)(a.CardHeader,{children:(0,t.jsx)(a.CardTitle,{children:"Recent Alerts"})}),(0,t.jsx)(a.CardContent,{children:(0,t.jsx)(j,{alerts:h,loading:u})})]}),(0,t.jsxs)(a.Card,{children:[(0,t.jsx)(a.CardHeader,{children:(0,t.jsx)(a.CardTitle,{children:"Detection Summary"})}),(0,t.jsx)(a.CardContent,{children:(0,t.jsx)("div",{className:"space-y-4",children:l?(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:"Loading..."}):(null==e?void 0:e.detection_counts)&&Object.entries(e.detection_counts).map(e=>{let[s,a]=e;return(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm font-medium capitalize",children:s}),(0,t.jsx)(r.Badge,{variant:"secondary",children:a})]},s)})})})]}),(0,t.jsx)("div",{className:"lg:col-span-1",children:(0,t.jsx)(w.CameraFeed,{})})]})]})}}]);