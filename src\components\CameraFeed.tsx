"use client";

import { useState, useRef, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Camera,
  Play,
  Pause,
  RotateCcw,
  Maximize,
  AlertCircle,
  Wifi,
} from "lucide-react";
import { apiService } from "@/services/api";
import { cn } from "@/lib/utils";

interface CameraFeedProps {
  className?: string;
  autoPlay?: boolean;
}

export function CameraFeed({ className, autoPlay = true }: CameraFeedProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  // const [isFullscreen, setIsFullscreen] = useState(false);

  const streamUrl = apiService.getCameraStreamUrl();

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleLoadStart = () => {
      setIsLoading(true);
      setError(null);
    };

    const handleCanPlay = () => {
      setIsLoading(false);
      if (autoPlay) {
        video.play().catch((err) => {
          console.error("Auto-play failed:", err);
          setError("Auto-play failed. Click play to start the stream.");
        });
      }
    };

    const handlePlay = () => {
      setIsPlaying(true);
      setError(null);
    };

    const handlePause = () => {
      setIsPlaying(false);
    };

    const handleError = () => {
      setIsLoading(false);
      setIsPlaying(false);
      setError("Failed to load camera stream. Please check your connection.");
    };

    // const handleFullscreenChange = () => {
    //   setIsFullscreen(!!document.fullscreenElement);
    // };

    video.addEventListener("loadstart", handleLoadStart);
    video.addEventListener("canplay", handleCanPlay);
    video.addEventListener("play", handlePlay);
    video.addEventListener("pause", handlePause);
    video.addEventListener("error", handleError);
    // document.addEventListener("fullscreenchange", handleFullscreenChange);

    return () => {
      video.removeEventListener("loadstart", handleLoadStart);
      video.removeEventListener("canplay", handleCanPlay);
      video.removeEventListener("play", handlePlay);
      video.removeEventListener("pause", handlePause);
      video.removeEventListener("error", handleError);
      // document.removeEventListener("fullscreenchange", handleFullscreenChange);
    };
  }, [autoPlay]);

  const togglePlay = () => {
    const video = videoRef.current;
    if (!video) return;

    if (isPlaying) {
      video.pause();
    } else {
      video.play().catch((err) => {
        console.error("Play failed:", err);
        setError("Failed to play stream. Please try again.");
      });
    }
  };

  const refreshStream = () => {
    const video = videoRef.current;
    if (!video) return;

    setError(null);
    setIsLoading(true);
    video.load();
  };

  const toggleFullscreen = () => {
    const video = videoRef.current;
    if (!video) return;

    if (!document.fullscreenElement) {
      video.requestFullscreen().catch((err) => {
        console.error("Fullscreen failed:", err);
      });
    } else {
      document.exitFullscreen();
    }
  };

  const getStatusBadge = () => {
    if (isLoading) {
      return <Badge variant="secondary">Connecting...</Badge>;
    }
    if (error) {
      return (
        <Badge className="bg-red-500/10 text-red-500 border-red-500/20">
          Offline
        </Badge>
      );
    }
    if (isPlaying) {
      return (
        <Badge className="bg-green-500/10 text-green-500 border-green-500/20">
          Live
        </Badge>
      );
    }
    return <Badge variant="secondary">Paused</Badge>;
  };

  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Camera className="h-5 w-5" />
            Camera Feed
            {getStatusBadge()}
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={refreshStream}
              disabled={isLoading}
            >
              <RotateCcw className="h-4 w-4" />
            </Button>

            <Button variant="outline" size="sm" onClick={toggleFullscreen}>
              <Maximize className="h-4 w-4" />
            </Button>
          </div>
        </CardTitle>
      </CardHeader>

      <CardContent className="p-0">
        <div className="relative bg-black aspect-video">
          {error ? (
            <Alert className="m-4 border-red-500/20 bg-red-500/10">
              <AlertCircle className="h-4 w-4 text-red-500" />
              <AlertDescription className="text-red-500">
                {error}
              </AlertDescription>
            </Alert>
          ) : (
            <>
              <video
                ref={videoRef}
                className="w-full h-full object-cover"
                controls={false}
                muted
                playsInline
              >
                <source src={streamUrl} type="video/mp4" />
                <source src={streamUrl} type="application/x-mpegURL" />
                Your browser does not support the video tag.
              </video>

              {/* Loading overlay */}
              {isLoading && (
                <div className="absolute inset-0 flex items-center justify-center bg-black/50">
                  <div className="text-white text-center">
                    <Wifi className="h-8 w-8 mx-auto mb-2 animate-pulse" />
                    <p>Connecting to camera...</p>
                  </div>
                </div>
              )}

              {/* Play/Pause overlay */}
              {!isLoading && !error && (
                <div className="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity bg-black/20">
                  <Button
                    variant="secondary"
                    size="lg"
                    onClick={togglePlay}
                    className="bg-black/50 hover:bg-black/70"
                  >
                    {isPlaying ? (
                      <Pause className="h-6 w-6" />
                    ) : (
                      <Play className="h-6 w-6" />
                    )}
                  </Button>
                </div>
              )}
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
