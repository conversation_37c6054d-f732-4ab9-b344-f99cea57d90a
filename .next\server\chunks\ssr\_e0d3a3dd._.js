module.exports=[24669,a=>{"use strict";a.s(["TrendingUp",()=>b],24669);let b=(0,a.i(70106).default)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},13513,a=>{"use strict";a.s(["Camera",()=>b],13513);let b=(0,a.i(70106).default)("camera",[["path",{d:"M13.997 4a2 2 0 0 1 1.76 1.05l.486.9A2 2 0 0 0 18.003 7H20a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V9a2 2 0 0 1 2-2h1.997a2 2 0 0 0 1.759-1.048l.489-.904A2 2 0 0 1 10.004 4z",key:"18u6gg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},24348,a=>{"use strict";a.s(["Alert",()=>f,"<PERSON>ertDescription",()=>g]);var b=a.i(87924),c=a.i(187),d=a.i(68114);let e=(0,c.cva)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function f({className:a,variant:c,...f}){return(0,b.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,d.cn)(e({variant:c}),a),...f})}function g({className:a,...c}){return(0,b.jsx)("div",{"data-slot":"alert-description",className:(0,d.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",a),...c})}},16201,62722,a=>{"use strict";a.s(["CheckCircle",()=>c],16201);var b=a.i(70106);let c=(0,b.default)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);a.s(["XCircle",()=>d],62722);let d=(0,b.default)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},92e3,a=>{"use strict";a.s(["AlertCircle",()=>b],92e3);let b=(0,a.i(70106).default)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},24987,a=>{"use strict";a.s(["MapPin",()=>b],24987);let b=(0,a.i(70106).default)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},99570,a=>{"use strict";a.s(["Button",()=>g]);var b=a.i(87924),c=a.i(11011),d=a.i(187),e=a.i(68114);let f=(0,d.cva)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function g({className:a,variant:d,size:g,asChild:h=!1,...i}){let j=h?c.Slot:"button";return(0,b.jsx)(j,{"data-slot":"button",className:(0,e.cn)(f({variant:d,size:g,className:a})),...i})}},72107,a=>{"use strict";a.s(["CameraFeed",()=>r],72107);var b=a.i(87924),c=a.i(72131),d=a.i(91119),e=a.i(99570),f=a.i(86304),g=a.i(24348),h=a.i(13513),i=a.i(70106);let j=(0,i.default)("play",[["path",{d:"M5 5a2 2 0 0 1 3.008-1.728l11.997 6.998a2 2 0 0 1 .003 3.458l-12 7A2 2 0 0 1 5 19z",key:"10ikf1"}]]),k=(0,i.default)("pause",[["rect",{x:"14",y:"3",width:"5",height:"18",rx:"1",key:"kaeet6"}],["rect",{x:"5",y:"3",width:"5",height:"18",rx:"1",key:"1wsw3u"}]]),l=(0,i.default)("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]),m=(0,i.default)("maximize",[["path",{d:"M8 3H5a2 2 0 0 0-2 2v3",key:"1dcmit"}],["path",{d:"M21 8V5a2 2 0 0 0-2-2h-3",key:"1e4gt3"}],["path",{d:"M3 16v3a2 2 0 0 0 2 2h3",key:"wsl5sc"}],["path",{d:"M16 21h3a2 2 0 0 0 2-2v-3",key:"18trek"}]]);var n=a.i(92e3),o=a.i(85264),p=a.i(84391),q=a.i(68114);function r({className:a,autoPlay:i=!0}){let r=(0,c.useRef)(null),[s,t]=(0,c.useState)(!1),[u,v]=(0,c.useState)(!0),[w,x]=(0,c.useState)(null),y=p.apiService.getCameraStreamUrl();return(0,c.useEffect)(()=>{let a=r.current;if(!a)return;let b=()=>{v(!0),x(null)},c=()=>{v(!1),i&&a.play().catch(a=>{console.error("Auto-play failed:",a),x("Auto-play failed. Click play to start the stream.")})},d=()=>{t(!0),x(null)},e=()=>{t(!1)},f=()=>{v(!1),t(!1),x("Failed to load camera stream. Please check your connection.")};return a.addEventListener("loadstart",b),a.addEventListener("canplay",c),a.addEventListener("play",d),a.addEventListener("pause",e),a.addEventListener("error",f),()=>{a.removeEventListener("loadstart",b),a.removeEventListener("canplay",c),a.removeEventListener("play",d),a.removeEventListener("pause",e),a.removeEventListener("error",f)}},[i]),(0,b.jsxs)(d.Card,{className:(0,q.cn)("overflow-hidden",a),children:[(0,b.jsx)(d.CardHeader,{children:(0,b.jsxs)(d.CardTitle,{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{className:"flex items-center gap-2",children:[(0,b.jsx)(h.Camera,{className:"h-5 w-5"}),"Camera Feed",u?(0,b.jsx)(f.Badge,{variant:"secondary",children:"Connecting..."}):w?(0,b.jsx)(f.Badge,{className:"bg-red-500/10 text-red-500 border-red-500/20",children:"Offline"}):s?(0,b.jsx)(f.Badge,{className:"bg-green-500/10 text-green-500 border-green-500/20",children:"Live"}):(0,b.jsx)(f.Badge,{variant:"secondary",children:"Paused"})]}),(0,b.jsxs)("div",{className:"flex items-center gap-2",children:[(0,b.jsx)(e.Button,{variant:"outline",size:"sm",onClick:()=>{let a=r.current;a&&(x(null),v(!0),a.load())},disabled:u,children:(0,b.jsx)(l,{className:"h-4 w-4"})}),(0,b.jsx)(e.Button,{variant:"outline",size:"sm",onClick:()=>{let a=r.current;a&&(document.fullscreenElement?document.exitFullscreen():a.requestFullscreen().catch(a=>{console.error("Fullscreen failed:",a)}))},children:(0,b.jsx)(m,{className:"h-4 w-4"})})]})]})}),(0,b.jsx)(d.CardContent,{className:"p-0",children:(0,b.jsx)("div",{className:"relative bg-black aspect-video",children:w?(0,b.jsxs)(g.Alert,{className:"m-4 border-red-500/20 bg-red-500/10",children:[(0,b.jsx)(n.AlertCircle,{className:"h-4 w-4 text-red-500"}),(0,b.jsx)(g.AlertDescription,{className:"text-red-500",children:w})]}):(0,b.jsxs)(b.Fragment,{children:[(0,b.jsxs)("video",{ref:r,className:"w-full h-full object-cover",controls:!1,muted:!0,playsInline:!0,children:[(0,b.jsx)("source",{src:y,type:"video/mp4"}),(0,b.jsx)("source",{src:y,type:"application/x-mpegURL"}),"Your browser does not support the video tag."]}),u&&(0,b.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-black/50",children:(0,b.jsxs)("div",{className:"text-white text-center",children:[(0,b.jsx)(o.Wifi,{className:"h-8 w-8 mx-auto mb-2 animate-pulse"}),(0,b.jsx)("p",{children:"Connecting to camera..."})]})}),!u&&!w&&(0,b.jsx)("div",{className:"absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity bg-black/20",children:(0,b.jsx)(e.Button,{variant:"secondary",size:"lg",onClick:()=>{let a=r.current;a&&(s?a.pause():a.play().catch(a=>{console.error("Play failed:",a),x("Failed to play stream. Please try again.")}))},className:"bg-black/50 hover:bg-black/70",children:s?(0,b.jsx)(k,{className:"h-6 w-6"}):(0,b.jsx)(j,{className:"h-6 w-6"})})})]})})})]})}},11022,a=>{"use strict";a.s(["DashboardOverview",()=>y],11022);var b=a.i(87924),c=a.i(33217),d=a.i(91119),e=a.i(86304),f=a.i(73570),g=a.i(70106);let h=(0,g.default)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);var i=a.i(24669),j=a.i(13513),k=a.i(84391),l=a.i(24348),m=a.i(16201),n=a.i(62722),o=a.i(92e3);let p=(0,g.default)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]),q=(0,g.default)("brain",[["path",{d:"M12 18V5",key:"adv99a"}],["path",{d:"M15 13a4.17 4.17 0 0 1-3-4 4.17 4.17 0 0 1-3 4",key:"1e3is1"}],["path",{d:"M17.598 6.5A3 3 0 1 0 12 5a3 3 0 1 0-5.598 1.5",key:"1gqd8o"}],["path",{d:"M17.997 5.125a4 4 0 0 1 2.526 5.77",key:"iwvgf7"}],["path",{d:"M18 18a4 4 0 0 0 2-7.464",key:"efp6ie"}],["path",{d:"M19.967 17.483A4 4 0 1 1 12 18a4 4 0 1 1-7.967-.517",key:"1gq6am"}],["path",{d:"M6 18a4 4 0 0 1-2-7.464",key:"k1g0md"}],["path",{d:"M6.003 5.125a4 4 0 0 0-2.526 5.77",key:"q97ue3"}]]);var r=a.i(85264),s=a.i(33591),t=a.i(68114);function u(){let{data:a,isLoading:f,error:g}=(0,c.useQuery)({queryKey:["health-status"],queryFn:()=>k.apiService.getHealthStatus(),refetchInterval:15e3,retry:3}),h=a=>{switch(a){case"healthy":case"up":return"text-green-500";case"degraded":return"text-yellow-500";case"unhealthy":case"down":return"text-red-500";default:return"text-gray-500"}},i=a=>{switch(a){case"healthy":case"up":return(0,b.jsx)(e.Badge,{className:"bg-green-500/10 text-green-500 border-green-500/20",children:"Online"});case"degraded":return(0,b.jsx)(e.Badge,{className:"bg-yellow-500/10 text-yellow-500 border-yellow-500/20",children:"Degraded"});case"unhealthy":case"down":return(0,b.jsx)(e.Badge,{className:"bg-red-500/10 text-red-500 border-red-500/20",children:"Offline"});default:return(0,b.jsx)(e.Badge,{variant:"secondary",children:"Unknown"})}};return g?(0,b.jsxs)(l.Alert,{className:"border-red-500/20 bg-red-500/10",children:[(0,b.jsx)(n.XCircle,{className:"h-4 w-4 text-red-500"}),(0,b.jsx)(l.AlertDescription,{className:"text-red-500",children:"Unable to connect to backend services. Please check your connection."})]}):(0,b.jsxs)(d.Card,{children:[(0,b.jsx)(d.CardHeader,{children:(0,b.jsxs)(d.CardTitle,{className:"flex items-center gap-2",children:[f?(0,b.jsx)(r.Wifi,{className:"h-5 w-5 text-muted-foreground animate-pulse"}):g?(0,b.jsx)(s.WifiOff,{className:"h-5 w-5 text-red-500"}):(0,b.jsx)("div",{className:(0,t.cn)("h-5 w-5",h(a?.status||"unknown")),children:(a=>{switch(a){case"healthy":case"up":return(0,b.jsx)(m.CheckCircle,{className:"h-4 w-4"});case"degraded":default:return(0,b.jsx)(o.AlertCircle,{className:"h-4 w-4"});case"unhealthy":case"down":return(0,b.jsx)(n.XCircle,{className:"h-4 w-4"})}})(a?.status||"unknown")}),"System Health",a&&i(a.status)]})}),(0,b.jsx)(d.CardContent,{children:f?(0,b.jsx)("div",{className:"text-sm text-muted-foreground",children:"Checking system status..."}):a?(0,b.jsxs)("div",{className:"space-y-4",children:[(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,b.jsxs)("div",{className:"flex items-center gap-2",children:[(0,b.jsx)(p,{className:(0,t.cn)("h-4 w-4",h(a.services.database))}),(0,b.jsx)("span",{className:"text-sm",children:"Database"}),i(a.services.database)]}),(0,b.jsxs)("div",{className:"flex items-center gap-2",children:[(0,b.jsx)(j.Camera,{className:(0,t.cn)("h-4 w-4",h(a.services.camera))}),(0,b.jsx)("span",{className:"text-sm",children:"Camera"}),i(a.services.camera)]}),(0,b.jsxs)("div",{className:"flex items-center gap-2",children:[(0,b.jsx)(q,{className:(0,t.cn)("h-4 w-4",h(a.services.ai_model))}),(0,b.jsx)("span",{className:"text-sm",children:"AI Model"}),i(a.services.ai_model)]})]}),(0,b.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Last updated: ",new Date(a.timestamp).toLocaleString(),a.uptime&&(0,b.jsxs)("span",{className:"ml-2",children:["• Uptime: ",Math.floor(a.uptime/3600),"h ",Math.floor(a.uptime%3600/60),"m"]})]})]}):(0,b.jsx)("div",{className:"text-sm text-muted-foreground",children:"No health data available"})})]})}var v=a.i(24987);function w({alerts:a,loading:c}){return c?(0,b.jsx)("div",{className:"space-y-3",children:[void 0,void 0,void 0].map((a,c)=>(0,b.jsx)("div",{className:"animate-pulse",children:(0,b.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,b.jsx)("div",{className:"h-8 w-8 bg-muted rounded-full"}),(0,b.jsxs)("div",{className:"flex-1 space-y-1",children:[(0,b.jsx)("div",{className:"h-4 bg-muted rounded w-3/4"}),(0,b.jsx)("div",{className:"h-3 bg-muted rounded w-1/2"})]})]})},c))}):a&&0!==a.length?(0,b.jsx)("div",{className:"space-y-3",children:a.map(a=>(0,b.jsxs)("div",{className:"flex items-start space-x-3 p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors",children:[(0,b.jsx)("div",{className:"flex-shrink-0",children:(0,b.jsx)(f.AlertTriangle,{className:"h-5 w-5 text-orange-500 mt-0.5"})}),(0,b.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,b.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,b.jsxs)("p",{className:"text-sm font-medium text-foreground truncate",children:[a.objects.join(", ")," detected"]}),(0,b.jsx)(e.Badge,{className:(0,t.cn)("text-xs",(a=>{switch(a){case"active":return"bg-red-500/10 text-red-500 border-red-500/20";case"investigating":return"bg-yellow-500/10 text-yellow-500 border-yellow-500/20";case"resolved":return"bg-green-500/10 text-green-500 border-green-500/20";default:return"bg-gray-500/10 text-gray-500 border-gray-500/20"}})(a.status)),children:a.status})]}),(0,b.jsxs)("div",{className:"flex items-center text-xs text-muted-foreground space-x-3",children:[(0,b.jsxs)("div",{className:"flex items-center",children:[(0,b.jsx)(h,{className:"h-3 w-3 mr-1"}),(a=>{let b=new Date,c=new Date(a),d=Math.floor((b.getTime()-c.getTime())/6e4);return d<1?"Just now":d<60?`${d}m ago`:d<1440?`${Math.floor(d/60)}h ago`:`${Math.floor(d/1440)}d ago`})(a.timestamp)]}),(0,b.jsx)("div",{className:"flex items-center",children:(0,b.jsxs)("span",{children:["Confidence: ",Math.round(100*a.confidence),"%"]})}),a.location&&(0,b.jsxs)("div",{className:"flex items-center",children:[(0,b.jsx)(v.MapPin,{className:"h-3 w-3 mr-1"}),(0,b.jsx)("span",{children:"Location"})]})]})]})]},a.id))}):(0,b.jsxs)("div",{className:"text-center py-6",children:[(0,b.jsx)(f.AlertTriangle,{className:"h-8 w-8 text-muted-foreground mx-auto mb-2"}),(0,b.jsx)("p",{className:"text-sm text-muted-foreground",children:"No recent alerts"})]})}var x=a.i(72107);function y(){let{data:a,isLoading:g}=(0,c.useQuery)({queryKey:["alert-stats"],queryFn:()=>k.apiService.getAlertStats(),refetchInterval:3e4}),{data:l,isLoading:m}=(0,c.useQuery)({queryKey:["recent-alerts"],queryFn:()=>k.apiService.getAlerts(5),refetchInterval:1e4});return(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsx)(u,{}),(0,b.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,b.jsxs)(d.Card,{children:[(0,b.jsxs)(d.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,b.jsx)(d.CardTitle,{className:"text-sm font-medium",children:"Total Alerts"}),(0,b.jsx)(f.AlertTriangle,{className:"h-4 w-4 text-muted-foreground"})]}),(0,b.jsxs)(d.CardContent,{children:[(0,b.jsx)("div",{className:"text-2xl font-bold",children:g?"...":a?.total_alerts||0}),(0,b.jsx)("p",{className:"text-xs text-muted-foreground",children:"All time detections"})]})]}),(0,b.jsxs)(d.Card,{children:[(0,b.jsxs)(d.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,b.jsx)(d.CardTitle,{className:"text-sm font-medium",children:"Today"}),(0,b.jsx)(h,{className:"h-4 w-4 text-muted-foreground"})]}),(0,b.jsxs)(d.CardContent,{children:[(0,b.jsx)("div",{className:"text-2xl font-bold",children:g?"...":a?.alerts_today||0}),(0,b.jsx)("p",{className:"text-xs text-muted-foreground",children:"Alerts in last 24h"})]})]}),(0,b.jsxs)(d.Card,{children:[(0,b.jsxs)(d.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,b.jsx)(d.CardTitle,{className:"text-sm font-medium",children:"This Week"}),(0,b.jsx)(i.TrendingUp,{className:"h-4 w-4 text-muted-foreground"})]}),(0,b.jsxs)(d.CardContent,{children:[(0,b.jsx)("div",{className:"text-2xl font-bold",children:g?"...":a?.alerts_this_week||0}),(0,b.jsx)("p",{className:"text-xs text-muted-foreground",children:"Weekly detections"})]})]}),(0,b.jsxs)(d.Card,{children:[(0,b.jsxs)(d.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,b.jsx)(d.CardTitle,{className:"text-sm font-medium",children:"Active Cameras"}),(0,b.jsx)(j.Camera,{className:"h-4 w-4 text-muted-foreground"})]}),(0,b.jsxs)(d.CardContent,{children:[(0,b.jsx)("div",{className:"text-2xl font-bold",children:"1"}),(0,b.jsx)("p",{className:"text-xs text-muted-foreground",children:"Monitoring zones"})]})]})]}),(0,b.jsxs)("div",{className:"grid gap-4 lg:grid-cols-3",children:[(0,b.jsxs)(d.Card,{children:[(0,b.jsx)(d.CardHeader,{children:(0,b.jsx)(d.CardTitle,{children:"Recent Alerts"})}),(0,b.jsx)(d.CardContent,{children:(0,b.jsx)(w,{alerts:l,loading:m})})]}),(0,b.jsxs)(d.Card,{children:[(0,b.jsx)(d.CardHeader,{children:(0,b.jsx)(d.CardTitle,{children:"Detection Summary"})}),(0,b.jsx)(d.CardContent,{children:(0,b.jsx)("div",{className:"space-y-4",children:g?(0,b.jsx)("div",{className:"text-sm text-muted-foreground",children:"Loading..."}):a?.detection_counts&&Object.entries(a.detection_counts).map(([a,c])=>(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsx)("span",{className:"text-sm font-medium capitalize",children:a}),(0,b.jsx)(e.Badge,{variant:"secondary",children:c})]},a))})})]}),(0,b.jsx)("div",{className:"lg:col-span-1",children:(0,b.jsx)(x.CameraFeed,{})})]})]})}}];

//# sourceMappingURL=_e0d3a3dd._.js.map