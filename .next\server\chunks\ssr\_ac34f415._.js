module.exports=[14315,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"}),c.isUnsafeProperty=function(a){return"__proto__"===a}},67984,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"}),c.isDeep<PERSON>ey=function(a){switch(typeof a){case"number":case"symbol":return!1;case"string":return a.includes(".")||a.includes("[")||a.includes("]")}}},21619,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"}),c.toKey=function(a){return"string"==typeof a||"symbol"==typeof a?a:Object.is(a?.valueOf?.(),-0)?"-0":String(a)}},16270,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"}),c.toPath=function(a){let b=[],c=a.length;if(0===c)return b;let d=0,e="",f="",g=!1;for(46===a.charCodeAt(0)&&(b.push(""),d++);d<c;){let h=a[d];f?"\\"===h&&d+1<c?e+=a[++d]:h===f?f="":e+=h:g?'"'===h||"'"===h?f=h:"]"===h?(g=!1,b.push(e),e=""):e+=h:"["===h?(g=!0,e&&(b.push(e),e="")):"."===h?e&&(b.push(e),e=""):e+=h,d++}return e&&b.push(e),b}},64093,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"});let d=a.r(14315),e=a.r(67984),f=a.r(21619),g=a.r(16270);c.get=function a(b,c,h){if(null==b)return h;switch(typeof c){case"string":{if(d.isUnsafeProperty(c))return h;let f=b[c];if(void 0===f)if(e.isDeepKey(c))return a(b,g.toPath(c),h);else return h;return f}case"number":case"symbol":{"number"==typeof c&&(c=f.toKey(c));let a=b[c];if(void 0===a)return h;return a}default:{if(Array.isArray(c)){var i=b,j=c,k=h;if(0===j.length)return k;let a=i;for(let b=0;b<j.length;b++){if(null==a||d.isUnsafeProperty(j[b]))return k;a=a[j[b]]}return void 0===a?k:a}if(c=Object.is(c?.valueOf(),-0)?"-0":String(c),d.isUnsafeProperty(c))return h;let a=b[c];if(void 0===a)return h;return a}}}},88804,(a,b,c)=>{b.exports=a.r(64093).get},67658,(a,b,c)=>{"use strict";var d=a.r(72131),e="function"==typeof Object.is?Object.is:function(a,b){return a===b&&(0!==a||1/a==1/b)||a!=a&&b!=b},f=d.useSyncExternalStore,g=d.useRef,h=d.useEffect,i=d.useMemo,j=d.useDebugValue;c.useSyncExternalStoreWithSelector=function(a,b,c,d,k){var l=g(null);if(null===l.current){var m={hasValue:!1,value:null};l.current=m}else m=l.current;var n=f(a,(l=i(function(){function a(a){if(!h){if(h=!0,f=a,a=d(a),void 0!==k&&m.hasValue){var b=m.value;if(k(b,a))return g=b}return g=a}if(b=g,e(f,a))return b;var c=d(a);return void 0!==k&&k(b,c)?(f=a,b):(f=a,g=c)}var f,g,h=!1,i=void 0===c?null:c;return[function(){return a(b())},null===i?void 0:function(){return a(i())}]},[b,c,d,k]))[0],l[1]);return h(function(){m.hasValue=!0,m.value=n},[n]),j(n),n}},32473,(a,b,c)=>{"use strict";b.exports=a.r(67658)},47349,(a,b,c)=>{"use strict";var d=a.r(72131);"function"==typeof Object.is&&Object.is,d.useState,d.useEffect,d.useLayoutEffect,d.useDebugValue,c.useSyncExternalStore=void 0!==d.useSyncExternalStore?d.useSyncExternalStore:function(a,b){return b()}},87610,(a,b,c)=>{"use strict";b.exports=a.r(47349)},97514,(a,b,c)=>{"use strict";var d=a.r(72131),e=a.r(87610),f="function"==typeof Object.is?Object.is:function(a,b){return a===b&&(0!==a||1/a==1/b)||a!=a&&b!=b},g=e.useSyncExternalStore,h=d.useRef,i=d.useEffect,j=d.useMemo,k=d.useDebugValue;c.useSyncExternalStoreWithSelector=function(a,b,c,d,e){var l=h(null);if(null===l.current){var m={hasValue:!1,value:null};l.current=m}else m=l.current;var n=g(a,(l=j(function(){function a(a){if(!i){if(i=!0,g=a,a=d(a),void 0!==e&&m.hasValue){var b=m.value;if(e(b,a))return h=b}return h=a}if(b=h,f(g,a))return b;var c=d(a);return void 0!==e&&e(b,c)?(g=a,b):(g=a,h=c)}var g,h,i=!1,j=void 0===c?null:c;return[function(){return a(b())},null===j?void 0:function(){return a(j())}]},[b,c,d,e]))[0],l[1]);return i(function(){m.hasValue=!0,m.value=n},[n]),k(n),n}},64442,(a,b,c)=>{"use strict";b.exports=a.r(97514)},70371,(a,b,c)=>{"use strict";function d(a){return"symbol"==typeof a?1:null===a?2:void 0===a?3:4*(a!=a)}Object.defineProperty(c,Symbol.toStringTag,{value:"Module"}),c.compareValues=(a,b,c)=>{if(a!==b){let e=d(a),f=d(b);if(e===f&&0===e){if(a<b)return"desc"===c?1:-1;if(a>b)return"desc"===c?-1:1}return"desc"===c?f-e:e-f}return 0}},62039,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"}),c.isSymbol=function(a){return"symbol"==typeof a||a instanceof Symbol}},71071,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"});let d=a.r(62039),e=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,f=/^\w*$/;c.isKey=function(a,b){return!Array.isArray(a)&&(!!("number"==typeof a||"boolean"==typeof a||null==a||d.isSymbol(a))||"string"==typeof a&&(f.test(a)||!e.test(a))||null!=b&&Object.hasOwn(b,a))}},1611,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"});let d=a.r(70371),e=a.r(71071),f=a.r(16270);c.orderBy=function(a,b,c,g){if(null==a)return[];c=g?void 0:c,Array.isArray(a)||(a=Object.values(a)),Array.isArray(b)||(b=null==b?[null]:[b]),0===b.length&&(b=[null]),Array.isArray(c)||(c=null==c?[]:[c]),c=c.map(a=>String(a));let h=(a,b)=>{let c=a;for(let a=0;a<b.length&&null!=c;++a)c=c[b[a]];return c},i=b.map(a=>(Array.isArray(a)&&1===a.length&&(a=a[0]),null==a||"function"==typeof a||Array.isArray(a)||e.isKey(a))?a:{key:a,path:f.toPath(a)});return a.map(a=>({original:a,criteria:i.map(b=>{var c,d;return c=b,null==(d=a)||null==c?d:"object"==typeof c&&"key"in c?Object.hasOwn(d,c.key)?d[c.key]:h(d,c.path):"function"==typeof c?c(d):Array.isArray(c)?h(d,c):"object"==typeof d?d[c]:d})})).slice().sort((a,b)=>{for(let e=0;e<i.length;e++){let f=d.compareValues(a.criteria[e],b.criteria[e],c[e]);if(0!==f)return f}return 0}).map(a=>a.original)}},52959,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"}),c.flatten=function(a,b=1){let c=[],d=Math.floor(b),e=(a,b)=>{for(let f=0;f<a.length;f++){let g=a[f];Array.isArray(g)&&b<d?e(g,b+1):c.push(g)}};return e(a,0),c}},36762,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"});let d=/^(?:0|[1-9]\d*)$/;c.isIndex=function(a,b=Number.MAX_SAFE_INTEGER){switch(typeof a){case"number":return Number.isInteger(a)&&a>=0&&a<b;case"symbol":return!1;case"string":return d.test(a)}}},42671,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"}),c.isLength=function(a){return Number.isSafeInteger(a)&&a>=0}},53673,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"});let d=a.r(42671);c.isArrayLike=function(a){return null!=a&&"function"!=typeof a&&d.isLength(a.length)}},22700,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"}),c.isObject=function(a){return null!==a&&("object"==typeof a||"function"==typeof a)}},70655,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"}),c.eq=function(a,b){return a===b||Number.isNaN(a)&&Number.isNaN(b)}},18496,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"});let d=a.r(36762),e=a.r(53673),f=a.r(22700),g=a.r(70655);c.isIterateeCall=function(a,b,c){return!!f.isObject(c)&&(!!("number"==typeof b&&e.isArrayLike(c)&&d.isIndex(b))&&b<c.length||"string"==typeof b&&b in c)&&g.eq(c[b],a)}},58311,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"});let d=a.r(1611),e=a.r(52959),f=a.r(18496);c.sortBy=function(a,...b){let c=b.length;return c>1&&f.isIterateeCall(a,b[0],b[1])?b=[]:c>2&&f.isIterateeCall(b[0],b[1],b[2])&&(b=[b[0]]),d.orderBy(a,e.flatten(b),["asc"])}},81101,(a,b,c)=>{b.exports=a.r(58311).sortBy},36131,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"});let d=a.r(62039);c.toNumber=function(a){return d.isSymbol(a)?NaN:Number(a)}},55362,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"});let d=a.r(36131);c.toFinite=function(a){return a?(a=d.toNumber(a))===1/0||a===-1/0?(a<0?-1:1)*Number.MAX_VALUE:a==a?a:0:0===a?a:0}},48127,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"});let d=a.r(18496),e=a.r(55362);c.range=function(a,b,c){c&&"number"!=typeof c&&d.isIterateeCall(a,b,c)&&(b=c=void 0),a=e.toFinite(a),void 0===b?(b=a,a=0):b=e.toFinite(b),c=void 0===c?a<b?1:-1:e.toFinite(c);let f=Math.max(Math.ceil((b-a)/(c||1)),0),g=Array(f);for(let b=0;b<f;b++)g[b]=a,a+=c;return g}},56161,(a,b,c)=>{b.exports=a.r(48127).range},13089,(a,b,c)=>{"use strict";var d="function"==typeof Symbol&&Symbol.for,e=d?Symbol.for("react.element"):60103,f=d?Symbol.for("react.portal"):60106,g=d?Symbol.for("react.fragment"):60107,h=d?Symbol.for("react.strict_mode"):60108,i=d?Symbol.for("react.profiler"):60114,j=d?Symbol.for("react.provider"):60109,k=d?Symbol.for("react.context"):60110,l=d?Symbol.for("react.async_mode"):60111,m=d?Symbol.for("react.concurrent_mode"):60111,n=d?Symbol.for("react.forward_ref"):60112,o=d?Symbol.for("react.suspense"):60113,p=d?Symbol.for("react.suspense_list"):60120,q=d?Symbol.for("react.memo"):60115,r=d?Symbol.for("react.lazy"):60116,s=d?Symbol.for("react.block"):60121,t=d?Symbol.for("react.fundamental"):60117,u=d?Symbol.for("react.responder"):60118,v=d?Symbol.for("react.scope"):60119;function w(a){if("object"==typeof a&&null!==a){var b=a.$$typeof;switch(b){case e:switch(a=a.type){case l:case m:case g:case i:case h:case o:return a;default:switch(a=a&&a.$$typeof){case k:case n:case r:case q:case j:return a;default:return b}}case f:return b}}}function x(a){return w(a)===m}c.AsyncMode=l,c.ConcurrentMode=m,c.ContextConsumer=k,c.ContextProvider=j,c.Element=e,c.ForwardRef=n,c.Fragment=g,c.Lazy=r,c.Memo=q,c.Portal=f,c.Profiler=i,c.StrictMode=h,c.Suspense=o,c.isAsyncMode=function(a){return x(a)||w(a)===l},c.isConcurrentMode=x,c.isContextConsumer=function(a){return w(a)===k},c.isContextProvider=function(a){return w(a)===j},c.isElement=function(a){return"object"==typeof a&&null!==a&&a.$$typeof===e},c.isForwardRef=function(a){return w(a)===n},c.isFragment=function(a){return w(a)===g},c.isLazy=function(a){return w(a)===r},c.isMemo=function(a){return w(a)===q},c.isPortal=function(a){return w(a)===f},c.isProfiler=function(a){return w(a)===i},c.isStrictMode=function(a){return w(a)===h},c.isSuspense=function(a){return w(a)===o},c.isValidElementType=function(a){return"string"==typeof a||"function"==typeof a||a===g||a===m||a===i||a===h||a===o||a===p||"object"==typeof a&&null!==a&&(a.$$typeof===r||a.$$typeof===q||a.$$typeof===j||a.$$typeof===k||a.$$typeof===n||a.$$typeof===t||a.$$typeof===u||a.$$typeof===v||a.$$typeof===s)},c.typeOf=w},66539,(a,b,c)=>{"use strict";b.exports=a.r(13089)},53686,(a,b,c)=>{"use strict";var d=Object.prototype.hasOwnProperty,e="~";function f(){}function g(a,b,c){this.fn=a,this.context=b,this.once=c||!1}function h(a,b,c,d,f){if("function"!=typeof c)throw TypeError("The listener must be a function");var h=new g(c,d||a,f),i=e?e+b:b;return a._events[i]?a._events[i].fn?a._events[i]=[a._events[i],h]:a._events[i].push(h):(a._events[i]=h,a._eventsCount++),a}function i(a,b){0==--a._eventsCount?a._events=new f:delete a._events[b]}function j(){this._events=new f,this._eventsCount=0}Object.create&&(f.prototype=Object.create(null),new f().__proto__||(e=!1)),j.prototype.eventNames=function(){var a,b,c=[];if(0===this._eventsCount)return c;for(b in a=this._events)d.call(a,b)&&c.push(e?b.slice(1):b);return Object.getOwnPropertySymbols?c.concat(Object.getOwnPropertySymbols(a)):c},j.prototype.listeners=function(a){var b=e?e+a:a,c=this._events[b];if(!c)return[];if(c.fn)return[c.fn];for(var d=0,f=c.length,g=Array(f);d<f;d++)g[d]=c[d].fn;return g},j.prototype.listenerCount=function(a){var b=e?e+a:a,c=this._events[b];return c?c.fn?1:c.length:0},j.prototype.emit=function(a,b,c,d,f,g){var h=e?e+a:a;if(!this._events[h])return!1;var i,j,k=this._events[h],l=arguments.length;if(k.fn){switch(k.once&&this.removeListener(a,k.fn,void 0,!0),l){case 1:return k.fn.call(k.context),!0;case 2:return k.fn.call(k.context,b),!0;case 3:return k.fn.call(k.context,b,c),!0;case 4:return k.fn.call(k.context,b,c,d),!0;case 5:return k.fn.call(k.context,b,c,d,f),!0;case 6:return k.fn.call(k.context,b,c,d,f,g),!0}for(j=1,i=Array(l-1);j<l;j++)i[j-1]=arguments[j];k.fn.apply(k.context,i)}else{var m,n=k.length;for(j=0;j<n;j++)switch(k[j].once&&this.removeListener(a,k[j].fn,void 0,!0),l){case 1:k[j].fn.call(k[j].context);break;case 2:k[j].fn.call(k[j].context,b);break;case 3:k[j].fn.call(k[j].context,b,c);break;case 4:k[j].fn.call(k[j].context,b,c,d);break;default:if(!i)for(m=1,i=Array(l-1);m<l;m++)i[m-1]=arguments[m];k[j].fn.apply(k[j].context,i)}}return!0},j.prototype.on=function(a,b,c){return h(this,a,b,c,!1)},j.prototype.once=function(a,b,c){return h(this,a,b,c,!0)},j.prototype.removeListener=function(a,b,c,d){var f=e?e+a:a;if(!this._events[f])return this;if(!b)return i(this,f),this;var g=this._events[f];if(g.fn)g.fn!==b||d&&!g.once||c&&g.context!==c||i(this,f);else{for(var h=0,j=[],k=g.length;h<k;h++)(g[h].fn!==b||d&&!g[h].once||c&&g[h].context!==c)&&j.push(g[h]);j.length?this._events[f]=1===j.length?j[0]:j:i(this,f)}return this},j.prototype.removeAllListeners=function(a){var b;return a?(b=e?e+a:a,this._events[b]&&i(this,b)):(this._events=new f,this._eventsCount=0),this},j.prototype.off=j.prototype.removeListener,j.prototype.addListener=j.prototype.on,j.prefixed=e,j.EventEmitter=j,b.exports=j},40483,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"}),c.last=function(a){return a[a.length-1]}},32969,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"}),c.toArray=function(a){return Array.isArray(a)?a:Array.from(a)}},16642,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"});let d=a.r(40483),e=a.r(32969),f=a.r(53673);c.last=function(a){if(f.isArrayLike(a))return d.last(e.toArray(a))}},78375,(a,b,c)=>{b.exports=a.r(16642).last},81537,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"}),c.isPlainObject=function(a){if("object"!=typeof a||null==a)return!1;if(null===Object.getPrototypeOf(a))return!0;if("[object Object]"!==Object.prototype.toString.call(a)){let b=a[Symbol.toStringTag];return null!=b&&!!Object.getOwnPropertyDescriptor(a,Symbol.toStringTag)?.writable&&a.toString()===`[object ${b}]`}let b=a;for(;null!==Object.getPrototypeOf(b);)b=Object.getPrototypeOf(b);return Object.getPrototypeOf(a)===b}},3068,(a,b,c)=>{b.exports=a.r(81537).isPlainObject},10183,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"}),c.isPlainObject=function(a){if(!a||"object"!=typeof a)return!1;let b=Object.getPrototypeOf(a);return(null===b||b===Object.prototype||null===Object.getPrototypeOf(b))&&"[object Object]"===Object.prototype.toString.call(a)}},81922,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"}),c.getSymbols=function(a){return Object.getOwnPropertySymbols(a).filter(b=>Object.prototype.propertyIsEnumerable.call(a,b))}},71755,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"}),c.getTag=function(a){return null==a?void 0===a?"[object Undefined]":"[object Null]":Object.prototype.toString.call(a)}},39792,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"}),c.argumentsTag="[object Arguments]",c.arrayBufferTag="[object ArrayBuffer]",c.arrayTag="[object Array]",c.bigInt64ArrayTag="[object BigInt64Array]",c.bigUint64ArrayTag="[object BigUint64Array]",c.booleanTag="[object Boolean]",c.dataViewTag="[object DataView]",c.dateTag="[object Date]",c.errorTag="[object Error]",c.float32ArrayTag="[object Float32Array]",c.float64ArrayTag="[object Float64Array]",c.functionTag="[object Function]",c.int16ArrayTag="[object Int16Array]",c.int32ArrayTag="[object Int32Array]",c.int8ArrayTag="[object Int8Array]",c.mapTag="[object Map]",c.numberTag="[object Number]",c.objectTag="[object Object]",c.regexpTag="[object RegExp]",c.setTag="[object Set]",c.stringTag="[object String]",c.symbolTag="[object Symbol]",c.uint16ArrayTag="[object Uint16Array]",c.uint32ArrayTag="[object Uint32Array]",c.uint8ArrayTag="[object Uint8Array]",c.uint8ClampedArrayTag="[object Uint8ClampedArray]"},95266,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"});let d=a.r(10183),e=a.r(81922),f=a.r(71755),g=a.r(39792),h=a.r(70655);c.isEqualWith=function(a,b,c){return function a(b,c,i,j,k,l,m){let n=m(b,c,i,j,k,l);if(void 0!==n)return n;if(typeof b==typeof c)switch(typeof b){case"bigint":case"string":case"boolean":case"symbol":case"undefined":case"function":return b===c;case"number":return b===c||Object.is(b,c)}return function b(c,i,j,k){if(Object.is(c,i))return!0;let l=f.getTag(c),m=f.getTag(i);if(l===g.argumentsTag&&(l=g.objectTag),m===g.argumentsTag&&(m=g.objectTag),l!==m)return!1;switch(l){case g.stringTag:return c.toString()===i.toString();case g.numberTag:{let a=c.valueOf(),b=i.valueOf();return h.eq(a,b)}case g.booleanTag:case g.dateTag:case g.symbolTag:return Object.is(c.valueOf(),i.valueOf());case g.regexpTag:return c.source===i.source&&c.flags===i.flags;case g.functionTag:return c===i}let n=(j=j??new Map).get(c),o=j.get(i);if(null!=n&&null!=o)return n===i;j.set(c,i),j.set(i,c);try{switch(l){case g.mapTag:if(c.size!==i.size)return!1;for(let[b,d]of c.entries())if(!i.has(b)||!a(d,i.get(b),b,c,i,j,k))return!1;return!0;case g.setTag:{if(c.size!==i.size)return!1;let b=Array.from(c.values()),d=Array.from(i.values());for(let e=0;e<b.length;e++){let f=b[e],g=d.findIndex(b=>a(f,b,void 0,c,i,j,k));if(-1===g)return!1;d.splice(g,1)}return!0}case g.arrayTag:case g.uint8ArrayTag:case g.uint8ClampedArrayTag:case g.uint16ArrayTag:case g.uint32ArrayTag:case g.bigUint64ArrayTag:case g.int8ArrayTag:case g.int16ArrayTag:case g.int32ArrayTag:case g.bigInt64ArrayTag:case g.float32ArrayTag:case g.float64ArrayTag:if("undefined"!=typeof Buffer&&Buffer.isBuffer(c)!==Buffer.isBuffer(i)||c.length!==i.length)return!1;for(let b=0;b<c.length;b++)if(!a(c[b],i[b],b,c,i,j,k))return!1;return!0;case g.arrayBufferTag:if(c.byteLength!==i.byteLength)return!1;return b(new Uint8Array(c),new Uint8Array(i),j,k);case g.dataViewTag:if(c.byteLength!==i.byteLength||c.byteOffset!==i.byteOffset)return!1;return b(new Uint8Array(c),new Uint8Array(i),j,k);case g.errorTag:return c.name===i.name&&c.message===i.message;case g.objectTag:{if(!(b(c.constructor,i.constructor,j,k)||d.isPlainObject(c)&&d.isPlainObject(i)))return!1;let f=[...Object.keys(c),...e.getSymbols(c)],g=[...Object.keys(i),...e.getSymbols(i)];if(f.length!==g.length)return!1;for(let b=0;b<f.length;b++){let d=f[b],e=c[d];if(!Object.hasOwn(i,d))return!1;let g=i[d];if(!a(e,g,d,c,i,j,k))return!1}return!0}default:return!1}}finally{j.delete(c),j.delete(i)}}(b,c,l,m)}(a,b,void 0,void 0,void 0,void 0,c)}},60125,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"}),c.noop=function(){}},97842,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"});let d=a.r(95266),e=a.r(60125);c.isEqual=function(a,b){return d.isEqualWith(a,b,e.noop)}},97854,(a,b,c)=>{b.exports=a.r(97842).isEqual},13542,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"}),c.uniqBy=function(a,b){let c=new Map;for(let d=0;d<a.length;d++){let e=a[d],f=b(e);c.has(f)||c.set(f,e)}return Array.from(c.values())}},31540,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"}),c.identity=function(a){return a}},970,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"}),c.isObjectLike=function(a){return"object"==typeof a&&null!==a}},90228,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"});let d=a.r(53673),e=a.r(970);c.isArrayLikeObject=function(a){return e.isObjectLike(a)&&d.isArrayLike(a)}},32100,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"});let d=a.r(64093);c.property=function(a){return function(b){return d.get(b,a)}}},79687,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"}),c.isPrimitive=function(a){return null==a||"object"!=typeof a&&"function"!=typeof a}},71112,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"});let d=a.r(35295),e=a.r(22700),f=a.r(79687),g=a.r(70655);function h(a,b,c,d){if(b===a)return!0;switch(typeof b){case"object":return function(a,b,c,d){if(null==b)return!0;if(Array.isArray(b))return i(a,b,c,d);if(b instanceof Map){var e=a,g=b,h=c,k=d;if(0===g.size)return!0;if(!(e instanceof Map))return!1;for(let[a,b]of g.entries())if(!1===h(e.get(a),b,a,e,g,k))return!1;return!0}if(b instanceof Set)return j(a,b,c,d);let l=Object.keys(b);if(null==a)return 0===l.length;if(0===l.length)return!0;if(d&&d.has(b))return d.get(b)===a;d&&d.set(b,a);try{for(let e=0;e<l.length;e++){let g=l[e];if(!f.isPrimitive(a)&&!(g in a)||void 0===b[g]&&void 0!==a[g]||null===b[g]&&null!==a[g]||!c(a[g],b[g],g,a,b,d))return!1}return!0}finally{d&&d.delete(b)}}(a,b,c,d);case"function":if(Object.keys(b).length>0)return h(a,{...b},c,d);return g.eq(a,b);default:if(!e.isObject(a))return g.eq(a,b);if("string"==typeof b)return""===b;return!0}}function i(a,b,c,d){if(0===b.length)return!0;if(!Array.isArray(a))return!1;let e=new Set;for(let f=0;f<b.length;f++){let g=b[f],h=!1;for(let i=0;i<a.length;i++){if(e.has(i))continue;let j=a[i],k=!1;if(c(j,g,f,a,b,d)&&(k=!0),k){e.add(i),h=!0;break}}if(!h)return!1}return!0}function j(a,b,c,d){return 0===b.size||a instanceof Set&&i([...a],[...b],c,d)}c.isMatchWith=function(a,b,c){return"function"!=typeof c?d.isMatch(a,b):h(a,b,function a(b,d,e,f,g,i){let j=c(b,d,e,f,g,i);return void 0!==j?!!j:h(b,d,a,i)},new Map)},c.isSetMatch=j},35295,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"});let d=a.r(71112);c.isMatch=function(a,b){return d.isMatchWith(a,b,()=>void 0)}},11963,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"}),c.isTypedArray=function(a){return ArrayBuffer.isView(a)&&!(a instanceof DataView)}},18844,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"});let d=a.r(81922),e=a.r(71755),f=a.r(39792),g=a.r(79687),h=a.r(11963);function i(a,b,c,d=new Map,k){let l=k?.(a,b,c,d);if(void 0!==l)return l;if(g.isPrimitive(a))return a;if(d.has(a))return d.get(a);if(Array.isArray(a)){let b=Array(a.length);d.set(a,b);for(let e=0;e<a.length;e++)b[e]=i(a[e],e,c,d,k);return Object.hasOwn(a,"index")&&(b.index=a.index),Object.hasOwn(a,"input")&&(b.input=a.input),b}if(a instanceof Date)return new Date(a.getTime());if(a instanceof RegExp){let b=new RegExp(a.source,a.flags);return b.lastIndex=a.lastIndex,b}if(a instanceof Map){let b=new Map;for(let[e,f]of(d.set(a,b),a))b.set(e,i(f,e,c,d,k));return b}if(a instanceof Set){let b=new Set;for(let e of(d.set(a,b),a))b.add(i(e,void 0,c,d,k));return b}if("undefined"!=typeof Buffer&&Buffer.isBuffer(a))return a.subarray();if(h.isTypedArray(a)){let b=new(Object.getPrototypeOf(a)).constructor(a.length);d.set(a,b);for(let e=0;e<a.length;e++)b[e]=i(a[e],e,c,d,k);return b}if(a instanceof ArrayBuffer||"undefined"!=typeof SharedArrayBuffer&&a instanceof SharedArrayBuffer)return a.slice(0);if(a instanceof DataView){let b=new DataView(a.buffer.slice(0),a.byteOffset,a.byteLength);return d.set(a,b),j(b,a,c,d,k),b}if("undefined"!=typeof File&&a instanceof File){let b=new File([a],a.name,{type:a.type});return d.set(a,b),j(b,a,c,d,k),b}if(a instanceof Blob){let b=new Blob([a],{type:a.type});return d.set(a,b),j(b,a,c,d,k),b}if(a instanceof Error){let b=new a.constructor;return d.set(a,b),b.message=a.message,b.name=a.name,b.stack=a.stack,b.cause=a.cause,j(b,a,c,d,k),b}if("object"==typeof a&&function(a){switch(e.getTag(a)){case f.argumentsTag:case f.arrayTag:case f.arrayBufferTag:case f.dataViewTag:case f.booleanTag:case f.dateTag:case f.float32ArrayTag:case f.float64ArrayTag:case f.int8ArrayTag:case f.int16ArrayTag:case f.int32ArrayTag:case f.mapTag:case f.numberTag:case f.objectTag:case f.regexpTag:case f.setTag:case f.stringTag:case f.symbolTag:case f.uint8ArrayTag:case f.uint8ClampedArrayTag:case f.uint16ArrayTag:case f.uint32ArrayTag:return!0;default:return!1}}(a)){let b=Object.create(Object.getPrototypeOf(a));return d.set(a,b),j(b,a,c,d,k),b}return a}function j(a,b,c=a,e,f){let g=[...Object.keys(b),...d.getSymbols(b)];for(let d=0;d<g.length;d++){let h=g[d],j=Object.getOwnPropertyDescriptor(a,h);(null==j||j.writable)&&(a[h]=i(b[h],h,c,e,f))}}c.cloneDeepWith=function(a,b){return i(a,void 0,a,new Map,b)},c.cloneDeepWithImpl=i,c.copyProperties=j},12188,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"});let d=a.r(18844);c.cloneDeep=function(a){return d.cloneDeepWithImpl(a,void 0,a,new Map,void 0)}},45794,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"});let d=a.r(35295),e=a.r(12188);c.matches=function(a){return a=e.cloneDeep(a),b=>d.isMatch(b,a)}},84955,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"});let d=a.r(18844),e=a.r(39792);c.cloneDeepWith=function(a,b){return d.cloneDeepWith(a,(c,f,g,h)=>{let i=b?.(c,f,g,h);if(void 0!==i)return i;if("object"==typeof a)switch(Object.prototype.toString.call(a)){case e.numberTag:case e.stringTag:case e.booleanTag:{let b=new a.constructor(a?.valueOf());return d.copyProperties(b,a),b}case e.argumentsTag:{let b={};return d.copyProperties(b,a),b.length=a.length,b[Symbol.iterator]=a[Symbol.iterator],b}default:return}})}},37595,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"});let d=a.r(84955);c.cloneDeep=function(a){return d.cloneDeepWith(a)}},21864,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"});let d=a.r(71755);c.isArguments=function(a){return null!==a&&"object"==typeof a&&"[object Arguments]"===d.getTag(a)}},70767,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"});let d=a.r(67984),e=a.r(36762),f=a.r(21864),g=a.r(16270);c.has=function(a,b){let c;if(0===(c=Array.isArray(b)?b:"string"==typeof b&&d.isDeepKey(b)&&a?.[b]==null?g.toPath(b):[b]).length)return!1;let h=a;for(let a=0;a<c.length;a++){let b=c[a];if((null==h||!Object.hasOwn(h,b))&&!((Array.isArray(h)||f.isArguments(h))&&e.isIndex(b)&&b<h.length))return!1;h=h[b]}return!0}},31544,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"});let d=a.r(35295),e=a.r(21619),f=a.r(37595),g=a.r(64093),h=a.r(70767);c.matchesProperty=function(a,b){switch(typeof a){case"object":Object.is(a?.valueOf(),-0)&&(a="-0");break;case"number":a=e.toKey(a)}return b=f.cloneDeep(b),function(c){let e=g.get(c,a);return void 0===e?h.has(c,a):void 0===b?void 0===e:d.isMatch(e,b)}}},59111,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"});let d=a.r(31540),e=a.r(32100),f=a.r(45794),g=a.r(31544);c.iteratee=function(a){if(null==a)return d.identity;switch(typeof a){case"function":return a;case"object":if(Array.isArray(a)&&2===a.length)return g.matchesProperty(a[0],a[1]);return f.matches(a);case"string":case"symbol":case"number":return e.property(a)}}},60066,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"});let d=a.r(13542),e=a.r(31540),f=a.r(90228),g=a.r(59111);c.uniqBy=function(a,b=e.identity){return f.isArrayLikeObject(a)?d.uniqBy(Array.from(a),g.iteratee(b)):[]}},53451,(a,b,c)=>{b.exports=a.r(60066).uniqBy},70257,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"}),c.debounce=function(a,b,{signal:c,edges:d}={}){let e,f=null,g=null!=d&&d.includes("leading"),h=null==d||d.includes("trailing"),i=()=>{null!==f&&(a.apply(e,f),e=void 0,f=null)},j=null,k=()=>{null!=j&&clearTimeout(j),j=setTimeout(()=>{j=null,h&&i(),l()},b)},l=()=>{null!==j&&(clearTimeout(j),j=null),e=void 0,f=null},m=function(...a){if(c?.aborted)return;e=this,f=a;let b=null==j;k(),g&&b&&i()};return m.schedule=k,m.cancel=l,m.flush=()=>{i()},c?.addEventListener("abort",l,{once:!0}),m}},52844,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"});let d=a.r(70257);c.debounce=function(a,b=0,c={}){let e;"object"!=typeof c&&(c={});let{leading:f=!1,trailing:g=!0,maxWait:h}=c,i=[,,];f&&(i[0]="leading"),g&&(i[1]="trailing");let j=null,k=d.debounce(function(...b){e=a.apply(this,b),j=null},b,{edges:i}),l=function(...b){return null!=h&&(null===j&&(j=Date.now()),Date.now()-j>=h)?(e=a.apply(this,b),j=Date.now(),k.cancel(),k.schedule(),e):(k.apply(this,b),e)};return l.cancel=k.cancel,l.flush=()=>(k.flush(),e),l}},4855,(a,b,c)=>{"use strict";Object.defineProperty(c,Symbol.toStringTag,{value:"Module"});let d=a.r(52844);c.throttle=function(a,b=0,c={}){let{leading:e=!0,trailing:f=!0}=c;return d.debounce(a,b,{leading:e,maxWait:b,trailing:f})}},61349,(a,b,c)=>{b.exports=a.r(4855).throttle},84267,a=>{"use strict";a.s(["AnalyticsCharts",()=>tw],84267);var b,c=a.i(87924),d=a.i(33217),e=a.i(91119),f=a.i(72131),g=Symbol.for("immer-nothing"),h=Symbol.for("immer-draftable"),i=Symbol.for("immer-state");function j(a){throw Error(`[Immer] minified error nr: ${a}. Full error at: https://bit.ly/3cXEKWf`)}var k=Object.getPrototypeOf;function l(a){return!!a&&!!a[i]}function m(a){return!!a&&(o(a)||Array.isArray(a)||!!a[h]||!!a.constructor?.[h]||t(a)||u(a))}var n=Object.prototype.constructor.toString();function o(a){if(!a||"object"!=typeof a)return!1;let b=k(a);if(null===b)return!0;let c=Object.hasOwnProperty.call(b,"constructor")&&b.constructor;return c===Object||"function"==typeof c&&Function.toString.call(c)===n}function p(a,b){0===q(a)?Reflect.ownKeys(a).forEach(c=>{b(c,a[c],a)}):a.forEach((c,d)=>b(d,c,a))}function q(a){let b=a[i];return b?b.type_:Array.isArray(a)?1:t(a)?2:3*!!u(a)}function r(a,b){return 2===q(a)?a.has(b):Object.prototype.hasOwnProperty.call(a,b)}function s(a,b,c){let d=q(a);2===d?a.set(b,c):3===d?a.add(c):a[b]=c}function t(a){return a instanceof Map}function u(a){return a instanceof Set}function v(a){return a.copy_||a.base_}function w(a,b){if(t(a))return new Map(a);if(u(a))return new Set(a);if(Array.isArray(a))return Array.prototype.slice.call(a);let c=o(a);if(!0!==b&&("class_only"!==b||c)){let b=k(a);return null!==b&&c?{...a}:Object.assign(Object.create(b),a)}{let b=Object.getOwnPropertyDescriptors(a);delete b[i];let c=Reflect.ownKeys(b);for(let d=0;d<c.length;d++){let e=c[d],f=b[e];!1===f.writable&&(f.writable=!0,f.configurable=!0),(f.get||f.set)&&(b[e]={configurable:!0,writable:!0,enumerable:f.enumerable,value:a[e]})}return Object.create(k(a),b)}}function x(a,b=!1){return z(a)||l(a)||!m(a)||(q(a)>1&&(a.set=a.add=a.clear=a.delete=y),Object.freeze(a),b&&Object.entries(a).forEach(([a,b])=>x(b,!0))),a}function y(){j(2)}function z(a){return Object.isFrozen(a)}var A={};function B(a){let b=A[a];return b||j(0,a),b}function C(a,b){b&&(B("Patches"),a.patches_=[],a.inversePatches_=[],a.patchListener_=b)}function D(a){E(a),a.drafts_.forEach(G),a.drafts_=null}function E(a){a===gY&&(gY=a.parent_)}function F(a){return gY={drafts_:[],parent_:gY,immer_:a,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function G(a){let b=a[i];0===b.type_||1===b.type_?b.revoke_():b.revoked_=!0}function H(a,b){b.unfinalizedDrafts_=b.drafts_.length;let c=b.drafts_[0];return void 0!==a&&a!==c?(c[i].modified_&&(D(b),j(4)),m(a)&&(a=I(b,a),b.parent_||K(b,a)),b.patches_&&B("Patches").generateReplacementPatches_(c[i].base_,a,b.patches_,b.inversePatches_)):a=I(b,c,[]),D(b),b.patches_&&b.patchListener_(b.patches_,b.inversePatches_),a!==g?a:void 0}function I(a,b,c){if(z(b))return b;let d=b[i];if(!d)return p(b,(e,f)=>J(a,d,b,e,f,c)),b;if(d.scope_!==a)return b;if(!d.modified_)return K(a,d.base_,!0),d.base_;if(!d.finalized_){d.finalized_=!0,d.scope_.unfinalizedDrafts_--;let b=d.copy_,e=b,f=!1;3===d.type_&&(e=new Set(b),b.clear(),f=!0),p(e,(e,g)=>J(a,d,b,e,g,c,f)),K(a,b,!1),c&&a.patches_&&B("Patches").generatePatches_(d,c,a.patches_,a.inversePatches_)}return d.copy_}function J(a,b,c,d,e,f,g){if(l(e)){let g=I(a,e,f&&b&&3!==b.type_&&!r(b.assigned_,d)?f.concat(d):void 0);if(s(c,d,g),!l(g))return;a.canAutoFreeze_=!1}else g&&c.add(e);if(m(e)&&!z(e)){if(!a.immer_.autoFreeze_&&a.unfinalizedDrafts_<1)return;I(a,e),(!b||!b.scope_.parent_)&&"symbol"!=typeof d&&Object.prototype.propertyIsEnumerable.call(c,d)&&K(a,e)}}function K(a,b,c=!1){!a.parent_&&a.immer_.autoFreeze_&&a.canAutoFreeze_&&x(b,c)}var L={get(a,b){if(b===i)return a;let c=v(a);if(!r(c,b)){var d=a,e=c,f=b;let g=O(e,f);return g?"value"in g?g.value:g.get?.call(d.draft_):void 0}let g=c[b];return a.finalized_||!m(g)?g:g===N(a.base_,b)?(Q(a),a.copy_[b]=R(g,a)):g},has:(a,b)=>b in v(a),ownKeys:a=>Reflect.ownKeys(v(a)),set(a,b,c){let d=O(v(a),b);if(d?.set)return d.set.call(a.draft_,c),!0;if(!a.modified_){let d=N(v(a),b),e=d?.[i];if(e&&e.base_===c)return a.copy_[b]=c,a.assigned_[b]=!1,!0;if((c===d?0!==c||1/c==1/d:c!=c&&d!=d)&&(void 0!==c||r(a.base_,b)))return!0;Q(a),P(a)}return!!(a.copy_[b]===c&&(void 0!==c||b in a.copy_)||Number.isNaN(c)&&Number.isNaN(a.copy_[b]))||(a.copy_[b]=c,a.assigned_[b]=!0,!0)},deleteProperty:(a,b)=>(void 0!==N(a.base_,b)||b in a.base_?(a.assigned_[b]=!1,Q(a),P(a)):delete a.assigned_[b],a.copy_&&delete a.copy_[b],!0),getOwnPropertyDescriptor(a,b){let c=v(a),d=Reflect.getOwnPropertyDescriptor(c,b);return d?{writable:!0,configurable:1!==a.type_||"length"!==b,enumerable:d.enumerable,value:c[b]}:d},defineProperty(){j(11)},getPrototypeOf:a=>k(a.base_),setPrototypeOf(){j(12)}},M={};function N(a,b){let c=a[i];return(c?v(c):a)[b]}function O(a,b){if(!(b in a))return;let c=k(a);for(;c;){let a=Object.getOwnPropertyDescriptor(c,b);if(a)return a;c=k(c)}}function P(a){!a.modified_&&(a.modified_=!0,a.parent_&&P(a.parent_))}function Q(a){a.copy_||(a.copy_=w(a.base_,a.scope_.immer_.useStrictShallowCopy_))}function R(a,b){let c=t(a)?B("MapSet").proxyMap_(a,b):u(a)?B("MapSet").proxySet_(a,b):function(a,b){let c=Array.isArray(a),d={type_:+!!c,scope_:b?b.scope_:gY,modified_:!1,finalized_:!1,assigned_:{},parent_:b,base_:a,draft_:null,copy_:null,revoke_:null,isManual_:!1},e=d,f=L;c&&(e=[d],f=M);let{revoke:g,proxy:h}=Proxy.revocable(e,f);return d.draft_=h,d.revoke_=g,h}(a,b);return(b?b.scope_:gY).drafts_.push(c),c}function S(a){return l(a)||j(10,a),function a(b){let c;if(!m(b)||z(b))return b;let d=b[i];if(d){if(!d.modified_)return d.base_;d.finalized_=!0,c=w(b,d.scope_.immer_.useStrictShallowCopy_)}else c=w(b,!0);return p(c,(b,d)=>{s(c,b,a(d))}),d&&(d.finalized_=!1),c}(a)}p(L,(a,b)=>{M[a]=function(){return arguments[0]=arguments[0][0],b.apply(this,arguments)}}),M.deleteProperty=function(a,b){return M.set.call(this,a,b,void 0)},M.set=function(a,b,c){return L.set.call(this,a[0],b,c,a[0])};var T=new class{constructor(a){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(a,b,c)=>{let d;if("function"==typeof a&&"function"!=typeof b){let c=b;b=a;let d=this;return function(a=c,...e){return d.produce(a,a=>b.call(this,a,...e))}}if("function"!=typeof b&&j(6),void 0!==c&&"function"!=typeof c&&j(7),m(a)){let e=F(this),f=R(a,void 0),g=!0;try{d=b(f),g=!1}finally{g?D(e):E(e)}return C(e,c),H(d,e)}if(a&&"object"==typeof a)j(1,a);else{if(void 0===(d=b(a))&&(d=a),d===g&&(d=void 0),this.autoFreeze_&&x(d,!0),c){let b=[],e=[];B("Patches").generateReplacementPatches_(a,d,b,e),c(b,e)}return d}},this.produceWithPatches=(a,b)=>{let c,d;return"function"==typeof a?(b,...c)=>this.produceWithPatches(b,b=>a(b,...c)):[this.produce(a,b,(a,b)=>{c=a,d=b}),c,d]},"boolean"==typeof a?.autoFreeze&&this.setAutoFreeze(a.autoFreeze),"boolean"==typeof a?.useStrictShallowCopy&&this.setUseStrictShallowCopy(a.useStrictShallowCopy)}createDraft(a){m(a)||j(8),l(a)&&(a=S(a));let b=F(this),c=R(a,void 0);return c[i].isManual_=!0,E(b),c}finishDraft(a,b){let c=a&&a[i];c&&c.isManual_||j(9);let{scope_:d}=c;return C(d,b),H(void 0,d)}setAutoFreeze(a){this.autoFreeze_=a}setUseStrictShallowCopy(a){this.useStrictShallowCopy_=a}applyPatches(a,b){let c;for(c=b.length-1;c>=0;c--){let d=b[c];if(0===d.path.length&&"replace"===d.op){a=d.value;break}}c>-1&&(b=b.slice(c+1));let d=B("Patches").applyPatches_;return l(a)?d(a,b):this.produce(a,a=>d(a,b))}},U=T.produce;T.produceWithPatches.bind(T),T.setAutoFreeze.bind(T),T.setUseStrictShallowCopy.bind(T),T.applyPatches.bind(T),T.createDraft.bind(T),T.finishDraft.bind(T);var V=a=>Array.isArray(a)?a:[a],W=0,X=class{revision=W;_value;_lastValue;_isEqual=Y;constructor(a,b=Y){this._value=this._lastValue=a,this._isEqual=b}get value(){return this._value}set value(a){this.value!==a&&(this._value=a,this.revision=++W)}};function Y(a,b){return a===b}function Z(a){return a instanceof X||console.warn("Not a valid cell! ",a),a.value}var $=(a,b)=>!1;function _(){return function(a,b=Y){return new X(null,b)}(0,$)}var aa=a=>{let b=a.collectionTag;null===b&&(b=a.collectionTag=_()),Z(b)};Symbol();var ab=0,ac=Object.getPrototypeOf({}),ad=class{constructor(a){this.value=a,this.value=a,this.tag.value=a}proxy=new Proxy(this,ae);tag=_();tags={};children={};collectionTag=null;id=ab++},ae={get:(a,b)=>(function(){let{value:c}=a,d=Reflect.get(c,b);if("symbol"==typeof b||b in ac)return d;if("object"==typeof d&&null!==d){var e;let c=a.children[b];return void 0===c&&(c=a.children[b]=Array.isArray(e=d)?new af(e):new ad(e)),c.tag&&Z(c.tag),c.proxy}{let c=a.tags[b];return void 0===c&&((c=a.tags[b]=_()).value=d),Z(c),d}})(),ownKeys:a=>(aa(a),Reflect.ownKeys(a.value)),getOwnPropertyDescriptor:(a,b)=>Reflect.getOwnPropertyDescriptor(a.value,b),has:(a,b)=>Reflect.has(a.value,b)},af=class{constructor(a){this.value=a,this.value=a,this.tag.value=a}proxy=new Proxy([this],ag);tag=_();tags={};children={};collectionTag=null;id=ab++},ag={get:([a],b)=>("length"===b&&aa(a),ae.get(a,b)),ownKeys:([a])=>ae.ownKeys(a),getOwnPropertyDescriptor:([a],b)=>ae.getOwnPropertyDescriptor(a,b),has:([a],b)=>ae.has(a,b)},ah="undefined"!=typeof WeakRef?WeakRef:class{constructor(a){this.value=a}deref(){return this.value}};function ai(){return{s:0,v:void 0,o:null,p:null}}function aj(a,b={}){let c,d=ai(),{resultEqualityCheck:e}=b,f=0;function g(){let b,g=d,{length:h}=arguments;for(let a=0;a<h;a++){let b=arguments[a];if("function"==typeof b||"object"==typeof b&&null!==b){let a=g.o;null===a&&(g.o=a=new WeakMap);let c=a.get(b);void 0===c?(g=ai(),a.set(b,g)):g=c}else{let a=g.p;null===a&&(g.p=a=new Map);let c=a.get(b);void 0===c?(g=ai(),a.set(b,g)):g=c}}let i=g;if(1===g.s)b=g.v;else if(b=a.apply(null,arguments),f++,e){let a=c?.deref?.()??c;null!=a&&e(a,b)&&(b=a,0!==f&&f--),c="object"==typeof b&&null!==b||"function"==typeof b?new ah(b):b}return i.s=1,i.v=b,b}return g.clearCache=()=>{d=ai(),g.resetResultsCount()},g.resultsCount=()=>f,g.resetResultsCount=()=>{f=0},g}var ak=function(a,...b){let c="function"==typeof a?{memoize:a,memoizeOptions:b}:a,d=(...a)=>{let b,d=0,e=0,f={},g=a.pop();"object"==typeof g&&(f=g,g=a.pop()),function(a,b=`expected a function, instead received ${typeof a}`){if("function"!=typeof a)throw TypeError(b)}(g,`createSelector expects an output function after the inputs, but received: [${typeof g}]`);let{memoize:h,memoizeOptions:i=[],argsMemoize:j=aj,argsMemoizeOptions:k=[],devModeChecks:l={}}={...c,...f},m=V(i),n=V(k),o=function(a){let b=Array.isArray(a[0])?a[0]:a;return!function(a,b="expected all items to be functions, instead received the following types: "){if(!a.every(a=>"function"==typeof a)){let c=a.map(a=>"function"==typeof a?`function ${a.name||"unnamed"}()`:typeof a).join(", ");throw TypeError(`${b}[${c}]`)}}(b,"createSelector expects all input-selectors to be functions, but received the following types: "),b}(a),p=h(function(){return d++,g.apply(null,arguments)},...m);return Object.assign(j(function(){e++;let a=function(a,b){let c=[],{length:d}=a;for(let e=0;e<d;e++)c.push(a[e].apply(null,b));return c}(o,arguments);return b=p.apply(null,a)},...n),{resultFunc:g,memoizedResultFunc:p,dependencies:o,dependencyRecomputations:()=>e,resetDependencyRecomputations:()=>{e=0},lastResult:()=>b,recomputations:()=>d,resetRecomputations:()=>{d=0},memoize:h,argsMemoize:j})};return Object.assign(d,{withTypes:()=>d}),d}(aj),al=Object.assign((a,b=ak)=>{!function(a,b=`expected an object, instead received ${typeof a}`){if("object"!=typeof a)throw TypeError(b)}(a,`createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof a}`);let c=Object.keys(a);return b(c.map(b=>a[b]),(...a)=>a.reduce((a,b,d)=>(a[c[d]]=b,a),{}))},{withTypes:()=>al});function am(a){return`Minified Redux error #${a}; visit https://redux.js.org/Errors?code=${a} for the full message or use the non-minified dev environment for full errors. `}var an="function"==typeof Symbol&&Symbol.observable||"@@observable",ao=()=>Math.random().toString(36).substring(7).split("").join("."),ap={INIT:`@@redux/INIT${ao()}`,REPLACE:`@@redux/REPLACE${ao()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${ao()}`};function aq(a){if("object"!=typeof a||null===a)return!1;let b=a;for(;null!==Object.getPrototypeOf(b);)b=Object.getPrototypeOf(b);return Object.getPrototypeOf(a)===b||null===Object.getPrototypeOf(a)}function ar(a){let b,c=Object.keys(a),d={};for(let b=0;b<c.length;b++){let e=c[b];"function"==typeof a[e]&&(d[e]=a[e])}let e=Object.keys(d);try{Object.keys(d).forEach(a=>{let b=d[a];if(void 0===b(void 0,{type:ap.INIT}))throw Error(am(12));if(void 0===b(void 0,{type:ap.PROBE_UNKNOWN_ACTION()}))throw Error(am(13))})}catch(a){b=a}return function(a={},c){if(b)throw b;let f=!1,g={};for(let b=0;b<e.length;b++){let h=e[b],i=d[h],j=a[h],k=i(j,c);if(void 0===k)throw c&&c.type,Error(am(14));g[h]=k,f=f||k!==j}return(f=f||e.length!==Object.keys(a).length)?g:a}}function as(...a){return 0===a.length?a=>a:1===a.length?a[0]:a.reduce((a,b)=>(...c)=>a(b(...c)))}function at(a){return aq(a)&&"type"in a&&"string"==typeof a.type}function au(a){return({dispatch:b,getState:c})=>d=>e=>"function"==typeof e?e(b,c,a):d(e)}var av=au(),aw=function(){if(0!=arguments.length)return"object"==typeof arguments[0]?as:as.apply(null,arguments)};function ax(a,b){function c(...d){if(b){let c=b(...d);if(!c)throw Error(ba(0));return{type:a,payload:c.payload,..."meta"in c&&{meta:c.meta},..."error"in c&&{error:c.error}}}return{type:a,payload:d[0]}}return c.toString=()=>`${a}`,c.type=a,c.match=b=>at(b)&&b.type===a,c}var ay=class a extends Array{constructor(...b){super(...b),Object.setPrototypeOf(this,a.prototype)}static get[Symbol.species](){return a}concat(...a){return super.concat.apply(this,a)}prepend(...b){return 1===b.length&&Array.isArray(b[0])?new a(...b[0].concat(this)):new a(...b.concat(this))}};function az(a){return m(a)?U(a,()=>{}):a}function aA(a,b,c){return a.has(b)?a.get(b):a.set(b,c(b)).get(b)}var aB=a=>b=>{setTimeout(b,a)};function aC(a){let b,c={},d=[],e={addCase(a,b){let d="string"==typeof a?a:a.type;if(!d)throw Error(ba(28));if(d in c)throw Error(ba(29));return c[d]=b,e},addMatcher:(a,b)=>(d.push({matcher:a,reducer:b}),e),addDefaultCase:a=>(b=a,e)};return a(e),[c,d,b]}var aD=Symbol.for("rtk-slice-createasyncthunk"),aE=(a=>(a.reducer="reducer",a.reducerWithPrepare="reducerWithPrepare",a.asyncThunk="asyncThunk",a))(aE||{}),aF=function({creators:a}={}){let b=a?.asyncThunk?.[aD];return function(a){let c,{name:d,reducerPath:e=d}=a;if(!d)throw Error(ba(11));let f=("function"==typeof a.reducers?a.reducers(function(){function a(a,b){return{_reducerDefinitionType:"asyncThunk",payloadCreator:a,...b}}return a.withTypes=()=>a,{reducer:a=>Object.assign({[a.name]:(...b)=>a(...b)}[a.name],{_reducerDefinitionType:"reducer"}),preparedReducer:(a,b)=>({_reducerDefinitionType:"reducerWithPrepare",prepare:a,reducer:b}),asyncThunk:a}}()):a.reducers)||{},g=Object.keys(f),h={},i={},j={},k=[],n={addCase(a,b){let c="string"==typeof a?a:a.type;if(!c)throw Error(ba(12));if(c in i)throw Error(ba(13));return i[c]=b,n},addMatcher:(a,b)=>(k.push({matcher:a,reducer:b}),n),exposeAction:(a,b)=>(j[a]=b,n),exposeCaseReducer:(a,b)=>(h[a]=b,n)};function o(){let[b={},c=[],d]="function"==typeof a.extraReducers?aC(a.extraReducers):[a.extraReducers],e={...b,...i};return function(a,b){let c,[d,e,f]=aC(b);if("function"==typeof a)c=()=>az(a());else{let b=az(a);c=()=>b}function g(a=c(),b){let h=[d[b.type],...e.filter(({matcher:a})=>a(b)).map(({reducer:a})=>a)];return 0===h.filter(a=>!!a).length&&(h=[f]),h.reduce((a,c)=>{if(c)if(l(a)){let d=c(a,b);return void 0===d?a:d}else{if(m(a))return U(a,a=>c(a,b));let d=c(a,b);if(void 0===d){if(null===a)return a;throw Error("A case reducer on a non-draftable value must not return undefined")}return d}return a},a)}return g.getInitialState=c,g}(a.initialState,a=>{for(let b in e)a.addCase(b,e[b]);for(let b of k)a.addMatcher(b.matcher,b.reducer);for(let b of c)a.addMatcher(b.matcher,b.reducer);d&&a.addDefaultCase(d)})}g.forEach(c=>{let e=f[c],g={reducerName:c,type:`${d}/${c}`,createNotation:"function"==typeof a.reducers};"asyncThunk"===e._reducerDefinitionType?function({type:a,reducerName:b},c,d,e){if(!e)throw Error(ba(18));let{payloadCreator:f,fulfilled:g,pending:h,rejected:i,settled:j,options:k}=c,l=e(a,f,k);d.exposeAction(b,l),g&&d.addCase(l.fulfilled,g),h&&d.addCase(l.pending,h),i&&d.addCase(l.rejected,i),j&&d.addMatcher(l.settled,j),d.exposeCaseReducer(b,{fulfilled:g||aG,pending:h||aG,rejected:i||aG,settled:j||aG})}(g,e,n,b):function({type:a,reducerName:b,createNotation:c},d,e){let f,g;if("reducer"in d){if(c&&"reducerWithPrepare"!==d._reducerDefinitionType)throw Error(ba(17));f=d.reducer,g=d.prepare}else f=d;e.addCase(a,f).exposeCaseReducer(b,f).exposeAction(b,g?ax(a,g):ax(a))}(g,e,n)});let p=a=>a,q=new Map,r=new WeakMap;function s(a,b){return c||(c=o()),c(a,b)}function t(){return c||(c=o()),c.getInitialState()}function u(b,c=!1){function d(a){let e=a[b];return void 0===e&&c&&(e=aA(r,d,t)),e}function e(b=p){let d=aA(q,c,()=>new WeakMap);return aA(d,b,()=>{let d={};for(let[e,f]of Object.entries(a.selectors??{}))d[e]=function(a,b,c,d){function e(f,...g){let h=b(f);return void 0===h&&d&&(h=c()),a(h,...g)}return e.unwrapped=a,e}(f,b,()=>aA(r,b,t),c);return d})}return{reducerPath:b,getSelectors:e,get selectors(){return e(d)},selectSlice:d}}let v={name:d,reducer:s,actions:j,caseReducers:h,getInitialState:t,...u(e),injectInto(a,{reducerPath:b,...c}={}){let d=b??e;return a.inject({reducerPath:d,reducer:s},c),{...v,...u(d,!0)}}};return v}}();function aG(){}var aH="listener",aI="completed",aJ="cancelled",aK=`task-${aJ}`,aL=`task-${aI}`,aM=`${aH}-${aJ}`,aN=`${aH}-${aI}`,aO=class{constructor(a){this.code=a,this.message=`task ${aJ} (reason: ${a})`}name="TaskAbortError";message},aP=(a,b)=>{if("function"!=typeof a)throw TypeError(ba(32))},aQ=()=>{},aR=(a,b=aQ)=>(a.catch(b),a),aS=(a,b)=>(a.addEventListener("abort",b,{once:!0}),()=>a.removeEventListener("abort",b)),aT=(a,b)=>{let c=a.signal;c.aborted||("reason"in c||Object.defineProperty(c,"reason",{enumerable:!0,value:b,configurable:!0,writable:!0}),a.abort(b))},aU=a=>{if(a.aborted){let{reason:b}=a;throw new aO(b)}};function aV(a,b){let c=aQ;return new Promise((d,e)=>{let f=()=>e(new aO(a.reason));if(a.aborted)return void f();c=aS(a,f),b.finally(()=>c()).then(d,e)}).finally(()=>{c=aQ})}var aW=async(a,b)=>{try{await Promise.resolve();let b=await a();return{status:"ok",value:b}}catch(a){return{status:a instanceof aO?"cancelled":"rejected",error:a}}finally{b?.()}},aX=a=>b=>aR(aV(a,b).then(b=>(aU(a),b))),aY=a=>{let b=aX(a);return a=>b(new Promise(b=>setTimeout(b,a)))},{assign:aZ}=Object,a$={},a_="listenerMiddleware",a0=a=>{let{type:b,actionCreator:c,matcher:d,predicate:e,effect:f}=a;if(b)e=ax(b).match;else if(c)b=c.type,e=c.match;else if(d)e=d;else if(e);else throw Error(ba(21));return aP(f,"options.listener"),{predicate:e,type:b,effect:f}},a1=aZ(a=>{let{type:b,predicate:c,effect:d}=a0(a);return{id:((a=21)=>{let b="",c=a;for(;c--;)b+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return b})(),effect:d,type:b,predicate:c,pending:new Set,unsubscribe:()=>{throw Error(ba(22))}}},{withTypes:()=>a1}),a2=(a,b)=>{let{type:c,effect:d,predicate:e}=a0(b);return Array.from(a.values()).find(a=>("string"==typeof c?a.type===c:a.predicate===e)&&a.effect===d)},a3=a=>{a.pending.forEach(a=>{aT(a,aM)})},a4=(a,b,c)=>{try{a(b,c)}catch(a){setTimeout(()=>{throw a},0)}},a5=aZ(ax(`${a_}/add`),{withTypes:()=>a5}),a6=ax(`${a_}/removeAll`),a7=aZ(ax(`${a_}/remove`),{withTypes:()=>a7}),a8=(...a)=>{console.error(`${a_}/error`,...a)},a9=(a={})=>{let b=new Map,{extra:c,onError:d=a8}=a;aP(d,"onError");let e=a=>(a=>(a.unsubscribe=()=>b.delete(a.id),b.set(a.id,a),b=>{a.unsubscribe(),b?.cancelActive&&a3(a)}))(a2(b,a)??a1(a));aZ(e,{withTypes:()=>e});let f=a=>{let c=a2(b,a);return c&&(c.unsubscribe(),a.cancelActive&&a3(c)),!!c};aZ(f,{withTypes:()=>f});let g=async(a,f,g,h)=>{let i=new AbortController,j=((a,b)=>{let c=async(c,d)=>{aU(b);let e=()=>{},f=[new Promise((b,d)=>{let f=a({predicate:c,effect:(a,c)=>{c.unsubscribe(),b([a,c.getState(),c.getOriginalState()])}});e=()=>{f(),d()}})];null!=d&&f.push(new Promise(a=>setTimeout(a,d,null)));try{let a=await aV(b,Promise.race(f));return aU(b),a}finally{e()}};return(a,b)=>aR(c(a,b))})(e,i.signal),k=[];try{a.pending.add(i),await Promise.resolve(a.effect(f,aZ({},g,{getOriginalState:h,condition:(a,b)=>j(a,b).then(Boolean),take:j,delay:aY(i.signal),pause:aX(i.signal),extra:c,signal:i.signal,fork:((a,b)=>(c,d)=>{aP(c,"taskExecutor");let e=new AbortController;aS(a,()=>aT(e,a.reason));let f=aW(async()=>{aU(a),aU(e.signal);let b=await c({pause:aX(e.signal),delay:aY(e.signal),signal:e.signal});return aU(e.signal),b},()=>aT(e,aL));return d?.autoJoin&&b.push(f.catch(aQ)),{result:aX(a)(f),cancel(){aT(e,aK)}}})(i.signal,k),unsubscribe:a.unsubscribe,subscribe:()=>{b.set(a.id,a)},cancelActiveListeners:()=>{a.pending.forEach((a,b,c)=>{a!==i&&(aT(a,aM),c.delete(a))})},cancel:()=>{aT(i,aM),a.pending.delete(i)},throwIfCancelled:()=>{aU(i.signal)}})))}catch(a){a instanceof aO||a4(d,a,{raisedBy:"effect"})}finally{await Promise.all(k),aT(i,aN),a.pending.delete(i)}},h=(a=>()=>{a.forEach(a3),a.clear()})(b);return{middleware:a=>c=>i=>{let j;if(!at(i))return c(i);if(a5.match(i))return e(i.payload);if(a6.match(i))return void h();if(a7.match(i))return f(i.payload);let k=a.getState(),l=()=>{if(k===a$)throw Error(ba(23));return k};try{if(j=c(i),b.size>0){let c=a.getState();for(let e of Array.from(b.values())){let b=!1;try{b=e.predicate(i,c,k)}catch(a){b=!1,a4(d,a,{raisedBy:"predicate"})}b&&g(e,i,a,l)}}}finally{k=a$}return j},startListening:e,stopListening:f,clearListeners:h}};function ba(a){return`Minified Redux Toolkit error #${a}; visit https://redux-toolkit.js.org/Errors?code=${a} for the full message or use the non-minified dev environment for full errors. `}Symbol.for("rtk-state-proxy-original");var bb=a.i(88804),bc=a=>0===a?0:a>0?1:-1,bd=a=>"number"==typeof a&&a!=+a,be=a=>"string"==typeof a&&a.indexOf("%")===a.length-1,bf=a=>("number"==typeof a||a instanceof Number)&&!bd(a),bg=a=>bf(a)||"string"==typeof a,bh=0,bi=a=>{var b=++bh;return"".concat(a||"").concat(b)},bj=function(a,b){var c,d=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,e=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!bf(a)&&"string"!=typeof a)return d;if(be(a)){if(null==b)return d;var f=a.indexOf("%");c=b*parseFloat(a.slice(0,f))/100}else c=+a;return bd(c)&&(c=d),e&&null!=b&&c>b&&(c=b),c},bk=a=>{if(!Array.isArray(a))return!1;for(var b=a.length,c={},d=0;d<b;d++)if(c[a[d]])return!0;else c[a[d]]=!0;return!1},bl=(a,b)=>bf(a)&&bf(b)?c=>a+c*(b-a):()=>b;function bm(a,b,c){return bf(a)&&bf(b)?a+c*(b-a):b}function bn(a,b,c){if(a&&a.length)return a.find(a=>a&&("function"==typeof b?b(a):(0,bb.default)(a,b))===c)}var bo=a=>null==a?a:"".concat(a.charAt(0).toUpperCase()).concat(a.slice(1));function bp(a,b){if(b){var c=Number.parseInt(b,10);if(!bd(c))return null==a?void 0:a[c]}}var bq=aF({name:"options",initialState:{chartName:"",tooltipPayloadSearcher:void 0,eventEmitter:void 0,defaultTooltipEventType:"axis"},reducers:{createEventEmitter:a=>{null==a.eventEmitter&&(a.eventEmitter=Symbol("rechartsEventEmitter"))}}}),br=bq.reducer,{createEventEmitter:bs}=bq.actions;a.i(32473);var bt={notify(){},get:()=>[]},bu="undefined"!=typeof navigator&&"ReactNative"===navigator.product,bv=bu?f.useLayoutEffect:f.useEffect;Object.getOwnPropertyNames,Object.getOwnPropertySymbols,Object.getOwnPropertyDescriptor,Object.getPrototypeOf,Object.prototype;var bw=Symbol.for("react-redux-context"),bx="undefined"!=typeof globalThis?globalThis:{},by=function(){if(!f.createContext)return{};let a=bx[bw]??=new Map,b=a.get(f.createContext);return b||(b=f.createContext(null),a.set(f.createContext,b)),b}(),bz=function(a){let{children:b,context:c,serverState:d,store:e}=a,g=f.useMemo(()=>{let a=function(a,b){let c,d=bt,e=0,f=!1;function g(){j.onStateChange&&j.onStateChange()}function h(){if(e++,!c){let b,e;c=a.subscribe(g),b=null,e=null,d={clear(){b=null,e=null},notify(){let a=b;for(;a;)a.callback(),a=a.next},get(){let a=[],c=b;for(;c;)a.push(c),c=c.next;return a},subscribe(a){let c=!0,d=e={callback:a,next:null,prev:e};return d.prev?d.prev.next=d:b=d,function(){c&&null!==b&&(c=!1,d.next?d.next.prev=d.prev:e=d.prev,d.prev?d.prev.next=d.next:b=d.next)}}}}}function i(){e--,c&&0===e&&(c(),c=void 0,d.clear(),d=bt)}let j={addNestedSub:function(a){h();let b=d.subscribe(a),c=!1;return()=>{c||(c=!0,b(),i())}},notifyNestedSubs:function(){d.notify()},handleChangeWrapper:g,isSubscribed:function(){return f},trySubscribe:function(){f||(f=!0,h())},tryUnsubscribe:function(){f&&(f=!1,i())},getListeners:()=>d};return j}(e);return{store:e,subscription:a,getServerState:d?()=>d:void 0}},[e,d]),h=f.useMemo(()=>e.getState(),[e]);return bv(()=>{let{subscription:a}=g;return a.onStateChange=a.notifyNestedSubs,a.trySubscribe(),h!==e.getState()&&a.notifyNestedSubs(),()=>{a.tryUnsubscribe(),a.onStateChange=void 0}},[g,h]),f.createElement((c||by).Provider,{value:g},b)};function bA(a=by){return function(){return f.useContext(a)}}var bB=bA();var bC={active:!1,index:null,dataKey:void 0,coordinate:void 0},bD=aF({name:"tooltip",initialState:{itemInteraction:{click:bC,hover:bC},axisInteraction:{click:bC,hover:bC},keyboardInteraction:bC,syncInteraction:{active:!1,index:null,dataKey:void 0,label:void 0,coordinate:void 0},tooltipItemPayloads:[],settings:{shared:void 0,trigger:"hover",axisId:0,active:!1,defaultIndex:void 0}},reducers:{addTooltipEntrySettings(a,b){a.tooltipItemPayloads.push(b.payload)},removeTooltipEntrySettings(a,b){var c=S(a).tooltipItemPayloads.indexOf(b.payload);c>-1&&a.tooltipItemPayloads.splice(c,1)},setTooltipSettingsState(a,b){a.settings=b.payload},setActiveMouseOverItemIndex(a,b){a.syncInteraction.active=!1,a.keyboardInteraction.active=!1,a.itemInteraction.hover.active=!0,a.itemInteraction.hover.index=b.payload.activeIndex,a.itemInteraction.hover.dataKey=b.payload.activeDataKey,a.itemInteraction.hover.coordinate=b.payload.activeCoordinate},mouseLeaveChart(a){a.itemInteraction.hover.active=!1,a.axisInteraction.hover.active=!1},mouseLeaveItem(a){a.itemInteraction.hover.active=!1},setActiveClickItemIndex(a,b){a.syncInteraction.active=!1,a.itemInteraction.click.active=!0,a.keyboardInteraction.active=!1,a.itemInteraction.click.index=b.payload.activeIndex,a.itemInteraction.click.dataKey=b.payload.activeDataKey,a.itemInteraction.click.coordinate=b.payload.activeCoordinate},setMouseOverAxisIndex(a,b){a.syncInteraction.active=!1,a.axisInteraction.hover.active=!0,a.keyboardInteraction.active=!1,a.axisInteraction.hover.index=b.payload.activeIndex,a.axisInteraction.hover.dataKey=b.payload.activeDataKey,a.axisInteraction.hover.coordinate=b.payload.activeCoordinate},setMouseClickAxisIndex(a,b){a.syncInteraction.active=!1,a.keyboardInteraction.active=!1,a.axisInteraction.click.active=!0,a.axisInteraction.click.index=b.payload.activeIndex,a.axisInteraction.click.dataKey=b.payload.activeDataKey,a.axisInteraction.click.coordinate=b.payload.activeCoordinate},setSyncInteraction(a,b){a.syncInteraction=b.payload},setKeyboardInteraction(a,b){a.keyboardInteraction.active=b.payload.active,a.keyboardInteraction.index=b.payload.activeIndex,a.keyboardInteraction.coordinate=b.payload.activeCoordinate,a.keyboardInteraction.dataKey=b.payload.activeDataKey}}}),{addTooltipEntrySettings:bE,removeTooltipEntrySettings:bF,setTooltipSettingsState:bG,setActiveMouseOverItemIndex:bH,mouseLeaveItem:bI,mouseLeaveChart:bJ,setActiveClickItemIndex:bK,setMouseOverAxisIndex:bL,setMouseClickAxisIndex:bM,setSyncInteraction:bN,setKeyboardInteraction:bO}=bD.actions,bP=bD.reducer,bQ=aF({name:"chartData",initialState:{chartData:void 0,computedData:void 0,dataStartIndex:0,dataEndIndex:0},reducers:{setChartData(a,b){if(a.chartData=b.payload,null==b.payload){a.dataStartIndex=0,a.dataEndIndex=0;return}b.payload.length>0&&a.dataEndIndex!==b.payload.length-1&&(a.dataEndIndex=b.payload.length-1)},setComputedData(a,b){a.computedData=b.payload},setDataStartEndIndexes(a,b){var{startIndex:c,endIndex:d}=b.payload;null!=c&&(a.dataStartIndex=c),null!=d&&(a.dataEndIndex=d)}}}),{setChartData:bR,setDataStartEndIndexes:bS,setComputedData:bT}=bQ.actions,bU=bQ.reducer,bV=aF({name:"chartLayout",initialState:{layoutType:"horizontal",width:0,height:0,margin:{top:5,right:5,bottom:5,left:5},scale:1},reducers:{setLayout(a,b){a.layoutType=b.payload},setChartSize(a,b){a.width=b.payload.width,a.height=b.payload.height},setMargin(a,b){a.margin.top=b.payload.top,a.margin.right=b.payload.right,a.margin.bottom=b.payload.bottom,a.margin.left=b.payload.left},setScale(a,b){a.scale=b.payload}}}),{setMargin:bW,setLayout:bX,setChartSize:bY,setScale:bZ}=bV.actions,b$=bV.reducer,b_=a.i(64442),b0=(0,f.createContext)(null),b1=a=>a,b2=()=>{var a=(0,f.useContext)(b0);return a?a.store.dispatch:b1},b3=()=>{},b4=()=>b3,b5=(a,b)=>a===b;function b6(a){var b=(0,f.useContext)(b0);return(0,b_.useSyncExternalStoreWithSelector)(b?b.subscription.addNestedSub:b4,b?b.store.getState:b3,b?b.store.getState:b3,b?a:b3,b5)}var b7=a.i(81101),b8=a=>a.legend.settings;function b9(a){return"object"==typeof a&&"length"in a?a:Array.from(a)}function ca(a){return function(){return a}}function cb(a,b){if((e=a.length)>1)for(var c,d,e,f=1,g=a[b[0]],h=g.length;f<e;++f)for(d=g,g=a[b[f]],c=0;c<h;++c)g[c][1]+=g[c][0]=isNaN(d[c][1])?d[c][0]:d[c][1]}function cc(a){for(var b=a.length,c=Array(b);--b>=0;)c[b]=b;return c}function cd(a,b){return a[b]}function ce(a){let b=[];return b.key=a,b}function cf(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function cg(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?cf(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):cf(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}ak([a=>a.legend.payload,b8],(a,b)=>{var{itemSorter:c}=b,d=a.flat(1);return c?(0,b7.default)(d,c):d}),Array.prototype.slice;var ch=Math.PI/180,ci=(a,b,c,d)=>({x:a+Math.cos(-ch*d)*c,y:b+Math.sin(-ch*d)*c}),cj=function(a,b){var c=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0,width:0,height:0,brushBottom:0};return Math.min(Math.abs(a-(c.left||0)-(c.right||0)),Math.abs(b-(c.top||0)-(c.bottom||0)))/2};function ck(a,b,c){return Array.isArray(a)&&a&&b+c!==0?a.slice(b,c+1):a}function cl(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function cm(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?cl(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):cl(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function cn(a,b,c){return null==a||null==b?c:bg(b)?(0,bb.default)(a,b,c):"function"==typeof b?b(a):c}var co=(a,b)=>"horizontal"===a&&"xAxis"===b||"vertical"===a&&"yAxis"===b||"centric"===a&&"angleAxis"===b||"radial"===a&&"radiusAxis"===b,cp=(a,b,c,d)=>{if(d)return a.map(a=>a.coordinate);var e,f,g=a.map(a=>(a.coordinate===b&&(e=!0),a.coordinate===c&&(f=!0),a.coordinate));return e||g.push(b),f||g.push(c),g},cq=(a,b,c)=>{if(!a)return null;var{duplicateDomain:d,type:e,range:f,scale:g,realScaleType:h,isCategorical:i,categoricalDomain:j,tickCount:k,ticks:l,niceTicks:m,axisType:n}=a;if(!g)return null;var o="scaleBand"===h&&g.bandwidth?g.bandwidth()/2:2,p=(b||c)&&"category"===e&&g.bandwidth?g.bandwidth()/o:0;return(p="angleAxis"===n&&f&&f.length>=2?2*bc(f[0]-f[1])*p:p,b&&(l||m))?(l||m||[]).map((a,b)=>({coordinate:g(d?d.indexOf(a):a)+p,value:a,offset:p,index:b})).filter(a=>!bd(a.coordinate)):i&&j?j.map((a,b)=>({coordinate:g(a)+p,value:a,index:b,offset:p})):g.ticks&&!c&&null!=k?g.ticks(k).map((a,b)=>({coordinate:g(a)+p,value:a,offset:p,index:b})):g.domain().map((a,b)=>({coordinate:g(a)+p,value:d?d[a]:a,index:b,offset:p}))},cr={sign:a=>{var b=a.length;if(!(b<=0))for(var c=0,d=a[0].length;c<d;++c)for(var e=0,f=0,g=0;g<b;++g){var h=bd(a[g][c][1])?a[g][c][0]:a[g][c][1];h>=0?(a[g][c][0]=e,a[g][c][1]=e+h,e=a[g][c][1]):(a[g][c][0]=f,a[g][c][1]=f+h,f=a[g][c][1])}},expand:function(a,b){if((d=a.length)>0){for(var c,d,e,f=0,g=a[0].length;f<g;++f){for(e=c=0;c<d;++c)e+=a[c][f][1]||0;if(e)for(c=0;c<d;++c)a[c][f][1]/=e}cb(a,b)}},none:cb,silhouette:function(a,b){if((c=a.length)>0){for(var c,d=0,e=a[b[0]],f=e.length;d<f;++d){for(var g=0,h=0;g<c;++g)h+=a[g][d][1]||0;e[d][1]+=e[d][0]=-h/2}cb(a,b)}},wiggle:function(a,b){if((e=a.length)>0&&(d=(c=a[b[0]]).length)>0){for(var c,d,e,f=0,g=1;g<d;++g){for(var h=0,i=0,j=0;h<e;++h){for(var k=a[b[h]],l=k[g][1]||0,m=(l-(k[g-1][1]||0))/2,n=0;n<h;++n){var o=a[b[n]];m+=(o[g][1]||0)-(o[g-1][1]||0)}i+=l,j+=m*l}c[g-1][1]+=c[g-1][0]=f,i&&(f-=j/i)}c[g-1][1]+=c[g-1][0]=f,cb(a,b)}},positive:a=>{var b=a.length;if(!(b<=0))for(var c=0,d=a[0].length;c<d;++c)for(var e=0,f=0;f<b;++f){var g=bd(a[f][c][1])?a[f][c][0]:a[f][c][1];g>=0?(a[f][c][0]=e,a[f][c][1]=e+g,e=a[f][c][1]):(a[f][c][0]=0,a[f][c][1]=0)}}};function cs(a){return null==a?void 0:String(a)}function ct(a){var{axis:b,ticks:c,bandSize:d,entry:e,index:f,dataKey:g}=a;if("category"===b.type){if(!b.allowDuplicatedCategory&&b.dataKey&&null!=e[b.dataKey]){var h=bn(c,"value",e[b.dataKey]);if(h)return h.coordinate+d/2}return c[f]?c[f].coordinate+d/2:null}var i=cn(e,null==g?b.dataKey:g);return null==i?null:b.scale(i)}var cu=a=>{var{axis:b,ticks:c,offset:d,bandSize:e,entry:f,index:g}=a;if("category"===b.type)return c[g]?c[g].coordinate+d:null;var h=cn(f,b.dataKey,b.scale.domain()[g]);return null==h?null:b.scale(h)-e/2+d},cv=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,cw=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,cx=(a,b,c)=>{if(a&&a.scale&&a.scale.bandwidth){var d=a.scale.bandwidth();if(!c||d>0)return d}if(a&&b&&b.length>=2){for(var e=(0,b7.default)(b,a=>a.coordinate),f=1/0,g=1,h=e.length;g<h;g++){var i=e[g],j=e[g-1];f=Math.min((i.coordinate||0)-(j.coordinate||0),f)}return f===1/0?0:f}return c?void 0:0};function cy(a){var{tooltipEntrySettings:b,dataKey:c,payload:d,value:e,name:f}=a;return cm(cm({},b),{},{dataKey:c,payload:d,value:e,name:f})}function cz(a,b){return a?String(a):"string"==typeof b?b:void 0}var cA=a=>a.layout.width,cB=a=>a.layout.height,cC=a=>a.layout.scale,cD=a=>a.layout.margin,cE=ak(a=>a.cartesianAxis.xAxis,a=>Object.values(a)),cF=ak(a=>a.cartesianAxis.yAxis,a=>Object.values(a)),cG="data-recharts-item-index",cH="data-recharts-item-data-key";function cI(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function cJ(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?cI(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):cI(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var cK=ak([cA,cB,cD,a=>a.brush.height,cE,cF,b8,a=>a.legend.size],(a,b,c,d,e,f,g,h)=>{var i=f.reduce((a,b)=>{var{orientation:c}=b;if(!b.mirror&&!b.hide){var d="number"==typeof b.width?b.width:60;return cJ(cJ({},a),{},{[c]:a[c]+d})}return a},{left:c.left||0,right:c.right||0}),j=e.reduce((a,b)=>{var{orientation:c}=b;return b.mirror||b.hide?a:cJ(cJ({},a),{},{[c]:(0,bb.default)(a,"".concat(c))+b.height})},{top:c.top||0,bottom:c.bottom||0}),k=cJ(cJ({},j),i),l=k.bottom;k.bottom+=d;var m=a-(k=((a,b,c)=>{if(b&&c){var{width:d,height:e}=c,{align:f,verticalAlign:g,layout:h}=b;if(("vertical"===h||"horizontal"===h&&"middle"===g)&&"center"!==f&&bf(a[f]))return cm(cm({},a),{},{[f]:a[f]+(d||0)});if(("horizontal"===h||"vertical"===h&&"center"===f)&&"middle"!==g&&bf(a[g]))return cm(cm({},a),{},{[g]:a[g]+(e||0)})}return a})(k,g,h)).left-k.right,n=b-k.top-k.bottom;return cJ(cJ({brushBottom:l},k),{},{width:Math.max(m,0),height:Math.max(n,0)})}),cL=ak(cK,a=>({x:a.left,y:a.top,width:a.width,height:a.height})),cM=ak(cA,cB,(a,b)=>({x:0,y:0,width:a,height:b})),cN=(0,f.createContext)(null),cO=()=>null!=(0,f.useContext)(cN),cP=a=>a.brush,cQ=ak([cP,cK,cD],(a,b,c)=>({height:a.height,x:bf(a.x)?a.x:b.left,y:bf(a.y)?a.y:b.top+b.height+b.brushBottom-((null==c?void 0:c.bottom)||0),width:bf(a.width)?a.width:b.width})),cR=()=>{var a,b=cO(),c=b6(cL),d=b6(cQ),e=null==(a=b6(cP))?void 0:a.padding;return b&&d&&e?{width:d.width-e.left-e.right,height:d.height-e.top-e.bottom,x:e.left,y:e.top}:c},cS={top:0,bottom:0,left:0,right:0,width:0,height:0,brushBottom:0},cT=()=>{var a;return null!=(a=b6(cK))?a:cS},cU=()=>b6(cA),cV=()=>b6(cB),cW=a=>a.layout.layoutType,cX=()=>b6(cW),cY=a.i(56161);function cZ(a,b){switch(arguments.length){case 0:break;case 1:this.range(a);break;default:this.range(b).domain(a)}return this}function c$(a,b){switch(arguments.length){case 0:break;case 1:"function"==typeof a?this.interpolator(a):this.range(a);break;default:this.domain(a),"function"==typeof b?this.interpolator(b):this.range(b)}return this}a.s([],62346),a.s(["scaleBand",()=>c4,"scaleDiverging",()=>gL,"scaleDivergingLog",()=>gM,"scaleDivergingPow",()=>gO,"scaleDivergingSqrt",()=>gP,"scaleDivergingSymlog",()=>gN,"scaleIdentity",()=>el,"scaleImplicit",()=>c2,"scaleLinear",()=>ek,"scaleLog",()=>eu,"scaleOrdinal",()=>c3,"scalePoint",()=>c5,"scalePow",()=>eD,"scaleQuantile",()=>eL,"scaleQuantize",()=>eM,"scaleRadial",()=>eG,"scaleSequential",()=>gE,"scaleSequentialLog",()=>gF,"scaleSequentialPow",()=>gH,"scaleSequentialQuantile",()=>gJ,"scaleSequentialSqrt",()=>gI,"scaleSequentialSymlog",()=>gG,"scaleSqrt",()=>eE,"scaleSymlog",()=>ey,"scaleThreshold",()=>eN,"scaleTime",()=>gA,"scaleUtc",()=>gB,"tickFormat",()=>ei],62599),a.i(62346),a.s(["scaleBand",()=>c4,"scaleDiverging",()=>gL,"scaleDivergingLog",()=>gM,"scaleDivergingPow",()=>gO,"scaleDivergingSqrt",()=>gP,"scaleDivergingSymlog",()=>gN,"scaleIdentity",()=>el,"scaleImplicit",()=>c2,"scaleLinear",()=>ek,"scaleLog",()=>eu,"scaleOrdinal",()=>c3,"scalePoint",()=>c5,"scalePow",()=>eD,"scaleQuantile",()=>eL,"scaleQuantize",()=>eM,"scaleRadial",()=>eG,"scaleSequential",()=>gE,"scaleSequentialLog",()=>gF,"scaleSequentialPow",()=>gH,"scaleSequentialQuantile",()=>gJ,"scaleSequentialSqrt",()=>gI,"scaleSequentialSymlog",()=>gG,"scaleSqrt",()=>eE,"scaleSymlog",()=>ey,"scaleThreshold",()=>eN,"scaleTime",()=>gA,"scaleUtc",()=>gB,"tickFormat",()=>ei],48117),a.s([],75333),a.i(75333);class c_ extends Map{constructor(a,b=c1){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:b}}),null!=a)for(let[b,c]of a)this.set(b,c)}get(a){return super.get(c0(this,a))}has(a){return super.has(c0(this,a))}set(a,b){return super.set(function({_intern:a,_key:b},c){let d=b(c);return a.has(d)?a.get(d):(a.set(d,c),c)}(this,a),b)}delete(a){return super.delete(function({_intern:a,_key:b},c){let d=b(c);return a.has(d)&&(c=a.get(d),a.delete(d)),c}(this,a))}}function c0({_intern:a,_key:b},c){let d=b(c);return a.has(d)?a.get(d):c}function c1(a){return null!==a&&"object"==typeof a?a.valueOf():a}let c2=Symbol("implicit");function c3(){var a=new c_,b=[],c=[],d=c2;function e(e){let f=a.get(e);if(void 0===f){if(d!==c2)return d;a.set(e,f=b.push(e)-1)}return c[f%c.length]}return e.domain=function(c){if(!arguments.length)return b.slice();for(let d of(b=[],a=new c_,c))a.has(d)||a.set(d,b.push(d)-1);return e},e.range=function(a){return arguments.length?(c=Array.from(a),e):c.slice()},e.unknown=function(a){return arguments.length?(d=a,e):d},e.copy=function(){return c3(b,c).unknown(d)},cZ.apply(e,arguments),e}function c4(){var a,b,c=c3().unknown(void 0),d=c.domain,e=c.range,f=0,g=1,h=!1,i=0,j=0,k=.5;function l(){var c=d().length,l=g<f,m=l?g:f,n=l?f:g;a=(n-m)/Math.max(1,c-i+2*j),h&&(a=Math.floor(a)),m+=(n-m-a*(c-i))*k,b=a*(1-i),h&&(m=Math.round(m),b=Math.round(b));var o=(function(a,b,c){a*=1,b*=1,c=(e=arguments.length)<2?(b=a,a=0,1):e<3?1:+c;for(var d=-1,e=0|Math.max(0,Math.ceil((b-a)/c)),f=Array(e);++d<e;)f[d]=a+d*c;return f})(c).map(function(b){return m+a*b});return e(l?o.reverse():o)}return delete c.unknown,c.domain=function(a){return arguments.length?(d(a),l()):d()},c.range=function(a){return arguments.length?([f,g]=a,f*=1,g*=1,l()):[f,g]},c.rangeRound=function(a){return[f,g]=a,f*=1,g*=1,h=!0,l()},c.bandwidth=function(){return b},c.step=function(){return a},c.round=function(a){return arguments.length?(h=!!a,l()):h},c.padding=function(a){return arguments.length?(i=Math.min(1,j=+a),l()):i},c.paddingInner=function(a){return arguments.length?(i=Math.min(1,a),l()):i},c.paddingOuter=function(a){return arguments.length?(j=+a,l()):j},c.align=function(a){return arguments.length?(k=Math.max(0,Math.min(1,a)),l()):k},c.copy=function(){return c4(d(),[f,g]).round(h).paddingInner(i).paddingOuter(j).align(k)},cZ.apply(l(),arguments)}function c5(){return function a(b){var c=b.copy;return b.padding=b.paddingOuter,delete b.paddingInner,delete b.paddingOuter,b.copy=function(){return a(c())},b}(c4.apply(null,arguments).paddingInner(1))}let c6=Math.sqrt(50),c7=Math.sqrt(10),c8=Math.sqrt(2);function c9(a,b,c){let d,e,f,g=(b-a)/Math.max(0,c),h=Math.floor(Math.log10(g)),i=g/Math.pow(10,h),j=i>=c6?10:i>=c7?5:i>=c8?2:1;return(h<0?(d=Math.round(a*(f=Math.pow(10,-h)/j)),e=Math.round(b*f),d/f<a&&++d,e/f>b&&--e,f=-f):(d=Math.round(a/(f=Math.pow(10,h)*j)),e=Math.round(b/f),d*f<a&&++d,e*f>b&&--e),e<d&&.5<=c&&c<2)?c9(a,b,2*c):[d,e,f]}function da(a,b,c){if(b*=1,a*=1,!((c*=1)>0))return[];if(a===b)return[a];let d=b<a,[e,f,g]=d?c9(b,a,c):c9(a,b,c);if(!(f>=e))return[];let h=f-e+1,i=Array(h);if(d)if(g<0)for(let a=0;a<h;++a)i[a]=-((f-a)/g);else for(let a=0;a<h;++a)i[a]=(f-a)*g;else if(g<0)for(let a=0;a<h;++a)i[a]=-((e+a)/g);else for(let a=0;a<h;++a)i[a]=(e+a)*g;return i}function db(a,b,c){return c9(a*=1,b*=1,c*=1)[2]}function dc(a,b,c){b*=1,a*=1,c*=1;let d=b<a,e=d?db(b,a,c):db(a,b,c);return(d?-1:1)*(e<0?-(1/e):e)}function dd(a,b){return null==a||null==b?NaN:a<b?-1:a>b?1:a>=b?0:NaN}function de(a,b){return null==a||null==b?NaN:b<a?-1:b>a?1:b>=a?0:NaN}function df(a){let b,c,d;function e(a,d,f=0,g=a.length){if(f<g){if(0!==b(d,d))return g;do{let b=f+g>>>1;0>c(a[b],d)?f=b+1:g=b}while(f<g)}return f}return 2!==a.length?(b=dd,c=(b,c)=>dd(a(b),c),d=(b,c)=>a(b)-c):(b=a===dd||a===de?a:dg,c=a,d=a),{left:e,center:function(a,b,c=0,f=a.length){let g=e(a,b,c,f-1);return g>c&&d(a[g-1],b)>-d(a[g],b)?g-1:g},right:function(a,d,e=0,f=a.length){if(e<f){if(0!==b(d,d))return f;do{let b=e+f>>>1;0>=c(a[b],d)?e=b+1:f=b}while(e<f)}return e}}}function dg(){return 0}function dh(a){return null===a?NaN:+a}let di=df(dd),dj=di.right;function dk(a,b,c){a.prototype=b.prototype=c,c.constructor=a}function dl(a,b){var c=Object.create(a.prototype);for(var d in b)c[d]=b[d];return c}function dm(){}di.left,df(dh).center;var dn="\\s*([+-]?\\d+)\\s*",dp="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",dq="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",dr=/^#([0-9a-f]{3,8})$/,ds=RegExp(`^rgb\\(${dn},${dn},${dn}\\)$`),dt=RegExp(`^rgb\\(${dq},${dq},${dq}\\)$`),du=RegExp(`^rgba\\(${dn},${dn},${dn},${dp}\\)$`),dv=RegExp(`^rgba\\(${dq},${dq},${dq},${dp}\\)$`),dw=RegExp(`^hsl\\(${dp},${dq},${dq}\\)$`),dx=RegExp(`^hsla\\(${dp},${dq},${dq},${dp}\\)$`),dy={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function dz(){return this.rgb().formatHex()}function dA(){return this.rgb().formatRgb()}function dB(a){var b,c;return a=(a+"").trim().toLowerCase(),(b=dr.exec(a))?(c=b[1].length,b=parseInt(b[1],16),6===c?dC(b):3===c?new dF(b>>8&15|b>>4&240,b>>4&15|240&b,(15&b)<<4|15&b,1):8===c?dD(b>>24&255,b>>16&255,b>>8&255,(255&b)/255):4===c?dD(b>>12&15|b>>8&240,b>>8&15|b>>4&240,b>>4&15|240&b,((15&b)<<4|15&b)/255):null):(b=ds.exec(a))?new dF(b[1],b[2],b[3],1):(b=dt.exec(a))?new dF(255*b[1]/100,255*b[2]/100,255*b[3]/100,1):(b=du.exec(a))?dD(b[1],b[2],b[3],b[4]):(b=dv.exec(a))?dD(255*b[1]/100,255*b[2]/100,255*b[3]/100,b[4]):(b=dw.exec(a))?dL(b[1],b[2]/100,b[3]/100,1):(b=dx.exec(a))?dL(b[1],b[2]/100,b[3]/100,b[4]):dy.hasOwnProperty(a)?dC(dy[a]):"transparent"===a?new dF(NaN,NaN,NaN,0):null}function dC(a){return new dF(a>>16&255,a>>8&255,255&a,1)}function dD(a,b,c,d){return d<=0&&(a=b=c=NaN),new dF(a,b,c,d)}function dE(a,b,c,d){var e;return 1==arguments.length?((e=a)instanceof dm||(e=dB(e)),e)?new dF((e=e.rgb()).r,e.g,e.b,e.opacity):new dF:new dF(a,b,c,null==d?1:d)}function dF(a,b,c,d){this.r=+a,this.g=+b,this.b=+c,this.opacity=+d}function dG(){return`#${dK(this.r)}${dK(this.g)}${dK(this.b)}`}function dH(){let a=dI(this.opacity);return`${1===a?"rgb(":"rgba("}${dJ(this.r)}, ${dJ(this.g)}, ${dJ(this.b)}${1===a?")":`, ${a})`}`}function dI(a){return isNaN(a)?1:Math.max(0,Math.min(1,a))}function dJ(a){return Math.max(0,Math.min(255,Math.round(a)||0))}function dK(a){return((a=dJ(a))<16?"0":"")+a.toString(16)}function dL(a,b,c,d){return d<=0?a=b=c=NaN:c<=0||c>=1?a=b=NaN:b<=0&&(a=NaN),new dN(a,b,c,d)}function dM(a){if(a instanceof dN)return new dN(a.h,a.s,a.l,a.opacity);if(a instanceof dm||(a=dB(a)),!a)return new dN;if(a instanceof dN)return a;var b=(a=a.rgb()).r/255,c=a.g/255,d=a.b/255,e=Math.min(b,c,d),f=Math.max(b,c,d),g=NaN,h=f-e,i=(f+e)/2;return h?(g=b===f?(c-d)/h+(c<d)*6:c===f?(d-b)/h+2:(b-c)/h+4,h/=i<.5?f+e:2-f-e,g*=60):h=i>0&&i<1?0:g,new dN(g,h,i,a.opacity)}function dN(a,b,c,d){this.h=+a,this.s=+b,this.l=+c,this.opacity=+d}function dO(a){return(a=(a||0)%360)<0?a+360:a}function dP(a){return Math.max(0,Math.min(1,a||0))}function dQ(a,b,c){return(a<60?b+(c-b)*a/60:a<180?c:a<240?b+(c-b)*(240-a)/60:b)*255}function dR(a,b,c,d,e){var f=a*a,g=f*a;return((1-3*a+3*f-g)*b+(4-6*f+3*g)*c+(1+3*a+3*f-3*g)*d+g*e)/6}dk(dm,dB,{copy(a){return Object.assign(new this.constructor,this,a)},displayable(){return this.rgb().displayable()},hex:dz,formatHex:dz,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return dM(this).formatHsl()},formatRgb:dA,toString:dA}),dk(dF,dE,dl(dm,{brighter(a){return a=null==a?1.4285714285714286:Math.pow(1.4285714285714286,a),new dF(this.r*a,this.g*a,this.b*a,this.opacity)},darker(a){return a=null==a?.7:Math.pow(.7,a),new dF(this.r*a,this.g*a,this.b*a,this.opacity)},rgb(){return this},clamp(){return new dF(dJ(this.r),dJ(this.g),dJ(this.b),dI(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:dG,formatHex:dG,formatHex8:function(){return`#${dK(this.r)}${dK(this.g)}${dK(this.b)}${dK((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:dH,toString:dH})),dk(dN,function(a,b,c,d){return 1==arguments.length?dM(a):new dN(a,b,c,null==d?1:d)},dl(dm,{brighter(a){return a=null==a?1.4285714285714286:Math.pow(1.4285714285714286,a),new dN(this.h,this.s,this.l*a,this.opacity)},darker(a){return a=null==a?.7:Math.pow(.7,a),new dN(this.h,this.s,this.l*a,this.opacity)},rgb(){var a=this.h%360+(this.h<0)*360,b=isNaN(a)||isNaN(this.s)?0:this.s,c=this.l,d=c+(c<.5?c:1-c)*b,e=2*c-d;return new dF(dQ(a>=240?a-240:a+120,e,d),dQ(a,e,d),dQ(a<120?a+240:a-120,e,d),this.opacity)},clamp(){return new dN(dO(this.h),dP(this.s),dP(this.l),dI(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let a=dI(this.opacity);return`${1===a?"hsl(":"hsla("}${dO(this.h)}, ${100*dP(this.s)}%, ${100*dP(this.l)}%${1===a?")":`, ${a})`}`}}));let dS=a=>()=>a;function dT(a,b){var c=b-a;return c?function(b){return a+b*c}:dS(isNaN(a)?b:a)}let dU=function a(b){var c,d=1==(c=+b)?dT:function(a,b){var d,e,f;return b-a?(d=a,e=b,d=Math.pow(d,f=c),e=Math.pow(e,f)-d,f=1/f,function(a){return Math.pow(d+a*e,f)}):dS(isNaN(a)?b:a)};function e(a,b){var c=d((a=dE(a)).r,(b=dE(b)).r),e=d(a.g,b.g),f=d(a.b,b.b),g=dT(a.opacity,b.opacity);return function(b){return a.r=c(b),a.g=e(b),a.b=f(b),a.opacity=g(b),a+""}}return e.gamma=a,e}(1);function dV(a){return function(b){var c,d,e=b.length,f=Array(e),g=Array(e),h=Array(e);for(c=0;c<e;++c)d=dE(b[c]),f[c]=d.r||0,g[c]=d.g||0,h[c]=d.b||0;return f=a(f),g=a(g),h=a(h),d.opacity=1,function(a){return d.r=f(a),d.g=g(a),d.b=h(a),d+""}}}function dW(a,b){return a*=1,b*=1,function(c){return a*(1-c)+b*c}}dV(function(a){var b=a.length-1;return function(c){var d=c<=0?c=0:c>=1?(c=1,b-1):Math.floor(c*b),e=a[d],f=a[d+1],g=d>0?a[d-1]:2*e-f,h=d<b-1?a[d+2]:2*f-e;return dR((c-d/b)*b,g,e,f,h)}}),dV(function(a){var b=a.length;return function(c){var d=Math.floor(((c%=1)<0?++c:c)*b),e=a[(d+b-1)%b],f=a[d%b],g=a[(d+1)%b],h=a[(d+2)%b];return dR((c-d/b)*b,e,f,g,h)}});var dX=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,dY=RegExp(dX.source,"g");function dZ(a,b){var c,d,e=typeof b;return null==b||"boolean"===e?dS(b):("number"===e?dW:"string"===e?(d=dB(b))?(b=d,dU):function(a,b){var c,d,e,f,g,h=dX.lastIndex=dY.lastIndex=0,i=-1,j=[],k=[];for(a+="",b+="";(e=dX.exec(a))&&(f=dY.exec(b));)(g=f.index)>h&&(g=b.slice(h,g),j[i]?j[i]+=g:j[++i]=g),(e=e[0])===(f=f[0])?j[i]?j[i]+=f:j[++i]=f:(j[++i]=null,k.push({i:i,x:dW(e,f)})),h=dY.lastIndex;return h<b.length&&(g=b.slice(h),j[i]?j[i]+=g:j[++i]=g),j.length<2?k[0]?(c=k[0].x,function(a){return c(a)+""}):(d=b,function(){return d}):(b=k.length,function(a){for(var c,d=0;d<b;++d)j[(c=k[d]).i]=c.x(a);return j.join("")})}:b instanceof dB?dU:b instanceof Date?function(a,b){var c=new Date;return a*=1,b*=1,function(d){return c.setTime(a*(1-d)+b*d),c}}:!ArrayBuffer.isView(c=b)||c instanceof DataView?Array.isArray(b)?function(a,b){var c,d=b?b.length:0,e=a?Math.min(d,a.length):0,f=Array(e),g=Array(d);for(c=0;c<e;++c)f[c]=dZ(a[c],b[c]);for(;c<d;++c)g[c]=b[c];return function(a){for(c=0;c<e;++c)g[c]=f[c](a);return g}}:"function"!=typeof b.valueOf&&"function"!=typeof b.toString||isNaN(b)?function(a,b){var c,d={},e={};for(c in(null===a||"object"!=typeof a)&&(a={}),(null===b||"object"!=typeof b)&&(b={}),b)c in a?d[c]=dZ(a[c],b[c]):e[c]=b[c];return function(a){for(c in d)e[c]=d[c](a);return e}}:dW:function(a,b){b||(b=[]);var c,d=a?Math.min(b.length,a.length):0,e=b.slice();return function(f){for(c=0;c<d;++c)e[c]=a[c]*(1-f)+b[c]*f;return e}})(a,b)}function d$(a,b){return a*=1,b*=1,function(c){return Math.round(a*(1-c)+b*c)}}function d_(a){return+a}var d0=[0,1];function d1(a){return a}function d2(a,b){var c;return(b-=a*=1)?function(c){return(c-a)/b}:(c=isNaN(b)?NaN:.5,function(){return c})}function d3(a,b,c){var d=a[0],e=a[1],f=b[0],g=b[1];return e<d?(d=d2(e,d),f=c(g,f)):(d=d2(d,e),f=c(f,g)),function(a){return f(d(a))}}function d4(a,b,c){var d=Math.min(a.length,b.length)-1,e=Array(d),f=Array(d),g=-1;for(a[d]<a[0]&&(a=a.slice().reverse(),b=b.slice().reverse());++g<d;)e[g]=d2(a[g],a[g+1]),f[g]=c(b[g],b[g+1]);return function(b){var c=dj(a,b,1,d)-1;return f[c](e[c](b))}}function d5(a,b){return b.domain(a.domain()).range(a.range()).interpolate(a.interpolate()).clamp(a.clamp()).unknown(a.unknown())}function d6(){var a,b,c,d,e,f,g=d0,h=d0,i=dZ,j=d1;function k(){var a,b,c,i=Math.min(g.length,h.length);return j!==d1&&(a=g[0],b=g[i-1],a>b&&(c=a,a=b,b=c),j=function(c){return Math.max(a,Math.min(b,c))}),d=i>2?d4:d3,e=f=null,l}function l(b){return null==b||isNaN(b*=1)?c:(e||(e=d(g.map(a),h,i)))(a(j(b)))}return l.invert=function(c){return j(b((f||(f=d(h,g.map(a),dW)))(c)))},l.domain=function(a){return arguments.length?(g=Array.from(a,d_),k()):g.slice()},l.range=function(a){return arguments.length?(h=Array.from(a),k()):h.slice()},l.rangeRound=function(a){return h=Array.from(a),i=d$,k()},l.clamp=function(a){return arguments.length?(j=!!a||d1,k()):j!==d1},l.interpolate=function(a){return arguments.length?(i=a,k()):i},l.unknown=function(a){return arguments.length?(c=a,l):c},function(c,d){return a=c,b=d,k()}}function d7(){return d6()(d1,d1)}function d8(a,b){if((c=(a=b?a.toExponential(b-1):a.toExponential()).indexOf("e"))<0)return null;var c,d=a.slice(0,c);return[d.length>1?d[0]+d.slice(2):d,+a.slice(c+1)]}function d9(a){return(a=d8(Math.abs(a)))?a[1]:NaN}var ea=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function eb(a){var b;if(!(b=ea.exec(a)))throw Error("invalid format: "+a);return new ec({fill:b[1],align:b[2],sign:b[3],symbol:b[4],zero:b[5],width:b[6],comma:b[7],precision:b[8]&&b[8].slice(1),trim:b[9],type:b[10]})}function ec(a){this.fill=void 0===a.fill?" ":a.fill+"",this.align=void 0===a.align?">":a.align+"",this.sign=void 0===a.sign?"-":a.sign+"",this.symbol=void 0===a.symbol?"":a.symbol+"",this.zero=!!a.zero,this.width=void 0===a.width?void 0:+a.width,this.comma=!!a.comma,this.precision=void 0===a.precision?void 0:+a.precision,this.trim=!!a.trim,this.type=void 0===a.type?"":a.type+""}function ed(a,b){var c=d8(a,b);if(!c)return a+"";var d=c[0],e=c[1];return e<0?"0."+Array(-e).join("0")+d:d.length>e+1?d.slice(0,e+1)+"."+d.slice(e+1):d+Array(e-d.length+2).join("0")}eb.prototype=ec.prototype,ec.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let ee={"%":(a,b)=>(100*a).toFixed(b),b:a=>Math.round(a).toString(2),c:a=>a+"",d:function(a){return Math.abs(a=Math.round(a))>=1e21?a.toLocaleString("en").replace(/,/g,""):a.toString(10)},e:(a,b)=>a.toExponential(b),f:(a,b)=>a.toFixed(b),g:(a,b)=>a.toPrecision(b),o:a=>Math.round(a).toString(8),p:(a,b)=>ed(100*a,b),r:ed,s:function(a,b){var c=d8(a,b);if(!c)return a+"";var d=c[0],e=c[1],f=e-(gZ=3*Math.max(-8,Math.min(8,Math.floor(e/3))))+1,g=d.length;return f===g?d:f>g?d+Array(f-g+1).join("0"):f>0?d.slice(0,f)+"."+d.slice(f):"0."+Array(1-f).join("0")+d8(a,Math.max(0,b+f-1))[0]},X:a=>Math.round(a).toString(16).toUpperCase(),x:a=>Math.round(a).toString(16)};function ef(a){return a}var eg=Array.prototype.map,eh=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function ei(a,b,c,d){var e,f,g=dc(a,b,c);switch((d=eb(null==d?",f":d)).type){case"s":var h=Math.max(Math.abs(a),Math.abs(b));return null!=d.precision||isNaN(f=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(d9(h)/3)))-d9(Math.abs(g))))||(d.precision=f),g0(d,h);case"":case"e":case"g":case"p":case"r":null!=d.precision||isNaN(f=Math.max(0,d9(Math.abs(Math.max(Math.abs(a),Math.abs(b)))-(e=Math.abs(e=g)))-d9(e))+1)||(d.precision=f-("e"===d.type));break;case"f":case"%":null!=d.precision||isNaN(f=Math.max(0,-d9(Math.abs(g))))||(d.precision=f-("%"===d.type)*2)}return g_(d)}function ej(a){var b=a.domain;return a.ticks=function(a){var c=b();return da(c[0],c[c.length-1],null==a?10:a)},a.tickFormat=function(a,c){var d=b();return ei(d[0],d[d.length-1],null==a?10:a,c)},a.nice=function(c){null==c&&(c=10);var d,e,f=b(),g=0,h=f.length-1,i=f[g],j=f[h],k=10;for(j<i&&(e=i,i=j,j=e,e=g,g=h,h=e);k-- >0;){if((e=db(i,j,c))===d)return f[g]=i,f[h]=j,b(f);if(e>0)i=Math.floor(i/e)*e,j=Math.ceil(j/e)*e;else if(e<0)i=Math.ceil(i*e)/e,j=Math.floor(j*e)/e;else break;d=e}return a},a}function ek(){var a=d7();return a.copy=function(){return d5(a,ek())},cZ.apply(a,arguments),ej(a)}function el(a){var b;function c(a){return null==a||isNaN(a*=1)?b:a}return c.invert=c,c.domain=c.range=function(b){return arguments.length?(a=Array.from(b,d_),c):a.slice()},c.unknown=function(a){return arguments.length?(b=a,c):b},c.copy=function(){return el(a).unknown(b)},a=arguments.length?Array.from(a,d_):[0,1],ej(c)}function em(a,b){a=a.slice();var c,d=0,e=a.length-1,f=a[d],g=a[e];return g<f&&(c=d,d=e,e=c,c=f,f=g,g=c),a[d]=b.floor(f),a[e]=b.ceil(g),a}function en(a){return Math.log(a)}function eo(a){return Math.exp(a)}function ep(a){return-Math.log(-a)}function eq(a){return-Math.exp(-a)}function er(a){return isFinite(a)?+("1e"+a):a<0?0:a}function es(a){return(b,c)=>-a(-b,c)}function et(a){let b,c,d=a(en,eo),e=d.domain,f=10;function g(){var g,h;return b=(g=f)===Math.E?Math.log:10===g&&Math.log10||2===g&&Math.log2||(g=Math.log(g),a=>Math.log(a)/g),c=10===(h=f)?er:h===Math.E?Math.exp:a=>Math.pow(h,a),e()[0]<0?(b=es(b),c=es(c),a(ep,eq)):a(en,eo),d}return d.base=function(a){return arguments.length?(f=+a,g()):f},d.domain=function(a){return arguments.length?(e(a),g()):e()},d.ticks=a=>{let d,g,h=e(),i=h[0],j=h[h.length-1],k=j<i;k&&([i,j]=[j,i]);let l=b(i),m=b(j),n=null==a?10:+a,o=[];if(!(f%1)&&m-l<n){if(l=Math.floor(l),m=Math.ceil(m),i>0){for(;l<=m;++l)for(d=1;d<f;++d)if(!((g=l<0?d/c(-l):d*c(l))<i)){if(g>j)break;o.push(g)}}else for(;l<=m;++l)for(d=f-1;d>=1;--d)if(!((g=l>0?d/c(-l):d*c(l))<i)){if(g>j)break;o.push(g)}2*o.length<n&&(o=da(i,j,n))}else o=da(l,m,Math.min(m-l,n)).map(c);return k?o.reverse():o},d.tickFormat=(a,e)=>{if(null==a&&(a=10),null==e&&(e=10===f?"s":","),"function"!=typeof e&&(f%1||null!=(e=eb(e)).precision||(e.trim=!0),e=g_(e)),a===1/0)return e;let g=Math.max(1,f*a/d.ticks().length);return a=>{let d=a/c(Math.round(b(a)));return d*f<f-.5&&(d*=f),d<=g?e(a):""}},d.nice=()=>e(em(e(),{floor:a=>c(Math.floor(b(a))),ceil:a=>c(Math.ceil(b(a)))})),d}function eu(){let a=et(d6()).domain([1,10]);return a.copy=()=>d5(a,eu()).base(a.base()),cZ.apply(a,arguments),a}function ev(a){return function(b){return Math.sign(b)*Math.log1p(Math.abs(b/a))}}function ew(a){return function(b){return Math.sign(b)*Math.expm1(Math.abs(b))*a}}function ex(a){var b=1,c=a(ev(1),ew(b));return c.constant=function(c){return arguments.length?a(ev(b=+c),ew(b)):b},ej(c)}function ey(){var a=ex(d6());return a.copy=function(){return d5(a,ey()).constant(a.constant())},cZ.apply(a,arguments)}function ez(a){return function(b){return b<0?-Math.pow(-b,a):Math.pow(b,a)}}function eA(a){return a<0?-Math.sqrt(-a):Math.sqrt(a)}function eB(a){return a<0?-a*a:a*a}function eC(a){var b=a(d1,d1),c=1;return b.exponent=function(b){return arguments.length?1==(c=+b)?a(d1,d1):.5===c?a(eA,eB):a(ez(c),ez(1/c)):c},ej(b)}function eD(){var a=eC(d6());return a.copy=function(){return d5(a,eD()).exponent(a.exponent())},cZ.apply(a,arguments),a}function eE(){return eD.apply(null,arguments).exponent(.5)}function eF(a){return Math.sign(a)*a*a}function eG(){var a,b=d7(),c=[0,1],d=!1;function e(c){var e,f=Math.sign(e=b(c))*Math.sqrt(Math.abs(e));return isNaN(f)?a:d?Math.round(f):f}return e.invert=function(a){return b.invert(eF(a))},e.domain=function(a){return arguments.length?(b.domain(a),e):b.domain()},e.range=function(a){return arguments.length?(b.range((c=Array.from(a,d_)).map(eF)),e):c.slice()},e.rangeRound=function(a){return e.range(a).round(!0)},e.round=function(a){return arguments.length?(d=!!a,e):d},e.clamp=function(a){return arguments.length?(b.clamp(a),e):b.clamp()},e.unknown=function(b){return arguments.length?(a=b,e):a},e.copy=function(){return eG(b.domain(),c).round(d).clamp(b.clamp()).unknown(a)},cZ.apply(e,arguments),ej(e)}function eH(a,b){let c;if(void 0===b)for(let b of a)null!=b&&(c<b||void 0===c&&b>=b)&&(c=b);else{let d=-1;for(let e of a)null!=(e=b(e,++d,a))&&(c<e||void 0===c&&e>=e)&&(c=e)}return c}function eI(a,b){let c;if(void 0===b)for(let b of a)null!=b&&(c>b||void 0===c&&b>=b)&&(c=b);else{let d=-1;for(let e of a)null!=(e=b(e,++d,a))&&(c>e||void 0===c&&e>=e)&&(c=e)}return c}function eJ(a,b){return(null==a||!(a>=a))-(null==b||!(b>=b))||(a<b?-1:+(a>b))}function eK(a,b,c){let d=a[b];a[b]=a[c],a[c]=d}function eL(){var a,b=[],c=[],d=[];function e(){var a=0,e=Math.max(1,c.length);for(d=Array(e-1);++a<e;)d[a-1]=function(a,b,c=dh){if(!(!(d=a.length)||isNaN(b*=1))){if(b<=0||d<2)return+c(a[0],0,a);if(b>=1)return+c(a[d-1],d-1,a);var d,e=(d-1)*b,f=Math.floor(e),g=+c(a[f],f,a);return g+(c(a[f+1],f+1,a)-g)*(e-f)}}(b,a/e);return f}function f(b){return null==b||isNaN(b*=1)?a:c[dj(d,b)]}return f.invertExtent=function(a){var e=c.indexOf(a);return e<0?[NaN,NaN]:[e>0?d[e-1]:b[0],e<d.length?d[e]:b[b.length-1]]},f.domain=function(a){if(!arguments.length)return b.slice();for(let c of(b=[],a))null==c||isNaN(c*=1)||b.push(c);return b.sort(dd),e()},f.range=function(a){return arguments.length?(c=Array.from(a),e()):c.slice()},f.unknown=function(b){return arguments.length?(a=b,f):a},f.quantiles=function(){return d.slice()},f.copy=function(){return eL().domain(b).range(c).unknown(a)},cZ.apply(f,arguments)}function eM(){var a,b=0,c=1,d=1,e=[.5],f=[0,1];function g(b){return null!=b&&b<=b?f[dj(e,b,0,d)]:a}function h(){var a=-1;for(e=Array(d);++a<d;)e[a]=((a+1)*c-(a-d)*b)/(d+1);return g}return g.domain=function(a){return arguments.length?([b,c]=a,b*=1,c*=1,h()):[b,c]},g.range=function(a){return arguments.length?(d=(f=Array.from(a)).length-1,h()):f.slice()},g.invertExtent=function(a){var g=f.indexOf(a);return g<0?[NaN,NaN]:g<1?[b,e[0]]:g>=d?[e[d-1],c]:[e[g-1],e[g]]},g.unknown=function(b){return arguments.length&&(a=b),g},g.thresholds=function(){return e.slice()},g.copy=function(){return eM().domain([b,c]).range(f).unknown(a)},cZ.apply(ej(g),arguments)}function eN(){var a,b=[.5],c=[0,1],d=1;function e(e){return null!=e&&e<=e?c[dj(b,e,0,d)]:a}return e.domain=function(a){return arguments.length?(d=Math.min((b=Array.from(a)).length,c.length-1),e):b.slice()},e.range=function(a){return arguments.length?(c=Array.from(a),d=Math.min(b.length,c.length-1),e):c.slice()},e.invertExtent=function(a){var d=c.indexOf(a);return[b[d-1],b[d]]},e.unknown=function(b){return arguments.length?(a=b,e):a},e.copy=function(){return eN().domain(b).range(c).unknown(a)},cZ.apply(e,arguments)}g_=(g$=function(a){var b,c,d,e=void 0===a.grouping||void 0===a.thousands?ef:(b=eg.call(a.grouping,Number),c=a.thousands+"",function(a,d){for(var e=a.length,f=[],g=0,h=b[0],i=0;e>0&&h>0&&(i+h+1>d&&(h=Math.max(1,d-i)),f.push(a.substring(e-=h,e+h)),!((i+=h+1)>d));)h=b[g=(g+1)%b.length];return f.reverse().join(c)}),f=void 0===a.currency?"":a.currency[0]+"",g=void 0===a.currency?"":a.currency[1]+"",h=void 0===a.decimal?".":a.decimal+"",i=void 0===a.numerals?ef:(d=eg.call(a.numerals,String),function(a){return a.replace(/[0-9]/g,function(a){return d[+a]})}),j=void 0===a.percent?"%":a.percent+"",k=void 0===a.minus?"−":a.minus+"",l=void 0===a.nan?"NaN":a.nan+"";function m(a){var b=(a=eb(a)).fill,c=a.align,d=a.sign,m=a.symbol,n=a.zero,o=a.width,p=a.comma,q=a.precision,r=a.trim,s=a.type;"n"===s?(p=!0,s="g"):ee[s]||(void 0===q&&(q=12),r=!0,s="g"),(n||"0"===b&&"="===c)&&(n=!0,b="0",c="=");var t="$"===m?f:"#"===m&&/[boxX]/.test(s)?"0"+s.toLowerCase():"",u="$"===m?g:/[%p]/.test(s)?j:"",v=ee[s],w=/[defgprs%]/.test(s);function x(a){var f,g,j,m=t,x=u;if("c"===s)x=v(a)+x,a="";else{var y=(a*=1)<0||1/a<0;if(a=isNaN(a)?l:v(Math.abs(a),q),r&&(a=function(a){a:for(var b,c=a.length,d=1,e=-1;d<c;++d)switch(a[d]){case".":e=b=d;break;case"0":0===e&&(e=d),b=d;break;default:if(!+a[d])break a;e>0&&(e=0)}return e>0?a.slice(0,e)+a.slice(b+1):a}(a)),y&&0==+a&&"+"!==d&&(y=!1),m=(y?"("===d?d:k:"-"===d||"("===d?"":d)+m,x=("s"===s?eh[8+gZ/3]:"")+x+(y&&"("===d?")":""),w){for(f=-1,g=a.length;++f<g;)if(48>(j=a.charCodeAt(f))||j>57){x=(46===j?h+a.slice(f+1):a.slice(f))+x,a=a.slice(0,f);break}}}p&&!n&&(a=e(a,1/0));var z=m.length+a.length+x.length,A=z<o?Array(o-z+1).join(b):"";switch(p&&n&&(a=e(A+a,A.length?o-x.length:1/0),A=""),c){case"<":a=m+a+x+A;break;case"=":a=m+A+a+x;break;case"^":a=A.slice(0,z=A.length>>1)+m+a+x+A.slice(z);break;default:a=A+m+a+x}return i(a)}return q=void 0===q?6:/[gprs]/.test(s)?Math.max(1,Math.min(21,q)):Math.max(0,Math.min(20,q)),x.toString=function(){return a+""},x}return{format:m,formatPrefix:function(a,b){var c=m(((a=eb(a)).type="f",a)),d=3*Math.max(-8,Math.min(8,Math.floor(d9(b)/3))),e=Math.pow(10,-d),f=eh[8+d/3];return function(a){return c(e*a)+f}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,g0=g$.formatPrefix;let eO=new Date,eP=new Date;function eQ(a,b,c,d){function e(b){return a(b=0==arguments.length?new Date:new Date(+b)),b}return e.floor=b=>(a(b=new Date(+b)),b),e.ceil=c=>(a(c=new Date(c-1)),b(c,1),a(c),c),e.round=a=>{let b=e(a),c=e.ceil(a);return a-b<c-a?b:c},e.offset=(a,c)=>(b(a=new Date(+a),null==c?1:Math.floor(c)),a),e.range=(c,d,f)=>{let g,h=[];if(c=e.ceil(c),f=null==f?1:Math.floor(f),!(c<d)||!(f>0))return h;do h.push(g=new Date(+c)),b(c,f),a(c);while(g<c&&c<d)return h},e.filter=c=>eQ(b=>{if(b>=b)for(;a(b),!c(b);)b.setTime(b-1)},(a,d)=>{if(a>=a)if(d<0)for(;++d<=0;)for(;b(a,-1),!c(a););else for(;--d>=0;)for(;b(a,1),!c(a););}),c&&(e.count=(b,d)=>(eO.setTime(+b),eP.setTime(+d),a(eO),a(eP),Math.floor(c(eO,eP))),e.every=a=>isFinite(a=Math.floor(a))&&a>0?a>1?e.filter(d?b=>d(b)%a==0:b=>e.count(0,b)%a==0):e:null),e}let eR=eQ(a=>{a.setMonth(0,1),a.setHours(0,0,0,0)},(a,b)=>{a.setFullYear(a.getFullYear()+b)},(a,b)=>b.getFullYear()-a.getFullYear(),a=>a.getFullYear());eR.every=a=>isFinite(a=Math.floor(a))&&a>0?eQ(b=>{b.setFullYear(Math.floor(b.getFullYear()/a)*a),b.setMonth(0,1),b.setHours(0,0,0,0)},(b,c)=>{b.setFullYear(b.getFullYear()+c*a)}):null,eR.range;let eS=eQ(a=>{a.setUTCMonth(0,1),a.setUTCHours(0,0,0,0)},(a,b)=>{a.setUTCFullYear(a.getUTCFullYear()+b)},(a,b)=>b.getUTCFullYear()-a.getUTCFullYear(),a=>a.getUTCFullYear());eS.every=a=>isFinite(a=Math.floor(a))&&a>0?eQ(b=>{b.setUTCFullYear(Math.floor(b.getUTCFullYear()/a)*a),b.setUTCMonth(0,1),b.setUTCHours(0,0,0,0)},(b,c)=>{b.setUTCFullYear(b.getUTCFullYear()+c*a)}):null,eS.range;let eT=eQ(a=>{a.setDate(1),a.setHours(0,0,0,0)},(a,b)=>{a.setMonth(a.getMonth()+b)},(a,b)=>b.getMonth()-a.getMonth()+(b.getFullYear()-a.getFullYear())*12,a=>a.getMonth());eT.range;let eU=eQ(a=>{a.setUTCDate(1),a.setUTCHours(0,0,0,0)},(a,b)=>{a.setUTCMonth(a.getUTCMonth()+b)},(a,b)=>b.getUTCMonth()-a.getUTCMonth()+(b.getUTCFullYear()-a.getUTCFullYear())*12,a=>a.getUTCMonth());eU.range;function eV(a){return eQ(b=>{b.setDate(b.getDate()-(b.getDay()+7-a)%7),b.setHours(0,0,0,0)},(a,b)=>{a.setDate(a.getDate()+7*b)},(a,b)=>(b-a-(b.getTimezoneOffset()-a.getTimezoneOffset())*6e4)/6048e5)}let eW=eV(0),eX=eV(1),eY=eV(2),eZ=eV(3),e$=eV(4),e_=eV(5),e0=eV(6);function e1(a){return eQ(b=>{b.setUTCDate(b.getUTCDate()-(b.getUTCDay()+7-a)%7),b.setUTCHours(0,0,0,0)},(a,b)=>{a.setUTCDate(a.getUTCDate()+7*b)},(a,b)=>(b-a)/6048e5)}eW.range,eX.range,eY.range,eZ.range,e$.range,e_.range,e0.range;let e2=e1(0),e3=e1(1),e4=e1(2),e5=e1(3),e6=e1(4),e7=e1(5),e8=e1(6);e2.range,e3.range,e4.range,e5.range,e6.range,e7.range,e8.range;let e9=eQ(a=>a.setHours(0,0,0,0),(a,b)=>a.setDate(a.getDate()+b),(a,b)=>(b-a-(b.getTimezoneOffset()-a.getTimezoneOffset())*6e4)/864e5,a=>a.getDate()-1);e9.range;let fa=eQ(a=>{a.setUTCHours(0,0,0,0)},(a,b)=>{a.setUTCDate(a.getUTCDate()+b)},(a,b)=>(b-a)/864e5,a=>a.getUTCDate()-1);fa.range;let fb=eQ(a=>{a.setUTCHours(0,0,0,0)},(a,b)=>{a.setUTCDate(a.getUTCDate()+b)},(a,b)=>(b-a)/864e5,a=>Math.floor(a/864e5));fb.range;let fc=eQ(a=>{a.setTime(a-a.getMilliseconds()-1e3*a.getSeconds()-6e4*a.getMinutes())},(a,b)=>{a.setTime(+a+36e5*b)},(a,b)=>(b-a)/36e5,a=>a.getHours());fc.range;let fd=eQ(a=>{a.setUTCMinutes(0,0,0)},(a,b)=>{a.setTime(+a+36e5*b)},(a,b)=>(b-a)/36e5,a=>a.getUTCHours());fd.range;let fe=eQ(a=>{a.setTime(a-a.getMilliseconds()-1e3*a.getSeconds())},(a,b)=>{a.setTime(+a+6e4*b)},(a,b)=>(b-a)/6e4,a=>a.getMinutes());fe.range;let ff=eQ(a=>{a.setUTCSeconds(0,0)},(a,b)=>{a.setTime(+a+6e4*b)},(a,b)=>(b-a)/6e4,a=>a.getUTCMinutes());ff.range;let fg=eQ(a=>{a.setTime(a-a.getMilliseconds())},(a,b)=>{a.setTime(+a+1e3*b)},(a,b)=>(b-a)/1e3,a=>a.getUTCSeconds());fg.range;let fh=eQ(()=>{},(a,b)=>{a.setTime(+a+b)},(a,b)=>b-a);function fi(a,b,c,d,e,f){let g=[[fg,1,1e3],[fg,5,5e3],[fg,15,15e3],[fg,30,3e4],[f,1,6e4],[f,5,3e5],[f,15,9e5],[f,30,18e5],[e,1,36e5],[e,3,108e5],[e,6,216e5],[e,12,432e5],[d,1,864e5],[d,2,1728e5],[c,1,6048e5],[b,1,2592e6],[b,3,7776e6],[a,1,31536e6]];function h(b,c,d){let e=Math.abs(c-b)/d,f=df(([,,a])=>a).right(g,e);if(f===g.length)return a.every(dc(b/31536e6,c/31536e6,d));if(0===f)return fh.every(Math.max(dc(b,c,d),1));let[h,i]=g[e/g[f-1][2]<g[f][2]/e?f-1:f];return h.every(i)}return[function(a,b,c){let d=b<a;d&&([a,b]=[b,a]);let e=c&&"function"==typeof c.range?c:h(a,b,c),f=e?e.range(a,+b+1):[];return d?f.reverse():f},h]}fh.every=a=>isFinite(a=Math.floor(a))&&a>0?a>1?eQ(b=>{b.setTime(Math.floor(b/a)*a)},(b,c)=>{b.setTime(+b+c*a)},(b,c)=>(c-b)/a):fh:null,fh.range;let[fj,fk]=fi(eS,eU,e2,fb,fd,ff),[fl,fm]=fi(eR,eT,eW,e9,fc,fe);function fn(a){if(0<=a.y&&a.y<100){var b=new Date(-1,a.m,a.d,a.H,a.M,a.S,a.L);return b.setFullYear(a.y),b}return new Date(a.y,a.m,a.d,a.H,a.M,a.S,a.L)}function fo(a){if(0<=a.y&&a.y<100){var b=new Date(Date.UTC(-1,a.m,a.d,a.H,a.M,a.S,a.L));return b.setUTCFullYear(a.y),b}return new Date(Date.UTC(a.y,a.m,a.d,a.H,a.M,a.S,a.L))}function fp(a,b,c){return{y:a,m:b,d:c,H:0,M:0,S:0,L:0}}var fq={"-":"",_:" ",0:"0"},fr=/^\s*\d+/,fs=/^%/,ft=/[\\^$*+?|[\]().{}]/g;function fu(a,b,c){var d=a<0?"-":"",e=(d?-a:a)+"",f=e.length;return d+(f<c?Array(c-f+1).join(b)+e:e)}function fv(a){return a.replace(ft,"\\$&")}function fw(a){return RegExp("^(?:"+a.map(fv).join("|")+")","i")}function fx(a){return new Map(a.map((a,b)=>[a.toLowerCase(),b]))}function fy(a,b,c){var d=fr.exec(b.slice(c,c+1));return d?(a.w=+d[0],c+d[0].length):-1}function fz(a,b,c){var d=fr.exec(b.slice(c,c+1));return d?(a.u=+d[0],c+d[0].length):-1}function fA(a,b,c){var d=fr.exec(b.slice(c,c+2));return d?(a.U=+d[0],c+d[0].length):-1}function fB(a,b,c){var d=fr.exec(b.slice(c,c+2));return d?(a.V=+d[0],c+d[0].length):-1}function fC(a,b,c){var d=fr.exec(b.slice(c,c+2));return d?(a.W=+d[0],c+d[0].length):-1}function fD(a,b,c){var d=fr.exec(b.slice(c,c+4));return d?(a.y=+d[0],c+d[0].length):-1}function fE(a,b,c){var d=fr.exec(b.slice(c,c+2));return d?(a.y=+d[0]+(+d[0]>68?1900:2e3),c+d[0].length):-1}function fF(a,b,c){var d=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(b.slice(c,c+6));return d?(a.Z=d[1]?0:-(d[2]+(d[3]||"00")),c+d[0].length):-1}function fG(a,b,c){var d=fr.exec(b.slice(c,c+1));return d?(a.q=3*d[0]-3,c+d[0].length):-1}function fH(a,b,c){var d=fr.exec(b.slice(c,c+2));return d?(a.m=d[0]-1,c+d[0].length):-1}function fI(a,b,c){var d=fr.exec(b.slice(c,c+2));return d?(a.d=+d[0],c+d[0].length):-1}function fJ(a,b,c){var d=fr.exec(b.slice(c,c+3));return d?(a.m=0,a.d=+d[0],c+d[0].length):-1}function fK(a,b,c){var d=fr.exec(b.slice(c,c+2));return d?(a.H=+d[0],c+d[0].length):-1}function fL(a,b,c){var d=fr.exec(b.slice(c,c+2));return d?(a.M=+d[0],c+d[0].length):-1}function fM(a,b,c){var d=fr.exec(b.slice(c,c+2));return d?(a.S=+d[0],c+d[0].length):-1}function fN(a,b,c){var d=fr.exec(b.slice(c,c+3));return d?(a.L=+d[0],c+d[0].length):-1}function fO(a,b,c){var d=fr.exec(b.slice(c,c+6));return d?(a.L=Math.floor(d[0]/1e3),c+d[0].length):-1}function fP(a,b,c){var d=fs.exec(b.slice(c,c+1));return d?c+d[0].length:-1}function fQ(a,b,c){var d=fr.exec(b.slice(c));return d?(a.Q=+d[0],c+d[0].length):-1}function fR(a,b,c){var d=fr.exec(b.slice(c));return d?(a.s=+d[0],c+d[0].length):-1}function fS(a,b){return fu(a.getDate(),b,2)}function fT(a,b){return fu(a.getHours(),b,2)}function fU(a,b){return fu(a.getHours()%12||12,b,2)}function fV(a,b){return fu(1+e9.count(eR(a),a),b,3)}function fW(a,b){return fu(a.getMilliseconds(),b,3)}function fX(a,b){return fW(a,b)+"000"}function fY(a,b){return fu(a.getMonth()+1,b,2)}function fZ(a,b){return fu(a.getMinutes(),b,2)}function f$(a,b){return fu(a.getSeconds(),b,2)}function f_(a){var b=a.getDay();return 0===b?7:b}function f0(a,b){return fu(eW.count(eR(a)-1,a),b,2)}function f1(a){var b=a.getDay();return b>=4||0===b?e$(a):e$.ceil(a)}function f2(a,b){return a=f1(a),fu(e$.count(eR(a),a)+(4===eR(a).getDay()),b,2)}function f3(a){return a.getDay()}function f4(a,b){return fu(eX.count(eR(a)-1,a),b,2)}function f5(a,b){return fu(a.getFullYear()%100,b,2)}function f6(a,b){return fu((a=f1(a)).getFullYear()%100,b,2)}function f7(a,b){return fu(a.getFullYear()%1e4,b,4)}function f8(a,b){var c=a.getDay();return fu((a=c>=4||0===c?e$(a):e$.ceil(a)).getFullYear()%1e4,b,4)}function f9(a){var b=a.getTimezoneOffset();return(b>0?"-":(b*=-1,"+"))+fu(b/60|0,"0",2)+fu(b%60,"0",2)}function ga(a,b){return fu(a.getUTCDate(),b,2)}function gb(a,b){return fu(a.getUTCHours(),b,2)}function gc(a,b){return fu(a.getUTCHours()%12||12,b,2)}function gd(a,b){return fu(1+fa.count(eS(a),a),b,3)}function ge(a,b){return fu(a.getUTCMilliseconds(),b,3)}function gf(a,b){return ge(a,b)+"000"}function gg(a,b){return fu(a.getUTCMonth()+1,b,2)}function gh(a,b){return fu(a.getUTCMinutes(),b,2)}function gi(a,b){return fu(a.getUTCSeconds(),b,2)}function gj(a){var b=a.getUTCDay();return 0===b?7:b}function gk(a,b){return fu(e2.count(eS(a)-1,a),b,2)}function gl(a){var b=a.getUTCDay();return b>=4||0===b?e6(a):e6.ceil(a)}function gm(a,b){return a=gl(a),fu(e6.count(eS(a),a)+(4===eS(a).getUTCDay()),b,2)}function gn(a){return a.getUTCDay()}function go(a,b){return fu(e3.count(eS(a)-1,a),b,2)}function gp(a,b){return fu(a.getUTCFullYear()%100,b,2)}function gq(a,b){return fu((a=gl(a)).getUTCFullYear()%100,b,2)}function gr(a,b){return fu(a.getUTCFullYear()%1e4,b,4)}function gs(a,b){var c=a.getUTCDay();return fu((a=c>=4||0===c?e6(a):e6.ceil(a)).getUTCFullYear()%1e4,b,4)}function gt(){return"+0000"}function gu(){return"%"}function gv(a){return+a}function gw(a){return Math.floor(a/1e3)}function gx(a){return new Date(a)}function gy(a){return a instanceof Date?+a:+new Date(+a)}function gz(a,b,c,d,e,f,g,h,i,j){var k=d7(),l=k.invert,m=k.domain,n=j(".%L"),o=j(":%S"),p=j("%I:%M"),q=j("%I %p"),r=j("%a %d"),s=j("%b %d"),t=j("%B"),u=j("%Y");function v(a){return(i(a)<a?n:h(a)<a?o:g(a)<a?p:f(a)<a?q:d(a)<a?e(a)<a?r:s:c(a)<a?t:u)(a)}return k.invert=function(a){return new Date(l(a))},k.domain=function(a){return arguments.length?m(Array.from(a,gy)):m().map(gx)},k.ticks=function(b){var c=m();return a(c[0],c[c.length-1],null==b?10:b)},k.tickFormat=function(a,b){return null==b?v:j(b)},k.nice=function(a){var c=m();return a&&"function"==typeof a.range||(a=b(c[0],c[c.length-1],null==a?10:a)),a?m(em(c,a)):k},k.copy=function(){return d5(k,gz(a,b,c,d,e,f,g,h,i,j))},k}function gA(){return cZ.apply(gz(fl,fm,eR,eT,eW,e9,fc,fe,fg,g2).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function gB(){return cZ.apply(gz(fj,fk,eS,eU,e2,fa,fd,ff,fg,g3).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function gC(){var a,b,c,d,e,f=0,g=1,h=d1,i=!1;function j(b){return null==b||isNaN(b*=1)?e:h(0===c?.5:(b=(d(b)-a)*c,i?Math.max(0,Math.min(1,b)):b))}function k(a){return function(b){var c,d;return arguments.length?([c,d]=b,h=a(c,d),j):[h(0),h(1)]}}return j.domain=function(e){return arguments.length?([f,g]=e,a=d(f*=1),b=d(g*=1),c=a===b?0:1/(b-a),j):[f,g]},j.clamp=function(a){return arguments.length?(i=!!a,j):i},j.interpolator=function(a){return arguments.length?(h=a,j):h},j.range=k(dZ),j.rangeRound=k(d$),j.unknown=function(a){return arguments.length?(e=a,j):e},function(e){return d=e,a=e(f),b=e(g),c=a===b?0:1/(b-a),j}}function gD(a,b){return b.domain(a.domain()).interpolator(a.interpolator()).clamp(a.clamp()).unknown(a.unknown())}function gE(){var a=ej(gC()(d1));return a.copy=function(){return gD(a,gE())},c$.apply(a,arguments)}function gF(){var a=et(gC()).domain([1,10]);return a.copy=function(){return gD(a,gF()).base(a.base())},c$.apply(a,arguments)}function gG(){var a=ex(gC());return a.copy=function(){return gD(a,gG()).constant(a.constant())},c$.apply(a,arguments)}function gH(){var a=eC(gC());return a.copy=function(){return gD(a,gH()).exponent(a.exponent())},c$.apply(a,arguments)}function gI(){return gH.apply(null,arguments).exponent(.5)}function gJ(){var a=[],b=d1;function c(c){if(null!=c&&!isNaN(c*=1))return b((dj(a,c,1)-1)/(a.length-1))}return c.domain=function(b){if(!arguments.length)return a.slice();for(let c of(a=[],b))null==c||isNaN(c*=1)||a.push(c);return a.sort(dd),c},c.interpolator=function(a){return arguments.length?(b=a,c):b},c.range=function(){return a.map((c,d)=>b(d/(a.length-1)))},c.quantiles=function(b){return Array.from({length:b+1},(c,d)=>(function(a,b,c){if(!(!(d=(a=Float64Array.from(function*(a,b){if(void 0===b)for(let b of a)null!=b&&(b*=1)>=b&&(yield b);else{let c=-1;for(let d of a)null!=(d=b(d,++c,a))&&(d*=1)>=d&&(yield d)}}(a,void 0))).length)||isNaN(b*=1))){if(b<=0||d<2)return eI(a);if(b>=1)return eH(a);var d,e=(d-1)*b,f=Math.floor(e),g=eH((function a(b,c,d=0,e=1/0,f){if(c=Math.floor(c),d=Math.floor(Math.max(0,d)),e=Math.floor(Math.min(b.length-1,e)),!(d<=c&&c<=e))return b;for(f=void 0===f?eJ:function(a=dd){if(a===dd)return eJ;if("function"!=typeof a)throw TypeError("compare is not a function");return(b,c)=>{let d=a(b,c);return d||0===d?d:(0===a(c,c))-(0===a(b,b))}}(f);e>d;){if(e-d>600){let g=e-d+1,h=c-d+1,i=Math.log(g),j=.5*Math.exp(2*i/3),k=.5*Math.sqrt(i*j*(g-j)/g)*(h-g/2<0?-1:1),l=Math.max(d,Math.floor(c-h*j/g+k)),m=Math.min(e,Math.floor(c+(g-h)*j/g+k));a(b,c,l,m,f)}let g=b[c],h=d,i=e;for(eK(b,d,c),f(b[e],g)>0&&eK(b,d,e);h<i;){for(eK(b,h,i),++h,--i;0>f(b[h],g);)++h;for(;f(b[i],g)>0;)--i}0===f(b[d],g)?eK(b,d,i):eK(b,++i,e),i<=c&&(d=i+1),c<=i&&(e=i-1)}return b})(a,f).subarray(0,f+1));return g+(eI(a.subarray(f+1))-g)*(e-f)}})(a,d/b))},c.copy=function(){return gJ(b).domain(a)},c$.apply(c,arguments)}function gK(){var a,b,c,d,e,f,g,h=0,i=.5,j=1,k=1,l=d1,m=!1;function n(a){return isNaN(a*=1)?g:(a=.5+((a=+f(a))-b)*(k*a<k*b?d:e),l(m?Math.max(0,Math.min(1,a)):a))}function o(a){return function(b){var c,d,e;return arguments.length?([c,d,e]=b,l=function(a,b){void 0===b&&(b=a,a=dZ);for(var c=0,d=b.length-1,e=b[0],f=Array(d<0?0:d);c<d;)f[c]=a(e,e=b[++c]);return function(a){var b=Math.max(0,Math.min(d-1,Math.floor(a*=d)));return f[b](a-b)}}(a,[c,d,e]),n):[l(0),l(.5),l(1)]}}return n.domain=function(g){return arguments.length?([h,i,j]=g,a=f(h*=1),b=f(i*=1),c=f(j*=1),d=a===b?0:.5/(b-a),e=b===c?0:.5/(c-b),k=b<a?-1:1,n):[h,i,j]},n.clamp=function(a){return arguments.length?(m=!!a,n):m},n.interpolator=function(a){return arguments.length?(l=a,n):l},n.range=o(dZ),n.rangeRound=o(d$),n.unknown=function(a){return arguments.length?(g=a,n):g},function(g){return f=g,a=g(h),b=g(i),c=g(j),d=a===b?0:.5/(b-a),e=b===c?0:.5/(c-b),k=b<a?-1:1,n}}function gL(){var a=ej(gK()(d1));return a.copy=function(){return gD(a,gL())},c$.apply(a,arguments)}function gM(){var a=et(gK()).domain([.1,1,10]);return a.copy=function(){return gD(a,gM()).base(a.base())},c$.apply(a,arguments)}function gN(){var a=ex(gK());return a.copy=function(){return gD(a,gN()).constant(a.constant())},c$.apply(a,arguments)}function gO(){var a=eC(gK());return a.copy=function(){return gD(a,gO()).exponent(a.exponent())},c$.apply(a,arguments)}function gP(){return gO.apply(null,arguments).exponent(.5)}g2=(g1=function(a){var b=a.dateTime,c=a.date,d=a.time,e=a.periods,f=a.days,g=a.shortDays,h=a.months,i=a.shortMonths,j=fw(e),k=fx(e),l=fw(f),m=fx(f),n=fw(g),o=fx(g),p=fw(h),q=fx(h),r=fw(i),s=fx(i),t={a:function(a){return g[a.getDay()]},A:function(a){return f[a.getDay()]},b:function(a){return i[a.getMonth()]},B:function(a){return h[a.getMonth()]},c:null,d:fS,e:fS,f:fX,g:f6,G:f8,H:fT,I:fU,j:fV,L:fW,m:fY,M:fZ,p:function(a){return e[+(a.getHours()>=12)]},q:function(a){return 1+~~(a.getMonth()/3)},Q:gv,s:gw,S:f$,u:f_,U:f0,V:f2,w:f3,W:f4,x:null,X:null,y:f5,Y:f7,Z:f9,"%":gu},u={a:function(a){return g[a.getUTCDay()]},A:function(a){return f[a.getUTCDay()]},b:function(a){return i[a.getUTCMonth()]},B:function(a){return h[a.getUTCMonth()]},c:null,d:ga,e:ga,f:gf,g:gq,G:gs,H:gb,I:gc,j:gd,L:ge,m:gg,M:gh,p:function(a){return e[+(a.getUTCHours()>=12)]},q:function(a){return 1+~~(a.getUTCMonth()/3)},Q:gv,s:gw,S:gi,u:gj,U:gk,V:gm,w:gn,W:go,x:null,X:null,y:gp,Y:gr,Z:gt,"%":gu},v={a:function(a,b,c){var d=n.exec(b.slice(c));return d?(a.w=o.get(d[0].toLowerCase()),c+d[0].length):-1},A:function(a,b,c){var d=l.exec(b.slice(c));return d?(a.w=m.get(d[0].toLowerCase()),c+d[0].length):-1},b:function(a,b,c){var d=r.exec(b.slice(c));return d?(a.m=s.get(d[0].toLowerCase()),c+d[0].length):-1},B:function(a,b,c){var d=p.exec(b.slice(c));return d?(a.m=q.get(d[0].toLowerCase()),c+d[0].length):-1},c:function(a,c,d){return y(a,b,c,d)},d:fI,e:fI,f:fO,g:fE,G:fD,H:fK,I:fK,j:fJ,L:fN,m:fH,M:fL,p:function(a,b,c){var d=j.exec(b.slice(c));return d?(a.p=k.get(d[0].toLowerCase()),c+d[0].length):-1},q:fG,Q:fQ,s:fR,S:fM,u:fz,U:fA,V:fB,w:fy,W:fC,x:function(a,b,d){return y(a,c,b,d)},X:function(a,b,c){return y(a,d,b,c)},y:fE,Y:fD,Z:fF,"%":fP};function w(a,b){return function(c){var d,e,f,g=[],h=-1,i=0,j=a.length;for(c instanceof Date||(c=new Date(+c));++h<j;)37===a.charCodeAt(h)&&(g.push(a.slice(i,h)),null!=(e=fq[d=a.charAt(++h)])?d=a.charAt(++h):e="e"===d?" ":"0",(f=b[d])&&(d=f(c,e)),g.push(d),i=h+1);return g.push(a.slice(i,h)),g.join("")}}function x(a,b){return function(c){var d,e,f=fp(1900,void 0,1);if(y(f,a,c+="",0)!=c.length)return null;if("Q"in f)return new Date(f.Q);if("s"in f)return new Date(1e3*f.s+("L"in f?f.L:0));if(!b||"Z"in f||(f.Z=0),"p"in f&&(f.H=f.H%12+12*f.p),void 0===f.m&&(f.m="q"in f?f.q:0),"V"in f){if(f.V<1||f.V>53)return null;"w"in f||(f.w=1),"Z"in f?(d=(e=(d=fo(fp(f.y,0,1))).getUTCDay())>4||0===e?e3.ceil(d):e3(d),d=fa.offset(d,(f.V-1)*7),f.y=d.getUTCFullYear(),f.m=d.getUTCMonth(),f.d=d.getUTCDate()+(f.w+6)%7):(d=(e=(d=fn(fp(f.y,0,1))).getDay())>4||0===e?eX.ceil(d):eX(d),d=e9.offset(d,(f.V-1)*7),f.y=d.getFullYear(),f.m=d.getMonth(),f.d=d.getDate()+(f.w+6)%7)}else("W"in f||"U"in f)&&("w"in f||(f.w="u"in f?f.u%7:+("W"in f)),e="Z"in f?fo(fp(f.y,0,1)).getUTCDay():fn(fp(f.y,0,1)).getDay(),f.m=0,f.d="W"in f?(f.w+6)%7+7*f.W-(e+5)%7:f.w+7*f.U-(e+6)%7);return"Z"in f?(f.H+=f.Z/100|0,f.M+=f.Z%100,fo(f)):fn(f)}}function y(a,b,c,d){for(var e,f,g=0,h=b.length,i=c.length;g<h;){if(d>=i)return -1;if(37===(e=b.charCodeAt(g++))){if(!(f=v[(e=b.charAt(g++))in fq?b.charAt(g++):e])||(d=f(a,c,d))<0)return -1}else if(e!=c.charCodeAt(d++))return -1}return d}return t.x=w(c,t),t.X=w(d,t),t.c=w(b,t),u.x=w(c,u),u.X=w(d,u),u.c=w(b,u),{format:function(a){var b=w(a+="",t);return b.toString=function(){return a},b},parse:function(a){var b=x(a+="",!1);return b.toString=function(){return a},b},utcFormat:function(a){var b=w(a+="",u);return b.toString=function(){return a},b},utcParse:function(a){var b=x(a+="",!0);return b.toString=function(){return a},b}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,g1.parse,g3=g1.utcFormat,g1.utcParse,a.i(48117);var gQ=a.i(62599),gR=a=>a.chartData,gS=ak([gR],a=>{var b=null!=a.chartData?a.chartData.length-1:0;return{chartData:a.chartData,computedData:a.computedData,dataEndIndex:b,dataStartIndex:0}}),gT=(a,b,c,d)=>d?gS(a):gR(a);function gU(a){return Number.isFinite(a)}function gV(a){return"number"==typeof a&&a>0&&Number.isFinite(a)}function gW(a){if(Array.isArray(a)&&2===a.length){var[b,c]=a;if(gU(b)&&gU(c))return!0}return!1}function gX(a,b,c){return c?a:[Math.min(a[0],b[0]),Math.max(a[1],b[1])]}var gY,gZ,g$,g_,g0,g1,g2,g3,g4,g5,g6=!0,g7="[DecimalError] ",g8=g7+"Invalid argument: ",g9=g7+"Exponent out of range: ",ha=Math.floor,hb=Math.pow,hc=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,hd=ha(1286742750677284.5),he={};function hf(a,b){var c,d,e,f,g,h,i,j,k=a.constructor,l=k.precision;if(!a.s||!b.s)return b.s||(b=new k(a)),g6?hp(b,l):b;if(i=a.d,j=b.d,g=a.e,e=b.e,i=i.slice(),f=g-e){for(f<0?(d=i,f=-f,h=j.length):(d=j,e=g,h=i.length),f>(h=(g=Math.ceil(l/7))>h?g+1:h+1)&&(f=h,d.length=1),d.reverse();f--;)d.push(0);d.reverse()}for((h=i.length)-(f=j.length)<0&&(f=h,d=j,j=i,i=d),c=0;f;)c=(i[--f]=i[f]+j[f]+c)/1e7|0,i[f]%=1e7;for(c&&(i.unshift(c),++e),h=i.length;0==i[--h];)i.pop();return b.d=i,b.e=e,g6?hp(b,l):b}function hg(a,b,c){if(a!==~~a||a<b||a>c)throw Error(g8+a)}function hh(a){var b,c,d,e=a.length-1,f="",g=a[0];if(e>0){for(f+=g,b=1;b<e;b++)(c=7-(d=a[b]+"").length)&&(f+=hm(c)),f+=d;(c=7-(d=(g=a[b])+"").length)&&(f+=hm(c))}else if(0===g)return"0";for(;g%10==0;)g/=10;return f+g}he.absoluteValue=he.abs=function(){var a=new this.constructor(this);return a.s&&(a.s=1),a},he.comparedTo=he.cmp=function(a){var b,c,d,e;if(a=new this.constructor(a),this.s!==a.s)return this.s||-a.s;if(this.e!==a.e)return this.e>a.e^this.s<0?1:-1;for(b=0,c=(d=this.d.length)<(e=a.d.length)?d:e;b<c;++b)if(this.d[b]!==a.d[b])return this.d[b]>a.d[b]^this.s<0?1:-1;return d===e?0:d>e^this.s<0?1:-1},he.decimalPlaces=he.dp=function(){var a=this.d.length-1,b=(a-this.e)*7;if(a=this.d[a])for(;a%10==0;a/=10)b--;return b<0?0:b},he.dividedBy=he.div=function(a){return hi(this,new this.constructor(a))},he.dividedToIntegerBy=he.idiv=function(a){var b=this.constructor;return hp(hi(this,new b(a),0,1),b.precision)},he.equals=he.eq=function(a){return!this.cmp(a)},he.exponent=function(){return hk(this)},he.greaterThan=he.gt=function(a){return this.cmp(a)>0},he.greaterThanOrEqualTo=he.gte=function(a){return this.cmp(a)>=0},he.isInteger=he.isint=function(){return this.e>this.d.length-2},he.isNegative=he.isneg=function(){return this.s<0},he.isPositive=he.ispos=function(){return this.s>0},he.isZero=function(){return 0===this.s},he.lessThan=he.lt=function(a){return 0>this.cmp(a)},he.lessThanOrEqualTo=he.lte=function(a){return 1>this.cmp(a)},he.logarithm=he.log=function(a){var b,c=this.constructor,d=c.precision,e=d+5;if(void 0===a)a=new c(10);else if((a=new c(a)).s<1||a.eq(g5))throw Error(g7+"NaN");if(this.s<1)throw Error(g7+(this.s?"NaN":"-Infinity"));return this.eq(g5)?new c(0):(g6=!1,b=hi(hn(this,e),hn(a,e),e),g6=!0,hp(b,d))},he.minus=he.sub=function(a){return a=new this.constructor(a),this.s==a.s?hq(this,a):hf(this,(a.s=-a.s,a))},he.modulo=he.mod=function(a){var b,c=this.constructor,d=c.precision;if(!(a=new c(a)).s)throw Error(g7+"NaN");return this.s?(g6=!1,b=hi(this,a,0,1).times(a),g6=!0,this.minus(b)):hp(new c(this),d)},he.naturalExponential=he.exp=function(){return hj(this)},he.naturalLogarithm=he.ln=function(){return hn(this)},he.negated=he.neg=function(){var a=new this.constructor(this);return a.s=-a.s||0,a},he.plus=he.add=function(a){return a=new this.constructor(a),this.s==a.s?hf(this,a):hq(this,(a.s=-a.s,a))},he.precision=he.sd=function(a){var b,c,d;if(void 0!==a&&!!a!==a&&1!==a&&0!==a)throw Error(g8+a);if(b=hk(this)+1,c=7*(d=this.d.length-1)+1,d=this.d[d]){for(;d%10==0;d/=10)c--;for(d=this.d[0];d>=10;d/=10)c++}return a&&b>c?b:c},he.squareRoot=he.sqrt=function(){var a,b,c,d,e,f,g,h=this.constructor;if(this.s<1){if(!this.s)return new h(0);throw Error(g7+"NaN")}for(a=hk(this),g6=!1,0==(e=Math.sqrt(+this))||e==1/0?(((b=hh(this.d)).length+a)%2==0&&(b+="0"),e=Math.sqrt(b),a=ha((a+1)/2)-(a<0||a%2),d=new h(b=e==1/0?"5e"+a:(b=e.toExponential()).slice(0,b.indexOf("e")+1)+a)):d=new h(e.toString()),e=g=(c=h.precision)+3;;)if(d=(f=d).plus(hi(this,f,g+2)).times(.5),hh(f.d).slice(0,g)===(b=hh(d.d)).slice(0,g)){if(b=b.slice(g-3,g+1),e==g&&"4999"==b){if(hp(f,c+1,0),f.times(f).eq(this)){d=f;break}}else if("9999"!=b)break;g+=4}return g6=!0,hp(d,c)},he.times=he.mul=function(a){var b,c,d,e,f,g,h,i,j,k=this.constructor,l=this.d,m=(a=new k(a)).d;if(!this.s||!a.s)return new k(0);for(a.s*=this.s,c=this.e+a.e,(i=l.length)<(j=m.length)&&(f=l,l=m,m=f,g=i,i=j,j=g),f=[],d=g=i+j;d--;)f.push(0);for(d=j;--d>=0;){for(b=0,e=i+d;e>d;)h=f[e]+m[d]*l[e-d-1]+b,f[e--]=h%1e7|0,b=h/1e7|0;f[e]=(f[e]+b)%1e7|0}for(;!f[--g];)f.pop();return b?++c:f.shift(),a.d=f,a.e=c,g6?hp(a,k.precision):a},he.toDecimalPlaces=he.todp=function(a,b){var c=this,d=c.constructor;return(c=new d(c),void 0===a)?c:(hg(a,0,1e9),void 0===b?b=d.rounding:hg(b,0,8),hp(c,a+hk(c)+1,b))},he.toExponential=function(a,b){var c,d=this,e=d.constructor;return void 0===a?c=hr(d,!0):(hg(a,0,1e9),void 0===b?b=e.rounding:hg(b,0,8),c=hr(d=hp(new e(d),a+1,b),!0,a+1)),c},he.toFixed=function(a,b){var c,d,e=this.constructor;return void 0===a?hr(this):(hg(a,0,1e9),void 0===b?b=e.rounding:hg(b,0,8),c=hr((d=hp(new e(this),a+hk(this)+1,b)).abs(),!1,a+hk(d)+1),this.isneg()&&!this.isZero()?"-"+c:c)},he.toInteger=he.toint=function(){var a=this.constructor;return hp(new a(this),hk(this)+1,a.rounding)},he.toNumber=function(){return+this},he.toPower=he.pow=function(a){var b,c,d,e,f,g,h=this,i=h.constructor,j=+(a=new i(a));if(!a.s)return new i(g5);if(!(h=new i(h)).s){if(a.s<1)throw Error(g7+"Infinity");return h}if(h.eq(g5))return h;if(d=i.precision,a.eq(g5))return hp(h,d);if(g=(b=a.e)>=(c=a.d.length-1),f=h.s,g){if((c=j<0?-j:j)<=0x1fffffffffffff){for(e=new i(g5),b=Math.ceil(d/7+4),g6=!1;c%2&&hs((e=e.times(h)).d,b),0!==(c=ha(c/2));)hs((h=h.times(h)).d,b);return g6=!0,a.s<0?new i(g5).div(e):hp(e,d)}}else if(f<0)throw Error(g7+"NaN");return f=f<0&&1&a.d[Math.max(b,c)]?-1:1,h.s=1,g6=!1,e=a.times(hn(h,d+12)),g6=!0,(e=hj(e)).s=f,e},he.toPrecision=function(a,b){var c,d,e=this,f=e.constructor;return void 0===a?(c=hk(e),d=hr(e,c<=f.toExpNeg||c>=f.toExpPos)):(hg(a,1,1e9),void 0===b?b=f.rounding:hg(b,0,8),c=hk(e=hp(new f(e),a,b)),d=hr(e,a<=c||c<=f.toExpNeg,a)),d},he.toSignificantDigits=he.tosd=function(a,b){var c=this.constructor;return void 0===a?(a=c.precision,b=c.rounding):(hg(a,1,1e9),void 0===b?b=c.rounding:hg(b,0,8)),hp(new c(this),a,b)},he.toString=he.valueOf=he.val=he.toJSON=he[Symbol.for("nodejs.util.inspect.custom")]=function(){var a=hk(this),b=this.constructor;return hr(this,a<=b.toExpNeg||a>=b.toExpPos)};var hi=function(){function a(a,b){var c,d=0,e=a.length;for(a=a.slice();e--;)c=a[e]*b+d,a[e]=c%1e7|0,d=c/1e7|0;return d&&a.unshift(d),a}function b(a,b,c,d){var e,f;if(c!=d)f=c>d?1:-1;else for(e=f=0;e<c;e++)if(a[e]!=b[e]){f=a[e]>b[e]?1:-1;break}return f}function c(a,b,c){for(var d=0;c--;)a[c]-=d,d=+(a[c]<b[c]),a[c]=1e7*d+a[c]-b[c];for(;!a[0]&&a.length>1;)a.shift()}return function(d,e,f,g){var h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z=d.constructor,A=d.s==e.s?1:-1,B=d.d,C=e.d;if(!d.s)return new z(d);if(!e.s)throw Error(g7+"Division by zero");for(j=0,i=d.e-e.e,x=C.length,v=B.length,o=(n=new z(A)).d=[];C[j]==(B[j]||0);)++j;if(C[j]>(B[j]||0)&&--i,(s=null==f?f=z.precision:g?f+(hk(d)-hk(e))+1:f)<0)return new z(0);if(s=s/7+2|0,j=0,1==x)for(k=0,C=C[0],s++;(j<v||k)&&s--;j++)t=1e7*k+(B[j]||0),o[j]=t/C|0,k=t%C|0;else{for((k=1e7/(C[0]+1)|0)>1&&(C=a(C,k),B=a(B,k),x=C.length,v=B.length),u=x,q=(p=B.slice(0,x)).length;q<x;)p[q++]=0;(y=C.slice()).unshift(0),w=C[0],C[1]>=1e7/2&&++w;do k=0,(h=b(C,p,x,q))<0?(r=p[0],x!=q&&(r=1e7*r+(p[1]||0)),(k=r/w|0)>1?(k>=1e7&&(k=1e7-1),m=(l=a(C,k)).length,q=p.length,1==(h=b(l,p,m,q))&&(k--,c(l,x<m?y:C,m))):(0==k&&(h=k=1),l=C.slice()),(m=l.length)<q&&l.unshift(0),c(p,l,q),-1==h&&(q=p.length,(h=b(C,p,x,q))<1&&(k++,c(p,x<q?y:C,q))),q=p.length):0===h&&(k++,p=[0]),o[j++]=k,h&&p[0]?p[q++]=B[u]||0:(p=[B[u]],q=1);while((u++<v||void 0!==p[0])&&s--)}return o[0]||o.shift(),n.e=i,hp(n,g?f+hk(n)+1:f)}}();function hj(a,b){var c,d,e,f,g,h=0,i=0,j=a.constructor,k=j.precision;if(hk(a)>16)throw Error(g9+hk(a));if(!a.s)return new j(g5);for(null==b?(g6=!1,g=k):g=b,f=new j(.03125);a.abs().gte(.1);)a=a.times(f),i+=5;for(g+=Math.log(hb(2,i))/Math.LN10*2+5|0,c=d=e=new j(g5),j.precision=g;;){if(d=hp(d.times(a),g),c=c.times(++h),hh((f=e.plus(hi(d,c,g))).d).slice(0,g)===hh(e.d).slice(0,g)){for(;i--;)e=hp(e.times(e),g);return j.precision=k,null==b?(g6=!0,hp(e,k)):e}e=f}}function hk(a){for(var b=7*a.e,c=a.d[0];c>=10;c/=10)b++;return b}function hl(a,b,c){if(b>a.LN10.sd())throw g6=!0,c&&(a.precision=c),Error(g7+"LN10 precision limit exceeded");return hp(new a(a.LN10),b)}function hm(a){for(var b="";a--;)b+="0";return b}function hn(a,b){var c,d,e,f,g,h,i,j,k,l=1,m=a,n=m.d,o=m.constructor,p=o.precision;if(m.s<1)throw Error(g7+(m.s?"NaN":"-Infinity"));if(m.eq(g5))return new o(0);if(null==b?(g6=!1,j=p):j=b,m.eq(10))return null==b&&(g6=!0),hl(o,j);if(o.precision=j+=10,d=(c=hh(n)).charAt(0),!(15e14>Math.abs(f=hk(m))))return i=hl(o,j+2,p).times(f+""),m=hn(new o(d+"."+c.slice(1)),j-10).plus(i),o.precision=p,null==b?(g6=!0,hp(m,p)):m;for(;d<7&&1!=d||1==d&&c.charAt(1)>3;)d=(c=hh((m=m.times(a)).d)).charAt(0),l++;for(f=hk(m),d>1?(m=new o("0."+c),f++):m=new o(d+"."+c.slice(1)),h=g=m=hi(m.minus(g5),m.plus(g5),j),k=hp(m.times(m),j),e=3;;){if(g=hp(g.times(k),j),hh((i=h.plus(hi(g,new o(e),j))).d).slice(0,j)===hh(h.d).slice(0,j))return h=h.times(2),0!==f&&(h=h.plus(hl(o,j+2,p).times(f+""))),h=hi(h,new o(l),j),o.precision=p,null==b?(g6=!0,hp(h,p)):h;h=i,e+=2}}function ho(a,b){var c,d,e;for((c=b.indexOf("."))>-1&&(b=b.replace(".","")),(d=b.search(/e/i))>0?(c<0&&(c=d),c+=+b.slice(d+1),b=b.substring(0,d)):c<0&&(c=b.length),d=0;48===b.charCodeAt(d);)++d;for(e=b.length;48===b.charCodeAt(e-1);)--e;if(b=b.slice(d,e)){if(e-=d,a.e=ha((c=c-d-1)/7),a.d=[],d=(c+1)%7,c<0&&(d+=7),d<e){for(d&&a.d.push(+b.slice(0,d)),e-=7;d<e;)a.d.push(+b.slice(d,d+=7));d=7-(b=b.slice(d)).length}else d-=e;for(;d--;)b+="0";if(a.d.push(+b),g6&&(a.e>hd||a.e<-hd))throw Error(g9+c)}else a.s=0,a.e=0,a.d=[0];return a}function hp(a,b,c){var d,e,f,g,h,i,j,k,l=a.d;for(g=1,f=l[0];f>=10;f/=10)g++;if((d=b-g)<0)d+=7,e=b,j=l[k=0];else{if((k=Math.ceil((d+1)/7))>=(f=l.length))return a;for(g=1,j=f=l[k];f>=10;f/=10)g++;d%=7,e=d-7+g}if(void 0!==c&&(h=j/(f=hb(10,g-e-1))%10|0,i=b<0||void 0!==l[k+1]||j%f,i=c<4?(h||i)&&(0==c||c==(a.s<0?3:2)):h>5||5==h&&(4==c||i||6==c&&(d>0?e>0?j/hb(10,g-e):0:l[k-1])%10&1||c==(a.s<0?8:7))),b<1||!l[0])return i?(f=hk(a),l.length=1,b=b-f-1,l[0]=hb(10,(7-b%7)%7),a.e=ha(-b/7)||0):(l.length=1,l[0]=a.e=a.s=0),a;if(0==d?(l.length=k,f=1,k--):(l.length=k+1,f=hb(10,7-d),l[k]=e>0?(j/hb(10,g-e)%hb(10,e)|0)*f:0),i)for(;;)if(0==k){1e7==(l[0]+=f)&&(l[0]=1,++a.e);break}else{if(l[k]+=f,1e7!=l[k])break;l[k--]=0,f=1}for(d=l.length;0===l[--d];)l.pop();if(g6&&(a.e>hd||a.e<-hd))throw Error(g9+hk(a));return a}function hq(a,b){var c,d,e,f,g,h,i,j,k,l,m=a.constructor,n=m.precision;if(!a.s||!b.s)return b.s?b.s=-b.s:b=new m(a),g6?hp(b,n):b;if(i=a.d,l=b.d,d=b.e,j=a.e,i=i.slice(),g=j-d){for((k=g<0)?(c=i,g=-g,h=l.length):(c=l,d=j,h=i.length),g>(e=Math.max(Math.ceil(n/7),h)+2)&&(g=e,c.length=1),c.reverse(),e=g;e--;)c.push(0);c.reverse()}else{for((k=(e=i.length)<(h=l.length))&&(h=e),e=0;e<h;e++)if(i[e]!=l[e]){k=i[e]<l[e];break}g=0}for(k&&(c=i,i=l,l=c,b.s=-b.s),h=i.length,e=l.length-h;e>0;--e)i[h++]=0;for(e=l.length;e>g;){if(i[--e]<l[e]){for(f=e;f&&0===i[--f];)i[f]=1e7-1;--i[f],i[e]+=1e7}i[e]-=l[e]}for(;0===i[--h];)i.pop();for(;0===i[0];i.shift())--d;return i[0]?(b.d=i,b.e=d,g6?hp(b,n):b):new m(0)}function hr(a,b,c){var d,e=hk(a),f=hh(a.d),g=f.length;return b?(c&&(d=c-g)>0?f=f.charAt(0)+"."+f.slice(1)+hm(d):g>1&&(f=f.charAt(0)+"."+f.slice(1)),f=f+(e<0?"e":"e+")+e):e<0?(f="0."+hm(-e-1)+f,c&&(d=c-g)>0&&(f+=hm(d))):e>=g?(f+=hm(e+1-g),c&&(d=c-e-1)>0&&(f=f+"."+hm(d))):((d=e+1)<g&&(f=f.slice(0,d)+"."+f.slice(d)),c&&(d=c-g)>0&&(e+1===g&&(f+="."),f+=hm(d))),a.s<0?"-"+f:f}function hs(a,b){if(a.length>b)return a.length=b,!0}function ht(a){if(!a||"object"!=typeof a)throw Error(g7+"Object expected");var b,c,d,e=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(b=0;b<e.length;b+=3)if(void 0!==(d=a[c=e[b]]))if(ha(d)===d&&d>=e[b+1]&&d<=e[b+2])this[c]=d;else throw Error(g8+c+": "+d);if(void 0!==(d=a[c="LN10"]))if(d==Math.LN10)this[c]=new this(d);else throw Error(g8+c+": "+d);return this}var g4=function a(b){var c,d,e;function f(a){if(!(this instanceof f))return new f(a);if(this.constructor=f,a instanceof f){this.s=a.s,this.e=a.e,this.d=(a=a.d)?a.slice():a;return}if("number"==typeof a){if(0*a!=0)throw Error(g8+a);if(a>0)this.s=1;else if(a<0)a=-a,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(a===~~a&&a<1e7){this.e=0,this.d=[a];return}return ho(this,a.toString())}if("string"!=typeof a)throw Error(g8+a);if(45===a.charCodeAt(0)?(a=a.slice(1),this.s=-1):this.s=1,hc.test(a))ho(this,a);else throw Error(g8+a)}if(f.prototype=he,f.ROUND_UP=0,f.ROUND_DOWN=1,f.ROUND_CEIL=2,f.ROUND_FLOOR=3,f.ROUND_HALF_UP=4,f.ROUND_HALF_DOWN=5,f.ROUND_HALF_EVEN=6,f.ROUND_HALF_CEIL=7,f.ROUND_HALF_FLOOR=8,f.clone=a,f.config=f.set=ht,void 0===b&&(b={}),b)for(c=0,e=["precision","rounding","toExpNeg","toExpPos","LN10"];c<e.length;)b.hasOwnProperty(d=e[c++])||(b[d]=this[d]);return f.config(b),f}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});g5=new g4(1);let hu=g4;var hv=a=>a,hw={},hx=a=>function b(){let c;return 0==arguments.length||1==arguments.length&&(c=arguments.length<=0?void 0:arguments[0],c===hw)?b:a(...arguments)},hy=(a,b)=>1===a?b:hx(function(){for(var c=arguments.length,d=Array(c),e=0;e<c;e++)d[e]=arguments[e];var f=d.filter(a=>a!==hw).length;return f>=a?b(...d):hy(a-f,hx(function(){for(var a=arguments.length,c=Array(a),e=0;e<a;e++)c[e]=arguments[e];return b(...d.map(a=>a===hw?c.shift():a),...c)}))}),hz=a=>hy(a.length,a),hA=(a,b)=>{for(var c=[],d=a;d<b;++d)c[d-a]=d;return c},hB=hz((a,b)=>Array.isArray(b)?b.map(a):Object.keys(b).map(a=>b[a]).map(a)),hC=function(){for(var a=arguments.length,b=Array(a),c=0;c<a;c++)b[c]=arguments[c];if(!b.length)return hv;var d=b.reverse(),e=d[0],f=d.slice(1);return function(){return f.reduce((a,b)=>b(a),e(...arguments))}},hD=a=>Array.isArray(a)?a.reverse():a.split("").reverse().join(""),hE=a=>{var b=null,c=null;return function(){for(var d=arguments.length,e=Array(d),f=0;f<d;f++)e[f]=arguments[f];return b&&e.every((a,c)=>{var d;return a===(null==(d=b)?void 0:d[c])})?c:(b=e,c=a(...e))}};function hF(a){return 0===a?1:Math.floor(new hu(a).abs().log(10).toNumber())+1}function hG(a,b,c){for(var d=new hu(a),e=0,f=[];d.lt(b)&&e<1e5;)f.push(d.toNumber()),d=d.add(c),e++;return f}hz((a,b,c)=>{var d=+a;return d+c*(b-d)}),hz((a,b,c)=>{var d=b-a;return(c-a)/(d=d||1/0)}),hz((a,b,c)=>{var d=b-a;return Math.max(0,Math.min(1,(c-a)/(d=d||1/0)))});var hH=a=>{var[b,c]=a,[d,e]=[b,c];return b>c&&([d,e]=[c,b]),[d,e]},hI=(a,b,c)=>{if(a.lte(0))return new hu(0);var d=hF(a.toNumber()),e=new hu(10).pow(d),f=a.div(e),g=1!==d?.05:.1,h=new hu(Math.ceil(f.div(g).toNumber())).add(c).mul(g).mul(e);return new hu(b?h.toNumber():Math.ceil(h.toNumber()))},hJ=function(a,b,c,d){var e,f=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((b-a)/(c-1)))return{step:new hu(0),tickMin:new hu(0),tickMax:new hu(0)};var g=hI(new hu(b).sub(a).div(c-1),d,f),h=Math.ceil((e=a<=0&&b>=0?new hu(0):(e=new hu(a).add(b).div(2)).sub(new hu(e).mod(g))).sub(a).div(g).toNumber()),i=Math.ceil(new hu(b).sub(e).div(g).toNumber()),j=h+i+1;return j>c?hJ(a,b,c,d,f+1):(j<c&&(i=b>0?i+(c-j):i,h=b>0?h:h+(c-j)),{step:g,tickMin:e.sub(new hu(h).mul(g)),tickMax:e.add(new hu(i).mul(g))})},hK=hE(function(a){var[b,c]=a,d=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,e=!(arguments.length>2)||void 0===arguments[2]||arguments[2],f=Math.max(d,2),[g,h]=hH([b,c]);if(g===-1/0||h===1/0){var i=h===1/0?[g,...hA(0,d-1).map(()=>1/0)]:[...hA(0,d-1).map(()=>-1/0),h];return b>c?hD(i):i}if(g===h){var j=new hu(1),k=new hu(g);if(!k.isint()&&e){var l=Math.abs(g);l<1?(j=new hu(10).pow(hF(g)-1),k=new hu(Math.floor(k.div(j).toNumber())).mul(j)):l>1&&(k=new hu(Math.floor(g)))}else 0===g?k=new hu(Math.floor((d-1)/2)):e||(k=new hu(Math.floor(g)));var m=Math.floor((d-1)/2);return hC(hB(a=>k.add(new hu(a-m).mul(j)).toNumber()),hA)(0,d)}var{step:n,tickMin:o,tickMax:p}=hJ(g,h,f,e,0),q=hG(o,p.add(new hu(.1).mul(n)),n);return b>c?hD(q):q}),hL=hE(function(a,b){var[c,d]=a,e=!(arguments.length>2)||void 0===arguments[2]||arguments[2],[f,g]=hH([c,d]);if(f===-1/0||g===1/0)return[c,d];if(f===g)return[f];var h=Math.max(b,2),i=hI(new hu(g).sub(f).div(h-1),e,0),j=[...hG(new hu(f),new hu(g),i),g];return!1===e&&(j=j.map(a=>Math.round(a))),c>d?hD(j):j}),hM=a=>a.rootProps.maxBarSize,hN=a=>a.rootProps.barCategoryGap,hO=a=>a.rootProps.stackOffset,hP=a=>a.options.chartName,hQ=a=>a.rootProps.syncId,hR=a=>a.rootProps.syncMethod,hS=a=>a.options.eventEmitter,hT={allowDuplicatedCategory:!0,angleAxisId:0,reversed:!1,scale:"auto",tick:!0,type:"category"},hU={allowDataOverflow:!1,allowDuplicatedCategory:!0,radiusAxisId:0,scale:"auto",tick:!0,tickCount:5,type:"number"},hV=(a,b)=>{if(a&&b)return null!=a&&a.reversed?[b[1],b[0]]:b},hW={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!1,dataKey:void 0,domain:void 0,id:hT.angleAxisId,includeHidden:!1,name:void 0,reversed:hT.reversed,scale:hT.scale,tick:hT.tick,tickCount:void 0,ticks:void 0,type:hT.type,unit:void 0},hX={allowDataOverflow:hU.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:hU.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:hU.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:hU.scale,tick:hU.tick,tickCount:hU.tickCount,ticks:void 0,type:hU.type,unit:void 0},hY={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:hT.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:hT.angleAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:hT.scale,tick:hT.tick,tickCount:void 0,ticks:void 0,type:"number",unit:void 0},hZ={allowDataOverflow:hU.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:hU.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:hU.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:hU.scale,tick:hU.tick,tickCount:hU.tickCount,ticks:void 0,type:"category",unit:void 0},h$=(a,b)=>null!=a.polarAxis.angleAxis[b]?a.polarAxis.angleAxis[b]:"radial"===a.layout.layoutType?hY:hW,h_=(a,b)=>null!=a.polarAxis.radiusAxis[b]?a.polarAxis.radiusAxis[b]:"radial"===a.layout.layoutType?hZ:hX,h0=a=>a.polarOptions,h1=ak([cA,cB,cK],cj),h2=ak([h0,h1],(a,b)=>{if(null!=a)return bj(a.innerRadius,b,0)}),h3=ak([h0,h1],(a,b)=>{if(null!=a)return bj(a.outerRadius,b,.8*b)}),h4=ak([h0],a=>{if(null==a)return[0,0];var{startAngle:b,endAngle:c}=a;return[b,c]});ak([h$,h4],hV);var h5=ak([h1,h2,h3],(a,b,c)=>{if(null!=a&&null!=b&&null!=c)return[b,c]});ak([h_,h5],hV);var h6=ak([cW,h0,h2,h3,cA,cB],(a,b,c,d,e,f)=>{if(("centric"===a||"radial"===a)&&null!=b&&null!=c&&null!=d){var{cx:g,cy:h,startAngle:i,endAngle:j}=b;return{cx:bj(g,e,e/2),cy:bj(h,f,f/2),innerRadius:c,outerRadius:d,startAngle:i,endAngle:j,clockWise:!1}}}),h7=(a,b)=>b,h8=(a,b,c)=>c;function h9(a){return null==a?void 0:a.id}var ia=a=>{var b=cW(a);return"horizontal"===b?"xAxis":"vertical"===b?"yAxis":"centric"===b?"angleAxis":"radiusAxis"},ib=a=>a.tooltip.settings.axisId,ic=a=>{var b=ia(a),c=ib(a);return ir(a,b,c)};function id(a,b,c){var{chartData:d=[]}=b,e=null==c?void 0:c.dataKey,f=new Map;return a.forEach(a=>{var b,c=null!=(b=a.data)?b:d;if(null!=c&&0!==c.length){var g=h9(a);c.forEach((b,c)=>{var d,h=null==e?c:String(cn(b,e,null)),i=cn(b,a.dataKey,0);Object.assign(d=f.has(h)?f.get(h):{},{[g]:i}),f.set(h,d)})}}),Array.from(f.values())}function ie(a){return null!=a.stackId&&null!=a.dataKey}function ig(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function ih(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?ig(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):ig(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var ii=[0,"auto"],ij={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:void 0,height:30,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"bottom",padding:{left:0,right:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"category",unit:void 0},ik=(a,b)=>{var c=a.cartesianAxis.xAxis[b];return null==c?ij:c},il={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:ii,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"left",padding:{top:0,bottom:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"number",unit:void 0,width:60},im=(a,b)=>{var c=a.cartesianAxis.yAxis[b];return null==c?il:c},io={domain:[0,"auto"],includeHidden:!1,reversed:!1,allowDataOverflow:!1,allowDuplicatedCategory:!1,dataKey:void 0,id:0,name:"",range:[64,64],scale:"auto",type:"number",unit:""},ip=(a,b)=>{var c=a.cartesianAxis.zAxis[b];return null==c?io:c},iq=(a,b,c)=>{switch(b){case"xAxis":return ik(a,c);case"yAxis":return im(a,c);case"zAxis":return ip(a,c);case"angleAxis":return h$(a,c);case"radiusAxis":return h_(a,c);default:throw Error("Unexpected axis type: ".concat(b))}},ir=(a,b,c)=>{switch(b){case"xAxis":return ik(a,c);case"yAxis":return im(a,c);case"angleAxis":return h$(a,c);case"radiusAxis":return h_(a,c);default:throw Error("Unexpected axis type: ".concat(b))}},is=a=>a.graphicalItems.cartesianItems.some(a=>"bar"===a.type)||a.graphicalItems.polarItems.some(a=>"radialBar"===a.type);function it(a,b){return c=>{switch(a){case"xAxis":return"xAxisId"in c&&c.xAxisId===b;case"yAxis":return"yAxisId"in c&&c.yAxisId===b;case"zAxis":return"zAxisId"in c&&c.zAxisId===b;case"angleAxis":return"angleAxisId"in c&&c.angleAxisId===b;case"radiusAxis":return"radiusAxisId"in c&&c.radiusAxisId===b;default:return!1}}}var iu=a=>a.graphicalItems.cartesianItems,iv=ak([h7,h8],it),iw=(a,b,c)=>a.filter(c).filter(a=>(null==b?void 0:b.includeHidden)===!0||!a.hide),ix=ak([iu,iq,iv],iw),iy=ak([ix],a=>a.filter(a=>"area"===a.type||"bar"===a.type).filter(ie)),iz=a=>a.filter(a=>!("stackId"in a)||void 0===a.stackId),iA=ak([ix],iz),iB=a=>a.map(a=>a.data).filter(Boolean).flat(1),iC=ak([ix],iB),iD=(a,b)=>{var{chartData:c=[],dataStartIndex:d,dataEndIndex:e}=b;return a.length>0?a:c.slice(d,e+1)},iE=ak([iC,gT],iD),iF=(a,b,c)=>(null==b?void 0:b.dataKey)!=null?a.map(a=>({value:cn(a,b.dataKey)})):c.length>0?c.map(a=>a.dataKey).flatMap(b=>a.map(a=>({value:cn(a,b)}))):a.map(a=>({value:a})),iG=ak([iE,iq,ix],iF);function iH(a,b){switch(a){case"xAxis":return"x"===b.direction;case"yAxis":return"y"===b.direction;default:return!1}}function iI(a){return a.filter(a=>bg(a)||a instanceof Date).map(Number).filter(a=>!1===bd(a))}var iJ=ak([iy,gT,ic],id),iK=(a,b,c)=>Object.fromEntries(Object.entries(b.reduce((a,b)=>(null==b.stackId||(null==a[b.stackId]&&(a[b.stackId]=[]),a[b.stackId].push(b)),a),{})).map(b=>{var[d,e]=b;return[d,{stackedData:((a,b,c)=>{var d=cr[c];return(function(){var a=ca([]),b=cc,c=cb,d=cd;function e(e){var f,g,h=Array.from(a.apply(this,arguments),ce),i=h.length,j=-1;for(let a of e)for(f=0,++j;f<i;++f)(h[f][j]=[0,+d(a,h[f].key,j,e)]).data=a;for(f=0,g=b9(b(h));f<i;++f)h[g[f]].index=f;return c(h,g),h}return e.keys=function(b){return arguments.length?(a="function"==typeof b?b:ca(Array.from(b)),e):a},e.value=function(a){return arguments.length?(d="function"==typeof a?a:ca(+a),e):d},e.order=function(a){return arguments.length?(b=null==a?cc:"function"==typeof a?a:ca(Array.from(a)),e):b},e.offset=function(a){return arguments.length?(c=null==a?cb:a,e):c},e})().keys(b).value((a,b)=>+cn(a,b,0)).order(cc).offset(d)(a)})(a,e.map(h9),c),graphicalItems:e}]})),iL=ak([iJ,iy,hO],iK),iM=(a,b,c)=>{var{dataStartIndex:d,dataEndIndex:e}=b;if("zAxis"!==c){var f=((a,b,c)=>{if(null!=a)return(a=>[a[0]===1/0?0:a[0],a[1]===-1/0?0:a[1]])(Object.keys(a).reduce((d,e)=>{var{stackedData:f}=a[e],g=f.reduce((a,d)=>{var e=(a=>{var b=a.flat(2).filter(bf);return[Math.min(...b),Math.max(...b)]})(ck(d,b,c));return[Math.min(a[0],e[0]),Math.max(a[1],e[1])]},[1/0,-1/0]);return[Math.min(g[0],d[0]),Math.max(g[1],d[1])]},[1/0,-1/0]))})(a,d,e);if(null==f||0!==f[0]||0!==f[1])return f}},iN=ak([iL,gR,h7],iM),iO=(a,b,c,d,e)=>c.length>0?a.flatMap(a=>c.flatMap(c=>{var f,g,h=null==(f=d[c.id])?void 0:f.filter(a=>iH(e,a)),i=cn(a,null!=(g=b.dataKey)?g:c.dataKey);return{value:i,errorDomain:function(a,b,c){return!c||"number"!=typeof b||bd(b)||!c.length?[]:iI(c.flatMap(c=>{var d,e,f=cn(a,c.dataKey);if(Array.isArray(f)?[d,e]=f:d=e=f,gU(d)&&gU(e))return[b-d,b+e]}))}(a,i,h)}})).filter(Boolean):(null==b?void 0:b.dataKey)!=null?a.map(a=>({value:cn(a,b.dataKey),errorDomain:[]})):a.map(a=>({value:a,errorDomain:[]})),iP=a=>a.errorBars,iQ=(a,b,c)=>a.flatMap(a=>b[a.id]).filter(Boolean).filter(a=>iH(c,a));ak([iA,iP,h7],iQ);var iR=ak([iE,iq,iA,iP,h7],iO);function iS(a){var{value:b}=a;if(bg(b)||b instanceof Date)return b}var iT=a=>{var b=iI(a.flatMap(a=>[a.value,a.errorDomain]).flat(1));if(0!==b.length)return[Math.min(...b),Math.max(...b)]},iU=a=>{var b;if(null==a||!("domain"in a))return ii;if(null!=a.domain)return a.domain;if(null!=a.ticks){if("number"===a.type){var c=iI(a.ticks);return[Math.min(...c),Math.max(...c)]}if("category"===a.type)return a.ticks.map(String)}return null!=(b=null==a?void 0:a.domain)?b:ii},iV=function(){for(var a=arguments.length,b=Array(a),c=0;c<a;c++)b[c]=arguments[c];var d=b.filter(Boolean);if(0!==d.length){var e=d.flat();return[Math.min(...e),Math.max(...e)]}},iW=a=>a.referenceElements.dots,iX=(a,b,c)=>a.filter(a=>"extendDomain"===a.ifOverflow).filter(a=>"xAxis"===b?a.xAxisId===c:a.yAxisId===c),iY=ak([iW,h7,h8],iX),iZ=a=>a.referenceElements.areas,i$=ak([iZ,h7,h8],iX),i_=a=>a.referenceElements.lines,i0=ak([i_,h7,h8],iX),i1=(a,b)=>{var c=iI(a.map(a=>"xAxis"===b?a.x:a.y));if(0!==c.length)return[Math.min(...c),Math.max(...c)]},i2=ak(iY,h7,i1),i3=(a,b)=>{var c=iI(a.flatMap(a=>["xAxis"===b?a.x1:a.y1,"xAxis"===b?a.x2:a.y2]));if(0!==c.length)return[Math.min(...c),Math.max(...c)]},i4=ak([i$,h7],i3),i5=(a,b)=>{var c=iI(a.map(a=>"xAxis"===b?a.x:a.y));if(0!==c.length)return[Math.min(...c),Math.max(...c)]},i6=ak(i0,h7,i5),i7=ak(i2,i6,i4,(a,b,c)=>iV(a,c,b)),i8=ak([iq],iU),i9=(a,b,c,d,e,f,g)=>{var h=function(a,b){if(b&&"function"!=typeof a&&Array.isArray(a)&&2===a.length){var c,d,[e,f]=a;if(gU(e))c=e;else if("function"==typeof e)return;if(gU(f))d=f;else if("function"==typeof f)return;var g=[c,d];if(gW(g))return g}}(b,a.allowDataOverflow);return null!=h?h:function(a,b,c){if(c||null!=b){if("function"==typeof a&&null!=b)try{var d=a(b,c);if(gW(d))return gX(d,b,c)}catch(a){}if(Array.isArray(a)&&2===a.length){var e,f,[g,h]=a;if("auto"===g)null!=b&&(e=Math.min(...b));else if(bf(g))e=g;else if("function"==typeof g)try{null!=b&&(e=g(null==b?void 0:b[0]))}catch(a){}else if("string"==typeof g&&cv.test(g)){var i=cv.exec(g);if(null==i||null==b)e=void 0;else{var j=+i[1];e=b[0]-j}}else e=null==b?void 0:b[0];if("auto"===h)null!=b&&(f=Math.max(...b));else if(bf(h))f=h;else if("function"==typeof h)try{null!=b&&(f=h(null==b?void 0:b[1]))}catch(a){}else if("string"==typeof h&&cw.test(h)){var k=cw.exec(h);if(null==k||null==b)f=void 0;else{var l=+k[1];f=b[1]+l}}else f=null==b?void 0:b[1];var m=[e,f];if(gW(m))return null==b?m:gX(m,b,c)}}}(b,"vertical"===f&&"xAxis"===g||"horizontal"===f&&"yAxis"===g?iV(c,e,iT(d)):iV(e,iT(d)),a.allowDataOverflow)},ja=ak([iq,i8,iN,iR,i7,cW,h7],i9),jb=[0,1],jc=(a,b,c,d,e,f,g)=>{if(null!=a&&null!=c&&0!==c.length||void 0!==g){var{dataKey:h,type:i}=a,j=co(b,f);return j&&null==h?(0,cY.default)(0,c.length):"category"===i?((a,b,c)=>{var d=a.map(iS).filter(a=>null!=a);return c&&(null==b.dataKey||b.allowDuplicatedCategory&&bk(d))?(0,cY.default)(0,a.length):b.allowDuplicatedCategory?d:Array.from(new Set(d))})(d,a,j):"expand"===e?jb:g}},jd=ak([iq,cW,iE,iG,hO,h7,ja],jc),je=(a,b,c,d,e)=>{if(null!=a){var{scale:f,type:g}=a;if("auto"===f)return"radial"===b&&"radiusAxis"===e?"band":"radial"===b&&"angleAxis"===e?"linear":"category"===g&&d&&(d.indexOf("LineChart")>=0||d.indexOf("AreaChart")>=0||d.indexOf("ComposedChart")>=0&&!c)?"point":"category"===g?"band":"linear";if("string"==typeof f){var h="scale".concat(bo(f));return h in gQ?h:"point"}}},jf=ak([iq,cW,is,hP,h7],je);function jg(a,b,c,d){if(null!=c&&null!=d){if("function"==typeof a.scale)return a.scale.copy().domain(c).range(d);var e=function(a){if(null!=a){if(a in gQ)return gQ[a]();var b="scale".concat(bo(a));if(b in gQ)return gQ[b]()}}(b);if(null!=e){var f=e.domain(c).range(d);return(a=>{var b=a.domain();if(b&&!(b.length<=2)){var c=b.length,d=a.range(),e=Math.min(d[0],d[1])-1e-4,f=Math.max(d[0],d[1])+1e-4,g=a(b[0]),h=a(b[c-1]);(g<e||g>f||h<e||h>f)&&a.domain([b[0],b[c-1]])}})(f),f}}}var jh=(a,b,c)=>{var d=iU(b);if("auto"===c||"linear"===c){if(null!=b&&b.tickCount&&Array.isArray(d)&&("auto"===d[0]||"auto"===d[1])&&gW(a))return hK(a,b.tickCount,b.allowDecimals);if(null!=b&&b.tickCount&&"number"===b.type&&gW(a))return hL(a,b.tickCount,b.allowDecimals)}},ji=ak([jd,ir,jf],jh),jj=(a,b,c,d)=>"angleAxis"!==d&&(null==a?void 0:a.type)==="number"&&gW(b)&&Array.isArray(c)&&c.length>0?[Math.min(b[0],c[0]),Math.max(b[1],c[c.length-1])]:b,jk=ak([iq,jd,ji,h7],jj),jl=ak(iG,iq,(a,b)=>{if(b&&"number"===b.type){var c=1/0,d=Array.from(iI(a.map(a=>a.value))).sort((a,b)=>a-b);if(d.length<2)return 1/0;var e=d[d.length-1]-d[0];if(0===e)return 1/0;for(var f=0;f<d.length-1;f++)c=Math.min(c,d[f+1]-d[f]);return c/e}}),jm=ak(jl,cW,hN,cK,(a,b,c,d)=>d,(a,b,c,d,e)=>{if(!gU(a))return 0;var f="vertical"===b?d.height:d.width;if("gap"===e)return a*f/2;if("no-gap"===e){var g=bj(c,a*f),h=a*f/2;return h-g-(h-g)/f*g}return 0}),jn=ak(ik,(a,b)=>{var c=ik(a,b);return null==c||"string"!=typeof c.padding?0:jm(a,"xAxis",b,c.padding)},(a,b)=>{if(null==a)return{left:0,right:0};var c,d,{padding:e}=a;return"string"==typeof e?{left:b,right:b}:{left:(null!=(c=e.left)?c:0)+b,right:(null!=(d=e.right)?d:0)+b}}),jo=ak(im,(a,b)=>{var c=im(a,b);return null==c||"string"!=typeof c.padding?0:jm(a,"yAxis",b,c.padding)},(a,b)=>{if(null==a)return{top:0,bottom:0};var c,d,{padding:e}=a;return"string"==typeof e?{top:b,bottom:b}:{top:(null!=(c=e.top)?c:0)+b,bottom:(null!=(d=e.bottom)?d:0)+b}}),jp=ak([cK,jn,cQ,cP,(a,b,c)=>c],(a,b,c,d,e)=>{var{padding:f}=d;return e?[f.left,c.width-f.right]:[a.left+b.left,a.left+a.width-b.right]}),jq=ak([cK,cW,jo,cQ,cP,(a,b,c)=>c],(a,b,c,d,e,f)=>{var{padding:g}=e;return f?[d.height-g.bottom,g.top]:"horizontal"===b?[a.top+a.height-c.bottom,a.top+c.top]:[a.top+c.top,a.top+a.height-c.bottom]}),jr=(a,b,c,d)=>{var e;switch(b){case"xAxis":return jp(a,c,d);case"yAxis":return jq(a,c,d);case"zAxis":return null==(e=ip(a,c))?void 0:e.range;case"angleAxis":return h4(a);case"radiusAxis":return h5(a,c);default:return}},js=ak([iq,jr],hV),jt=ak([iq,jf,jk,js],jg);function ju(a,b){return a.id<b.id?-1:+(a.id>b.id)}ak([ix,iP,h7],iQ);var jv=(a,b)=>b,jw=(a,b,c)=>c,jx=ak(cE,jv,jw,(a,b,c)=>a.filter(a=>a.orientation===b).filter(a=>a.mirror===c).sort(ju)),jy=ak(cF,jv,jw,(a,b,c)=>a.filter(a=>a.orientation===b).filter(a=>a.mirror===c).sort(ju)),jz=(a,b)=>({width:a.width,height:b.height}),jA=ak(cK,ik,jz),jB=ak(cB,cK,jx,jv,jw,(a,b,c,d,e)=>{var f,g={};return c.forEach(c=>{var h=jz(b,c);null==f&&(f=((a,b,c)=>{switch(b){case"top":return a.top;case"bottom":return c-a.bottom;default:return 0}})(b,d,a));var i="top"===d&&!e||"bottom"===d&&e;g[c.id]=f-Number(i)*h.height,f+=(i?-1:1)*h.height}),g}),jC=ak(cA,cK,jy,jv,jw,(a,b,c,d,e)=>{var f,g={};return c.forEach(c=>{var h=((a,b)=>({width:"number"==typeof b.width?b.width:60,height:a.height}))(b,c);null==f&&(f=((a,b,c)=>{switch(b){case"left":return a.left;case"right":return c-a.right;default:return 0}})(b,d,a));var i="left"===d&&!e||"right"===d&&e;g[c.id]=f-Number(i)*h.width,f+=(i?-1:1)*h.width}),g}),jD=ak(cK,im,(a,b)=>({width:"number"==typeof b.width?b.width:60,height:a.height})),jE=(a,b,c)=>{switch(b){case"xAxis":return jA(a,c).width;case"yAxis":return jD(a,c).height;default:return}},jF=(a,b,c,d)=>{if(null!=c){var{allowDuplicatedCategory:e,type:f,dataKey:g}=c,h=co(a,d),i=b.map(a=>a.value);if(g&&h&&"category"===f&&e&&bk(i))return i}},jG=ak([cW,iG,iq,h7],jF),jH=(a,b,c,d)=>{if(null!=c&&null!=c.dataKey){var{type:e,scale:f}=c;if(co(a,d)&&("number"===e||"auto"!==f))return b.map(a=>a.value)}},jI=ak([cW,iG,ir,h7],jH),jJ=ak([cW,(a,b,c)=>{switch(b){case"xAxis":return ik(a,c);case"yAxis":return im(a,c);default:throw Error("Unexpected axis type: ".concat(b))}},jf,jt,jG,jI,jr,ji,h7],(a,b,c,d,e,f,g,h,i)=>{if(null==b)return null;var j=co(a,i);return{angle:b.angle,interval:b.interval,minTickGap:b.minTickGap,orientation:b.orientation,tick:b.tick,tickCount:b.tickCount,tickFormatter:b.tickFormatter,ticks:b.ticks,type:b.type,unit:b.unit,axisType:i,categoricalDomain:f,duplicateDomain:e,isCategorical:j,niceTicks:h,range:g,realScaleType:c,scale:d}}),jK=ak([cW,ir,jf,jt,ji,jr,jG,jI,h7],(a,b,c,d,e,f,g,h,i)=>{if(null!=b&&null!=d){var j=co(a,i),{type:k,ticks:l,tickCount:m}=b,n="scaleBand"===c&&"function"==typeof d.bandwidth?d.bandwidth()/2:2,o="category"===k&&d.bandwidth?d.bandwidth()/n:0;o="angleAxis"===i&&null!=f&&f.length>=2?2*bc(f[0]-f[1])*o:o;var p=l||e;return p?p.map((a,b)=>({index:b,coordinate:d(g?g.indexOf(a):a)+o,value:a,offset:o})).filter(a=>!bd(a.coordinate)):j&&h?h.map((a,b)=>({coordinate:d(a)+o,value:a,index:b,offset:o})):d.ticks?d.ticks(m).map(a=>({coordinate:d(a)+o,value:a,offset:o})):d.domain().map((a,b)=>({coordinate:d(a)+o,value:g?g[a]:a,index:b,offset:o}))}}),jL=ak([cW,ir,jt,jr,jG,jI,h7],(a,b,c,d,e,f,g)=>{if(null!=b&&null!=c&&null!=d&&d[0]!==d[1]){var h=co(a,g),{tickCount:i}=b,j=0;return(j="angleAxis"===g&&(null==d?void 0:d.length)>=2?2*bc(d[0]-d[1])*j:j,h&&f)?f.map((a,b)=>({coordinate:c(a)+j,value:a,index:b,offset:j})):c.ticks?c.ticks(i).map(a=>({coordinate:c(a)+j,value:a,offset:j})):c.domain().map((a,b)=>({coordinate:c(a)+j,value:e?e[a]:a,index:b,offset:j}))}}),jM=ak(iq,jt,(a,b)=>{if(null!=a&&null!=b)return ih(ih({},a),{},{scale:b})}),jN=ak([iq,jf,jd,js],jg);ak((a,b,c)=>ip(a,c),jN,(a,b)=>{if(null!=a&&null!=b)return ih(ih({},a),{},{scale:b})});var jO=ak([cW,cE,cF],(a,b,c)=>{switch(a){case"horizontal":return b.some(a=>a.reversed)?"right-to-left":"left-to-right";case"vertical":return c.some(a=>a.reversed)?"bottom-to-top":"top-to-bottom";case"centric":case"radial":return"left-to-right";default:return}}),jP=a=>a.options.defaultTooltipEventType,jQ=a=>a.options.validateTooltipEventTypes;function jR(a,b,c){if(null==a)return b;var d=a?"axis":"item";return null==c?b:c.includes(d)?d:b}function jS(a,b){return jR(b,jP(a),jQ(a))}var jT=(a,b)=>{var c,d=Number(b);if(!bd(d)&&null!=b)return d>=0?null==a||null==(c=a[d])?void 0:c.value:void 0};function jU(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function jV(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?jU(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):jU(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var jW=(a,b,c,d)=>{if(null==b)return bC;var e=function(a,b,c){return"axis"===b?"click"===c?a.axisInteraction.click:a.axisInteraction.hover:"click"===c?a.itemInteraction.click:a.itemInteraction.hover}(a,b,c);if(null==e)return bC;if(e.active)return e;if(a.keyboardInteraction.active)return a.keyboardInteraction;if(a.syncInteraction.active&&null!=a.syncInteraction.index)return a.syncInteraction;var f=!0===a.settings.active;if(null!=e.index){if(f)return jV(jV({},e),{},{active:!0})}else if(null!=d)return{active:!0,coordinate:void 0,dataKey:void 0,index:d};return jV(jV({},bC),{},{coordinate:e.coordinate})},jX=(a,b)=>{var c=null==a?void 0:a.index;if(null==c)return null;var d=Number(c);if(!gU(d))return c;var e=Infinity;return b.length>0&&(e=b.length-1),String(Math.max(0,Math.min(d,e)))},jY=(a,b,c,d,e,f,g,h)=>{if(null!=f&&null!=h){var i=g[0],j=null==i?void 0:h(i.positions,f);if(null!=j)return j;var k=null==e?void 0:e[Number(f)];if(k)if("horizontal"===c)return{x:k.coordinate,y:(d.top+b)/2};else return{x:(d.left+a)/2,y:k.coordinate}}},jZ=(a,b,c,d)=>{var e;return"axis"===b?a.tooltipItemPayloads:0===a.tooltipItemPayloads.length?[]:null==(e="hover"===c?a.itemInteraction.hover.dataKey:a.itemInteraction.click.dataKey)&&null!=d?[a.tooltipItemPayloads[0]]:a.tooltipItemPayloads.filter(a=>{var b;return(null==(b=a.settings)?void 0:b.dataKey)===e})},j$=a=>a.options.tooltipPayloadSearcher,j_=a=>a.tooltip;function j0(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function j1(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?j0(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):j0(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var j2=(a,b,c,d,e,f,g)=>{if(null!=b&&null!=f){var{chartData:h,computedData:i,dataStartIndex:j,dataEndIndex:k}=c;return a.reduce((a,c)=>{var l,m,n,o,p,{dataDefinedOnItem:q,settings:r}=c,s=(l=q,m=h,null!=l?l:m),t=Array.isArray(s)?ck(s,j,k):s,u=null!=(n=null==r?void 0:r.dataKey)?n:null==d?void 0:d.dataKey,v=null==r?void 0:r.nameKey;return Array.isArray(o=null!=d&&d.dataKey&&Array.isArray(t)&&!Array.isArray(t[0])&&"axis"===g?bn(t,d.dataKey,e):f(t,b,i,v))?o.forEach(b=>{var c=j1(j1({},r),{},{name:b.name,unit:b.unit,color:void 0,fill:void 0});a.push(cy({tooltipEntrySettings:c,dataKey:b.dataKey,payload:b.payload,value:cn(b.payload,b.dataKey),name:b.name}))}):a.push(cy({tooltipEntrySettings:r,dataKey:u,payload:o,value:cn(o,u),name:null!=(p=cn(o,v))?p:null==r?void 0:r.name})),a},[])}},j3=ak([ic,cW,is,hP,ia],je),j4=ak([a=>a.graphicalItems.cartesianItems,a=>a.graphicalItems.polarItems],(a,b)=>[...a,...b]),j5=ak([ia,ib],it),j6=ak([j4,ic,j5],iw),j7=ak([j6],a=>a.filter(ie)),j8=ak([j6],iB),j9=ak([j8,gR],iD),ka=ak([j7,gR,ic],id),kb=ak([j9,ic,j6],iF),kc=ak([ic],iU),kd=ak([j6],a=>a.filter(ie)),ke=ak([ka,kd,hO],iK),kf=ak([ke,gR,ia],iM),kg=ak([j6],iz),kh=ak([j9,ic,kg,iP,ia],iO),ki=ak([iW,ia,ib],iX),kj=ak([ki,ia],i1),kk=ak([iZ,ia,ib],iX),kl=ak([kk,ia],i3),km=ak([i_,ia,ib],iX),kn=ak([km,ia],i5),ko=ak([kj,kn,kl],iV),kp=ak([ic,kc,kf,kh,ko,cW,ia],i9),kq=ak([ic,cW,j9,kb,hO,ia,kp],jc),kr=ak([kq,ic,j3],jh),ks=ak([ic,kq,kr,ia],jj),kt=a=>{var b=ia(a),c=ib(a);return jr(a,b,c,!1)},ku=ak([ic,kt],hV),kv=ak([ic,j3,ks,ku],jg),kw=ak([cW,kb,ic,ia],jF),kx=ak([cW,kb,ic,ia],jH),ky=ak([cW,ic,j3,kv,kt,kw,kx,ia],(a,b,c,d,e,f,g,h)=>{if(b){var{type:i}=b,j=co(a,h);if(d){var k="scaleBand"===c&&d.bandwidth?d.bandwidth()/2:2,l="category"===i&&d.bandwidth?d.bandwidth()/k:0;return(l="angleAxis"===h&&null!=e&&(null==e?void 0:e.length)>=2?2*bc(e[0]-e[1])*l:l,j&&g)?g.map((a,b)=>({coordinate:d(a)+l,value:a,index:b,offset:l})):d.domain().map((a,b)=>({coordinate:d(a)+l,value:f?f[a]:a,index:b,offset:l}))}}}),kz=ak([jP,jQ,a=>a.tooltip.settings],(a,b,c)=>jR(c.shared,a,b)),kA=a=>a.tooltip.settings.trigger,kB=a=>a.tooltip.settings.defaultIndex,kC=ak([j_,kz,kA,kB],jW),kD=ak([kC,j9],jX),kE=ak([ky,kD],jT),kF=ak([kC],a=>{if(a)return a.dataKey}),kG=ak([j_,kz,kA,kB],jZ),kH=ak([cA,cB,cW,cK,ky,kB,kG,j$],jY),kI=ak([kC,kH],(a,b)=>null!=a&&a.coordinate?a.coordinate:b),kJ=ak([kC],a=>a.active),kK=ak([kG,kD,gR,ic,kE,j$,kz],j2),kL=ak([kK],a=>{if(null!=a)return Array.from(new Set(a.map(a=>a.payload).filter(a=>null!=a)))}),kM=()=>b6(hP),kN=(a,b)=>b,kO=(a,b,c)=>c,kP=(a,b,c,d)=>d,kQ=ak(ky,a=>(0,b7.default)(a,a=>a.coordinate)),kR=ak([j_,kN,kO,kP],jW),kS=ak([kR,j9],jX),kT=ak([j_,kN,kO,kP],jZ),kU=ak([cA,cB,cW,cK,ky,kP,kT,j$],jY),kV=ak([kR,kU],(a,b)=>{var c;return null!=(c=a.coordinate)?c:b}),kW=ak(ky,kS,jT),kX=ak([kT,kS,gR,ic,kW,j$,kN],j2),kY=ak([kR],a=>({isActive:a.active,activeIndex:a.index})),kZ=ak([(a,b)=>b,cW,h6,ia,ku,ky,kQ,cK],(a,b,c,d,e,f,g,h)=>{if(a&&b&&d&&e&&f){var i=function(a,b,c,d,e){return"horizontal"===c||"vertical"===c?a>=e.left&&a<=e.left+e.width&&b>=e.top&&b<=e.top+e.height?{x:a,y:b}:null:d?((a,b)=>{var c,{x:d,y:e}=a,{radius:f,angle:g}=((a,b)=>{var{x:c,y:d}=a,{cx:e,cy:f}=b,g=((a,b)=>{var{x:c,y:d}=a,{x:e,y:f}=b;return Math.sqrt((c-e)**2+(d-f)**2)})({x:c,y:d},{x:e,y:f});if(g<=0)return{radius:g,angle:0};var h=Math.acos((c-e)/g);return d>f&&(h=2*Math.PI-h),{radius:g,angle:180*h/Math.PI,angleInRadian:h}})({x:d,y:e},b),{innerRadius:h,outerRadius:i}=b;if(f<h||f>i||0===f)return null;var{startAngle:j,endAngle:k}=(a=>{var{startAngle:b,endAngle:c}=a,d=Math.min(Math.floor(b/360),Math.floor(c/360));return{startAngle:b-360*d,endAngle:c-360*d}})(b),l=g;if(j<=k){for(;l>k;)l-=360;for(;l<j;)l+=360;c=l>=j&&l<=k}else{for(;l>j;)l-=360;for(;l<k;)l+=360;c=l>=k&&l<=j}return c?cg(cg({},b),{},{radius:f,angle:((a,b)=>{var{startAngle:c,endAngle:d}=b;return a+360*Math.min(Math.floor(c/360),Math.floor(d/360))})(l,b)}):null})({x:a,y:b},d):null}(a.chartX,a.chartY,b,c,h);if(i){var j=((a,b,c,d,e)=>{var f,g=-1,h=null!=(f=null==b?void 0:b.length)?f:0;if(h<=1||null==a)return 0;if("angleAxis"===d&&null!=e&&1e-6>=Math.abs(Math.abs(e[1]-e[0])-360))for(var i=0;i<h;i++){var j=i>0?c[i-1].coordinate:c[h-1].coordinate,k=c[i].coordinate,l=i>=h-1?c[0].coordinate:c[i+1].coordinate,m=void 0;if(bc(k-j)!==bc(l-k)){var n=[];if(bc(l-k)===bc(e[1]-e[0])){m=l;var o=k+e[1]-e[0];n[0]=Math.min(o,(o+j)/2),n[1]=Math.max(o,(o+j)/2)}else{m=j;var p=l+e[1]-e[0];n[0]=Math.min(k,(p+k)/2),n[1]=Math.max(k,(p+k)/2)}var q=[Math.min(k,(m+k)/2),Math.max(k,(m+k)/2)];if(a>q[0]&&a<=q[1]||a>=n[0]&&a<=n[1]){({index:g}=c[i]);break}}else{var r=Math.min(j,l),s=Math.max(j,l);if(a>(r+k)/2&&a<=(s+k)/2){({index:g}=c[i]);break}}}else if(b){for(var t=0;t<h;t++)if(0===t&&a<=(b[t].coordinate+b[t+1].coordinate)/2||t>0&&t<h-1&&a>(b[t].coordinate+b[t-1].coordinate)/2&&a<=(b[t].coordinate+b[t+1].coordinate)/2||t===h-1&&a>(b[t].coordinate+b[t-1].coordinate)/2){({index:g}=b[t]);break}}return g})(((a,b)=>"horizontal"===b?a.x:"vertical"===b?a.y:"centric"===b?a.angle:a.radius)(i,b),g,f,d,e),k=((a,b,c,d)=>{var e=b.find(a=>a&&a.index===c);if(e){if("horizontal"===a)return{x:e.coordinate,y:d.y};if("vertical"===a)return{x:d.x,y:e.coordinate};if("centric"===a){var f=e.coordinate,{radius:g}=d;return cm(cm(cm({},d),ci(d.cx,d.cy,g,f)),{},{angle:f,radius:g})}var h=e.coordinate,{angle:i}=d;return cm(cm(cm({},d),ci(d.cx,d.cy,h,i)),{},{angle:i,radius:h})}return{x:0,y:0}})(b,f,j,i);return{activeIndex:String(j),activeCoordinate:k}}}}),k$=a=>{var b=a.currentTarget.getBoundingClientRect(),c=b.width/a.currentTarget.offsetWidth,d=b.height/a.currentTarget.offsetHeight;return{chartX:Math.round((a.clientX-b.left)/c),chartY:Math.round((a.clientY-b.top)/d)}},k_=ax("mouseClick"),k0=a9();k0.startListening({actionCreator:k_,effect:(a,b)=>{var c=a.payload,d=kZ(b.getState(),k$(c));(null==d?void 0:d.activeIndex)!=null&&b.dispatch(bM({activeIndex:d.activeIndex,activeDataKey:void 0,activeCoordinate:d.activeCoordinate}))}});var k1=ax("mouseMove"),k2=a9();function k3(a,b){return b instanceof HTMLElement?"HTMLElement <".concat(b.tagName,' class="').concat(b.className,'">'):b===window?"global.window":b}function k4(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function k5(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?k4(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):k4(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}k2.startListening({actionCreator:k1,effect:(a,b)=>{var c=a.payload,d=b.getState(),e=jS(d,d.tooltip.settings.shared),f=kZ(d,k$(c));"axis"===e&&((null==f?void 0:f.activeIndex)!=null?b.dispatch(bL({activeIndex:f.activeIndex,activeDataKey:void 0,activeCoordinate:f.activeCoordinate})):b.dispatch(bJ()))}});var k6=aF({name:"cartesianAxis",initialState:{xAxis:{},yAxis:{},zAxis:{}},reducers:{addXAxis(a,b){a.xAxis[b.payload.id]=b.payload},removeXAxis(a,b){delete a.xAxis[b.payload.id]},addYAxis(a,b){a.yAxis[b.payload.id]=b.payload},removeYAxis(a,b){delete a.yAxis[b.payload.id]},addZAxis(a,b){a.zAxis[b.payload.id]=b.payload},removeZAxis(a,b){delete a.zAxis[b.payload.id]},updateYAxisWidth(a,b){var{id:c,width:d}=b.payload;a.yAxis[c]&&(a.yAxis[c]=k5(k5({},a.yAxis[c]),{},{width:d}))}}}),{addXAxis:k7,removeXAxis:k8,addYAxis:k9,removeYAxis:la,addZAxis:lb,removeZAxis:lc,updateYAxisWidth:ld}=k6.actions,le=k6.reducer,lf=aF({name:"graphicalItems",initialState:{cartesianItems:[],polarItems:[]},reducers:{addCartesianGraphicalItem(a,b){a.cartesianItems.push(b.payload)},replaceCartesianGraphicalItem(a,b){var{prev:c,next:d}=b.payload,e=S(a).cartesianItems.indexOf(c);e>-1&&(a.cartesianItems[e]=d)},removeCartesianGraphicalItem(a,b){var c=S(a).cartesianItems.indexOf(b.payload);c>-1&&a.cartesianItems.splice(c,1)},addPolarGraphicalItem(a,b){a.polarItems.push(b.payload)},removePolarGraphicalItem(a,b){var c=S(a).polarItems.indexOf(b.payload);c>-1&&a.polarItems.splice(c,1)}}}),{addCartesianGraphicalItem:lg,replaceCartesianGraphicalItem:lh,removeCartesianGraphicalItem:li,addPolarGraphicalItem:lj,removePolarGraphicalItem:lk}=lf.actions,ll=lf.reducer,lm=aF({name:"referenceElements",initialState:{dots:[],areas:[],lines:[]},reducers:{addDot:(a,b)=>{a.dots.push(b.payload)},removeDot:(a,b)=>{var c=S(a).dots.findIndex(a=>a===b.payload);-1!==c&&a.dots.splice(c,1)},addArea:(a,b)=>{a.areas.push(b.payload)},removeArea:(a,b)=>{var c=S(a).areas.findIndex(a=>a===b.payload);-1!==c&&a.areas.splice(c,1)},addLine:(a,b)=>{a.lines.push(b.payload)},removeLine:(a,b)=>{var c=S(a).lines.findIndex(a=>a===b.payload);-1!==c&&a.lines.splice(c,1)}}}),{addDot:ln,removeDot:lo,addArea:lp,removeArea:lq,addLine:lr,removeLine:ls}=lm.actions,lt=lm.reducer,lu={x:0,y:0,width:0,height:0,padding:{top:0,right:0,bottom:0,left:0}},lv=aF({name:"brush",initialState:lu,reducers:{setBrushSettings:(a,b)=>null==b.payload?lu:b.payload}}),{setBrushSettings:lw}=lv.actions,lx=lv.reducer,ly=aF({name:"legend",initialState:{settings:{layout:"horizontal",align:"center",verticalAlign:"middle",itemSorter:"value"},size:{width:0,height:0},payload:[]},reducers:{setLegendSize(a,b){a.size.width=b.payload.width,a.size.height=b.payload.height},setLegendSettings(a,b){a.settings.align=b.payload.align,a.settings.layout=b.payload.layout,a.settings.verticalAlign=b.payload.verticalAlign,a.settings.itemSorter=b.payload.itemSorter},addLegendPayload(a,b){a.payload.push(b.payload)},removeLegendPayload(a,b){var c=S(a).payload.indexOf(b.payload);c>-1&&a.payload.splice(c,1)}}}),{setLegendSize:lz,setLegendSettings:lA,addLegendPayload:lB,removeLegendPayload:lC}=ly.actions,lD=ly.reducer,lE={accessibilityLayer:!0,barCategoryGap:"10%",barGap:4,barSize:void 0,className:void 0,maxBarSize:void 0,stackOffset:"none",syncId:void 0,syncMethod:"index"},lF=aF({name:"rootProps",initialState:lE,reducers:{updateOptions:(a,b)=>{var c;a.accessibilityLayer=b.payload.accessibilityLayer,a.barCategoryGap=b.payload.barCategoryGap,a.barGap=null!=(c=b.payload.barGap)?c:lE.barGap,a.barSize=b.payload.barSize,a.maxBarSize=b.payload.maxBarSize,a.stackOffset=b.payload.stackOffset,a.syncId=b.payload.syncId,a.syncMethod=b.payload.syncMethod,a.className=b.payload.className}}}),lG=lF.reducer,{updateOptions:lH}=lF.actions,lI=aF({name:"polarAxis",initialState:{radiusAxis:{},angleAxis:{}},reducers:{addRadiusAxis(a,b){a.radiusAxis[b.payload.id]=b.payload},removeRadiusAxis(a,b){delete a.radiusAxis[b.payload.id]},addAngleAxis(a,b){a.angleAxis[b.payload.id]=b.payload},removeAngleAxis(a,b){delete a.angleAxis[b.payload.id]}}}),{addRadiusAxis:lJ,removeRadiusAxis:lK,addAngleAxis:lL,removeAngleAxis:lM}=lI.actions,lN=lI.reducer,lO=aF({name:"polarOptions",initialState:null,reducers:{updatePolarOptions:(a,b)=>b.payload}}),{updatePolarOptions:lP}=lO.actions,lQ=lO.reducer,lR=ax("keyDown"),lS=ax("focus"),lT=a9();lT.startListening({actionCreator:lR,effect:(a,b)=>{var c=b.getState();if(!1!==c.rootProps.accessibilityLayer){var{keyboardInteraction:d}=c.tooltip,e=a.payload;if("ArrowRight"===e||"ArrowLeft"===e||"Enter"===e){var f=Number(jX(d,j9(c))),g=ky(c);if("Enter"===e){var h=kU(c,"axis","hover",String(d.index));b.dispatch(bO({active:!d.active,activeIndex:d.index,activeDataKey:d.dataKey,activeCoordinate:h}));return}var i=f+("ArrowRight"===e?1:-1)*("left-to-right"===jO(c)?1:-1);if(null!=g&&!(i>=g.length)&&!(i<0)){var j=kU(c,"axis","hover",String(i));b.dispatch(bO({active:!0,activeIndex:i.toString(),activeDataKey:void 0,activeCoordinate:j}))}}}}}),lT.startListening({actionCreator:lS,effect:(a,b)=>{var c=b.getState();if(!1!==c.rootProps.accessibilityLayer){var{keyboardInteraction:d}=c.tooltip;if(!d.active&&null==d.index){var e=kU(c,"axis","hover",String("0"));b.dispatch(bO({activeDataKey:void 0,active:!0,activeIndex:"0",activeCoordinate:e}))}}}});var lU=ax("externalEvent"),lV=a9();lV.startListening({actionCreator:lU,effect:(a,b)=>{if(null!=a.payload.handler){var c=b.getState(),d={activeCoordinate:kI(c),activeDataKey:kF(c),activeIndex:kD(c),activeLabel:kE(c),activeTooltipIndex:kD(c),isTooltipActive:kJ(c)};a.payload.handler(d,a.payload.reactEvent)}}});var lW=ak([j_],a=>a.tooltipItemPayloads),lX=ak([lW,j$,(a,b,c)=>b,(a,b,c)=>c],(a,b,c,d)=>{var e=a.find(a=>a.settings.dataKey===d);if(null!=e){var{positions:f}=e;if(null!=f)return b(f,c)}}),lY=ax("touchMove"),lZ=a9();lZ.startListening({actionCreator:lY,effect:(a,b)=>{var c=a.payload,d=b.getState(),e=jS(d,d.tooltip.settings.shared);if("axis"===e){var f=kZ(d,k$({clientX:c.touches[0].clientX,clientY:c.touches[0].clientY,currentTarget:c.currentTarget}));(null==f?void 0:f.activeIndex)!=null&&b.dispatch(bL({activeIndex:f.activeIndex,activeDataKey:void 0,activeCoordinate:f.activeCoordinate}))}else if("item"===e){var g,h=c.touches[0],i=document.elementFromPoint(h.clientX,h.clientY);if(!i||!i.getAttribute)return;var j=i.getAttribute(cG),k=null!=(g=i.getAttribute(cH))?g:void 0,l=lX(b.getState(),j,k);b.dispatch(bH({activeDataKey:k,activeIndex:j,activeCoordinate:l}))}}});var l$=aF({name:"errorBars",initialState:{},reducers:{addErrorBar:(a,b)=>{var{itemId:c,errorBar:d}=b.payload;a[c]||(a[c]=[]),a[c].push(d)},removeErrorBar:(a,b)=>{var{itemId:c,errorBar:d}=b.payload;a[c]&&(a[c]=a[c].filter(a=>a.dataKey!==d.dataKey||a.direction!==d.direction))}}}),{addErrorBar:l_,removeErrorBar:l0}=l$.actions,l1=ar({brush:lx,cartesianAxis:le,chartData:bU,errorBars:l$.reducer,graphicalItems:ll,layout:b$,legend:lD,options:br,polarAxis:lN,polarOptions:lQ,referenceElements:lt,rootProps:lG,tooltip:bP}),l2=function(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Chart";return function(a){let b,c,d,e=function(a){let{thunk:b=!0,immutableCheck:c=!0,serializableCheck:d=!0,actionCreatorCheck:e=!0}=a??{},f=new ay;return b&&("boolean"==typeof b?f.push(av):f.push(au(b.extraArgument))),f},{reducer:f,middleware:g,devTools:h=!0,duplicateMiddlewareCheck:i=!0,preloadedState:j,enhancers:k}=a||{};if("function"==typeof f)b=f;else if(aq(f))b=ar(f);else throw Error(ba(1));c="function"==typeof g?g(e):e();let l=as;h&&(l=aw({trace:!1,..."object"==typeof h&&h}));let m=(d=function(...a){return b=>(c,d)=>{let e=b(c,d),f=()=>{throw Error(am(15))},g={getState:e.getState,dispatch:(a,...b)=>f(a,...b)};return f=as(...a.map(a=>a(g)))(e.dispatch),{...e,dispatch:f}}}(...c),function(a){let{autoBatch:b=!0}=a??{},c=new ay(d);return b&&c.push(((a={type:"raf"})=>b=>(...c)=>{let d=b(...c),e=!0,f=!1,g=!1,h=new Set,i="tick"===a.type?queueMicrotask:"raf"===a.type?aB(10):"callback"===a.type?a.queueNotification:aB(a.timeout),j=()=>{g=!1,f&&(f=!1,h.forEach(a=>a()))};return Object.assign({},d,{subscribe(a){let b=d.subscribe(()=>e&&a());return h.add(a),()=>{b(),h.delete(a)}},dispatch(a){try{return(f=!(e=!a?.meta?.RTK_autoBatch))&&!g&&(g=!0,i(j)),d.dispatch(a)}finally{e=!0}}})})("object"==typeof b?b:void 0)),c});return function a(b,c,d){if("function"!=typeof b)throw Error(am(2));if("function"==typeof c&&"function"==typeof d||"function"==typeof d&&"function"==typeof arguments[3])throw Error(am(0));if("function"==typeof c&&void 0===d&&(d=c,c=void 0),void 0!==d){if("function"!=typeof d)throw Error(am(1));return d(a)(b,c)}let e=b,f=c,g=new Map,h=g,i=0,j=!1;function k(){h===g&&(h=new Map,g.forEach((a,b)=>{h.set(b,a)}))}function l(){if(j)throw Error(am(3));return f}function m(a){if("function"!=typeof a)throw Error(am(4));if(j)throw Error(am(5));let b=!0;k();let c=i++;return h.set(c,a),function(){if(b){if(j)throw Error(am(6));b=!1,k(),h.delete(c),g=null}}}function n(a){if(!aq(a))throw Error(am(7));if(void 0===a.type)throw Error(am(8));if("string"!=typeof a.type)throw Error(am(17));if(j)throw Error(am(9));try{j=!0,f=e(f,a)}finally{j=!1}return(g=h).forEach(a=>{a()}),a}return n({type:ap.INIT}),{dispatch:n,subscribe:m,getState:l,replaceReducer:function(a){if("function"!=typeof a)throw Error(am(10));e=a,n({type:ap.REPLACE})},[an]:function(){return{subscribe(a){if("object"!=typeof a||null===a)throw Error(am(11));function b(){a.next&&a.next(l())}return b(),{unsubscribe:m(b)}},[an](){return this}}}}}(b,j,l(..."function"==typeof k?k(m):m()))}({reducer:l1,preloadedState:a,middleware:a=>a({serializableCheck:!1}).concat([k0.middleware,k2.middleware,lT.middleware,lV.middleware,lZ.middleware]),devTools:{serialize:{replacer:k3},name:"recharts-".concat(b)}})};function l3(a){var{preloadedState:b,children:c,reduxStoreName:d}=a,e=cO(),g=(0,f.useRef)(null);return e?c:(null==g.current&&(g.current=l2(b,d)),f.createElement(bz,{context:b0,store:g.current},c))}var l4=a=>{var{chartData:b}=a,c=b2(),d=cO();return(0,f.useEffect)(()=>d?()=>{}:(c(bR(b)),()=>{c(bR(void 0))}),[b,c,d]),null};function l5(a){var{layout:b,width:c,height:d,margin:e}=a,g=b2(),h=cO();return(0,f.useEffect)(()=>{h||(g(bX(b)),g(bY({width:c,height:d})),g(bW(e)))},[g,h,b,c,d,e]),null}function l6(a){var b=b2();return(0,f.useEffect)(()=>{b(lH(a))},[b,a]),null}var l7=()=>b6(a=>a.rootProps.accessibilityLayer),l8=a.i(98621),l9=a.i(66539),ma=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"];function mb(a){return"string"==typeof a&&ma.includes(a)}var mc=["points","pathLength"],md={svg:["viewBox","children"],polygon:mc,polyline:mc},me=(a,b)=>{if(!a||"function"==typeof a||"boolean"==typeof a)return null;var c=a;if((0,f.isValidElement)(a)&&(c=a.props),"object"!=typeof c&&"function"!=typeof c)return null;var d={};return Object.keys(c).forEach(a=>{mb(a)&&(d[a]=b||(b=>c[a](c,b)))}),d},mf=(a,b,c)=>{if(null===a||"object"!=typeof a&&"function"!=typeof a)return null;var d=null;return Object.keys(a).forEach(e=>{var f=a[e];mb(e)&&"function"==typeof f&&(d||(d={}),d[e]=a=>(f(b,c,a),null))}),d},mg=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"];function mh(a){return"string"==typeof a&&mg.includes(a)}function mi(a){return Object.fromEntries(Object.entries(a).filter(a=>{var[b]=a;return mh(b)}))}var mj=a=>"string"==typeof a?a:a?a.displayName||a.name||"Component":"",mk=null,ml=null,mm=a=>{if(a===mk&&Array.isArray(ml))return ml;var b=[];return f.Children.forEach(a,a=>{null==a||((0,l9.isFragment)(a)?b=b.concat(mm(a.props.children)):b.push(a))}),ml=b,mk=a,b};function mn(a,b){var c=[],d=[];return d=Array.isArray(b)?b.map(a=>mj(a)):[mj(b)],mm(a).forEach(a=>{var b=(0,bb.default)(a,"type.displayName")||(0,bb.default)(a,"type.name");-1!==d.indexOf(b)&&c.push(a)}),c}var mo=a=>!a||"object"!=typeof a||!("clipDot"in a)||!!a.clipDot,mp=(a,b,c)=>{if(!a||"function"==typeof a||"boolean"==typeof a)return null;var d=a;if((0,f.isValidElement)(a)&&(d=a.props),"object"!=typeof d&&"function"!=typeof d)return null;var e={};return Object.keys(d).forEach(a=>{var f;((a,b,c,d)=>{if("symbol"==typeof b||"number"==typeof b)return!0;var e,f=null!=(e=d&&(null==md?void 0:md[d]))?e:[],g=b.startsWith("data-"),h="function"!=typeof a&&(!!d&&f.includes(b)||mh(b)),i=!!c&&mb(b);return g||h||i})(null==(f=d)?void 0:f[a],a,b,c)&&(e[a]=d[a])}),e},mq=["children","width","height","viewBox","className","style","title","desc"];function mr(){return(mr=Object.assign.bind()).apply(null,arguments)}var ms=(0,f.forwardRef)((a,b)=>{var{children:c,width:d,height:e,viewBox:g,className:h,style:i,title:j,desc:k}=a,l=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,mq),m=g||{width:d,height:e,x:0,y:0},n=(0,l8.clsx)("recharts-surface",h);return f.createElement("svg",mr({},mp(l,!0,"svg"),{className:n,width:d,height:e,style:i,viewBox:"".concat(m.x," ").concat(m.y," ").concat(m.width," ").concat(m.height),ref:b}),f.createElement("title",null,j),f.createElement("desc",null,k),c)}),mt=["children"];function mu(){return(mu=Object.assign.bind()).apply(null,arguments)}var mv={width:"100%",height:"100%"},mw=(0,f.forwardRef)((a,b)=>{var c,d,e=cU(),g=cV(),h=l7();if(!gV(e)||!gV(g))return null;var{children:i,otherAttributes:j,title:k,desc:l}=a;return c="number"==typeof j.tabIndex?j.tabIndex:h?0:void 0,d="string"==typeof j.role?j.role:h?"application":void 0,f.createElement(ms,mu({},j,{title:k,desc:l,role:d,tabIndex:c,width:e,height:g,style:mv,ref:b}),i)}),mx=a=>{var{children:b}=a,c=b6(cQ);if(!c)return null;var{width:d,height:e,y:g,x:h}=c;return f.createElement(ms,{width:d,height:e,x:h,y:g},b)},my=(0,f.forwardRef)((a,b)=>{var{children:c}=a,d=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,mt);return cO()?f.createElement(mx,null,c):f.createElement(mw,mu({ref:b},d),c)}),mz=new(a.i(53686)).default,mA="recharts.syncEvent.tooltip",mB="recharts.syncEvent.brush";function mC(a){return a.tooltip.syncInteraction}var mD=()=>{},mE=(0,f.createContext)(null),mF=(0,f.createContext)(null);function mG(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}var mH=(0,f.forwardRef)((a,b)=>{var{children:c,className:d,height:e,onClick:g,onContextMenu:h,onDoubleClick:i,onMouseDown:j,onMouseEnter:k,onMouseLeave:l,onMouseMove:m,onMouseUp:n,onTouchEnd:o,onTouchMove:p,onTouchStart:q,style:r,width:s}=a,t=b2(),[u,v]=(0,f.useState)(null),[w,x]=(0,f.useState)(null);!function(){var a,b,c,d,e,g,h,i,j,k,l,m=b2();(0,f.useEffect)(()=>{m(bs())},[m]),a=b6(hQ),b=b6(hS),c=b2(),d=b6(hR),e=b6(ky),g=cX(),h=cR(),i=b6(a=>a.rootProps.className),(0,f.useEffect)(()=>{if(null==a)return mD;var f=(f,i,j)=>{if(b!==j&&a===f){if("index"===d)return void c(i);if(null!=e){if("function"==typeof d){var k,l=d(e,{activeTooltipIndex:null==i.payload.index?void 0:Number(i.payload.index),isTooltipActive:i.payload.active,activeIndex:null==i.payload.index?void 0:Number(i.payload.index),activeLabel:i.payload.label,activeDataKey:i.payload.dataKey,activeCoordinate:i.payload.coordinate});k=e[l]}else"value"===d&&(k=e.find(a=>String(a.value)===i.payload.label));var{coordinate:m}=i.payload;if(null==k||!1===i.payload.active||null==m||null==h)return void c(bN({active:!1,coordinate:void 0,dataKey:void 0,index:null,label:void 0}));var{x:n,y:o}=m,p=Math.min(n,h.x+h.width),q=Math.min(o,h.y+h.height),r={x:"horizontal"===g?k.coordinate:p,y:"horizontal"===g?q:k.coordinate};c(bN({active:i.payload.active,coordinate:r,dataKey:i.payload.dataKey,index:String(k.index),label:i.payload.label}))}}};return mz.on(mA,f),()=>{mz.off(mA,f)}},[i,c,b,a,d,e,g,h]),j=b6(hQ),k=b6(hS),l=b2(),(0,f.useEffect)(()=>{if(null==j)return mD;var a=(a,b,c)=>{k!==c&&j===a&&l(bS(b))};return mz.on(mB,a),()=>{mz.off(mB,a)}},[l,k,j])}();var y=function(){var a=b2(),[b,c]=(0,f.useState)(null),d=b6(cC);return(0,f.useEffect)(()=>{if(null!=b){var c=b.getBoundingClientRect().width/b.offsetWidth;gU(c)&&c!==d&&a(bZ(c))}},[b,a,d]),c}(),z=(0,f.useCallback)(a=>{y(a),"function"==typeof b&&b(a),v(a),x(a)},[y,b,v,x]),A=(0,f.useCallback)(a=>{t(k_(a)),t(lU({handler:g,reactEvent:a}))},[t,g]),B=(0,f.useCallback)(a=>{t(k1(a)),t(lU({handler:k,reactEvent:a}))},[t,k]),C=(0,f.useCallback)(a=>{t(bJ()),t(lU({handler:l,reactEvent:a}))},[t,l]),D=(0,f.useCallback)(a=>{t(k1(a)),t(lU({handler:m,reactEvent:a}))},[t,m]),E=(0,f.useCallback)(()=>{t(lS())},[t]),F=(0,f.useCallback)(a=>{t(lR(a.key))},[t]),G=(0,f.useCallback)(a=>{t(lU({handler:h,reactEvent:a}))},[t,h]),H=(0,f.useCallback)(a=>{t(lU({handler:i,reactEvent:a}))},[t,i]),I=(0,f.useCallback)(a=>{t(lU({handler:j,reactEvent:a}))},[t,j]),J=(0,f.useCallback)(a=>{t(lU({handler:n,reactEvent:a}))},[t,n]),K=(0,f.useCallback)(a=>{t(lU({handler:q,reactEvent:a}))},[t,q]),L=(0,f.useCallback)(a=>{t(lY(a)),t(lU({handler:p,reactEvent:a}))},[t,p]),M=(0,f.useCallback)(a=>{t(lU({handler:o,reactEvent:a}))},[t,o]);return f.createElement(mE.Provider,{value:u},f.createElement(mF.Provider,{value:w},f.createElement("div",{className:(0,l8.clsx)("recharts-wrapper",d),style:function(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?mG(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):mG(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}({position:"relative",cursor:"default",width:s,height:e},r),onClick:A,onContextMenu:G,onDoubleClick:H,onFocus:E,onKeyDown:F,onMouseDown:I,onMouseEnter:B,onMouseLeave:C,onMouseMove:D,onMouseUp:J,onTouchEnd:M,onTouchMove:L,onTouchStart:K,ref:z},c)))}),mI=ak([cK],a=>{if(a)return{top:a.top,bottom:a.bottom,left:a.left,right:a.right}}),mJ=ak([mI,cA,cB],(a,b,c)=>{if(a&&null!=b&&null!=c)return{x:a.left,y:a.top,width:Math.max(0,b-a.left-a.right),height:Math.max(0,c-a.top-a.bottom)}}),mK=()=>b6(mJ),mL=(0,f.createContext)(void 0),mM=a=>{var{children:b}=a,[c]=(0,f.useState)("".concat(bi("recharts"),"-clip")),d=mK();if(null==d)return null;var{x:e,y:g,width:h,height:i}=d;return f.createElement(mL.Provider,{value:c},f.createElement("defs",null,f.createElement("clipPath",{id:c},f.createElement("rect",{x:e,y:g,height:i,width:h}))),b)},mN=["children","className","width","height","style","compact","title","desc"],mO=(0,f.forwardRef)((a,b)=>{var{children:c,className:d,width:e,height:g,style:h,compact:i,title:j,desc:k}=a,l=mi(function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,mN));return i?f.createElement(my,{otherAttributes:l,title:j,desc:k},c):f.createElement(mH,{className:d,style:h,width:e,height:g,onClick:a.onClick,onMouseLeave:a.onMouseLeave,onMouseEnter:a.onMouseEnter,onMouseMove:a.onMouseMove,onMouseDown:a.onMouseDown,onMouseUp:a.onMouseUp,onContextMenu:a.onContextMenu,onDoubleClick:a.onDoubleClick,onTouchStart:a.onTouchStart,onTouchMove:a.onTouchMove,onTouchEnd:a.onTouchEnd},f.createElement(my,{otherAttributes:l,title:j,desc:k,ref:b},f.createElement(mM,null,c)))});function mP(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function mQ(a,b){var c=function(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?mP(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):mP(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}({},a);return Object.keys(b).reduce((a,c)=>(void 0===a[c]&&void 0!==b[c]&&(a[c]=b[c]),a),c)}var mR=["width","height"];function mS(){return(mS=Object.assign.bind()).apply(null,arguments)}var mT={accessibilityLayer:!0,layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},mU=(0,f.forwardRef)(function(a,b){var c,d=mQ(a.categoricalChartProps,mT),{width:e,height:g}=d,h=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(d,mR);if(!gV(e)||!gV(g))return null;var{chartName:i,defaultTooltipEventType:j,validateTooltipEventTypes:k,tooltipPayloadSearcher:l,categoricalChartProps:m}=a;return f.createElement(l3,{preloadedState:{options:{chartName:i,defaultTooltipEventType:j,validateTooltipEventTypes:k,tooltipPayloadSearcher:l,eventEmitter:void 0}},reduxStoreName:null!=(c=m.id)?c:i},f.createElement(l4,{chartData:m.data}),f.createElement(l5,{width:e,height:g,layout:d.layout,margin:d.margin}),f.createElement(l6,{accessibilityLayer:d.accessibilityLayer,barCategoryGap:d.barCategoryGap,maxBarSize:d.maxBarSize,stackOffset:d.stackOffset,barGap:d.barGap,barSize:d.barSize,syncId:d.syncId,syncMethod:d.syncMethod,className:d.className}),f.createElement(mO,mS({},h,{width:e,height:g,ref:b})))}),mV=["axis","item"],mW=(0,f.forwardRef)((a,b)=>f.createElement(mU,{chartName:"BarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:mV,tooltipPayloadSearcher:bp,categoricalChartProps:a,ref:b})),mX=f,mY=["children","className"];function mZ(){return(mZ=Object.assign.bind()).apply(null,arguments)}var m$=f.forwardRef((a,b)=>{var{children:c,className:d}=a,e=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,mY),g=(0,l8.clsx)("recharts-layer",d);return f.createElement("g",mZ({className:g},mp(e,!0),{ref:b}),c)}),m_=a=>null;m_.displayName="Cell";var m0=a.i(78375),m1={isSsr:!0};function m2(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}var m3=function(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?m2(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):m2(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}({},{cacheSize:2e3,enableCache:!0}),m4=new class{constructor(a){!function(a,b,c){var d;(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c}(this,"cache",new Map),this.maxSize=a}get(a){var b=this.cache.get(a);return void 0!==b&&(this.cache.delete(a),this.cache.set(a,b)),b}set(a,b){if(this.cache.has(a))this.cache.delete(a);else if(this.cache.size>=this.maxSize){var c=this.cache.keys().next().value;this.cache.delete(c)}this.cache.set(a,b)}clear(){this.cache.clear()}size(){return this.cache.size}}(m3.cacheSize),m5={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},m6="recharts_measurement_span",m7=(a,b)=>{try{var c=document.getElementById(m6);c||((c=document.createElement("span")).setAttribute("id",m6),c.setAttribute("aria-hidden","true"),document.body.appendChild(c)),Object.assign(c.style,m5,b),c.textContent="".concat(a);var d=c.getBoundingClientRect();return{width:d.width,height:d.height}}catch(a){return{width:0,height:0}}},m8=function(a){var b,c,d,e,f,g,h=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==a||m1.isSsr)return{width:0,height:0};if(!m3.enableCache)return m7(a,h);var i=(b=h.fontSize||"",c=h.fontFamily||"",d=h.fontWeight||"",e=h.fontStyle||"",f=h.letterSpacing||"",g=h.textTransform||"","".concat(a,"|").concat(b,"|").concat(c,"|").concat(d,"|").concat(e,"|").concat(f,"|").concat(g)),j=m4.get(i);if(j)return j;var k=m7(a,h);return m4.set(i,k),k},m9=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,na=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,nb=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,nc=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,nd={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},ne=Object.keys(nd);class nf{static parse(a){var b,[,c,d]=null!=(b=nc.exec(a))?b:[];return new nf(parseFloat(c),null!=d?d:"")}constructor(a,b){this.num=a,this.unit=b,this.num=a,this.unit=b,bd(a)&&(this.unit=""),""===b||nb.test(b)||(this.num=NaN,this.unit=""),ne.includes(b)&&(this.num=a*nd[b],this.unit="px")}add(a){return this.unit!==a.unit?new nf(NaN,""):new nf(this.num+a.num,this.unit)}subtract(a){return this.unit!==a.unit?new nf(NaN,""):new nf(this.num-a.num,this.unit)}multiply(a){return""!==this.unit&&""!==a.unit&&this.unit!==a.unit?new nf(NaN,""):new nf(this.num*a.num,this.unit||a.unit)}divide(a){return""!==this.unit&&""!==a.unit&&this.unit!==a.unit?new nf(NaN,""):new nf(this.num/a.num,this.unit||a.unit)}toString(){return"".concat(this.num).concat(this.unit)}isNaN(){return bd(this.num)}}function ng(a){if(a.includes("NaN"))return"NaN";for(var b=a;b.includes("*")||b.includes("/");){var c,[,d,e,f]=null!=(c=m9.exec(b))?c:[],g=nf.parse(null!=d?d:""),h=nf.parse(null!=f?f:""),i="*"===e?g.multiply(h):g.divide(h);if(i.isNaN())return"NaN";b=b.replace(m9,i.toString())}for(;b.includes("+")||/.-\d+(?:\.\d+)?/.test(b);){var j,[,k,l,m]=null!=(j=na.exec(b))?j:[],n=nf.parse(null!=k?k:""),o=nf.parse(null!=m?m:""),p="+"===l?n.add(o):n.subtract(o);if(p.isNaN())return"NaN";b=b.replace(na,p.toString())}return b}var nh=/\(([^()]*)\)/;function ni(a){var b=function(a){try{var b;return b=a.replace(/\s+/g,""),b=function(a){for(var b,c=a;null!=(b=nh.exec(c));){var[,d]=b;c=c.replace(nh,ng(d))}return c}(b),b=ng(b)}catch(a){return"NaN"}}(a.slice(5,-1));return"NaN"===b?"":b}var nj=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],nk=["dx","dy","angle","className","breakAll"];function nl(){return(nl=Object.assign.bind()).apply(null,arguments)}function nm(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}var nn=/[ \f\n\r\t\v\u2028\u2029]+/,no=a=>{var{children:b,breakAll:c,style:d}=a;try{let a;var e=[];a=b,null==a||(e=c?b.toString().split(""):b.toString().split(nn));var f=e.map(a=>({word:a,width:m8(a,d).width})),g=c?0:m8(" ",d).width;return{wordsWithComputedWidth:f,spaceWidth:g}}catch(a){return null}},np=a=>[{words:null==a?[]:a.toString().split(nn)}],nq="#808080",nr=(0,f.forwardRef)((a,b)=>{var c,{x:d=0,y:e=0,lineHeight:g="1em",capHeight:h="0.71em",scaleToFit:i=!1,textAnchor:j="start",verticalAnchor:k="end",fill:l=nq}=a,m=nm(a,nj),n=(0,f.useMemo)(()=>(a=>{var{width:b,scaleToFit:c,children:d,style:e,breakAll:f,maxLines:g}=a;if((b||c)&&!m1.isSsr){var h=no({breakAll:f,children:d,style:e});if(!h)return np(d);var{wordsWithComputedWidth:i,spaceWidth:j}=h;return((a,b,c,d,e)=>{var f,{maxLines:g,children:h,style:i,breakAll:j}=a,k=bf(g),l=function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return a.reduce((a,b)=>{var{word:f,width:g}=b,h=a[a.length-1];return h&&(null==d||e||h.width+g+c<Number(d))?(h.words.push(f),h.width+=g+c):a.push({words:[f],width:g}),a},[])},m=l(b),n=a=>a.reduce((a,b)=>a.width>b.width?a:b);if(!k||e||!(m.length>g||n(m).width>Number(d)))return m;for(var o=a=>{var b=l(no({breakAll:j,style:i,children:h.slice(0,a)+"…"}).wordsWithComputedWidth);return[b.length>g||n(b).width>Number(d),b]},p=0,q=h.length-1,r=0;p<=q&&r<=h.length-1;){var s=Math.floor((p+q)/2),[t,u]=o(s-1),[v]=o(s);if(t||v||(p=s+1),t&&v&&(q=s-1),!t&&v){f=u;break}r++}return f||m})({breakAll:f,children:d,maxLines:g,style:e},i,j,b,c)}return np(d)})({breakAll:m.breakAll,children:m.children,maxLines:m.maxLines,scaleToFit:i,style:m.style,width:m.width}),[m.breakAll,m.children,m.maxLines,i,m.style,m.width]),{dx:o,dy:p,angle:q,className:r,breakAll:s}=m,t=nm(m,nk);if(!bg(d)||!bg(e))return null;var u=d+(bf(o)?o:0),v=e+(bf(p)?p:0);switch(k){case"start":c=ni("calc(".concat(h,")"));break;case"middle":c=ni("calc(".concat((n.length-1)/2," * -").concat(g," + (").concat(h," / 2))"));break;default:c=ni("calc(".concat(n.length-1," * -").concat(g,")"))}var w=[];if(i){var x=n[0].width,{width:y}=m;w.push("scale(".concat(bf(y)?y/x:1,")"))}return q&&w.push("rotate(".concat(q,", ").concat(u,", ").concat(v,")")),w.length&&(t.transform=w.join(" ")),f.createElement("text",nl({},mp(t,!0),{ref:b,x:u,y:v,className:(0,l8.clsx)("recharts-text",r),textAnchor:j,fill:l.includes("url")?nq:l}),n.map((a,b)=>{var d=a.words.join(s?"":" ");return f.createElement("tspan",{x:u,dy:0===b?c:g,key:"".concat(d,"-").concat(b)},d)}))});nr.displayName="Text";var ns=["offset"],nt=["labelRef"];function nu(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}function nv(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function nw(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?nv(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):nv(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function nx(){return(nx=Object.assign.bind()).apply(null,arguments)}var ny=a=>null!=a&&"function"==typeof a;function nz(a){var b,{offset:c=5}=a,d=nw({offset:c},nu(a,ns)),{viewBox:e,position:g,value:h,children:i,content:j,className:k="",textBreakAll:l,labelRef:m}=d,n=b6(h6),o=cR(),p=e||("center"===g?o:null!=n?n:o);if(!p||null==h&&null==i&&!(0,f.isValidElement)(j)&&"function"!=typeof j)return null;var q=nw(nw({},d),{},{viewBox:p});if((0,f.isValidElement)(j)){var{labelRef:r}=q,s=nu(q,nt);return(0,f.cloneElement)(j,s)}if("function"==typeof j){if(b=(0,f.createElement)(j,q),(0,f.isValidElement)(b))return b}else b=(a=>{var{value:b,formatter:c}=a,d=null==a.children?b:a.children;return"function"==typeof c?c(d):d})(d);var t="cx"in p&&bf(p.cx),u=mp(d,!0);if(t&&("insideStart"===g||"insideEnd"===g||"end"===g))return((a,b,c,d)=>{let e,g;var h,i,{position:j,offset:k,className:l}=a,{cx:m,cy:n,innerRadius:o,outerRadius:p,startAngle:q,endAngle:r,clockWise:s}=d,t=(o+p)/2,u=(e=q,bc((g=r)-e)*Math.min(Math.abs(g-e),360)),v=u>=0?1:-1;"insideStart"===j?(h=q+v*k,i=s):"insideEnd"===j?(h=r-v*k,i=!s):"end"===j&&(h=r+v*k,i=s),i=u<=0?i:!i;var w=ci(m,n,t,h),x=ci(m,n,t,h+(i?1:-1)*359),y="M".concat(w.x,",").concat(w.y,"\n    A").concat(t,",").concat(t,",0,1,").concat(+!i,",\n    ").concat(x.x,",").concat(x.y),z=null==a.id?bi("recharts-radial-line-"):a.id;return f.createElement("text",nx({},c,{dominantBaseline:"central",className:(0,l8.clsx)("recharts-radial-bar-label",l)}),f.createElement("defs",null,f.createElement("path",{id:z,d:y})),f.createElement("textPath",{xlinkHref:"#".concat(z)},b))})(d,b,u,p);var v=t?((a,b,c)=>{var{cx:d,cy:e,innerRadius:f,outerRadius:g,startAngle:h,endAngle:i}=a,j=(h+i)/2;if("outside"===c){var{x:k,y:l}=ci(d,e,g+b,j);return{x:k,y:l,textAnchor:k>=d?"start":"end",verticalAnchor:"middle"}}if("center"===c)return{x:d,y:e,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===c)return{x:d,y:e,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===c)return{x:d,y:e,textAnchor:"middle",verticalAnchor:"end"};var{x:m,y:n}=ci(d,e,(f+g)/2,j);return{x:m,y:n,textAnchor:"middle",verticalAnchor:"middle"}})(p,d.offset,d.position):((a,b)=>{var{parentViewBox:c,offset:d,position:e}=a,{x:f,y:g,width:h,height:i}=b,j=i>=0?1:-1,k=j*d,l=j>0?"end":"start",m=j>0?"start":"end",n=h>=0?1:-1,o=n*d,p=n>0?"end":"start",q=n>0?"start":"end";if("top"===e)return nw(nw({},{x:f+h/2,y:g-j*d,textAnchor:"middle",verticalAnchor:l}),c?{height:Math.max(g-c.y,0),width:h}:{});if("bottom"===e)return nw(nw({},{x:f+h/2,y:g+i+k,textAnchor:"middle",verticalAnchor:m}),c?{height:Math.max(c.y+c.height-(g+i),0),width:h}:{});if("left"===e){var r={x:f-o,y:g+i/2,textAnchor:p,verticalAnchor:"middle"};return nw(nw({},r),c?{width:Math.max(r.x-c.x,0),height:i}:{})}if("right"===e){var s={x:f+h+o,y:g+i/2,textAnchor:q,verticalAnchor:"middle"};return nw(nw({},s),c?{width:Math.max(c.x+c.width-s.x,0),height:i}:{})}var t=c?{width:h,height:i}:{};return"insideLeft"===e?nw({x:f+o,y:g+i/2,textAnchor:q,verticalAnchor:"middle"},t):"insideRight"===e?nw({x:f+h-o,y:g+i/2,textAnchor:p,verticalAnchor:"middle"},t):"insideTop"===e?nw({x:f+h/2,y:g+k,textAnchor:"middle",verticalAnchor:m},t):"insideBottom"===e?nw({x:f+h/2,y:g+i-k,textAnchor:"middle",verticalAnchor:l},t):"insideTopLeft"===e?nw({x:f+o,y:g+k,textAnchor:q,verticalAnchor:m},t):"insideTopRight"===e?nw({x:f+h-o,y:g+k,textAnchor:p,verticalAnchor:m},t):"insideBottomLeft"===e?nw({x:f+o,y:g+i-k,textAnchor:q,verticalAnchor:l},t):"insideBottomRight"===e?nw({x:f+h-o,y:g+i-k,textAnchor:p,verticalAnchor:l},t):e&&"object"==typeof e&&(bf(e.x)||be(e.x))&&(bf(e.y)||be(e.y))?nw({x:f+bj(e.x,h),y:g+bj(e.y,i),textAnchor:"end",verticalAnchor:"end"},t):nw({x:f+h/2,y:g+i/2,textAnchor:"middle",verticalAnchor:"middle"},t)})(d,p);return f.createElement(nr,nx({ref:m,className:(0,l8.clsx)("recharts-label",k)},u,v,{breakAll:l}),b)}nz.displayName="Label";var nA=a=>{var{cx:b,cy:c,angle:d,startAngle:e,endAngle:f,r:g,radius:h,innerRadius:i,outerRadius:j,x:k,y:l,top:m,left:n,width:o,height:p,clockWise:q,labelViewBox:r}=a;if(r)return r;if(bf(o)&&bf(p)){if(bf(k)&&bf(l))return{x:k,y:l,width:o,height:p};if(bf(m)&&bf(n))return{x:m,y:n,width:o,height:p}}return bf(k)&&bf(l)?{x:k,y:l,width:0,height:0}:bf(b)&&bf(c)?{cx:b,cy:c,startAngle:e||d||0,endAngle:f||d||0,innerRadius:i||0,outerRadius:j||h||g||0,clockWise:q}:a.viewBox?a.viewBox:void 0};nz.parseViewBox=nA,nz.renderCallByParent=function(a,b){var c=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!a||!a.children&&c&&!a.label)return null;var{children:d,labelRef:e}=a,g=nA(a),h=mn(d,nz).map((a,c)=>(0,f.cloneElement)(a,{viewBox:b||g,key:"label-".concat(c)}));return c?[((a,b,c)=>{if(!a)return null;var d={viewBox:b,labelRef:c};return!0===a?f.createElement(nz,nx({key:"label-implicit"},d)):bg(a)?f.createElement(nz,nx({key:"label-implicit",value:a},d)):(0,f.isValidElement)(a)?a.type===nz?(0,f.cloneElement)(a,nw({key:"label-implicit"},d)):f.createElement(nz,nx({key:"label-implicit",content:a},d)):ny(a)?f.createElement(nz,nx({key:"label-implicit",content:a},d)):a&&"object"==typeof a?f.createElement(nz,nx({},a,{key:"label-implicit"},d)):null})(a.label,b||g,e),...h]:h};var nB=["valueAccessor"],nC=["data","dataKey","clockWise","id","textBreakAll"];function nD(){return(nD=Object.assign.bind()).apply(null,arguments)}function nE(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function nF(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?nE(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):nE(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function nG(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}var nH=a=>Array.isArray(a.value)?(0,m0.default)(a.value):a.value;function nI(a){var{valueAccessor:b=nH}=a,c=nG(a,nB),{data:d,dataKey:e,clockWise:g,id:h,textBreakAll:i}=c,j=nG(c,nC);return d&&d.length?f.createElement(m$,{className:"recharts-label-list"},d.map((a,c)=>{var d=null==e?b(a,c):cn(a&&a.payload,e),k=null==h?{}:{id:"".concat(h,"-").concat(c)};return f.createElement(nz,nD({},mp(a,!0),j,k,{parentViewBox:a.parentViewBox,value:d,textBreakAll:i,viewBox:nz.parseViewBox(null==g?a:nF(nF({},a),{},{clockWise:g})),key:"label-".concat(c),index:c}))})):null}nI.displayName="LabelList",nI.renderCallByParent=function(a,b){var c,d=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!a||!a.children&&d&&!a.label)return null;var{children:e}=a,g=mn(e,nI).map((a,c)=>(0,f.cloneElement)(a,{data:b,key:"labelList-".concat(c)}));return d?[(c=a.label,c?!0===c?f.createElement(nI,{key:"labelList-implicit",data:b}):f.isValidElement(c)||ny(c)?f.createElement(nI,{key:"labelList-implicit",data:b,content:c}):"object"==typeof c?f.createElement(nI,nD({data:b},c,{key:"labelList-implicit"})):null:null),...g]:g};var nJ=a.i(3068),nK=f,nL=a.i(97854),nM=(a,b)=>[0,3*a,3*b-6*a,3*a-3*b+1],nN=(a,b)=>a.map((a,c)=>a*b**c).reduce((a,b)=>a+b),nO=(a,b)=>c=>nN(nM(a,b),c),nP=function(){let a,b;for(var c,d,e,f,g=arguments.length,h=Array(g),i=0;i<g;i++)h[i]=arguments[i];if(1===h.length)switch(h[0]){case"linear":[c,e,d,f]=[0,0,1,1];break;case"ease":[c,e,d,f]=[.25,.1,.25,1];break;case"ease-in":[c,e,d,f]=[.42,0,1,1];break;case"ease-out":[c,e,d,f]=[.42,0,.58,1];break;case"ease-in-out":[c,e,d,f]=[0,0,.58,1];break;default:var j=h[0].split("(");"cubic-bezier"===j[0]&&4===j[1].split(")")[0].split(",").length&&([c,e,d,f]=j[1].split(")")[0].split(",").map(a=>parseFloat(a)))}else 4===h.length&&([c,e,d,f]=h);var k=nO(c,d),l=nO(e,f),m=(a=c,b=d,c=>nN([...nM(a,b).map((a,b)=>a*b).slice(1),0],c)),n=a=>a>1?1:a<0?0:a,o=a=>{for(var b=a>1?1:a,c=b,d=0;d<8;++d){var e=k(c)-b,f=m(c);if(1e-4>Math.abs(e-b)||f<1e-4)break;c=n(c-e/f)}return l(c)};return o.isStepper=!1,o},nQ=function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{stiff:b=100,damping:c=8,dt:d=17}=a,e=(a,e,f)=>{var g=f+(-(a-e)*b-f*c)*d/1e3,h=f*d/1e3+a;return 1e-4>Math.abs(h-e)&&1e-4>Math.abs(g)?[e,0]:[h,g]};return e.isStepper=!0,e.dt=d,e},nR=a=>{if("string"==typeof a)switch(a){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return nP(a);case"spring":return nQ();default:if("cubic-bezier"===a.split("(")[0])return nP(a)}return"function"==typeof a?a:null};function nS(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function nT(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?nS(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):nS(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var nU=(a,b)=>Object.keys(b).reduce((c,d)=>nT(nT({},c),{},{[d]:a(d,b[d])}),{});function nV(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function nW(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?nV(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):nV(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var nX=(a,b,c)=>a+(b-a)*c,nY=a=>{var{from:b,to:c}=a;return b!==c},nZ=(a,b,c)=>{var d=nU((b,c)=>{if(nY(c)){var[d,e]=a(c.from,c.to,c.velocity);return nW(nW({},c),{},{from:d,velocity:e})}return c},b);return c<1?nU((a,b)=>nY(b)?nW(nW({},b),{},{velocity:nX(b.velocity,d[a].velocity,c),from:nX(b.from,d[a].from,c)}):b,b):nZ(a,d,c-1)};let n$=(a,b,c,d,e,f)=>{var g=[Object.keys(a),Object.keys(b)].reduce((a,b)=>a.filter(a=>b.includes(a)));return!0===c.isStepper?function(a,b,c,d,e,f){var g,h=d.reduce((c,d)=>nW(nW({},c),{},{[d]:{from:a[d],velocity:0,to:b[d]}}),{}),i=null,j=d=>{g||(g=d);var k=(d-g)/c.dt;h=nZ(c,h,k),e(nW(nW(nW({},a),b),nU((a,b)=>b.from,h))),g=d,Object.values(h).filter(nY).length&&(i=f.setTimeout(j))};return()=>(i=f.setTimeout(j),()=>{i()})}(a,b,c,g,e,f):function(a,b,c,d,e,f,g){var h,i=null,j=e.reduce((c,d)=>nW(nW({},c),{},{[d]:[a[d],b[d]]}),{}),k=e=>{h||(h=e);var l=(e-h)/d,m=nU((a,b)=>nX(...b,c(l)),j);if(f(nW(nW(nW({},a),b),m)),l<1)i=g.setTimeout(k);else{var n=nU((a,b)=>nX(...b,c(1)),j);f(nW(nW(nW({},a),b),n))}};return()=>(i=g.setTimeout(k),()=>{i()})}(a,b,c,d,g,e,f)};class n_{setTimeout(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,c=performance.now(),d=null,e=f=>{f-c>=b?a(f):"function"==typeof requestAnimationFrame&&(d=requestAnimationFrame(e))};return d=requestAnimationFrame(e),()=>{cancelAnimationFrame(d)}}}var n0=(0,f.createContext)(function(){var a,b,c,d,e;return a=new n_,b=()=>null,c=!1,d=null,e=f=>{if(!c){if(Array.isArray(f)){if(!f.length)return;var[g,...h]=f;if("number"==typeof g){d=a.setTimeout(e.bind(null,h),g);return}e(g),d=a.setTimeout(e.bind(null,h));return}"string"==typeof f&&b(f),"object"==typeof f&&b(f),"function"==typeof f&&f()}},{stop:()=>{c=!0},start:a=>{c=!1,d&&(d(),d=null),e(a)},subscribe:a=>(b=a,()=>{b=()=>null}),getTimeoutController:()=>a}});function n1(a,b){var c=(0,f.useContext)(n0);return(0,f.useMemo)(()=>null!=b?b:c(a),[a,b,c])}var n2=["children","begin","duration","attributeName","easing","isActive","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart","animationManager"];function n3(){return(n3=Object.assign.bind()).apply(null,arguments)}function n4(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function n5(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?n4(Object(c),!0).forEach(function(b){n6(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):n4(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function n6(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}class n7 extends nK.PureComponent{constructor(a,b){super(a,b),n6(this,"mounted",!1),n6(this,"manager",void 0),n6(this,"stopJSAnimation",null),n6(this,"unSubscribe",null);var{isActive:c,attributeName:d,from:e,to:f,children:g,duration:h,animationManager:i}=this.props;if(this.manager=i,this.handleStyleChange=this.handleStyleChange.bind(this),this.changeStyle=this.changeStyle.bind(this),!c||h<=0){this.state={style:{}},"function"==typeof g&&(this.state={style:f});return}if(e){if("function"==typeof g){this.state={style:e};return}this.state={style:d?{[d]:e}:e}}else this.state={style:{}}}componentDidMount(){var{isActive:a,canBegin:b}=this.props;this.mounted=!0,a&&b&&this.runAnimation(this.props)}componentDidUpdate(a){var{isActive:b,canBegin:c,attributeName:d,shouldReAnimate:e,to:f,from:g}=this.props,{style:h}=this.state;if(c){if(!b){this.state&&h&&(d&&h[d]!==f||!d&&h!==f)&&this.setState({style:d?{[d]:f}:f});return}if(!(0,nL.default)(a.to,f)||!a.canBegin||!a.isActive){var i=!a.canBegin||!a.isActive;this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var j=i||e?g:a.to;this.state&&h&&(d&&h[d]!==j||!d&&h!==j)&&this.setState({style:d?{[d]:j}:j}),this.runAnimation(n5(n5({},this.props),{},{from:j,begin:0}))}}}componentWillUnmount(){this.mounted=!1;var{onAnimationEnd:a}=this.props;this.unSubscribe&&this.unSubscribe(),this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation(),a&&a()}handleStyleChange(a){this.changeStyle(a)}changeStyle(a){this.mounted&&this.setState({style:a})}runJSAnimation(a){var{from:b,to:c,duration:d,easing:e,begin:f,onAnimationEnd:g,onAnimationStart:h}=a,i=n$(b,c,nR(e),d,this.changeStyle,this.manager.getTimeoutController()),j=()=>{this.stopJSAnimation=i()};this.manager.start([h,f,j,d,g])}runAnimation(a){let b;var{begin:c,duration:d,attributeName:e,to:f,easing:g,onAnimationStart:h,onAnimationEnd:i,children:j}=a;if(this.unSubscribe=this.manager.subscribe(this.handleStyleChange),"function"==typeof g||"function"==typeof j||"spring"===g)return void this.runJSAnimation(a);var k=e?{[e]:f}:f,l=(b=Object.keys(k),b.map(a=>"".concat(a.replace(/([A-Z])/g,a=>"-".concat(a.toLowerCase()))," ").concat(d,"ms ").concat(g)).join(","));this.manager.start([h,c,n5(n5({},k),{},{transition:l}),d,i])}render(){var a=this.props,{children:b,begin:c,duration:d,attributeName:e,easing:f,isActive:g,from:h,to:i,canBegin:j,onAnimationEnd:k,shouldReAnimate:l,onAnimationReStart:m,animationManager:n}=a,o=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,n2),p=nK.Children.count(b),q=this.state.style;if("function"==typeof b)return b(q);if(!g||0===p||d<=0)return b;var r=a=>{var{style:b={},className:c}=a.props;return(0,nK.cloneElement)(a,n5(n5({},o),{},{style:n5(n5({},b),q),className:c}))};return 1===p?r(nK.Children.only(b)):nK.createElement("div",null,nK.Children.map(b,a=>r(a)))}}function n8(a){var b,c=n1(null!=(b=a.attributeName)?b:Object.keys(a.to).join(","),a.animationManager);return nK.createElement(n7,n3({},a,{animationManager:c}))}function n9(){return(n9=Object.assign.bind()).apply(null,arguments)}n6(n7,"displayName","Animate"),n6(n7,"defaultProps",{begin:0,duration:1e3,attributeName:"",easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}});var oa=(a,b,c,d,e)=>{var f,g=Math.min(Math.abs(c)/2,Math.abs(d)/2),h=d>=0?1:-1,i=c>=0?1:-1,j=+(d>=0&&c>=0||d<0&&c<0);if(g>0&&e instanceof Array){for(var k=[0,0,0,0],l=0;l<4;l++)k[l]=e[l]>g?g:e[l];f="M".concat(a,",").concat(b+h*k[0]),k[0]>0&&(f+="A ".concat(k[0],",").concat(k[0],",0,0,").concat(j,",").concat(a+i*k[0],",").concat(b)),f+="L ".concat(a+c-i*k[1],",").concat(b),k[1]>0&&(f+="A ".concat(k[1],",").concat(k[1],",0,0,").concat(j,",\n        ").concat(a+c,",").concat(b+h*k[1])),f+="L ".concat(a+c,",").concat(b+d-h*k[2]),k[2]>0&&(f+="A ".concat(k[2],",").concat(k[2],",0,0,").concat(j,",\n        ").concat(a+c-i*k[2],",").concat(b+d)),f+="L ".concat(a+i*k[3],",").concat(b+d),k[3]>0&&(f+="A ".concat(k[3],",").concat(k[3],",0,0,").concat(j,",\n        ").concat(a,",").concat(b+d-h*k[3])),f+="Z"}else if(g>0&&e===+e&&e>0){var m=Math.min(g,e);f="M ".concat(a,",").concat(b+h*m,"\n            A ").concat(m,",").concat(m,",0,0,").concat(j,",").concat(a+i*m,",").concat(b,"\n            L ").concat(a+c-i*m,",").concat(b,"\n            A ").concat(m,",").concat(m,",0,0,").concat(j,",").concat(a+c,",").concat(b+h*m,"\n            L ").concat(a+c,",").concat(b+d-h*m,"\n            A ").concat(m,",").concat(m,",0,0,").concat(j,",").concat(a+c-i*m,",").concat(b+d,"\n            L ").concat(a+i*m,",").concat(b+d,"\n            A ").concat(m,",").concat(m,",0,0,").concat(j,",").concat(a,",").concat(b+d-h*m," Z")}else f="M ".concat(a,",").concat(b," h ").concat(c," v ").concat(d," h ").concat(-c," Z");return f},ob={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},oc=a=>{var b=mQ(a,ob),c=(0,f.useRef)(null),[d,e]=(0,f.useState)(-1);(0,f.useEffect)(()=>{if(c.current&&c.current.getTotalLength)try{var a=c.current.getTotalLength();a&&e(a)}catch(a){}},[]);var{x:g,y:h,width:i,height:j,radius:k,className:l}=b,{animationEasing:m,animationDuration:n,animationBegin:o,isAnimationActive:p,isUpdateAnimationActive:q}=b;if(g!==+g||h!==+h||i!==+i||j!==+j||0===i||0===j)return null;var r=(0,l8.clsx)("recharts-rectangle",l);return q?f.createElement(n8,{canBegin:d>0,from:{width:i,height:j,x:g,y:h},to:{width:i,height:j,x:g,y:h},duration:n,animationEasing:m,isActive:q},a=>{var{width:e,height:g,x:h,y:i}=a;return f.createElement(n8,{canBegin:d>0,from:"0px ".concat(-1===d?1:d,"px"),to:"".concat(d,"px 0px"),attributeName:"strokeDasharray",begin:o,duration:n,isActive:p,easing:m},f.createElement("path",n9({},mp(b,!0),{className:r,d:oa(h,i,e,g,k),ref:c})))}):f.createElement("path",n9({},mp(b,!0),{className:r,d:oa(g,h,i,j,k)}))};function od(){return(od=Object.assign.bind()).apply(null,arguments)}var oe=(a,b,c,d,e)=>{var f=c-d;return"M ".concat(a,",").concat(b)+"L ".concat(a+c,",").concat(b)+"L ".concat(a+c-f/2,",").concat(b+e)+"L ".concat(a+c-f/2-d,",").concat(b+e)+"L ".concat(a,",").concat(b," Z")},of={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},og=a=>{var b=mQ(a,of),c=(0,f.useRef)(),[d,e]=(0,f.useState)(-1);(0,f.useEffect)(()=>{if(c.current&&c.current.getTotalLength)try{var a=c.current.getTotalLength();a&&e(a)}catch(a){}},[]);var{x:g,y:h,upperWidth:i,lowerWidth:j,height:k,className:l}=b,{animationEasing:m,animationDuration:n,animationBegin:o,isUpdateAnimationActive:p}=b;if(g!==+g||h!==+h||i!==+i||j!==+j||k!==+k||0===i&&0===j||0===k)return null;var q=(0,l8.clsx)("recharts-trapezoid",l);return p?f.createElement(n8,{canBegin:d>0,from:{upperWidth:0,lowerWidth:0,height:k,x:g,y:h},to:{upperWidth:i,lowerWidth:j,height:k,x:g,y:h},duration:n,animationEasing:m,isActive:p},a=>{var{upperWidth:e,lowerWidth:g,height:h,x:i,y:j}=a;return f.createElement(n8,{canBegin:d>0,from:"0px ".concat(-1===d?1:d,"px"),to:"".concat(d,"px 0px"),attributeName:"strokeDasharray",begin:o,duration:n,easing:m},f.createElement("path",od({},mp(b,!0),{className:q,d:oe(i,j,e,g,h),ref:c})))}):f.createElement("g",null,f.createElement("path",od({},mp(b,!0),{className:q,d:oe(g,h,i,j,k)})))};function oh(){return(oh=Object.assign.bind()).apply(null,arguments)}var oi=a=>{var{cx:b,cy:c,radius:d,angle:e,sign:f,isExternal:g,cornerRadius:h,cornerIsExternal:i}=a,j=h*(g?1:-1)+d,k=Math.asin(h/j)/ch,l=i?e:e+f*k,m=ci(b,c,j,l);return{center:m,circleTangency:ci(b,c,d,l),lineTangency:ci(b,c,j*Math.cos(k*ch),i?e-f*k:e),theta:k}},oj=a=>{var{cx:b,cy:c,innerRadius:d,outerRadius:e,startAngle:f,endAngle:g}=a,h=((a,b)=>bc(b-a)*Math.min(Math.abs(b-a),359.999))(f,g),i=f+h,j=ci(b,c,e,f),k=ci(b,c,e,i),l="M ".concat(j.x,",").concat(j.y,"\n    A ").concat(e,",").concat(e,",0,\n    ").concat(+(Math.abs(h)>180),",").concat(+(f>i),",\n    ").concat(k.x,",").concat(k.y,"\n  ");if(d>0){var m=ci(b,c,d,f),n=ci(b,c,d,i);l+="L ".concat(n.x,",").concat(n.y,"\n            A ").concat(d,",").concat(d,",0,\n            ").concat(+(Math.abs(h)>180),",").concat(+(f<=i),",\n            ").concat(m.x,",").concat(m.y," Z")}else l+="L ".concat(b,",").concat(c," Z");return l},ok={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},ol=a=>{var b,c=mQ(a,ok),{cx:d,cy:e,innerRadius:g,outerRadius:h,cornerRadius:i,forceCornerRadius:j,cornerIsExternal:k,startAngle:l,endAngle:m,className:n}=c;if(h<g||l===m)return null;var o=(0,l8.clsx)("recharts-sector",n),p=h-g,q=bj(i,p,0,!0);return b=q>0&&360>Math.abs(l-m)?(a=>{var{cx:b,cy:c,innerRadius:d,outerRadius:e,cornerRadius:f,forceCornerRadius:g,cornerIsExternal:h,startAngle:i,endAngle:j}=a,k=bc(j-i),{circleTangency:l,lineTangency:m,theta:n}=oi({cx:b,cy:c,radius:e,angle:i,sign:k,cornerRadius:f,cornerIsExternal:h}),{circleTangency:o,lineTangency:p,theta:q}=oi({cx:b,cy:c,radius:e,angle:j,sign:-k,cornerRadius:f,cornerIsExternal:h}),r=h?Math.abs(i-j):Math.abs(i-j)-n-q;if(r<0)return g?"M ".concat(m.x,",").concat(m.y,"\n        a").concat(f,",").concat(f,",0,0,1,").concat(2*f,",0\n        a").concat(f,",").concat(f,",0,0,1,").concat(-(2*f),",0\n      "):oj({cx:b,cy:c,innerRadius:d,outerRadius:e,startAngle:i,endAngle:j});var s="M ".concat(m.x,",").concat(m.y,"\n    A").concat(f,",").concat(f,",0,0,").concat(+(k<0),",").concat(l.x,",").concat(l.y,"\n    A").concat(e,",").concat(e,",0,").concat(+(r>180),",").concat(+(k<0),",").concat(o.x,",").concat(o.y,"\n    A").concat(f,",").concat(f,",0,0,").concat(+(k<0),",").concat(p.x,",").concat(p.y,"\n  ");if(d>0){var{circleTangency:t,lineTangency:u,theta:v}=oi({cx:b,cy:c,radius:d,angle:i,sign:k,isExternal:!0,cornerRadius:f,cornerIsExternal:h}),{circleTangency:w,lineTangency:x,theta:y}=oi({cx:b,cy:c,radius:d,angle:j,sign:-k,isExternal:!0,cornerRadius:f,cornerIsExternal:h}),z=h?Math.abs(i-j):Math.abs(i-j)-v-y;if(z<0&&0===f)return"".concat(s,"L").concat(b,",").concat(c,"Z");s+="L".concat(x.x,",").concat(x.y,"\n      A").concat(f,",").concat(f,",0,0,").concat(+(k<0),",").concat(w.x,",").concat(w.y,"\n      A").concat(d,",").concat(d,",0,").concat(+(z>180),",").concat(+(k>0),",").concat(t.x,",").concat(t.y,"\n      A").concat(f,",").concat(f,",0,0,").concat(+(k<0),",").concat(u.x,",").concat(u.y,"Z")}else s+="L".concat(b,",").concat(c,"Z");return s})({cx:d,cy:e,innerRadius:g,outerRadius:h,cornerRadius:Math.min(q,p/2),forceCornerRadius:j,cornerIsExternal:k,startAngle:l,endAngle:m}):oj({cx:d,cy:e,innerRadius:g,outerRadius:h,startAngle:l,endAngle:m}),f.createElement("path",oh({},mp(c,!0),{className:o,d:b}))};let om=Math.PI,on=2*om,oo=on-1e-6;function op(a){this._+=a[0];for(let b=1,c=a.length;b<c;++b)this._+=arguments[b]+a[b]}class oq{constructor(a){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==a?op:function(a){let b=Math.floor(a);if(!(b>=0))throw Error(`invalid digits: ${a}`);if(b>15)return op;let c=10**b;return function(a){this._+=a[0];for(let b=1,d=a.length;b<d;++b)this._+=Math.round(arguments[b]*c)/c+a[b]}}(a)}moveTo(a,b){this._append`M${this._x0=this._x1=+a},${this._y0=this._y1=+b}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(a,b){this._append`L${this._x1=+a},${this._y1=+b}`}quadraticCurveTo(a,b,c,d){this._append`Q${+a},${+b},${this._x1=+c},${this._y1=+d}`}bezierCurveTo(a,b,c,d,e,f){this._append`C${+a},${+b},${+c},${+d},${this._x1=+e},${this._y1=+f}`}arcTo(a,b,c,d,e){if(a*=1,b*=1,c*=1,d*=1,(e*=1)<0)throw Error(`negative radius: ${e}`);let f=this._x1,g=this._y1,h=c-a,i=d-b,j=f-a,k=g-b,l=j*j+k*k;if(null===this._x1)this._append`M${this._x1=a},${this._y1=b}`;else if(l>1e-6)if(Math.abs(k*h-i*j)>1e-6&&e){let m=c-f,n=d-g,o=h*h+i*i,p=Math.sqrt(o),q=Math.sqrt(l),r=e*Math.tan((om-Math.acos((o+l-(m*m+n*n))/(2*p*q)))/2),s=r/q,t=r/p;Math.abs(s-1)>1e-6&&this._append`L${a+s*j},${b+s*k}`,this._append`A${e},${e},0,0,${+(k*m>j*n)},${this._x1=a+t*h},${this._y1=b+t*i}`}else this._append`L${this._x1=a},${this._y1=b}`}arc(a,b,c,d,e,f){if(a*=1,b*=1,c*=1,f=!!f,c<0)throw Error(`negative radius: ${c}`);let g=c*Math.cos(d),h=c*Math.sin(d),i=a+g,j=b+h,k=1^f,l=f?d-e:e-d;null===this._x1?this._append`M${i},${j}`:(Math.abs(this._x1-i)>1e-6||Math.abs(this._y1-j)>1e-6)&&this._append`L${i},${j}`,c&&(l<0&&(l=l%on+on),l>oo?this._append`A${c},${c},0,1,${k},${a-g},${b-h}A${c},${c},0,1,${k},${this._x1=i},${this._y1=j}`:l>1e-6&&this._append`A${c},${c},0,${+(l>=om)},${k},${this._x1=a+c*Math.cos(e)},${this._y1=b+c*Math.sin(e)}`)}rect(a,b,c,d){this._append`M${this._x0=this._x1=+a},${this._y0=this._y1=+b}h${c*=1}v${+d}h${-c}Z`}toString(){return this._}}function or(a){let b=3;return a.digits=function(c){if(!arguments.length)return b;if(null==c)b=null;else{let a=Math.floor(c);if(!(a>=0))throw RangeError(`invalid digits: ${c}`);b=a}return a},()=>new oq(b)}oq.prototype;let os=Math.cos,ot=Math.sin,ou=Math.sqrt,ov=Math.PI,ow=2*ov;ou(3);let ox={draw(a,b){let c=ou(b/ov);a.moveTo(c,0),a.arc(0,0,c,0,ow)}},oy=ou(1/3),oz=2*oy,oA=ot(ov/10)/ot(7*ov/10),oB=ot(ow/10)*oA,oC=-os(ow/10)*oA,oD=ou(3);ou(3);let oE=ou(3)/2,oF=1/ou(12),oG=(oF/2+1)*3;var oH=["type","size","sizeType"];function oI(){return(oI=Object.assign.bind()).apply(null,arguments)}function oJ(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function oK(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?oJ(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):oJ(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var oL={symbolCircle:ox,symbolCross:{draw(a,b){let c=ou(b/5)/2;a.moveTo(-3*c,-c),a.lineTo(-c,-c),a.lineTo(-c,-3*c),a.lineTo(c,-3*c),a.lineTo(c,-c),a.lineTo(3*c,-c),a.lineTo(3*c,c),a.lineTo(c,c),a.lineTo(c,3*c),a.lineTo(-c,3*c),a.lineTo(-c,c),a.lineTo(-3*c,c),a.closePath()}},symbolDiamond:{draw(a,b){let c=ou(b/oz),d=c*oy;a.moveTo(0,-c),a.lineTo(d,0),a.lineTo(0,c),a.lineTo(-d,0),a.closePath()}},symbolSquare:{draw(a,b){let c=ou(b),d=-c/2;a.rect(d,d,c,c)}},symbolStar:{draw(a,b){let c=ou(.8908130915292852*b),d=oB*c,e=oC*c;a.moveTo(0,-c),a.lineTo(d,e);for(let b=1;b<5;++b){let f=ow*b/5,g=os(f),h=ot(f);a.lineTo(h*c,-g*c),a.lineTo(g*d-h*e,h*d+g*e)}a.closePath()}},symbolTriangle:{draw(a,b){let c=-ou(b/(3*oD));a.moveTo(0,2*c),a.lineTo(-oD*c,-c),a.lineTo(oD*c,-c),a.closePath()}},symbolWye:{draw(a,b){let c=ou(b/oG),d=c/2,e=c*oF,f=c*oF+c,g=-d;a.moveTo(d,e),a.lineTo(d,f),a.lineTo(g,f),a.lineTo(-.5*d-oE*e,oE*d+-.5*e),a.lineTo(-.5*d-oE*f,oE*d+-.5*f),a.lineTo(-.5*g-oE*f,oE*g+-.5*f),a.lineTo(-.5*d+oE*e,-.5*e-oE*d),a.lineTo(-.5*d+oE*f,-.5*f-oE*d),a.lineTo(-.5*g+oE*f,-.5*f-oE*g),a.closePath()}}},oM=Math.PI/180,oN=a=>{var{type:b="circle",size:c=64,sizeType:d="area"}=a,e=oK(oK({},function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,oH)),{},{type:b,size:c,sizeType:d}),{className:g,cx:h,cy:i}=e,j=mp(e,!0);return h===+h&&i===+i&&c===+c?f.createElement("path",oI({},j,{className:(0,l8.clsx)("recharts-symbols",g),transform:"translate(".concat(h,", ").concat(i,")"),d:(()=>{var a=oL["symbol".concat(bo(b))]||ox;return(function(a,b){let c=null,d=or(e);function e(){let e;if(c||(c=e=d()),a.apply(this,arguments).draw(c,+b.apply(this,arguments)),e)return c=null,e+""||null}return a="function"==typeof a?a:ca(a||ox),b="function"==typeof b?b:ca(void 0===b?64:+b),e.type=function(b){return arguments.length?(a="function"==typeof b?b:ca(b),e):a},e.size=function(a){return arguments.length?(b="function"==typeof a?a:ca(+a),e):b},e.context=function(a){return arguments.length?(c=null==a?null:a,e):c},e})().type(a).size(((a,b,c)=>{if("area"===b)return a;switch(c){case"cross":return 5*a*a/9;case"diamond":return .5*a*a/Math.sqrt(3);case"square":return a*a;case"star":var d=18*oM;return 1.25*a*a*(Math.tan(d)-Math.tan(2*d)*Math.tan(d)**2);case"triangle":return Math.sqrt(3)*a*a/4;case"wye":return(21-10*Math.sqrt(3))*a*a/8;default:return Math.PI*a*a/4}})(c,d,b))()})()})):null};oN.registerSymbol=(a,b)=>{oL["symbol".concat(bo(a))]=b};var oO=["option","shapeType","propTransformer","activeClassName","isActive"];function oP(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function oQ(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?oP(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):oP(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function oR(a,b){return oQ(oQ({},b),a)}function oS(a){var{shapeType:b,elementProps:c}=a;switch(b){case"rectangle":return f.createElement(oc,c);case"trapezoid":return f.createElement(og,c);case"sector":return f.createElement(ol,c);case"symbols":if("symbols"===b)return f.createElement(oN,c);break;default:return null}}function oT(a){var b,{option:c,shapeType:d,propTransformer:e=oR,activeClassName:g="recharts-active-shape",isActive:h}=a,i=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,oO);if((0,f.isValidElement)(c))b=(0,f.cloneElement)(c,oQ(oQ({},i),(0,f.isValidElement)(c)?c.props:c));else if("function"==typeof c)b=c(i);else if((0,nJ.default)(c)&&"boolean"!=typeof c){var j=e(c,i);b=f.createElement(oS,{shapeType:d,elementProps:j})}else b=f.createElement(oS,{shapeType:d,elementProps:i});return h?f.createElement(m$,{className:g},b):b}var oU=["x","y"];function oV(){return(oV=Object.assign.bind()).apply(null,arguments)}function oW(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function oX(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?oW(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):oW(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function oY(a,b){var{x:c,y:d}=a,e=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,oU),f=parseInt("".concat(c),10),g=parseInt("".concat(d),10),h=parseInt("".concat(b.height||e.height),10),i=parseInt("".concat(b.width||e.width),10);return oX(oX(oX(oX(oX({},b),e),f?{x:f}:{}),g?{y:g}:{}),{},{height:h,width:i,name:b.name,radius:b.radius})}function oZ(a){return f.createElement(oT,oV({shapeType:"rectangle",propTransformer:oY,activeClassName:"recharts-active-bar"},a))}var o$=function(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(c,d)=>{if(bf(a))return a;var e=bf(c)||null==c;return e?a(c,d):(e||function(a,b){if(!a)throw Error("Invariant failed")}(!1),b)}},o_=(a,b)=>{var c=b2();return(d,e)=>f=>{null==a||a(d,e,f),c(bH({activeIndex:String(e),activeDataKey:b,activeCoordinate:d.tooltipPosition}))}},o0=a=>{var b=b2();return(c,d)=>e=>{null==a||a(c,d,e),b(bI())}},o1=(a,b)=>{var c=b2();return(d,e)=>f=>{null==a||a(d,e,f),c(bK({activeIndex:String(e),activeDataKey:b,activeCoordinate:d.tooltipPosition}))}};function o2(a){var{fn:b,args:c}=a,d=b2(),e=cO();return(0,f.useEffect)(()=>{if(!e){var a=b(c);return d(bE(a)),()=>{d(bF(a))}}},[b,c,d,e]),null}var o3=null!=(b=f["useId".toString()])?b:()=>{var[a]=f.useState(()=>bi("uid-"));return a},o4=(0,f.createContext)(void 0),o5=a=>{var{id:b,type:c,children:d}=a,e=function(a,b){var c=o3();return b||(a?"".concat(a,"-").concat(c):c)}("recharts-".concat(c),b);return f.createElement(o4.Provider,{value:e},d(e))},o6=["children"],o7=(0,f.createContext)({data:[],xAxisId:"xAxis-0",yAxisId:"yAxis-0",dataPointFormatter:()=>({x:0,y:0,value:0}),errorBarOffset:0});function o8(a){var{children:b}=a,c=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,o6);return f.createElement(o7.Provider,{value:c},b)}function o9(a,b){var c,d,e=b6(b=>ik(b,a)),f=b6(a=>im(a,b)),g=null!=(c=null==e?void 0:e.allowDataOverflow)?c:ij.allowDataOverflow,h=null!=(d=null==f?void 0:f.allowDataOverflow)?d:il.allowDataOverflow;return{needClip:g||h,needClipX:g,needClipY:h}}function pa(a){var{xAxisId:b,yAxisId:c,clipPathId:d}=a,e=mK(),{needClipX:g,needClipY:h,needClip:i}=o9(b,c);if(!i)return null;var{x:j,y:k,width:l,height:m}=e;return f.createElement("clipPath",{id:"clipPath-".concat(d)},f.createElement("rect",{x:g?j:j-l/2,y:h?k:k-m/2,width:g?l:2*l,height:h?m:2*m}))}function pb(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function pc(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?pb(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):pb(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var pd=ak([iu,(a,b,c,d,e)=>e],(a,b)=>a.filter(a=>"bar"===a.type).find(a=>a.id===b)),pe=ak([pd],a=>null==a?void 0:a.maxBarSize),pf=(a,b,c)=>{var d=null!=c?c:a;if(null!=d)return bj(d,b,0)},pg=ak([cW,iu,(a,b)=>b,(a,b,c)=>c,(a,b,c,d)=>d],(a,b,c,d,e)=>b.filter(b=>"horizontal"===a?b.xAxisId===c:b.yAxisId===d).filter(a=>a.isPanorama===e).filter(a=>!1===a.hide).filter(a=>"bar"===a.type)),ph=ak([pg,a=>a.rootProps.barSize,(a,b,c)=>"horizontal"===cW(a)?jE(a,"xAxis",b):jE(a,"yAxis",c)],(a,b,c)=>{var d=a.filter(ie),e=a.filter(a=>null==a.stackId);return[...Object.entries(d.reduce((a,b)=>(a[b.stackId]||(a[b.stackId]=[]),a[b.stackId].push(b),a),{})).map(a=>{var[d,e]=a;return{stackId:d,dataKeys:e.map(a=>a.dataKey),barSize:pf(b,c,e[0].barSize)}}),...e.map(a=>({stackId:void 0,dataKeys:[a.dataKey].filter(a=>null!=a),barSize:pf(b,c,a.barSize)}))]}),pi=(a,b,c,d)=>{var e,f;return"horizontal"===cW(a)?(e=jM(a,"xAxis",b,d),f=jL(a,"xAxis",b,d)):(e=jM(a,"yAxis",c,d),f=jL(a,"yAxis",c,d)),cx(e,f)},pj=ak([ph,hM,a=>a.rootProps.barGap,hN,(a,b,c,d,e)=>{var f,g,h,i,j=pd(a,b,c,d,e);if(null!=j){var k=cW(a),l=hM(a),{maxBarSize:m}=j,n=null==m?l:m;return"horizontal"===k?(h=jM(a,"xAxis",b,d),i=jL(a,"xAxis",b,d)):(h=jM(a,"yAxis",c,d),i=jL(a,"yAxis",c,d)),null!=(f=null!=(g=cx(h,i,!0))?g:n)?f:0}},pi,pe],(a,b,c,d,e,f,g)=>{var h=function(a,b,c,d,e){var f,g=d.length;if(!(g<1)){var h=bj(a,c,0,!0),i=[];if(gU(d[0].barSize)){var j=!1,k=c/g,l=d.reduce((a,b)=>a+(b.barSize||0),0);(l+=(g-1)*h)>=c&&(l-=(g-1)*h,h=0),l>=c&&k>0&&(j=!0,k*=.9,l=g*k);var m={offset:((c-l)/2|0)-h,size:0};f=d.reduce((a,b)=>{var c,d=[...a,{stackId:b.stackId,dataKeys:b.dataKeys,position:{offset:m.offset+m.size+h,size:j?k:null!=(c=b.barSize)?c:0}}];return m=d[d.length-1].position,d},i)}else{var n=bj(b,c,0,!0);c-2*n-(g-1)*h<=0&&(h=0);var o=(c-2*n-(g-1)*h)/g;o>1&&(o>>=0);var p=gU(e)?Math.min(o,e):o;f=d.reduce((a,b,c)=>[...a,{stackId:b.stackId,dataKeys:b.dataKeys,position:{offset:n+(o+h)*c+(o-p)/2,size:p}}],i)}return f}}(c,d,e!==f?e:f,a,null==g?b:g);return e!==f&&null!=h&&(h=h.map(a=>pc(pc({},a),{},{position:pc(pc({},a.position),{},{offset:a.position.offset-e/2})}))),h}),pk=ak([pj,pd],(a,b)=>{if(null!=a&&null!=b){var c=a.find(a=>a.stackId===b.stackId&&null!=b.dataKey&&a.dataKeys.includes(b.dataKey));if(null!=c)return c.position}}),pl=ak([(a,b,c,d)=>"horizontal"===cW(a)?iL(a,"yAxis",c,d):iL(a,"xAxis",b,d),pd],(a,b)=>{var c=h9(b);if(!a||null==c||null==b)return;var{stackId:d}=b;if(null!=d){var e=a[d];if(e){var{stackedData:f}=e;if(f)return f.find(a=>a.key===c)}}}),pm=ak([cK,(a,b,c,d)=>jM(a,"xAxis",b,d),(a,b,c,d)=>jM(a,"yAxis",c,d),(a,b,c,d)=>jL(a,"xAxis",b,d),(a,b,c,d)=>jL(a,"yAxis",c,d),pk,cW,gT,pi,pl,pd,(a,b,c,d,e,f)=>f],(a,b,c,d,e,f,g,h,i,j,k,l)=>{var m,{chartData:n,dataStartIndex:o,dataEndIndex:p}=h;if(null!=k&&null!=f&&("horizontal"===g||"vertical"===g)&&null!=b&&null!=c&&null!=d&&null!=e&&null!=i){var{data:q}=k;if(null!=(m=null!=q&&q.length>0?q:null==n?void 0:n.slice(o,p+1)))return function(a){var{layout:b,barSettings:{dataKey:c,minPointSize:d},pos:e,bandSize:f,xAxis:g,yAxis:h,xAxisTicks:i,yAxisTicks:j,stackedData:k,displayedData:l,offset:m,cells:n}=a,o="horizontal"===b?h:g,p=k?o.scale.domain():null,q=(a=>{var{numericAxis:b}=a,c=b.scale.domain();if("number"===b.type){var d=Math.min(c[0],c[1]),e=Math.max(c[0],c[1]);return d<=0&&e>=0?0:e<0?e:d}return c[0]})({numericAxis:o});return l.map((a,l)=>{k?r=((a,b)=>{if(!b||2!==b.length||!bf(b[0])||!bf(b[1]))return a;var c=Math.min(b[0],b[1]),d=Math.max(b[0],b[1]),e=[a[0],a[1]];return(!bf(a[0])||a[0]<c)&&(e[0]=c),(!bf(a[1])||a[1]>d)&&(e[1]=d),e[0]>d&&(e[0]=d),e[1]<c&&(e[1]=c),e})(k[l],p):Array.isArray(r=cn(a,c))||(r=[q,r]);var o=o$(d,0)(r[1],l);if("horizontal"===b){var r,s,t,u,v,w,x,[y,z]=[h.scale(r[0]),h.scale(r[1])];s=cu({axis:g,ticks:i,bandSize:f,offset:e.offset,entry:a,index:l}),t=null!=(x=null!=z?z:y)?x:void 0,u=e.size;var A=y-z;if(v=bd(A)?0:A,w={x:s,y:m.top,width:u,height:m.height},Math.abs(o)>0&&Math.abs(v)<Math.abs(o)){var B=bc(v||o)*(Math.abs(o)-Math.abs(v));t-=B,v+=B}}else{var[C,D]=[g.scale(r[0]),g.scale(r[1])];if(s=C,t=cu({axis:h,ticks:j,bandSize:f,offset:e.offset,entry:a,index:l}),u=D-C,v=e.size,w={x:m.left,y:t,width:m.width,height:v},Math.abs(o)>0&&Math.abs(u)<Math.abs(o)){var E=bc(u||o)*(Math.abs(o)-Math.abs(u));u+=E}}return null==s||null==t||null==u||null==v?null:pE(pE({},a),{},{x:s,y:t,width:u,height:v,value:k?r:r[1],payload:a,background:w,tooltipPosition:{x:s+u/2,y:t+v/2}},n&&n[l]&&n[l].props)}).filter(Boolean)}({layout:g,barSettings:k,pos:f,bandSize:i,xAxis:b,yAxis:c,xAxisTicks:d,yAxisTicks:e,stackedData:j,displayedData:m,offset:a,cells:l})}}),pn=()=>{};function po(a){var{legendPayload:b}=a,c=b2(),d=cO();return(0,f.useEffect)(()=>d?pn:(c(lB(b)),()=>{c(lC(b))}),[c,d,b]),null}function pp(a){var{legendPayload:b}=a,c=b2(),d=b6(cW);return(0,f.useEffect)(()=>"centric"!==d&&"radial"!==d?pn:(c(lB(b)),()=>{c(lC(b))}),[c,d,b]),null}function pq(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"animation-",c=(0,f.useRef)(bi(b)),d=(0,f.useRef)(a);return d.current!==a&&(c.current=bi(b),d.current=a),c.current}function pr(a){var b=b2(),c=(0,f.useRef)(null);return(0,f.useEffect)(()=>{null===c.current?b(lg(a)):c.current!==a&&b(lh({prev:c.current,next:a})),c.current=a},[b,a]),(0,f.useEffect)(()=>()=>{c.current&&(b(li(c.current)),c.current=null)},[b]),null}function ps(a){var b=b2();return(0,f.useEffect)(()=>(b(lj(a)),()=>{b(lk(a))}),[b,a]),null}function pt(){}var pu={begin:0,duration:1e3,easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}},pv={t:0},pw={t:1};function px(a){var b=mQ(a,pu),{isActive:c,canBegin:d,duration:e,easing:g,begin:h,onAnimationEnd:i,onAnimationStart:j,children:k}=b,l=n1("JavascriptAnimate",b.animationManager),[m,n]=(0,f.useState)(c?pv:pw),o=(0,f.useRef)(null);return(0,f.useEffect)(()=>{c||n(pw)},[c]),(0,f.useEffect)(()=>{if(!c||!d)return pt;var a=n$(pv,pw,nR(g),e,n,l.getTimeoutController());return l.start([j,h,()=>{o.current=a()},e,i]),()=>{l.stop(),o.current&&o.current(),i()}},[c,d,e,g,h,j,i,l]),k(m.t)}var py=["onMouseEnter","onMouseLeave","onClick"],pz=["value","background","tooltipPosition"],pA=["id"],pB=["onMouseEnter","onClick","onMouseLeave"];function pC(){return(pC=Object.assign.bind()).apply(null,arguments)}function pD(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function pE(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?pD(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):pD(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function pF(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}function pG(a){var{dataKey:b,stroke:c,strokeWidth:d,fill:e,name:f,hide:g,unit:h}=a;return{dataDefinedOnItem:void 0,positions:void 0,settings:{stroke:c,strokeWidth:d,fill:e,dataKey:b,nameKey:void 0,name:cz(f,b),hide:g,type:a.tooltipType,color:a.fill,unit:h}}}function pH(a){var b=b6(kD),{data:c,dataKey:d,background:e,allOtherBarProps:f}=a,{onMouseEnter:g,onMouseLeave:h,onClick:i}=f,j=pF(f,py),k=o_(g,d),l=o0(h),m=o1(i,d);if(!e||null==c)return null;var n=mp(e,!1);return mX.createElement(mX.Fragment,null,c.map((a,c)=>{var{value:f,background:g,tooltipPosition:h}=a,i=pF(a,pz);if(!g)return null;var o=k(a,c),p=l(a,c),q=m(a,c),r=pE(pE(pE(pE(pE({option:e,isActive:String(c)===b},i),{},{fill:"#eee"},g),n),mf(j,a,c)),{},{onMouseEnter:o,onMouseLeave:p,onClick:q,dataKey:d,index:c,className:"recharts-bar-background-rectangle"});return mX.createElement(oZ,pC({key:"background-bar-".concat(c)},r))}))}function pI(a){var{data:b,props:c,showLabels:d}=a,e=mi(c),{id:f}=e,g=pF(e,pA),{shape:h,dataKey:i,activeBar:j}=c,k=b6(kD),l=b6(kF),{onMouseEnter:m,onClick:n,onMouseLeave:o}=c,p=pF(c,pB),q=o_(m,i),r=o0(o),s=o1(n,i);return b?mX.createElement(mX.Fragment,null,b.map((a,b)=>{var c=j&&String(b)===k&&(null==l||i===l),d=pE(pE(pE({},g),a),{},{isActive:c,option:c?j:h,index:b,dataKey:i});return mX.createElement(m$,pC({className:"recharts-bar-rectangle"},mf(p,a,b),{onMouseEnter:q(a,b),onMouseLeave:r(a,b),onClick:s(a,b),key:"rectangle-".concat(null==a?void 0:a.x,"-").concat(null==a?void 0:a.y,"-").concat(null==a?void 0:a.value,"-").concat(b)}),mX.createElement(oZ,d))}),d&&nI.renderCallByParent(c,b)):null}function pJ(a){var{props:b,previousRectanglesRef:c}=a,{data:d,layout:e,isAnimationActive:f,animationBegin:g,animationDuration:h,animationEasing:i,onAnimationEnd:j,onAnimationStart:k}=b,l=c.current,m=pq(b,"recharts-bar-"),[n,o]=(0,mX.useState)(!1),p=(0,mX.useCallback)(()=>{"function"==typeof j&&j(),o(!1)},[j]),q=(0,mX.useCallback)(()=>{"function"==typeof k&&k(),o(!0)},[k]);return mX.createElement(px,{begin:g,duration:h,isActive:f,easing:i,onAnimationEnd:p,onAnimationStart:q,key:m},a=>{var f=1===a?d:null==d?void 0:d.map((b,c)=>{var d=l&&l[c];if(d)return pE(pE({},b),{},{x:bm(d.x,b.x,a),y:bm(d.y,b.y,a),width:bm(d.width,b.width,a),height:bm(d.height,b.height,a)});if("horizontal"===e){var f=bm(0,b.height,a);return pE(pE({},b),{},{y:b.y+b.height-f,height:f})}var g=bm(0,b.width,a);return pE(pE({},b),{},{width:g})});return(a>0&&(c.current=null!=f?f:null),null==f)?null:mX.createElement(m$,null,mX.createElement(pI,{props:b,data:f,showLabels:!n}))})}function pK(a){var{data:b,isAnimationActive:c}=a,d=(0,mX.useRef)(null);return c&&b&&b.length&&(null==d.current||d.current!==b)?mX.createElement(pJ,{previousRectanglesRef:d,props:a}):mX.createElement(pI,{props:a,data:b,showLabels:!0})}var pL=(a,b)=>{var c=Array.isArray(a.value)?a.value[1]:a.value;return{x:a.x,y:a.y,value:c,errorVal:cn(a,b)}};class pM extends mX.PureComponent{render(){var{hide:a,data:b,dataKey:c,className:d,xAxisId:e,yAxisId:f,needClip:g,background:h,id:i}=this.props;if(a)return null;var j=(0,l8.clsx)("recharts-bar",d);return mX.createElement(m$,{className:j,id:i},g&&mX.createElement("defs",null,mX.createElement(pa,{clipPathId:i,xAxisId:e,yAxisId:f})),mX.createElement(m$,{className:"recharts-bar-rectangles",clipPath:g?"url(#clipPath-".concat(i,")"):void 0},mX.createElement(pH,{data:b,dataKey:c,background:h,allOtherBarProps:this.props}),mX.createElement(pK,this.props)),this.props.children)}}var pN={activeBar:!1,animationBegin:0,animationDuration:400,animationEasing:"ease",hide:!1,isAnimationActive:!m1.isSsr,legendType:"rect",minPointSize:0,xAxisId:0,yAxisId:0};function pO(a){var b,{xAxisId:c,yAxisId:d,hide:e,legendType:f,minPointSize:g,activeBar:h,animationBegin:i,animationDuration:j,animationEasing:k,isAnimationActive:l}=a,{needClip:m}=o9(c,d),n=cX(),o=cO(),p=mn(a.children,m_),q=b6(b=>pm(b,c,d,o,a.id,p));if("vertical"!==n&&"horizontal"!==n)return null;var r=null==q?void 0:q[0];return b=null==r||null==r.height||null==r.width?0:"vertical"===n?r.height/2:r.width/2,mX.createElement(o8,{xAxisId:c,yAxisId:d,data:q,dataPointFormatter:pL,errorBarOffset:b},mX.createElement(pM,pC({},a,{layout:n,needClip:m,data:q,xAxisId:c,yAxisId:d,hide:e,legendType:f,minPointSize:g,activeBar:h,animationBegin:i,animationDuration:j,animationEasing:k,isAnimationActive:l})))}function pP(a){var b=mQ(a,pN),c=cO();return mX.createElement(o5,{id:b.id,type:"bar"},a=>mX.createElement(mX.Fragment,null,mX.createElement(po,{legendPayload:(a=>{var{dataKey:b,name:c,fill:d,legendType:e,hide:f}=a;return[{inactive:f,dataKey:b,type:e,color:d,value:cz(c,b),payload:a}]})(b)}),mX.createElement(o2,{fn:pG,args:b}),mX.createElement(pr,{type:"bar",id:a,data:void 0,xAxisId:b.xAxisId,yAxisId:b.yAxisId,zAxisId:0,dataKey:b.dataKey,stackId:cs(b.stackId),hide:b.hide,barSize:b.barSize,minPointSize:b.minPointSize,maxBarSize:b.maxBarSize,isPanorama:c}),mX.createElement(pO,pC({},b,{id:a}))))}pP.displayName="Bar";var pQ=f,pR=f;function pS(a,b){for(var c in a)if(({}).hasOwnProperty.call(a,c)&&(!({}).hasOwnProperty.call(b,c)||a[c]!==b[c]))return!1;for(var d in b)if(({}).hasOwnProperty.call(b,d)&&!({}).hasOwnProperty.call(a,d))return!1;return!0}class pT{static create(a){return new pT(a)}constructor(a){this.scale=a}get domain(){return this.scale.domain}get range(){return this.scale.range}get rangeMin(){return this.range()[0]}get rangeMax(){return this.range()[1]}get bandwidth(){return this.scale.bandwidth}apply(a){var{bandAware:b,position:c}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(void 0!==a){if(c)switch(c){case"start":default:return this.scale(a);case"middle":var d=this.bandwidth?this.bandwidth()/2:0;return this.scale(a)+d;case"end":var e=this.bandwidth?this.bandwidth():0;return this.scale(a)+e}if(b){var f=this.bandwidth?this.bandwidth()/2:0;return this.scale(a)+f}return this.scale(a)}}isInRange(a){var b=this.range(),c=b[0],d=b[b.length-1];return c<=d?a>=c&&a<=d:a>=d&&a<=c}}!function(a,b,c){var d;(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:1e-4,enumerable:!0,configurable:!0,writable:!0}):a[b]=1e-4}(pT,"EPS",1e-4);var pU=function(a){var{width:b,height:c}=a,d=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,e=(d%180+180)%180*Math.PI/180,f=Math.atan(c/b);return Math.abs(e>f&&e<Math.PI-f?c/Math.sin(e):b/Math.cos(e))};function pV(a,b,c){if(b<1)return[];if(1===b&&void 0===c)return a;for(var d=[],e=0;e<a.length;e+=b)if(void 0!==c&&!0!==c(a[e]))return;else d.push(a[e]);return d}function pW(a,b,c,d,e){if(a*b<a*d||a*b>a*e)return!1;var f=c();return a*(b-a*f/2-d)>=0&&a*(b+a*f/2-e)<=0}function pX(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function pY(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?pX(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):pX(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function pZ(a,b,c){var d,{tick:e,ticks:f,viewBox:g,minTickGap:h,orientation:i,interval:j,tickFormatter:k,unit:l,angle:m}=a;if(!f||!f.length||!e)return[];if(bf(j)||m1.isSsr)return null!=(d=pV(f,(bf(j)?j:0)+1))?d:[];var n="top"===i||"bottom"===i?"width":"height",o=l&&"width"===n?m8(l,{fontSize:b,letterSpacing:c}):{width:0,height:0},p=(a,d)=>{var e,f="function"==typeof k?k(a.value,d):a.value;return"width"===n?(e=m8(f,{fontSize:b,letterSpacing:c}),pU({width:e.width+o.width,height:e.height+o.height},m)):m8(f,{fontSize:b,letterSpacing:c})[n]},q=f.length>=2?bc(f[1].coordinate-f[0].coordinate):1,r=function(a,b,c){var d="width"===c,{x:e,y:f,width:g,height:h}=a;return 1===b?{start:d?e:f,end:d?e+g:f+h}:{start:d?e+g:f+h,end:d?e:f}}(g,q,n);return"equidistantPreserveStart"===j?function(a,b,c,d,e){for(var f,g=(d||[]).slice(),{start:h,end:i}=b,j=0,k=1,l=h;k<=g.length;)if(f=function(){var b,f=null==d?void 0:d[j];if(void 0===f)return{v:pV(d,k)};var g=j,m=()=>(void 0===b&&(b=c(f,g)),b),n=f.coordinate,o=0===j||pW(a,n,m,l,i);o||(j=0,l=h,k+=1),o&&(l=n+a*(m()/2+e),j+=k)}())return f.v;return[]}(q,r,p,f,h):("preserveStart"===j||"preserveStartEnd"===j?function(a,b,c,d,e,f){var g=(d||[]).slice(),h=g.length,{start:i,end:j}=b;if(f){var k=d[h-1],l=c(k,h-1),m=a*(k.coordinate+a*l/2-j);g[h-1]=k=pY(pY({},k),{},{tickCoord:m>0?k.coordinate-m*a:k.coordinate}),pW(a,k.tickCoord,()=>l,i,j)&&(j=k.tickCoord-a*(l/2+e),g[h-1]=pY(pY({},k),{},{isShow:!0}))}for(var n=f?h-1:h,o=function(b){var d,f=g[b],h=()=>(void 0===d&&(d=c(f,b)),d);if(0===b){var k=a*(f.coordinate-a*h()/2-i);g[b]=f=pY(pY({},f),{},{tickCoord:k<0?f.coordinate-k*a:f.coordinate})}else g[b]=f=pY(pY({},f),{},{tickCoord:f.coordinate});pW(a,f.tickCoord,h,i,j)&&(i=f.tickCoord+a*(h()/2+e),g[b]=pY(pY({},f),{},{isShow:!0}))},p=0;p<n;p++)o(p);return g}(q,r,p,f,h,"preserveStartEnd"===j):function(a,b,c,d,e){for(var f=(d||[]).slice(),g=f.length,{start:h}=b,{end:i}=b,j=function(b){var d,j=f[b],k=()=>(void 0===d&&(d=c(j,b)),d);if(b===g-1){var l=a*(j.coordinate+a*k()/2-i);f[b]=j=pY(pY({},j),{},{tickCoord:l>0?j.coordinate-l*a:j.coordinate})}else f[b]=j=pY(pY({},j),{},{tickCoord:j.coordinate});pW(a,j.tickCoord,k,h,i)&&(i=j.tickCoord-a*(k()/2+e),f[b]=pY(pY({},j),{},{isShow:!0}))},k=g-1;k>=0;k--)j(k);return f}(q,r,p,f,h)).filter(a=>a.isShow)}var p$=["viewBox"],p_=["viewBox"];function p0(){return(p0=Object.assign.bind()).apply(null,arguments)}function p1(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function p2(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?p1(Object(c),!0).forEach(function(b){p4(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):p1(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function p3(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}function p4(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}class p5 extends pR.Component{constructor(a){super(a),this.tickRefs=pR.createRef(),this.tickRefs.current=[],this.state={fontSize:"",letterSpacing:""}}shouldComponentUpdate(a,b){var{viewBox:c}=a,d=p3(a,p$),e=this.props,{viewBox:f}=e,g=p3(e,p_);return!pS(c,f)||!pS(d,g)||!pS(b,this.state)}getTickLineCoord(a){var b,c,d,e,f,g,{x:h,y:i,width:j,height:k,orientation:l,tickSize:m,mirror:n,tickMargin:o}=this.props,p=n?-1:1,q=a.tickSize||m,r=bf(a.tickCoord)?a.tickCoord:a.coordinate;switch(l){case"top":b=c=a.coordinate,g=(d=(e=i+!n*k)-p*q)-p*o,f=r;break;case"left":d=e=a.coordinate,f=(b=(c=h+!n*j)-p*q)-p*o,g=r;break;case"right":d=e=a.coordinate,f=(b=(c=h+n*j)+p*q)+p*o,g=r;break;default:b=c=a.coordinate,g=(d=(e=i+n*k)+p*q)+p*o,f=r}return{line:{x1:b,y1:d,x2:c,y2:e},tick:{x:f,y:g}}}getTickTextAnchor(){var a,{orientation:b,mirror:c}=this.props;switch(b){case"left":a=c?"start":"end";break;case"right":a=c?"end":"start";break;default:a="middle"}return a}getTickVerticalAnchor(){var{orientation:a,mirror:b}=this.props;switch(a){case"left":case"right":return"middle";case"top":return b?"start":"end";default:return b?"end":"start"}}renderAxisLine(){var{x:a,y:b,width:c,height:d,orientation:e,mirror:f,axisLine:g}=this.props,h=p2(p2(p2({},mp(this.props,!1)),mp(g,!1)),{},{fill:"none"});if("top"===e||"bottom"===e){var i=+("top"===e&&!f||"bottom"===e&&f);h=p2(p2({},h),{},{x1:a,y1:b+i*d,x2:a+c,y2:b+i*d})}else{var j=+("left"===e&&!f||"right"===e&&f);h=p2(p2({},h),{},{x1:a+j*c,y1:b,x2:a+j*c,y2:b+d})}return pR.createElement("line",p0({},h,{className:(0,l8.clsx)("recharts-cartesian-axis-line",(0,bb.default)(g,"className"))}))}static renderTickItem(a,b,c){var d,e=(0,l8.clsx)(b.className,"recharts-cartesian-axis-tick-value");if(pR.isValidElement(a))d=pR.cloneElement(a,p2(p2({},b),{},{className:e}));else if("function"==typeof a)d=a(p2(p2({},b),{},{className:e}));else{var f="recharts-cartesian-axis-tick-value";"boolean"!=typeof a&&(f=(0,l8.clsx)(f,a.className)),d=pR.createElement(nr,p0({},b,{className:f}),c)}return d}renderTicks(a,b){var c=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],{tickLine:d,stroke:e,tick:f,tickFormatter:g,unit:h,padding:i}=this.props,j=pZ(p2(p2({},this.props),{},{ticks:c}),a,b),k=this.getTickTextAnchor(),l=this.getTickVerticalAnchor(),m=mi(this.props),n=mp(f,!1),o=p2(p2({},m),{},{fill:"none"},mp(d,!1)),p=j.map((a,b)=>{var{line:c,tick:p}=this.getTickLineCoord(a),q=p2(p2(p2(p2({textAnchor:k,verticalAnchor:l},m),{},{stroke:"none",fill:e},n),p),{},{index:b,payload:a,visibleTicksCount:j.length,tickFormatter:g,padding:i});return pR.createElement(m$,p0({className:"recharts-cartesian-axis-tick",key:"tick-".concat(a.value,"-").concat(a.coordinate,"-").concat(a.tickCoord)},mf(this.props,a,b)),d&&pR.createElement("line",p0({},o,c,{className:(0,l8.clsx)("recharts-cartesian-axis-tick-line",(0,bb.default)(d,"className"))})),f&&p5.renderTickItem(f,q,"".concat("function"==typeof g?g(a.value,b):a.value).concat(h||"")))});return p.length>0?pR.createElement("g",{className:"recharts-cartesian-axis-ticks"},p):null}render(){var{axisLine:a,width:b,height:c,className:d,hide:e}=this.props;if(e)return null;var{ticks:f}=this.props;return null!=b&&b<=0||null!=c&&c<=0?null:pR.createElement(m$,{className:(0,l8.clsx)("recharts-cartesian-axis",d),ref:a=>{if(a){var b=a.getElementsByClassName("recharts-cartesian-axis-tick-value");this.tickRefs.current=Array.from(b);var c=b[0];if(c){var d=window.getComputedStyle(c).fontSize,e=window.getComputedStyle(c).letterSpacing;(d!==this.state.fontSize||e!==this.state.letterSpacing)&&this.setState({fontSize:window.getComputedStyle(c).fontSize,letterSpacing:window.getComputedStyle(c).letterSpacing})}}}},a&&this.renderAxisLine(),this.renderTicks(this.state.fontSize,this.state.letterSpacing,f),nz.renderCallByParent(this.props))}}p4(p5,"displayName","CartesianAxis"),p4(p5,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var p6=["children"],p7=["dangerouslySetInnerHTML","ticks"];function p8(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function p9(){return(p9=Object.assign.bind()).apply(null,arguments)}function qa(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}function qb(a){var b=b2(),c=(0,pQ.useMemo)(()=>{var{children:b}=a;return qa(a,p6)},[a]),d=b6(a=>ik(a,c.id)),e=c===d;return((0,pQ.useEffect)(()=>(b(k7(c)),()=>{b(k8(c))}),[c,b]),e)?a.children:null}var qc=a=>{var{xAxisId:b,className:c}=a,d=b6(cM),e=cO(),f="xAxis",g=b6(a=>jt(a,f,b,e)),h=b6(a=>jK(a,f,b,e)),i=b6(a=>jA(a,b)),j=b6(a=>((a,b)=>{var c=cK(a),d=ik(a,b);if(null!=d){var e=jB(a,d.orientation,d.mirror)[b];return null==e?{x:c.left,y:0}:{x:c.left,y:e}}})(a,b));if(null==i||null==j)return null;var{dangerouslySetInnerHTML:k,ticks:l}=a,m=qa(a,p7);return pQ.createElement(p5,p9({},m,{scale:g,x:j.x,y:j.y,width:i.width,height:i.height,className:(0,l8.clsx)("recharts-".concat(f," ").concat(f),c),viewBox:d,ticks:h}))},qd=a=>{var b,c,d,e,f;return pQ.createElement(qb,{interval:null!=(b=a.interval)?b:"preserveEnd",id:a.xAxisId,scale:a.scale,type:a.type,padding:a.padding,allowDataOverflow:a.allowDataOverflow,domain:a.domain,dataKey:a.dataKey,allowDuplicatedCategory:a.allowDuplicatedCategory,allowDecimals:a.allowDecimals,tickCount:a.tickCount,includeHidden:null!=(c=a.includeHidden)&&c,reversed:a.reversed,ticks:a.ticks,height:a.height,orientation:a.orientation,mirror:a.mirror,hide:a.hide,unit:a.unit,name:a.name,angle:null!=(d=a.angle)?d:0,minTickGap:null!=(e=a.minTickGap)?e:5,tick:null==(f=a.tick)||f,tickFormatter:a.tickFormatter},pQ.createElement(qc,a))};class qe extends pQ.Component{render(){return pQ.createElement(qd,this.props)}}p8(qe,"displayName","XAxis"),p8(qe,"defaultProps",{allowDataOverflow:ij.allowDataOverflow,allowDecimals:ij.allowDecimals,allowDuplicatedCategory:ij.allowDuplicatedCategory,height:ij.height,hide:!1,mirror:ij.mirror,orientation:ij.orientation,padding:ij.padding,reversed:ij.reversed,scale:ij.scale,tickCount:ij.tickCount,type:ij.type,xAxisId:0});var qf=f,qg=["dangerouslySetInnerHTML","ticks"];function qh(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function qi(){return(qi=Object.assign.bind()).apply(null,arguments)}function qj(a){var b=b2();return(0,qf.useEffect)(()=>(b(k9(a)),()=>{b(la(a))}),[a,b]),null}var qk=a=>{var b,{yAxisId:c,className:d,width:e,label:f}=a,g=(0,qf.useRef)(null),h=(0,qf.useRef)(null),i=b6(cM),j=cO(),k=b2(),l="yAxis",m=b6(a=>jt(a,l,c,j)),n=b6(a=>jD(a,c)),o=b6(a=>((a,b)=>{var c=cK(a),d=im(a,b);if(null!=d){var e=jC(a,d.orientation,d.mirror)[b];return null==e?{x:0,y:c.top}:{x:e,y:c.top}}})(a,c)),p=b6(a=>jK(a,l,c,j));if((0,qf.useLayoutEffect)(()=>{if(!("auto"!==e||!n||ny(f)||(0,qf.isValidElement)(f))){var a,b=g.current,d=null==b||null==(a=b.tickRefs)?void 0:a.current,{tickSize:i,tickMargin:j}=b.props,l=(a=>{var{ticks:b,label:c,labelGapWithTick:d=5,tickSize:e=0,tickMargin:f=0}=a,g=0;if(b){b.forEach(a=>{if(a){var b=a.getBoundingClientRect();b.width>g&&(g=b.width)}});var h=c?c.getBoundingClientRect().width:0;return Math.round(g+(e+f)+h+(c?d:0))}return 0})({ticks:d,label:h.current,labelGapWithTick:5,tickSize:i,tickMargin:j});Math.round(n.width)!==Math.round(l)&&k(ld({id:c,width:l}))}},[g,null==g||null==(b=g.current)||null==(b=b.tickRefs)?void 0:b.current,null==n?void 0:n.width,n,k,f,c,e]),null==n||null==o)return null;var{dangerouslySetInnerHTML:q,ticks:r}=a,s=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,qg);return qf.createElement(p5,qi({},s,{ref:g,labelRef:h,scale:m,x:o.x,y:o.y,width:n.width,height:n.height,className:(0,l8.clsx)("recharts-".concat(l," ").concat(l),d),viewBox:i,ticks:p}))},ql=a=>{var b,c,d,e,f;return qf.createElement(qf.Fragment,null,qf.createElement(qj,{interval:null!=(b=a.interval)?b:"preserveEnd",id:a.yAxisId,scale:a.scale,type:a.type,domain:a.domain,allowDataOverflow:a.allowDataOverflow,dataKey:a.dataKey,allowDuplicatedCategory:a.allowDuplicatedCategory,allowDecimals:a.allowDecimals,tickCount:a.tickCount,padding:a.padding,includeHidden:null!=(c=a.includeHidden)&&c,reversed:a.reversed,ticks:a.ticks,width:a.width,orientation:a.orientation,mirror:a.mirror,hide:a.hide,unit:a.unit,name:a.name,angle:null!=(d=a.angle)?d:0,minTickGap:null!=(e=a.minTickGap)?e:5,tick:null==(f=a.tick)||f,tickFormatter:a.tickFormatter}),qf.createElement(qk,a))},qm={allowDataOverflow:il.allowDataOverflow,allowDecimals:il.allowDecimals,allowDuplicatedCategory:il.allowDuplicatedCategory,hide:!1,mirror:il.mirror,orientation:il.orientation,padding:il.padding,reversed:il.reversed,scale:il.scale,tickCount:il.tickCount,type:il.type,width:il.width,yAxisId:0};class qn extends qf.Component{render(){return qf.createElement(ql,this.props)}}qh(qn,"displayName","YAxis"),qh(qn,"defaultProps",qm);var qo=function(a,b){for(var c=arguments.length,d=Array(c>2?c-2:0),e=2;e<c;e++)d[e-2]=arguments[e]},qp=["x1","y1","x2","y2","key"],qq=["offset"],qr=["xAxisId","yAxisId"],qs=["xAxisId","yAxisId"];function qt(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function qu(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?qt(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):qt(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function qv(){return(qv=Object.assign.bind()).apply(null,arguments)}function qw(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}var qx=a=>{var{fill:b}=a;if(!b||"none"===b)return null;var{fillOpacity:c,x:d,y:e,width:g,height:h,ry:i}=a;return f.createElement("rect",{x:d,y:e,ry:i,width:g,height:h,stroke:"none",fill:b,fillOpacity:c,className:"recharts-cartesian-grid-bg"})};function qy(a,b){var c;if(f.isValidElement(a))c=f.cloneElement(a,b);else if("function"==typeof a)c=a(b);else{var{x1:d,y1:e,x2:g,y2:h,key:i}=b,j=mi(qw(b,qp)),{offset:k}=j,l=qw(j,qq);c=f.createElement("line",qv({},l,{x1:d,y1:e,x2:g,y2:h,fill:"none",key:i}))}return c}function qz(a){var{x:b,width:c,horizontal:d=!0,horizontalPoints:e}=a;if(!d||!e||!e.length)return null;var{xAxisId:g,yAxisId:h}=a,i=qw(a,qr),j=e.map((a,e)=>qy(d,qu(qu({},i),{},{x1:b,y1:a,x2:b+c,y2:a,key:"line-".concat(e),index:e})));return f.createElement("g",{className:"recharts-cartesian-grid-horizontal"},j)}function qA(a){var{y:b,height:c,vertical:d=!0,verticalPoints:e}=a;if(!d||!e||!e.length)return null;var{xAxisId:g,yAxisId:h}=a,i=qw(a,qs),j=e.map((a,e)=>qy(d,qu(qu({},i),{},{x1:a,y1:b,x2:a,y2:b+c,key:"line-".concat(e),index:e})));return f.createElement("g",{className:"recharts-cartesian-grid-vertical"},j)}function qB(a){var{horizontalFill:b,fillOpacity:c,x:d,y:e,width:g,height:h,horizontalPoints:i,horizontal:j=!0}=a;if(!j||!b||!b.length)return null;var k=i.map(a=>Math.round(a+e-e)).sort((a,b)=>a-b);e!==k[0]&&k.unshift(0);var l=k.map((a,i)=>{var j=k[i+1]?k[i+1]-a:e+h-a;if(j<=0)return null;var l=i%b.length;return f.createElement("rect",{key:"react-".concat(i),y:a,x:d,height:j,width:g,stroke:"none",fill:b[l],fillOpacity:c,className:"recharts-cartesian-grid-bg"})});return f.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},l)}function qC(a){var{vertical:b=!0,verticalFill:c,fillOpacity:d,x:e,y:g,width:h,height:i,verticalPoints:j}=a;if(!b||!c||!c.length)return null;var k=j.map(a=>Math.round(a+e-e)).sort((a,b)=>a-b);e!==k[0]&&k.unshift(0);var l=k.map((a,b)=>{var j=k[b+1]?k[b+1]-a:e+h-a;if(j<=0)return null;var l=b%c.length;return f.createElement("rect",{key:"react-".concat(b),x:a,y:g,width:j,height:i,stroke:"none",fill:c[l],fillOpacity:d,className:"recharts-cartesian-grid-bg"})});return f.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},l)}var qD=(a,b)=>{var{xAxis:c,width:d,height:e,offset:f}=a;return cp(pZ(qu(qu(qu({},p5.defaultProps),c),{},{ticks:cq(c,!0),viewBox:{x:0,y:0,width:d,height:e}})),f.left,f.left+f.width,b)},qE=(a,b)=>{var{yAxis:c,width:d,height:e,offset:f}=a;return cp(pZ(qu(qu(qu({},p5.defaultProps),c),{},{ticks:cq(c,!0),viewBox:{x:0,y:0,width:d,height:e}})),f.top,f.top+f.height,b)},qF={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[],xAxisId:0,yAxisId:0};function qG(a){var b=cU(),c=cV(),d=cT(),e=qu(qu({},mQ(a,qF)),{},{x:bf(a.x)?a.x:d.left,y:bf(a.y)?a.y:d.top,width:bf(a.width)?a.width:d.width,height:bf(a.height)?a.height:d.height}),{xAxisId:g,yAxisId:h,x:i,y:j,width:k,height:l,syncWithTicks:m,horizontalValues:n,verticalValues:o}=e,p=cO(),q=b6(a=>jJ(a,"xAxis",g,p)),r=b6(a=>jJ(a,"yAxis",h,p));if(!bf(k)||k<=0||!bf(l)||l<=0||!bf(i)||i!==+i||!bf(j)||j!==+j)return null;var s=e.verticalCoordinatesGenerator||qD,t=e.horizontalCoordinatesGenerator||qE,{horizontalPoints:u,verticalPoints:v}=e;if((!u||!u.length)&&"function"==typeof t){var w=n&&n.length,x=t({yAxis:r?qu(qu({},r),{},{ticks:w?n:r.ticks}):void 0,width:b,height:c,offset:d},!!w||m);qo(Array.isArray(x),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(typeof x,"]")),Array.isArray(x)&&(u=x)}if((!v||!v.length)&&"function"==typeof s){var y=o&&o.length,z=s({xAxis:q?qu(qu({},q),{},{ticks:y?o:q.ticks}):void 0,width:b,height:c,offset:d},!!y||m);qo(Array.isArray(z),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(typeof z,"]")),Array.isArray(z)&&(v=z)}return f.createElement("g",{className:"recharts-cartesian-grid"},f.createElement(qx,{fill:e.fill,fillOpacity:e.fillOpacity,x:e.x,y:e.y,width:e.width,height:e.height,ry:e.ry}),f.createElement(qB,qv({},e,{horizontalPoints:u})),f.createElement(qC,qv({},e,{verticalPoints:v})),f.createElement(qz,qv({},e,{offset:d,horizontalPoints:u,xAxis:q,yAxis:r})),f.createElement(qA,qv({},e,{offset:d,verticalPoints:v,xAxis:q,yAxis:r})))}qG.displayName="CartesianGrid";var qH=a.i(35112);function qI(){return(qI=Object.assign.bind()).apply(null,arguments)}function qJ(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function qK(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?qJ(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):qJ(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function qL(a){return Array.isArray(a)&&bg(a[0])&&bg(a[1])?a.join(" ~ "):a}var qM=a=>{var{separator:b=" : ",contentStyle:c={},itemStyle:d={},labelStyle:e={},payload:g,formatter:h,itemSorter:i,wrapperClassName:j,labelClassName:k,label:l,labelFormatter:m,accessibilityLayer:n=!1}=a,o=qK({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},c),p=qK({margin:0},e),q=null!=l,r=q?l:"",s=(0,l8.clsx)("recharts-default-tooltip",j),t=(0,l8.clsx)("recharts-tooltip-label",k);return q&&m&&null!=g&&(r=m(l,g)),f.createElement("div",qI({className:s,style:o},n?{role:"status","aria-live":"assertive"}:{}),f.createElement("p",{className:t,style:p},f.isValidElement(r)?r:"".concat(r)),(()=>{if(g&&g.length){var a=(i?(0,b7.default)(g,i):g).map((a,c)=>{if("none"===a.type)return null;var e=a.formatter||h||qL,{value:i,name:j}=a,k=i,l=j;if(e){var m=e(i,j,a,c,g);if(Array.isArray(m))[k,l]=m;else{if(null==m)return null;k=m}}var n=qK({display:"block",paddingTop:4,paddingBottom:4,color:a.color||"#000"},d);return f.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(c),style:n},bg(l)?f.createElement("span",{className:"recharts-tooltip-item-name"},l):null,bg(l)?f.createElement("span",{className:"recharts-tooltip-item-separator"},b):null,f.createElement("span",{className:"recharts-tooltip-item-value"},k),f.createElement("span",{className:"recharts-tooltip-item-unit"},a.unit||""))});return f.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},a)}return null})())},qN=f,qO="recharts-tooltip-wrapper",qP={visibility:"hidden"};function qQ(a){var{allowEscapeViewBox:b,coordinate:c,key:d,offsetTopLeft:e,position:f,reverseDirection:g,tooltipDimension:h,viewBox:i,viewBoxDimension:j}=a;if(f&&bf(f[d]))return f[d];var k=c[d]-h-(e>0?e:0),l=c[d]+e;if(b[d])return g[d]?k:l;var m=i[d];return null==m?0:g[d]?k<m?Math.max(l,m):Math.max(k,m):null==j?0:l+h>m+j?Math.max(k,m):Math.max(l,m)}function qR(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function qS(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?qR(Object(c),!0).forEach(function(b){qT(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):qR(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function qT(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}class qU extends qN.PureComponent{constructor(){super(...arguments),qT(this,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0}}),qT(this,"handleKeyDown",a=>{if("Escape"===a.key){var b,c,d,e;this.setState({dismissed:!0,dismissedAtCoordinate:{x:null!=(b=null==(c=this.props.coordinate)?void 0:c.x)?b:0,y:null!=(d=null==(e=this.props.coordinate)?void 0:e.y)?d:0}})}})}componentDidMount(){document.addEventListener("keydown",this.handleKeyDown)}componentWillUnmount(){document.removeEventListener("keydown",this.handleKeyDown)}componentDidUpdate(){var a,b;this.state.dismissed&&((null==(a=this.props.coordinate)?void 0:a.x)!==this.state.dismissedAtCoordinate.x||(null==(b=this.props.coordinate)?void 0:b.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}render(){var{active:a,allowEscapeViewBox:b,animationDuration:c,animationEasing:d,children:e,coordinate:f,hasPayload:g,isAnimationActive:h,offset:i,position:j,reverseDirection:k,useTranslate3d:l,viewBox:m,wrapperStyle:n,lastBoundingBox:o,innerRef:p,hasPortalFromProps:q}=this.props,{cssClasses:r,cssProperties:s}=function(a){var b,c,d,{allowEscapeViewBox:e,coordinate:f,offsetTopLeft:g,position:h,reverseDirection:i,tooltipBox:j,useTranslate3d:k,viewBox:l}=a;return{cssProperties:b=j.height>0&&j.width>0&&f?function(a){var{translateX:b,translateY:c,useTranslate3d:d}=a;return{transform:d?"translate3d(".concat(b,"px, ").concat(c,"px, 0)"):"translate(".concat(b,"px, ").concat(c,"px)")}}({translateX:c=qQ({allowEscapeViewBox:e,coordinate:f,key:"x",offsetTopLeft:g,position:h,reverseDirection:i,tooltipDimension:j.width,viewBox:l,viewBoxDimension:l.width}),translateY:d=qQ({allowEscapeViewBox:e,coordinate:f,key:"y",offsetTopLeft:g,position:h,reverseDirection:i,tooltipDimension:j.height,viewBox:l,viewBoxDimension:l.height}),useTranslate3d:k}):qP,cssClasses:function(a){var{coordinate:b,translateX:c,translateY:d}=a;return(0,l8.clsx)(qO,{["".concat(qO,"-right")]:bf(c)&&b&&bf(b.x)&&c>=b.x,["".concat(qO,"-left")]:bf(c)&&b&&bf(b.x)&&c<b.x,["".concat(qO,"-bottom")]:bf(d)&&b&&bf(b.y)&&d>=b.y,["".concat(qO,"-top")]:bf(d)&&b&&bf(b.y)&&d<b.y})}({translateX:c,translateY:d,coordinate:f})}}({allowEscapeViewBox:b,coordinate:f,offsetTopLeft:i,position:j,reverseDirection:k,tooltipBox:{height:o.height,width:o.width},useTranslate3d:l,viewBox:m}),t=q?{}:qS(qS({transition:h&&a?"transform ".concat(c,"ms ").concat(d):void 0},s),{},{pointerEvents:"none",visibility:!this.state.dismissed&&a&&g?"visible":"hidden",position:"absolute",top:0,left:0}),u=qS(qS({},t),{},{visibility:!this.state.dismissed&&a&&g?"visible":"hidden"},n);return qN.createElement("div",{xmlns:"http://www.w3.org/1999/xhtml",tabIndex:-1,className:r,style:u,ref:p},e)}}var qV=a.i(53451);function qW(a){this._context=a}function qX(a){return new qW(a)}function qY(a){return a[0]}function qZ(a){return a[1]}function q$(a,b){var c=ca(!0),d=null,e=qX,f=null,g=or(h);function h(h){var i,j,k,l=(h=b9(h)).length,m=!1;for(null==d&&(f=e(k=g())),i=0;i<=l;++i)!(i<l&&c(j=h[i],i,h))===m&&((m=!m)?f.lineStart():f.lineEnd()),m&&f.point(+a(j,i,h),+b(j,i,h));if(k)return f=null,k+""||null}return a="function"==typeof a?a:void 0===a?qY:ca(a),b="function"==typeof b?b:void 0===b?qZ:ca(b),h.x=function(b){return arguments.length?(a="function"==typeof b?b:ca(+b),h):a},h.y=function(a){return arguments.length?(b="function"==typeof a?a:ca(+a),h):b},h.defined=function(a){return arguments.length?(c="function"==typeof a?a:ca(!!a),h):c},h.curve=function(a){return arguments.length?(e=a,null!=d&&(f=e(d)),h):e},h.context=function(a){return arguments.length?(null==a?d=f=null:f=e(d=a),h):d},h}function q_(a,b,c){var d=null,e=ca(!0),f=null,g=qX,h=null,i=or(j);function j(j){var k,l,m,n,o,p=(j=b9(j)).length,q=!1,r=Array(p),s=Array(p);for(null==f&&(h=g(o=i())),k=0;k<=p;++k){if(!(k<p&&e(n=j[k],k,j))===q)if(q=!q)l=k,h.areaStart(),h.lineStart();else{for(h.lineEnd(),h.lineStart(),m=k-1;m>=l;--m)h.point(r[m],s[m]);h.lineEnd(),h.areaEnd()}q&&(r[k]=+a(n,k,j),s[k]=+b(n,k,j),h.point(d?+d(n,k,j):r[k],c?+c(n,k,j):s[k]))}if(o)return h=null,o+""||null}function k(){return q$().defined(e).curve(g).context(f)}return a="function"==typeof a?a:void 0===a?qY:ca(+a),b="function"==typeof b?b:void 0===b?ca(0):ca(+b),c="function"==typeof c?c:void 0===c?qZ:ca(+c),j.x=function(b){return arguments.length?(a="function"==typeof b?b:ca(+b),d=null,j):a},j.x0=function(b){return arguments.length?(a="function"==typeof b?b:ca(+b),j):a},j.x1=function(a){return arguments.length?(d=null==a?null:"function"==typeof a?a:ca(+a),j):d},j.y=function(a){return arguments.length?(b="function"==typeof a?a:ca(+a),c=null,j):b},j.y0=function(a){return arguments.length?(b="function"==typeof a?a:ca(+a),j):b},j.y1=function(a){return arguments.length?(c=null==a?null:"function"==typeof a?a:ca(+a),j):c},j.lineX0=j.lineY0=function(){return k().x(a).y(b)},j.lineY1=function(){return k().x(a).y(c)},j.lineX1=function(){return k().x(d).y(b)},j.defined=function(a){return arguments.length?(e="function"==typeof a?a:ca(!!a),j):e},j.curve=function(a){return arguments.length?(g=a,null!=f&&(h=g(f)),j):g},j.context=function(a){return arguments.length?(null==a?f=h=null:h=g(f=a),j):f},j}function q0(){}function q1(a,b,c){a._context.bezierCurveTo((2*a._x0+a._x1)/3,(2*a._y0+a._y1)/3,(a._x0+2*a._x1)/3,(a._y0+2*a._y1)/3,(a._x0+4*a._x1+b)/6,(a._y0+4*a._y1+c)/6)}function q2(a){this._context=a}function q3(a){this._context=a}function q4(a){this._context=a}qW.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(a,b):this._context.moveTo(a,b);break;case 1:this._point=2;default:this._context.lineTo(a,b)}}},q2.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:q1(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(a,b):this._context.moveTo(a,b);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:q1(this,a,b)}this._x0=this._x1,this._x1=a,this._y0=this._y1,this._y1=b}},q3.prototype={areaStart:q0,areaEnd:q0,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1,this._x2=a,this._y2=b;break;case 1:this._point=2,this._x3=a,this._y3=b;break;case 2:this._point=3,this._x4=a,this._y4=b,this._context.moveTo((this._x0+4*this._x1+a)/6,(this._y0+4*this._y1+b)/6);break;default:q1(this,a,b)}this._x0=this._x1,this._x1=a,this._y0=this._y1,this._y1=b}},q4.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var c=(this._x0+4*this._x1+a)/6,d=(this._y0+4*this._y1+b)/6;this._line?this._context.lineTo(c,d):this._context.moveTo(c,d);break;case 3:this._point=4;default:q1(this,a,b)}this._x0=this._x1,this._x1=a,this._y0=this._y1,this._y1=b}};class q5{constructor(a,b){this._context=a,this._x=b}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(a,b):this._context.moveTo(a,b);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+a)/2,this._y0,this._x0,b,a,b):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+b)/2,a,this._y0,a,b)}this._x0=a,this._y0=b}}function q6(a){this._context=a}q6.prototype={areaStart:q0,areaEnd:q0,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(a,b){a*=1,b*=1,this._point?this._context.lineTo(a,b):(this._point=1,this._context.moveTo(a,b))}};function q7(a,b,c){var d=a._x1-a._x0,e=b-a._x1,f=(a._y1-a._y0)/(d||e<0&&-0),g=(c-a._y1)/(e||d<0&&-0);return((f<0?-1:1)+(g<0?-1:1))*Math.min(Math.abs(f),Math.abs(g),.5*Math.abs((f*e+g*d)/(d+e)))||0}function q8(a,b){var c=a._x1-a._x0;return c?(3*(a._y1-a._y0)/c-b)/2:b}function q9(a,b,c){var d=a._x0,e=a._y0,f=a._x1,g=a._y1,h=(f-d)/3;a._context.bezierCurveTo(d+h,e+h*b,f-h,g-h*c,f,g)}function ra(a){this._context=a}function rb(a){this._context=new rc(a)}function rc(a){this._context=a}function rd(a){this._context=a}function re(a){var b,c,d=a.length-1,e=Array(d),f=Array(d),g=Array(d);for(e[0]=0,f[0]=2,g[0]=a[0]+2*a[1],b=1;b<d-1;++b)e[b]=1,f[b]=4,g[b]=4*a[b]+2*a[b+1];for(e[d-1]=2,f[d-1]=7,g[d-1]=8*a[d-1]+a[d],b=1;b<d;++b)c=e[b]/f[b-1],f[b]-=c,g[b]-=c*g[b-1];for(e[d-1]=g[d-1]/f[d-1],b=d-2;b>=0;--b)e[b]=(g[b]-e[b+1])/f[b];for(b=0,f[d-1]=(a[d]+e[d-1])/2;b<d-1;++b)f[b]=2*a[b+1]-e[b+1];return[e,f]}function rf(a,b){this._context=a,this._t=b}function rg(){return(rg=Object.assign.bind()).apply(null,arguments)}function rh(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function ri(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?rh(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):rh(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}ra.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:q9(this,this._t0,q8(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(a,b){var c=NaN;if(b*=1,(a*=1)!==this._x1||b!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(a,b):this._context.moveTo(a,b);break;case 1:this._point=2;break;case 2:this._point=3,q9(this,q8(this,c=q7(this,a,b)),c);break;default:q9(this,this._t0,c=q7(this,a,b))}this._x0=this._x1,this._x1=a,this._y0=this._y1,this._y1=b,this._t0=c}}},(rb.prototype=Object.create(ra.prototype)).point=function(a,b){ra.prototype.point.call(this,b,a)},rc.prototype={moveTo:function(a,b){this._context.moveTo(b,a)},closePath:function(){this._context.closePath()},lineTo:function(a,b){this._context.lineTo(b,a)},bezierCurveTo:function(a,b,c,d,e,f){this._context.bezierCurveTo(b,a,d,c,f,e)}},rd.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var a=this._x,b=this._y,c=a.length;if(c)if(this._line?this._context.lineTo(a[0],b[0]):this._context.moveTo(a[0],b[0]),2===c)this._context.lineTo(a[1],b[1]);else for(var d=re(a),e=re(b),f=0,g=1;g<c;++f,++g)this._context.bezierCurveTo(d[0][f],e[0][f],d[1][f],e[1][f],a[g],b[g]);(this._line||0!==this._line&&1===c)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(a,b){this._x.push(+a),this._y.push(+b)}},rf.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(a,b):this._context.moveTo(a,b);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,b),this._context.lineTo(a,b);else{var c=this._x*(1-this._t)+a*this._t;this._context.lineTo(c,this._y),this._context.lineTo(c,b)}}this._x=a,this._y=b}};var rj={curveBasisClosed:function(a){return new q3(a)},curveBasisOpen:function(a){return new q4(a)},curveBasis:function(a){return new q2(a)},curveBumpX:function(a){return new q5(a,!0)},curveBumpY:function(a){return new q5(a,!1)},curveLinearClosed:function(a){return new q6(a)},curveLinear:qX,curveMonotoneX:function(a){return new ra(a)},curveMonotoneY:function(a){return new rb(a)},curveNatural:function(a){return new rd(a)},curveStep:function(a){return new rf(a,.5)},curveStepAfter:function(a){return new rf(a,1)},curveStepBefore:function(a){return new rf(a,0)}},rk=a=>gU(a.x)&&gU(a.y),rl=a=>a.x,rm=a=>a.y,rn=a=>{var{className:b,points:c,path:d,pathRef:e}=a;if((!c||!c.length)&&!d)return null;var g=c&&c.length?(a=>{var b,{type:c="linear",points:d=[],baseLine:e,layout:f,connectNulls:g=!1}=a,h=((a,b)=>{if("function"==typeof a)return a;var c="curve".concat(bo(a));return("curveMonotone"===c||"curveBump"===c)&&b?rj["".concat(c).concat("vertical"===b?"Y":"X")]:rj[c]||qX})(c,f),i=g?d.filter(rk):d;if(Array.isArray(e)){var j=g?e.filter(a=>rk(a)):e,k=i.map((a,b)=>ri(ri({},a),{},{base:j[b]}));return(b="vertical"===f?q_().y(rm).x1(rl).x0(a=>a.base.x):q_().x(rl).y1(rm).y0(a=>a.base.y)).defined(rk).curve(h),b(k)}return(b="vertical"===f&&bf(e)?q_().y(rm).x1(rl).x0(e):bf(e)?q_().x(rl).y1(rm).y0(e):q$().x(rl).y(rm)).defined(rk).curve(h),b(i)})(a):d;return f.createElement("path",rg({},mi(a),me(a),{className:(0,l8.clsx)("recharts-curve",b),d:null===g?void 0:g,ref:e}))},ro=["x","y","top","left","width","height","className"];function rp(){return(rp=Object.assign.bind()).apply(null,arguments)}function rq(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}var rr=a=>{var{x:b=0,y:c=0,top:d=0,left:e=0,width:g=0,height:h=0,className:i}=a,j=function(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?rq(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):rq(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}({x:b,y:c,top:d,left:e,width:g,height:h},function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,ro));return bf(b)&&bf(c)&&bf(g)&&bf(h)&&bf(d)&&bf(e)?f.createElement("path",rp({},mp(j,!0),{className:(0,l8.clsx)("recharts-cross",i),d:"M".concat(b,",").concat(d,"v").concat(h,"M").concat(e,",").concat(c,"h").concat(g)})):null};function rs(a){var{cx:b,cy:c,radius:d,startAngle:e,endAngle:f}=a;return{points:[ci(b,c,d,e),ci(b,c,d,f)],cx:b,cy:c,radius:d,startAngle:e,endAngle:f}}function rt(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function ru(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?rt(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):rt(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function rv(){return(rv=Object.assign.bind()).apply(null,arguments)}function rw(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function rx(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?rw(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):rw(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function ry(a){var b,c,d,{coordinate:e,payload:g,index:h,offset:i,tooltipAxisBandSize:j,layout:k,cursor:l,tooltipEventType:m,chartName:n}=a;if(!l||!e||"ScatterChart"!==n&&"axis"!==m)return null;if("ScatterChart"===n)c=e,d=rr;else if("BarChart"===n)b=j/2,c={stroke:"none",fill:"#ccc",x:"horizontal"===k?e.x-b:i.left+.5,y:"horizontal"===k?i.top+.5:e.y-b,width:"horizontal"===k?j:i.width-1,height:"horizontal"===k?i.height-1:j},d=oc;else if("radial"===k){var{cx:o,cy:p,radius:q,startAngle:r,endAngle:s}=rs(e);c={cx:o,cy:p,startAngle:r,endAngle:s,innerRadius:q,outerRadius:q},d=ol}else c={points:function(a,b,c){var d,e,f,g;if("horizontal"===a)f=d=b.x,e=c.top,g=c.top+c.height;else if("vertical"===a)g=e=b.y,d=c.left,f=c.left+c.width;else if(null!=b.cx&&null!=b.cy)if("centric"!==a)return rs(b);else{var{cx:h,cy:i,innerRadius:j,outerRadius:k,angle:l}=b,m=ci(h,i,j,l),n=ci(h,i,k,l);d=m.x,e=m.y,f=n.x,g=n.y}return[{x:d,y:e},{x:f,y:g}]}(k,e,i)},d=rn;var t="object"==typeof l&&"className"in l?l.className:void 0,u=rx(rx(rx(rx({stroke:"#ccc",pointerEvents:"none"},i),c),mp(l,!1)),{},{payload:g,payloadIndex:h,className:(0,l8.clsx)("recharts-tooltip-cursor",t)});return(0,f.isValidElement)(l)?(0,f.cloneElement)(l,u):(0,f.createElement)(d,u)}function rz(a){var b,c,d,e=(b=b6(ic),c=b6(ky),d=b6(kv),cx(ru(ru({},b),{},{scale:d}),c)),g=cT(),h=cX(),i=kM();return f.createElement(ry,rv({},a,{coordinate:a.coordinate,index:a.index,payload:a.payload,offset:g,layout:h,tooltipAxisBandSize:e,chartName:i}))}function rA(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function rB(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?rA(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):rA(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function rC(a){return a.dataKey}var rD=[],rE={allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",axisId:0,contentStyle:{},cursor:!0,filterNull:!0,isAnimationActive:!m1.isSsr,itemSorter:"name",itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,wrapperStyle:{}};function rF(a){var b,c,d,e,g,h,i,j,k=mQ(a,rE),{active:l,allowEscapeViewBox:m,animationDuration:n,animationEasing:o,content:p,filterNull:q,isAnimationActive:r,offset:s,payloadUniqBy:t,position:u,reverseDirection:v,useTranslate3d:w,wrapperStyle:x,cursor:y,shared:z,trigger:A,defaultIndex:B,portal:C,axisId:D}=k,E=b2(),F="number"==typeof B?String(B):B;(0,f.useEffect)(()=>{E(bG({shared:z,trigger:A,axisId:D,active:l,defaultIndex:F}))},[E,z,A,D,l,F]);var G=cR(),H=l7(),I=b6(a=>jS(a,z)),{activeIndex:J,isActive:K}=b6(a=>kY(a,I,A,F)),L=b6(a=>kX(a,I,A,F)),M=b6(a=>kW(a,I,A,F)),N=b6(a=>kV(a,I,A,F)),O=(0,f.useContext)(mE),P=null!=l?l:K,[Q,R]=function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],[b,c]=(0,f.useState)({height:0,left:0,top:0,width:0}),d=(0,f.useCallback)(a=>{if(null!=a){var d=a.getBoundingClientRect(),e={height:d.height,left:d.left,top:d.top,width:d.width};(Math.abs(e.height-b.height)>1||Math.abs(e.left-b.left)>1||Math.abs(e.top-b.top)>1||Math.abs(e.width-b.width)>1)&&c({height:e.height,left:e.left,top:e.top,width:e.width})}},[b.width,b.height,b.top,b.left,...a]);return[b,d]}([L,P]),S="axis"===I?M:void 0;b=b6(a=>((a,b,c)=>{if(null!=b){var d=j_(a);return"axis"===b?"hover"===c?d.axisInteraction.hover.dataKey:d.axisInteraction.click.dataKey:"hover"===c?d.itemInteraction.hover.dataKey:d.itemInteraction.click.dataKey}})(a,I,A)),c=b6(hS),d=b6(hQ),e=b6(hR),h=null==(g=b6(mC))?void 0:g.active,(0,f.useEffect)(()=>{if(!h&&null!=d&&null!=c){var a=bN({active:P,coordinate:N,dataKey:b,index:J,label:"number"==typeof S?String(S):S});mz.emit(mA,d,a,c)}},[h,N,b,J,S,c,d,e,P]);var T=null!=C?C:O;if(null==T)return null;var U=null!=L?L:rD;P||(U=rD),q&&U.length&&(i=L.filter(a=>null!=a.value&&(!0!==a.hide||k.includeHidden)),U=!0===t?(0,qV.default)(i,rC):"function"==typeof t?(0,qV.default)(i,t):i);var V=U.length>0,W=f.createElement(qU,{allowEscapeViewBox:m,animationDuration:n,animationEasing:o,isAnimationActive:r,active:P,coordinate:N,hasPayload:V,offset:s,position:u,reverseDirection:v,useTranslate3d:w,viewBox:G,wrapperStyle:x,lastBoundingBox:Q,innerRef:R,hasPortalFromProps:!!C},(j=rB(rB({},k),{},{payload:U,label:S,active:P,coordinate:N,accessibilityLayer:H}),f.isValidElement(p)?f.cloneElement(p,j):"function"==typeof p?f.createElement(p,j):f.createElement(qM,j)));return f.createElement(f.Fragment,null,(0,qH.createPortal)(W,T),P&&f.createElement(rz,{cursor:y,tooltipEventType:I,coordinate:N,payload:L,index:J}))}var rG=a.i(61349);function rH(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function rI(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?rH(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):rH(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var rJ=(0,f.forwardRef)((a,b)=>{var{aspect:c,initialDimension:d={width:-1,height:-1},width:e="100%",height:g="100%",minWidth:h=0,minHeight:i,maxHeight:j,children:k,debounce:l=0,id:m,className:n,onResize:o,style:p={}}=a,q=(0,f.useRef)(null),r=(0,f.useRef)();r.current=o,(0,f.useImperativeHandle)(b,()=>q.current);var[s,t]=(0,f.useState)({containerWidth:d.width,containerHeight:d.height}),u=(0,f.useCallback)((a,b)=>{t(c=>{var d=Math.round(a),e=Math.round(b);return c.containerWidth===d&&c.containerHeight===e?c:{containerWidth:d,containerHeight:e}})},[]);(0,f.useEffect)(()=>{var a=a=>{var b,{width:c,height:d}=a[0].contentRect;u(c,d),null==(b=r.current)||b.call(r,c,d)};l>0&&(a=(0,rG.default)(a,l,{trailing:!0,leading:!1}));var b=new ResizeObserver(a),{width:c,height:d}=q.current.getBoundingClientRect();return u(c,d),b.observe(q.current),()=>{b.disconnect()}},[u,l]);var v=(0,f.useMemo)(()=>{var{containerWidth:a,containerHeight:b}=s;if(a<0||b<0)return null;qo(be(e)||be(g),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",e,g),qo(!c||c>0,"The aspect(%s) must be greater than zero.",c);var d=be(e)?a:e,l=be(g)?b:g;return c&&c>0&&(d?l=d/c:l&&(d=l*c),j&&l>j&&(l=j)),qo(d>0||l>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",d,l,e,g,h,i,c),f.Children.map(k,a=>(0,f.cloneElement)(a,{width:d,height:l,style:rI({width:d,height:l},a.props.style)}))},[c,k,g,j,i,h,s,e]);return f.createElement("div",{id:m?"".concat(m):void 0,className:(0,l8.clsx)("recharts-responsive-container",n),style:rI(rI({},p),{},{width:e,height:g,minWidth:h,minHeight:i,maxHeight:j}),ref:q},f.createElement("div",{style:{width:0,height:0,overflow:"visible"}},v))});function rK(a){var b=b2();return(0,f.useEffect)(()=>{b(lP(a))},[b,a]),null}var rL=["width","height","layout"];function rM(){return(rM=Object.assign.bind()).apply(null,arguments)}var rN={accessibilityLayer:!0,stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index",layout:"radial"},rO=(0,f.forwardRef)(function(a,b){var c,d=mQ(a.categoricalChartProps,rN),{width:e,height:g,layout:h}=d,i=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(d,rL);if(!gV(e)||!gV(g))return null;var{chartName:j,defaultTooltipEventType:k,validateTooltipEventTypes:l,tooltipPayloadSearcher:m}=a;return f.createElement(l3,{preloadedState:{options:{chartName:j,defaultTooltipEventType:k,validateTooltipEventTypes:l,tooltipPayloadSearcher:m,eventEmitter:void 0}},reduxStoreName:null!=(c=d.id)?c:j},f.createElement(l4,{chartData:d.data}),f.createElement(l5,{width:e,height:g,layout:h,margin:d.margin}),f.createElement(l6,{accessibilityLayer:d.accessibilityLayer,barCategoryGap:d.barCategoryGap,maxBarSize:d.maxBarSize,stackOffset:d.stackOffset,barGap:d.barGap,barSize:d.barSize,syncId:d.syncId,syncMethod:d.syncMethod,className:d.className}),f.createElement(rK,{cx:d.cx,cy:d.cy,startAngle:d.startAngle,endAngle:d.endAngle,innerRadius:d.innerRadius,outerRadius:d.outerRadius}),f.createElement(mO,rM({width:e,height:g},i,{ref:b})))}),rP=["item"],rQ={layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},rR=(0,f.forwardRef)((a,b)=>{var c=mQ(a,rQ);return f.createElement(rO,{chartName:"PieChart",defaultTooltipEventType:"item",validateTooltipEventTypes:rP,tooltipPayloadSearcher:bp,categoricalChartProps:c,ref:b})}),rS=a=>a.graphicalItems.polarItems,rT=ak([h7,h8],it),rU=ak([rS,iq,rT],iw),rV=ak([rU],iB),rW=ak([rV,gS],iD),rX=ak([rW,iq,rU],iF),rY=ak([rW,iq,rU],(a,b,c)=>c.length>0?a.flatMap(a=>c.flatMap(c=>{var d;return{value:cn(a,null!=(d=b.dataKey)?d:c.dataKey),errorDomain:[]}})).filter(Boolean):(null==b?void 0:b.dataKey)!=null?a.map(a=>({value:cn(a,b.dataKey),errorDomain:[]})):a.map(a=>({value:a,errorDomain:[]}))),rZ=()=>void 0,r$=ak([iq,i8,rZ,rY,rZ,cW,h7],i9),r_=ak([iq,cW,rW,rX,hO,h7,r$],jc),r0=ak([r_,iq,jf],jh);function r1(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function r2(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?r1(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):r1(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}ak([iq,r_,r0,h7],jj);var r3=ak([rS,(a,b)=>b],(a,b)=>a.filter(a=>"pie"===a.type).find(a=>a.id===b)),r4=[],r5=(a,b,c)=>(null==c?void 0:c.length)===0?r4:c,r6=ak([gS,r3,r5],(a,b,c)=>{var d,{chartData:e}=a;if(null!=b&&((d=(null==b?void 0:b.data)!=null&&b.data.length>0?b.data:e)&&d.length||null==c||(d=c.map(a=>r2(r2({},b.presentationProps),a.props))),null!=d))return d}),r7=ak([r6,r3,r5],(a,b,c)=>{if(null!=a&&null!=b)return a.map((a,d)=>{var e,f,g=cn(a,b.nameKey,b.name);return f=null!=c&&null!=(e=c[d])&&null!=(e=e.props)&&e.fill?c[d].props.fill:"object"==typeof a&&null!=a&&"fill"in a?a.fill:b.fill,{value:cz(g,b.dataKey),color:f,payload:a,type:b.legendType}})}),r8=ak([r6,r3,r5,cK],(a,b,c,d)=>{if(null!=b&&null!=a)return function(a){var b,c,d,{pieSettings:e,displayedData:f,cells:g,offset:h}=a,{cornerRadius:i,startAngle:j,endAngle:k,dataKey:l,nameKey:m,tooltipType:n}=e,o=Math.abs(e.minAngle),p=bc(k-j)*Math.min(Math.abs(k-j),360),q=Math.abs(p),r=f.length<=1?0:null!=(b=e.paddingAngle)?b:0,s=f.filter(a=>0!==cn(a,l,0)).length,t=q-s*o-(q>=360?s:s-1)*r,u=f.reduce((a,b)=>{var c=cn(b,l,0);return a+(bf(c)?c:0)},0);return u>0&&(c=f.map((a,b)=>{var c,f=cn(a,l,0),k=cn(a,m,b),q=((a,b,c)=>{let d,e,f;var{top:g,left:h,width:i,height:j}=b,k=cj(i,j),l=h+bj(a.cx,i,i/2),m=g+bj(a.cy,j,j/2),n=bj(a.innerRadius,k,0);return{cx:l,cy:m,innerRadius:n,outerRadius:(d=c,e=a.outerRadius,f=k,"function"==typeof e?e(d):bj(e,f,.8*f)),maxRadius:a.maxRadius||Math.sqrt(i*i+j*j)/2}})(e,h,a),s=(bf(f)?f:0)/u,v=se(se({},a),g&&g[b]&&g[b].props),w=(c=b?d.endAngle+bc(p)*r*(0!==f):j)+bc(p)*((0!==f?o:0)+s*t),x=(c+w)/2,y=(q.innerRadius+q.outerRadius)/2,z=[{name:k,value:f,payload:v,dataKey:l,type:n}],A=ci(q.cx,q.cy,y,x);return d=se(se(se(se({},e.presentationProps),{},{percent:s,cornerRadius:i,name:k,tooltipPayload:z,midAngle:x,middleRadius:y,tooltipPosition:A},v),q),{},{value:cn(a,l),startAngle:c,endAngle:w,payload:v,paddingAngle:bc(p)*r})})),c}({offset:d,pieSettings:b,displayedData:a,cells:c})}),r9=["onMouseEnter","onClick","onMouseLeave"],sa=["id"],sb=["id"];function sc(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}function sd(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function se(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?sd(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):sd(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function sf(){return(sf=Object.assign.bind()).apply(null,arguments)}function sg(a){var b=(0,f.useMemo)(()=>mn(a.children,m_),[a.children]),c=b6(c=>r7(c,a.id,b));return null==c?null:f.createElement(pp,{legendPayload:c})}function sh(a){var{dataKey:b,nameKey:c,sectors:d,stroke:e,strokeWidth:f,fill:g,name:h,hide:i,tooltipType:j}=a;return{dataDefinedOnItem:null==d?void 0:d.map(a=>a.tooltipPayload),positions:null==d?void 0:d.map(a=>a.tooltipPosition),settings:{stroke:e,strokeWidth:f,fill:g,dataKey:b,nameKey:c,name:cz(h,b),hide:i,type:j,color:g,unit:""}}}function si(a){var{sectors:b,props:c,showLabels:d}=a,{label:e,labelLine:g,dataKey:h}=c;if(!d||!e||!b)return null;var i=mi(c),j=mp(e,!1),k=mp(g,!1),l="object"==typeof e&&"offsetRadius"in e&&e.offsetRadius||20,m=b.map((a,b)=>{var c,d,m=(a.startAngle+a.endAngle)/2,n=ci(a.cx,a.cy,a.outerRadius+l,m),o=se(se(se(se({},i),a),{},{stroke:"none"},j),{},{index:b,textAnchor:(c=n.x)>(d=a.cx)?"start":c<d?"end":"middle"},n),p=se(se(se(se({},i),a),{},{fill:"none",stroke:a.fill},k),{},{index:b,points:[ci(a.cx,a.cy,a.outerRadius,m),n],key:"line"});return f.createElement(m$,{key:"label-".concat(a.startAngle,"-").concat(a.endAngle,"-").concat(a.midAngle,"-").concat(b)},g&&((a,b)=>{if(f.isValidElement(a))return f.cloneElement(a,b);if("function"==typeof a)return a(b);var c=(0,l8.clsx)("recharts-pie-label-line","boolean"!=typeof a?a.className:"");return f.createElement(rn,sf({},b,{type:"linear",className:c}))})(g,p),((a,b,c)=>{if(f.isValidElement(a))return f.cloneElement(a,b);var d=c;if("function"==typeof a&&(d=a(b),f.isValidElement(d)))return d;var e=(0,l8.clsx)("recharts-pie-label-text","boolean"!=typeof a&&"function"!=typeof a?a.className:"");return f.createElement(nr,sf({},b,{alignmentBaseline:"middle",className:e}),d)})(e,o,cn(a,h)))});return f.createElement(m$,{className:"recharts-pie-labels"},m)}function sj(a){var{sectors:b,activeShape:c,inactiveShape:d,allOtherPieProps:e,showLabels:g}=a,h=b6(kD),{onMouseEnter:i,onClick:j,onMouseLeave:k}=e,l=sc(e,r9),m=o_(i,e.dataKey),n=o0(k),o=o1(j,e.dataKey);return null==b?null:f.createElement(f.Fragment,null,b.map((a,g)=>{if((null==a?void 0:a.startAngle)===0&&(null==a?void 0:a.endAngle)===0&&1!==b.length)return null;var i=c&&String(g)===h,j=i?c:h?d:null,k=se(se({},a),{},{stroke:a.stroke,tabIndex:-1,[cG]:g,[cH]:e.dataKey});return f.createElement(m$,sf({tabIndex:-1,className:"recharts-pie-sector"},mf(l,a,g),{onMouseEnter:m(a,g),onMouseLeave:n(a,g),onClick:o(a,g),key:"sector-".concat(null==a?void 0:a.startAngle,"-").concat(null==a?void 0:a.endAngle,"-").concat(a.midAngle,"-").concat(g)}),f.createElement(oT,sf({option:j,isActive:i,shapeType:"sector"},k)))}),f.createElement(si,{sectors:b,props:e,showLabels:g}))}function sk(a){var{props:b,previousSectorsRef:c}=a,{sectors:d,isAnimationActive:e,animationBegin:g,animationDuration:h,animationEasing:i,activeShape:j,inactiveShape:k,onAnimationStart:l,onAnimationEnd:m}=b,n=pq(b,"recharts-pie-"),o=c.current,[p,q]=(0,f.useState)(!0),r=(0,f.useCallback)(()=>{"function"==typeof m&&m(),q(!1)},[m]),s=(0,f.useCallback)(()=>{"function"==typeof l&&l(),q(!0)},[l]);return f.createElement(px,{begin:g,duration:h,isActive:e,easing:i,onAnimationStart:s,onAnimationEnd:r,key:n},a=>{var e=[],g=(d&&d[0]).startAngle;return d.forEach((b,c)=>{var d=o&&o[c],f=c>0?(0,bb.default)(b,"paddingAngle",0):0;if(d){var h=bl(d.endAngle-d.startAngle,b.endAngle-b.startAngle),i=se(se({},b),{},{startAngle:g+f,endAngle:g+h(a)+f});e.push(i),g=i.endAngle}else{var{endAngle:j,startAngle:k}=b,l=bl(0,j-k)(a),m=se(se({},b),{},{startAngle:g+f,endAngle:g+l+f});e.push(m),g=m.endAngle}}),c.current=e,f.createElement(m$,null,f.createElement(sj,{sectors:e,activeShape:j,inactiveShape:k,allOtherPieProps:b,showLabels:!p}))})}function sl(a){var{sectors:b,isAnimationActive:c,activeShape:d,inactiveShape:e}=a,g=(0,f.useRef)(null),h=g.current;return c&&b&&b.length&&(!h||h!==b)?f.createElement(sk,{props:a,previousSectorsRef:g}):f.createElement(sj,{sectors:b,activeShape:d,inactiveShape:e,allOtherPieProps:a,showLabels:!0})}function sm(a){var{hide:b,className:c,rootTabIndex:d}=a,e=(0,l8.clsx)("recharts-pie",c);return b?null:f.createElement(m$,{tabIndex:d,className:e},f.createElement(sl,a))}var sn={animationBegin:400,animationDuration:1500,animationEasing:"ease",cx:"50%",cy:"50%",dataKey:"value",endAngle:360,fill:"#808080",hide:!1,innerRadius:0,isAnimationActive:!m1.isSsr,labelLine:!0,legendType:"rect",minAngle:0,nameKey:"name",outerRadius:"80%",paddingAngle:0,rootTabIndex:0,startAngle:0,stroke:"#fff"};function so(a){var{id:b}=a,c=sc(a,sa),d=(0,f.useMemo)(()=>mn(a.children,m_),[a.children]),e=b6(a=>r8(a,b,d));return f.createElement(f.Fragment,null,f.createElement(o2,{fn:sh,args:se(se({},a),{},{sectors:e})}),f.createElement(sm,sf({},c,{sectors:e})))}function sp(a){var b=mQ(a,sn),{id:c}=b,d=sc(b,sb),e=mi(d);return f.createElement(o5,{id:c,type:"pie"},a=>f.createElement(f.Fragment,null,f.createElement(ps,{type:"pie",id:a,data:d.data,dataKey:d.dataKey,hide:d.hide,angleAxisId:0,radiusAxisId:0,name:d.name,nameKey:d.nameKey,tooltipType:d.tooltipType,legendType:d.legendType,fill:d.fill,cx:d.cx,cy:d.cy,startAngle:d.startAngle,endAngle:d.endAngle,paddingAngle:d.paddingAngle,minAngle:d.minAngle,innerRadius:d.innerRadius,outerRadius:d.outerRadius,cornerRadius:d.cornerRadius,presentationProps:e}),f.createElement(sg,sf({},d,{id:a})),f.createElement(so,sf({},d,{id:a})),d.children))}sp.displayName="Pie";var sq=["axis"],sr=(0,f.forwardRef)((a,b)=>f.createElement(mU,{chartName:"LineChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:sq,tooltipPayloadSearcher:bp,categoricalChartProps:a,ref:b})),ss=f;function st(){return(st=Object.assign.bind()).apply(null,arguments)}var su=a=>{var{cx:b,cy:c,r:d,className:e}=a,g=(0,l8.clsx)("recharts-dot",e);return b===+b&&c===+c&&d===+d?f.createElement("circle",st({},mi(a),me(a),{className:g,cx:b,cy:c,r:d})):null};function sv(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function sw(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?sv(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):sv(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function sx(a){var{points:b,mainColor:c,activeDot:d,itemDataKey:e}=a,g=b6(kD),h=b6(kL);if(null==b||null==h)return null;var i=b.find(a=>h.includes(a.payload));return null==i?null:(a=>{var b,{point:c,childIndex:d,mainColor:e,activeDot:g,dataKey:h}=a;if(!1===g||null==c.x||null==c.y)return null;var i=sw(sw({index:d,dataKey:h,cx:c.x,cy:c.y,r:4,fill:null!=e?e:"none",strokeWidth:2,stroke:"#fff",payload:c.payload,value:c.value},mp(g,!1)),me(g));return b=(0,f.isValidElement)(g)?(0,f.cloneElement)(g,i):"function"==typeof g?g(i):f.createElement(su,i),f.createElement(m$,{className:"recharts-active-dot"},b)})({point:i,childIndex:Number(g),mainColor:c,dataKey:e,activeDot:d})}var sy=(a,b,c,d)=>jM(a,"xAxis",b,d),sz=(a,b,c,d)=>jL(a,"xAxis",b,d),sA=(a,b,c,d)=>jM(a,"yAxis",c,d),sB=(a,b,c,d)=>jL(a,"yAxis",c,d),sC=ak([cW,sy,sA,sz,sB],(a,b,c,d,e)=>co(a,"xAxis")?cx(b,d,!1):cx(c,e,!1));function sD(a){return"line"===a.type}var sE=ak([iu,(a,b,c,d,e)=>e],(a,b)=>a.filter(sD).find(a=>a.id===b)),sF=ak([cW,sy,sA,sz,sB,sE,sC,gT],(a,b,c,d,e,f,g,h)=>{var i,{chartData:j,dataStartIndex:k,dataEndIndex:l}=h;if(null!=f&&null!=b&&null!=c&&null!=d&&null!=e&&0!==d.length&&0!==e.length&&null!=g){var{dataKey:m,data:n}=f;if(null!=(i=null!=n&&n.length>0?n:null==j?void 0:j.slice(k,l+1)))return function(a){var{layout:b,xAxis:c,yAxis:d,xAxisTicks:e,yAxisTicks:f,dataKey:g,bandSize:h,displayedData:i}=a;return i.map((a,i)=>{var j=cn(a,g);if("horizontal"===b)return{x:ct({axis:c,ticks:e,bandSize:h,entry:a,index:i}),y:null==j?null:d.scale(j),value:j,payload:a};var k=null==j?null:c.scale(j),l=ct({axis:d,ticks:f,bandSize:h,entry:a,index:i});return null==k||null==l?null:{x:k,y:l,value:j,payload:a}}).filter(Boolean)}({layout:a,xAxis:b,yAxis:c,xAxisTicks:d,yAxisTicks:e,dataKey:m,bandSize:g,displayedData:i})}}),sG=["id"],sH=["type","layout","connectNulls","needClip"],sI=["activeDot","animateNewValues","animationBegin","animationDuration","animationEasing","connectNulls","dot","hide","isAnimationActive","label","legendType","xAxisId","yAxisId","id"];function sJ(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function sK(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?sJ(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):sJ(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function sL(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}function sM(){return(sM=Object.assign.bind()).apply(null,arguments)}function sN(a){var{dataKey:b,data:c,stroke:d,strokeWidth:e,fill:f,name:g,hide:h,unit:i}=a;return{dataDefinedOnItem:c,positions:void 0,settings:{stroke:d,strokeWidth:e,fill:f,dataKey:b,nameKey:void 0,name:cz(g,b),hide:h,type:a.tooltipType,color:a.stroke,unit:i}}}var sO=(a,b)=>"".concat(b,"px ").concat(a-b,"px");function sP(a){var{clipPathId:b,points:c,props:d}=a,{dot:e,dataKey:f,needClip:g}=d;if(null==c||!e&&1!==c.length)return null;var{id:h}=d,i=sL(d,sG),j=mo(e),k=mi(i),l=mp(e,!0),m=c.map((a,b)=>{var d,g=sK(sK(sK({key:"dot-".concat(b),r:3},k),l),{},{index:b,cx:a.x,cy:a.y,dataKey:f,value:a.value,payload:a.payload,points:c});if(ss.isValidElement(e))d=ss.cloneElement(e,g);else if("function"==typeof e)d=e(g);else{var h=(0,l8.clsx)("recharts-line-dot","boolean"!=typeof e?e.className:"");d=ss.createElement(su,sM({},g,{className:h}))}return d}),n={clipPath:g?"url(#clipPath-".concat(j?"":"dots-").concat(b,")"):void 0};return ss.createElement(m$,sM({className:"recharts-line-dots",key:"dots"},n),m)}function sQ(a){var{clipPathId:b,pathRef:c,points:d,strokeDasharray:e,props:f,showLabels:g}=a,{type:h,layout:i,connectNulls:j,needClip:k}=f,l=sK(sK({},mp(sL(f,sH),!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:k?"url(#clipPath-".concat(b,")"):void 0,points:d,type:h,layout:i,connectNulls:j,strokeDasharray:null!=e?e:f.strokeDasharray});return ss.createElement(ss.Fragment,null,(null==d?void 0:d.length)>1&&ss.createElement(rn,sM({},l,{pathRef:c})),ss.createElement(sP,{points:d,clipPathId:b,props:f}),g&&nI.renderCallByParent(f,d))}function sR(a){var{clipPathId:b,props:c,pathRef:d,previousPointsRef:e,longestAnimatedLengthRef:f}=a,{points:g,strokeDasharray:h,isAnimationActive:i,animationBegin:j,animationDuration:k,animationEasing:l,animateNewValues:m,width:n,height:o,onAnimationEnd:p,onAnimationStart:q}=c,r=e.current,s=pq(c,"recharts-line-"),[t,u]=(0,ss.useState)(!1),v=(0,ss.useCallback)(()=>{"function"==typeof p&&p(),u(!1)},[p]),w=(0,ss.useCallback)(()=>{"function"==typeof q&&q(),u(!0)},[q]),x=function(a){try{return a&&a.getTotalLength&&a.getTotalLength()||0}catch(a){return 0}}(d.current),y=f.current;return ss.createElement(px,{begin:j,duration:k,isActive:i,easing:l,onAnimationEnd:v,onAnimationStart:w,key:s},a=>{var i,j=Math.min(bm(y,x+y,a),x);if(i=h?((a,b,c)=>{var d=c.reduce((a,b)=>a+b);if(!d)return sO(b,a);for(var e=Math.floor(a/d),f=a%d,g=b-a,h=[],i=0,j=0;i<c.length;j+=c[i],++i)if(j+c[i]>f){h=[...c.slice(0,i),f-j];break}var k=h.length%2==0?[0,g]:[g];return[...function(a,b){for(var c=a.length%2!=0?[...a,0]:a,d=[],e=0;e<b;++e)d=[...d,...c];return d}(c,e),...h,...k].map(a=>"".concat(a,"px")).join(", ")})(j,x,"".concat(h).split(/[,\s]+/gim).map(a=>parseFloat(a))):sO(x,j),r){var k=r.length/g.length,l=1===a?g:g.map((b,c)=>{var d=Math.floor(c*k);if(r[d]){var e=r[d];return sK(sK({},b),{},{x:bm(e.x,b.x,a),y:bm(e.y,b.y,a)})}return m?sK(sK({},b),{},{x:bm(2*n,b.x,a),y:bm(o/2,b.y,a)}):sK(sK({},b),{},{x:b.x,y:b.y})});return e.current=l,ss.createElement(sQ,{props:c,points:l,clipPathId:b,pathRef:d,showLabels:!t,strokeDasharray:i})}return a>0&&x>0&&(e.current=g,f.current=j),ss.createElement(sQ,{props:c,points:g,clipPathId:b,pathRef:d,showLabels:!t,strokeDasharray:i})})}function sS(a){var{clipPathId:b,props:c}=a,{points:d,isAnimationActive:e}=c,f=(0,ss.useRef)(null),g=(0,ss.useRef)(0),h=(0,ss.useRef)(null),i=f.current;return e&&d&&d.length&&i!==d?ss.createElement(sR,{props:c,clipPathId:b,previousPointsRef:f,longestAnimatedLengthRef:g,pathRef:h}):ss.createElement(sQ,{props:c,points:d,clipPathId:b,pathRef:h,showLabels:!0})}var sT=(a,b)=>({x:a.x,y:a.y,value:a.value,errorVal:cn(a.payload,b)});class sU extends ss.Component{render(){var a,{hide:b,dot:c,points:d,className:e,xAxisId:f,yAxisId:g,top:h,left:i,width:j,height:k,id:l,needClip:m}=this.props;if(b)return null;var n=(0,l8.clsx)("recharts-line",e),{r:o=3,strokeWidth:p=2}=null!=(a=mp(c,!1))?a:{r:3,strokeWidth:2},q=mo(c),r=2*o+p;return ss.createElement(ss.Fragment,null,ss.createElement(m$,{className:n},m&&ss.createElement("defs",null,ss.createElement(pa,{clipPathId:l,xAxisId:f,yAxisId:g}),!q&&ss.createElement("clipPath",{id:"clipPath-dots-".concat(l)},ss.createElement("rect",{x:i-r/2,y:h-r/2,width:j+r,height:k+r}))),ss.createElement(sS,{props:this.props,clipPathId:l}),ss.createElement(o8,{xAxisId:f,yAxisId:g,data:d,dataPointFormatter:sT,errorBarOffset:0},this.props.children)),ss.createElement(sx,{activeDot:this.props.activeDot,points:d,mainColor:this.props.stroke,itemDataKey:this.props.dataKey}))}}var sV={activeDot:!0,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",connectNulls:!1,dot:!0,fill:"#fff",hide:!1,isAnimationActive:!m1.isSsr,label:!1,legendType:"line",stroke:"#3182bd",strokeWidth:1,xAxisId:0,yAxisId:0};function sW(a){var b=mQ(a,sV),{activeDot:c,animateNewValues:d,animationBegin:e,animationDuration:f,animationEasing:g,connectNulls:h,dot:i,hide:j,isAnimationActive:k,label:l,legendType:m,xAxisId:n,yAxisId:o,id:p}=b,q=sL(b,sI),{needClip:r}=o9(n,o),s=mK(),t=cX(),u=cO(),v=b6(a=>sF(a,n,o,u,p));if("horizontal"!==t&&"vertical"!==t||null==v||null==s)return null;var{height:w,width:x,x:y,y:z}=s;return ss.createElement(sU,sM({},q,{id:p,connectNulls:h,dot:i,activeDot:c,animateNewValues:d,animationBegin:e,animationDuration:f,animationEasing:g,isAnimationActive:k,hide:j,label:l,legendType:m,xAxisId:n,yAxisId:o,points:v,layout:t,height:w,width:x,left:y,top:z,needClip:r}))}function sX(a){var b=mQ(a,sV),c=cO();return ss.createElement(o5,{id:b.id,type:"line"},a=>ss.createElement(ss.Fragment,null,ss.createElement(po,{legendPayload:(a=>{var{dataKey:b,name:c,stroke:d,legendType:e,hide:f}=a;return[{inactive:f,dataKey:b,type:e,color:d,value:cz(c,b),payload:a}]})(b)}),ss.createElement(o2,{fn:sN,args:b}),ss.createElement(pr,{type:"line",id:a,data:b.data,xAxisId:b.xAxisId,yAxisId:b.yAxisId,zAxisId:0,dataKey:b.dataKey,hide:b.hide,isPanorama:c}),ss.createElement(sW,sM({},b,{id:a}))))}sX.displayName="Line";var sY=f,sZ=(a,b,c,d)=>jM(a,"xAxis",b,d),s$=(a,b,c,d)=>jL(a,"xAxis",b,d),s_=(a,b,c,d)=>jM(a,"yAxis",c,d),s0=(a,b,c,d)=>jL(a,"yAxis",c,d),s1=ak([cW,sZ,s_,s$,s0],(a,b,c,d,e)=>co(a,"xAxis")?cx(b,d,!1):cx(c,e,!1)),s2=ak([iu,(a,b,c,d,e)=>e],(a,b)=>a.filter(a=>"area"===a.type).find(a=>a.id===b)),s3=ak([cW,sZ,s_,s$,s0,(a,b,c,d,e)=>{var f,g,h=s2(a,b,c,d,e);if(null!=h&&null!=(g=co(cW(a),"xAxis")?iL(a,"yAxis",c,d):iL(a,"xAxis",b,d))){var{stackId:i}=h,j=h9(h);if(null!=i&&null!=j){var k=null==(f=g[i])?void 0:f.stackedData;return null==k?void 0:k.find(a=>a.key===j)}}},gT,s1,s2],(a,b,c,d,e,f,g,h,i)=>{var j,{chartData:k,dataStartIndex:l,dataEndIndex:m}=g;if(null!=i&&("horizontal"===a||"vertical"===a)&&null!=b&&null!=c&&null!=d&&null!=e&&0!==d.length&&0!==e.length&&null!=h){var{data:n}=i;if(null!=(j=n&&n.length>0?n:null==k?void 0:k.slice(l,m+1)))return function(a){var b,{areaSettings:{connectNulls:c,baseValue:d,dataKey:e},stackedData:f,layout:g,chartBaseValue:h,xAxis:i,yAxis:j,displayedData:k,dataStartIndex:l,xAxisTicks:m,yAxisTicks:n,bandSize:o}=a,p=f&&f.length,q=((a,b,c,d,e)=>{var f=null!=c?c:b;if(bf(f))return f;var g="horizontal"===a?e:d,h=g.scale.domain();if("number"===g.type){var i=Math.max(h[0],h[1]),j=Math.min(h[0],h[1]);return"dataMin"===f?j:"dataMax"===f||i<0?i:Math.max(Math.min(h[0],h[1]),0)}return"dataMin"===f?h[0]:"dataMax"===f?h[1]:h[0]})(g,h,d,i,j),r="horizontal"===g,s=!1,t=k.map((a,b)=>{p?d=f[l+b]:Array.isArray(d=cn(a,e))?s=!0:d=[q,d];var d,g=null==d[1]||p&&!c&&null==cn(a,e);return r?{x:ct({axis:i,ticks:m,bandSize:o,entry:a,index:b}),y:g?null:j.scale(d[1]),value:d,payload:a}:{x:g?null:i.scale(d[1]),y:ct({axis:j,ticks:n,bandSize:o,entry:a,index:b}),value:d,payload:a}});return b=p||s?t.map(a=>{var b=Array.isArray(a.value)?a.value[0]:null;return r?{x:a.x,y:null!=b&&null!=a.y?j.scale(b):null,payload:a.payload}:{x:null!=b?i.scale(b):null,y:a.y,payload:a.payload}}):r?j.scale(q):i.scale(q),{points:t,baseLine:b,isRange:s}}({layout:a,xAxis:b,yAxis:c,xAxisTicks:d,yAxisTicks:e,dataStartIndex:l,areaSettings:i,stackedData:f,displayedData:j,chartBaseValue:void 0,bandSize:h})}}),s4=["id"],s5=["activeDot","animationBegin","animationDuration","animationEasing","connectNulls","dot","fill","fillOpacity","hide","isAnimationActive","legendType","stroke","xAxisId","yAxisId"];function s6(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}function s7(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function s8(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?s7(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):s7(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function s9(){return(s9=Object.assign.bind()).apply(null,arguments)}function ta(a,b){return a&&"none"!==a?a:b}function tb(a){var{dataKey:b,data:c,stroke:d,strokeWidth:e,fill:f,name:g,hide:h,unit:i}=a;return{dataDefinedOnItem:c,positions:void 0,settings:{stroke:d,strokeWidth:e,fill:f,dataKey:b,nameKey:void 0,name:cz(g,b),hide:h,type:a.tooltipType,color:ta(d,f),unit:i}}}function tc(a){var{clipPathId:b,points:c,props:d}=a,{needClip:e,dot:f,dataKey:g}=d;if(null==c||!f&&1!==c.length)return null;var h=mo(f),i=mi(d),j=mp(f,!0),k=c.map((a,b)=>{var d,e=s8(s8(s8({key:"dot-".concat(b),r:3},i),j),{},{index:b,cx:a.x,cy:a.y,dataKey:g,value:a.value,payload:a.payload,points:c});if(sY.isValidElement(f))d=sY.cloneElement(f,e);else if("function"==typeof f)d=f(e);else{var h=(0,l8.clsx)("recharts-area-dot","boolean"!=typeof f?f.className:"");d=sY.createElement(su,s9({},e,{className:h}))}return d}),l={clipPath:e?"url(#clipPath-".concat(h?"":"dots-").concat(b,")"):void 0};return sY.createElement(m$,s9({className:"recharts-area-dots"},l),k)}function td(a){var{points:b,baseLine:c,needClip:d,clipPathId:e,props:f,showLabels:g}=a,{layout:h,type:i,stroke:j,connectNulls:k,isRange:l}=f,{id:m}=f,n=s6(f,s4),o=mi(n);return sY.createElement(sY.Fragment,null,(null==b?void 0:b.length)>1&&sY.createElement(m$,{clipPath:d?"url(#clipPath-".concat(e,")"):void 0},sY.createElement(rn,s9({},o,{id:m,points:b,connectNulls:k,type:i,baseLine:c,layout:h,stroke:"none",className:"recharts-area-area"})),"none"!==j&&sY.createElement(rn,s9({},o,{className:"recharts-area-curve",layout:h,type:i,connectNulls:k,fill:"none",points:b})),"none"!==j&&l&&sY.createElement(rn,s9({},o,{className:"recharts-area-curve",layout:h,type:i,connectNulls:k,fill:"none",points:c}))),sY.createElement(tc,{points:b,props:n,clipPathId:e}),g&&nI.renderCallByParent(n,b))}function te(a){var{alpha:b,baseLine:c,points:d,strokeWidth:e}=a,f=d[0].y,g=d[d.length-1].y;if(!gU(f)||!gU(g))return null;var h=b*Math.abs(f-g),i=Math.max(...d.map(a=>a.x||0));return(bf(c)?i=Math.max(c,i):c&&Array.isArray(c)&&c.length&&(i=Math.max(...c.map(a=>a.x||0),i)),bf(i))?sY.createElement("rect",{x:0,y:f<g?f:f-h,width:i+(e?parseInt("".concat(e),10):1),height:Math.floor(h)}):null}function tf(a){var{alpha:b,baseLine:c,points:d,strokeWidth:e}=a,f=d[0].x,g=d[d.length-1].x;if(!gU(f)||!gU(g))return null;var h=b*Math.abs(f-g),i=Math.max(...d.map(a=>a.y||0));return(bf(c)?i=Math.max(c,i):c&&Array.isArray(c)&&c.length&&(i=Math.max(...c.map(a=>a.y||0),i)),bf(i))?sY.createElement("rect",{x:f<g?f:f-h,y:0,width:h,height:Math.floor(i+(e?parseInt("".concat(e),10):1))}):null}function tg(a){var{alpha:b,layout:c,points:d,baseLine:e,strokeWidth:f}=a;return"vertical"===c?sY.createElement(te,{alpha:b,points:d,baseLine:e,strokeWidth:f}):sY.createElement(tf,{alpha:b,points:d,baseLine:e,strokeWidth:f})}function th(a){var{needClip:b,clipPathId:c,props:d,previousPointsRef:e,previousBaselineRef:f}=a,{points:g,baseLine:h,isAnimationActive:i,animationBegin:j,animationDuration:k,animationEasing:l,onAnimationStart:m,onAnimationEnd:n}=d,o=pq(d,"recharts-area-"),[p,q]=(0,sY.useState)(!0),r=(0,sY.useCallback)(()=>{"function"==typeof n&&n(),q(!1)},[n]),s=(0,sY.useCallback)(()=>{"function"==typeof m&&m(),q(!0)},[m]),t=e.current,u=f.current;return sY.createElement(px,{begin:j,duration:k,isActive:i,easing:l,onAnimationEnd:r,onAnimationStart:s,key:o},a=>{if(t){var i,j=t.length/g.length,k=1===a?g:g.map((b,c)=>{var d=Math.floor(c*j);if(t[d]){var e=t[d];return s8(s8({},b),{},{x:bm(e.x,b.x,a),y:bm(e.y,b.y,a)})}return b});if(bf(h))i=bm(u,h,a);else i=null==h||bd(h)?bm(u,0,a):h.map((b,c)=>{var d=Math.floor(c*j);if(Array.isArray(u)&&u[d]){var e=u[d];return s8(s8({},b),{},{x:bm(e.x,b.x,a),y:bm(e.y,b.y,a)})}return b});return a>0&&(e.current=k,f.current=i),sY.createElement(td,{points:k,baseLine:i,needClip:b,clipPathId:c,props:d,showLabels:!p})}return a>0&&(e.current=g,f.current=h),sY.createElement(m$,null,sY.createElement("defs",null,sY.createElement("clipPath",{id:"animationClipPath-".concat(c)},sY.createElement(tg,{alpha:a,points:g,baseLine:h,layout:d.layout,strokeWidth:d.strokeWidth}))),sY.createElement(m$,{clipPath:"url(#animationClipPath-".concat(c,")")},sY.createElement(td,{points:g,baseLine:h,needClip:b,clipPathId:c,props:d,showLabels:!0})))})}function ti(a){var{needClip:b,clipPathId:c,props:d}=a,{points:e,baseLine:f,isAnimationActive:g}=d,h=(0,sY.useRef)(null),i=(0,sY.useRef)(),j=h.current,k=i.current;return g&&e&&e.length&&(j!==e||k!==f)?sY.createElement(th,{needClip:b,clipPathId:c,props:d,previousPointsRef:h,previousBaselineRef:i}):sY.createElement(td,{points:e,baseLine:f,needClip:b,clipPathId:c,props:d,showLabels:!0})}class tj extends sY.PureComponent{render(){var a,{hide:b,dot:c,points:d,className:e,top:f,left:g,needClip:h,xAxisId:i,yAxisId:j,width:k,height:l,id:m,baseLine:n}=this.props;if(b)return null;var o=(0,l8.clsx)("recharts-area",e),{r:p=3,strokeWidth:q=2}=null!=(a=mp(c,!1))?a:{r:3,strokeWidth:2},r=mo(c),s=2*p+q;return sY.createElement(sY.Fragment,null,sY.createElement(m$,{className:o},h&&sY.createElement("defs",null,sY.createElement(pa,{clipPathId:m,xAxisId:i,yAxisId:j}),!r&&sY.createElement("clipPath",{id:"clipPath-dots-".concat(m)},sY.createElement("rect",{x:g-s/2,y:f-s/2,width:k+s,height:l+s}))),sY.createElement(ti,{needClip:h,clipPathId:m,props:this.props})),sY.createElement(sx,{points:d,mainColor:ta(this.props.stroke,this.props.fill),itemDataKey:this.props.dataKey,activeDot:this.props.activeDot}),this.props.isRange&&Array.isArray(n)&&sY.createElement(sx,{points:n,mainColor:ta(this.props.stroke,this.props.fill),itemDataKey:this.props.dataKey,activeDot:this.props.activeDot}))}}var tk={activeDot:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",connectNulls:!1,dot:!1,fill:"#3182bd",fillOpacity:.6,hide:!1,isAnimationActive:!m1.isSsr,legendType:"line",stroke:"#3182bd",xAxisId:0,yAxisId:0};function tl(a){var b,c=mQ(a,tk),{activeDot:d,animationBegin:e,animationDuration:f,animationEasing:g,connectNulls:h,dot:i,fill:j,fillOpacity:k,hide:l,isAnimationActive:m,legendType:n,stroke:o,xAxisId:p,yAxisId:q}=c,r=s6(c,s5),s=cX(),t=kM(),{needClip:u}=o9(p,q),v=cO(),{points:w,isRange:x,baseLine:y}=null!=(b=b6(b=>s3(b,p,q,v,a.id)))?b:{},z=mK();if("horizontal"!==s&&"vertical"!==s||null==z||"AreaChart"!==t&&"ComposedChart"!==t)return null;var{height:A,width:B,x:C,y:D}=z;return w&&w.length?sY.createElement(tj,s9({},r,{activeDot:d,animationBegin:e,animationDuration:f,animationEasing:g,baseLine:y,connectNulls:h,dot:i,fill:j,fillOpacity:k,height:A,hide:l,layout:s,isAnimationActive:m,isRange:x,legendType:n,needClip:u,points:w,stroke:o,width:B,left:C,top:D,xAxisId:p,yAxisId:q})):null}function tm(a){var b=mQ(a,tk),c=cO();return sY.createElement(o5,{id:b.id,type:"area"},a=>sY.createElement(sY.Fragment,null,sY.createElement(po,{legendPayload:(a=>{var{dataKey:b,name:c,stroke:d,fill:e,legendType:f,hide:g}=a;return[{inactive:g,dataKey:b,type:f,color:ta(d,e),value:cz(c,b),payload:a}]})(b)}),sY.createElement(o2,{fn:tb,args:b}),sY.createElement(pr,{type:"area",id:a,data:b.data,dataKey:b.dataKey,xAxisId:b.xAxisId,yAxisId:b.yAxisId,zAxisId:0,stackId:cs(b.stackId),hide:b.hide,barSize:void 0,baseValue:b.baseValue,isPanorama:c,connectNulls:b.connectNulls}),sY.createElement(tl,s9({},b,{id:a}))))}tm.displayName="Area";var tn=["axis"],to=(0,f.forwardRef)((a,b)=>f.createElement(mU,{chartName:"AreaChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:tn,tooltipPayloadSearcher:bp,categoricalChartProps:a,ref:b})),tp=a.i(24669),tq=a.i(39761),tr=a.i(70106);let ts=(0,tr.default)("chart-pie",[["path",{d:"M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z",key:"pzmjnu"}],["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}]]),tt=(0,tr.default)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]);var tu=a.i(84391);let tv=["#0088FE","#00C49F","#FFBB28","#FF8042","#8884D8"];function tw(){let{data:a,isLoading:b,error:f}=(0,d.useQuery)({queryKey:["alert-stats"],queryFn:()=>tu.apiService.getAlertStats(),refetchInterval:6e4});if(b)return(0,c.jsx)("div",{className:"grid gap-4 md:grid-cols-2",children:[void 0,void 0,void 0,void 0].map((a,b)=>(0,c.jsxs)(e.Card,{children:[(0,c.jsx)(e.CardHeader,{children:(0,c.jsx)("div",{className:"h-6 bg-muted rounded w-1/3 animate-pulse"})}),(0,c.jsx)(e.CardContent,{children:(0,c.jsx)("div",{className:"h-64 bg-muted rounded animate-pulse"})})]},b))});if(f||!a)return(0,c.jsx)(e.Card,{children:(0,c.jsx)(e.CardContent,{className:"flex items-center justify-center h-64",children:(0,c.jsxs)("div",{className:"text-center",children:[(0,c.jsx)(tq.BarChart3,{className:"h-8 w-8 text-muted-foreground mx-auto mb-2"}),(0,c.jsx)("p",{className:"text-sm text-muted-foreground",children:"Failed to load analytics data"})]})})});let g=Object.entries(a.detection_counts||{}).map(([a,b])=>({name:a.charAt(0).toUpperCase()+a.slice(1),value:b})),h=a.hourly_distribution||[],i=a.daily_distribution||[];return(0,c.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,c.jsxs)(e.Card,{children:[(0,c.jsx)(e.CardHeader,{children:(0,c.jsxs)(e.CardTitle,{className:"flex items-center gap-2",children:[(0,c.jsx)(ts,{className:"h-5 w-5"}),"Detection Types"]})}),(0,c.jsx)(e.CardContent,{children:(0,c.jsx)(rJ,{width:"100%",height:300,children:(0,c.jsxs)(rR,{children:[(0,c.jsx)(sp,{data:g,cx:"50%",cy:"50%",labelLine:!1,label:({name:a,percent:b})=>`${a} ${(100*(b||0)).toFixed(0)}%`,outerRadius:80,fill:"#8884d8",dataKey:"value",children:g.map((a,b)=>(0,c.jsx)(m_,{fill:tv[b%tv.length]},`cell-${b}`))}),(0,c.jsx)(rF,{})]})})})]}),(0,c.jsxs)(e.Card,{children:[(0,c.jsx)(e.CardHeader,{children:(0,c.jsxs)(e.CardTitle,{className:"flex items-center gap-2",children:[(0,c.jsx)(tq.BarChart3,{className:"h-5 w-5"}),"Hourly Distribution"]})}),(0,c.jsx)(e.CardContent,{children:(0,c.jsx)(rJ,{width:"100%",height:300,children:(0,c.jsxs)(mW,{data:h,children:[(0,c.jsx)(qG,{strokeDasharray:"3 3"}),(0,c.jsx)(qe,{dataKey:"hour",tickFormatter:a=>`${a}:00`}),(0,c.jsx)(qn,{}),(0,c.jsx)(rF,{labelFormatter:a=>`${a}:00`,formatter:a=>[a,"Alerts"]}),(0,c.jsx)(pP,{dataKey:"count",fill:"#0088FE"})]})})})]}),(0,c.jsxs)(e.Card,{children:[(0,c.jsx)(e.CardHeader,{children:(0,c.jsxs)(e.CardTitle,{className:"flex items-center gap-2",children:[(0,c.jsx)(tp.TrendingUp,{className:"h-5 w-5"}),"Daily Trend"]})}),(0,c.jsx)(e.CardContent,{children:(0,c.jsx)(rJ,{width:"100%",height:300,children:(0,c.jsxs)(sr,{data:i,children:[(0,c.jsx)(qG,{strokeDasharray:"3 3"}),(0,c.jsx)(qe,{dataKey:"date",tickFormatter:a=>new Date(a).toLocaleDateString()}),(0,c.jsx)(qn,{}),(0,c.jsx)(rF,{labelFormatter:a=>new Date(a).toLocaleDateString(),formatter:a=>[a,"Alerts"]}),(0,c.jsx)(sX,{type:"monotone",dataKey:"count",stroke:"#00C49F",strokeWidth:2,dot:{fill:"#00C49F"}})]})})})]}),(0,c.jsxs)(e.Card,{children:[(0,c.jsx)(e.CardHeader,{children:(0,c.jsxs)(e.CardTitle,{className:"flex items-center gap-2",children:[(0,c.jsx)(tt,{className:"h-5 w-5"}),"Activity Overview"]})}),(0,c.jsx)(e.CardContent,{children:(0,c.jsx)(rJ,{width:"100%",height:300,children:(0,c.jsxs)(to,{data:i,children:[(0,c.jsx)(qG,{strokeDasharray:"3 3"}),(0,c.jsx)(qe,{dataKey:"date",tickFormatter:a=>new Date(a).toLocaleDateString()}),(0,c.jsx)(qn,{}),(0,c.jsx)(rF,{labelFormatter:a=>new Date(a).toLocaleDateString(),formatter:a=>[a,"Alerts"]}),(0,c.jsx)(tm,{type:"monotone",dataKey:"count",stroke:"#FFBB28",fill:"#FFBB28",fillOpacity:.3})]})})})]})]})}}];

//# sourceMappingURL=_ac34f415._.js.map