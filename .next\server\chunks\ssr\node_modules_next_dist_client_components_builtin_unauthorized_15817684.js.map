{"version": 3, "sources": ["turbopack:///[project]/node_modules/next/src/client/components/builtin/unauthorized.tsx"], "sourcesContent": ["import { HTTPAccessErrorFallback } from '../http-access-fallback/error-fallback'\n\nexport default function Unauthorized() {\n  return (\n    <HTTPAccessErrorFallback\n      status={401}\n      message=\"You're not authorized to access this page.\"\n    />\n  )\n}\n"], "names": ["Unauthorized", "HTTPAccessErrorFallback", "status", "message"], "mappings": "sHAEA,UAAA,qCAAwBA,yBAFgB,CAAA,CAAA,IAAA,GAEzB,SAASA,IACtB,MACE,CADF,AACE,EAAA,EAAA,GAAA,EAACC,EADH,AACGA,uBAAuB,CAAA,CACtBC,OAAQ,IACRC,QAAQ,8CAGd", "ignoreList": [0]}