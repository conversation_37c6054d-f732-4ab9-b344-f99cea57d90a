module.exports=[18622,(a,b,c)=>{b.exports=a.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},42602,(a,b,c)=>{"use strict";b.exports=a.r(18622)},87924,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored["react-ssr"].ReactJsxRuntime},72131,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored["react-ssr"].React},35112,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored["react-ssr"].ReactDOM},42871,a=>{"use strict";a.s(["addToEnd",()=>t,"addToStart",()=>u,"ensureQueryFn",()=>w,"functionalUpdate",()=>d,"hashKey",()=>l,"hashQueryKeyByOptions",()=>k,"isServer",()=>b,"isValidTimeout",()=>e,"matchMutation",()=>j,"matchQuery",()=>i,"noop",()=>c,"partialMatchKey",()=>m,"replaceData",()=>s,"resolveEnabled",()=>h,"resolveStaleTime",()=>g,"shallowEqualObjects",()=>n,"shouldThrowError",()=>x,"skipToken",()=>v,"sleep",()=>r,"timeUntilStale",()=>f]);var b=!0;function c(){}function d(a,b){return"function"==typeof a?a(b):a}function e(a){return"number"==typeof a&&a>=0&&a!==1/0}function f(a,b){return Math.max(a+(b||0)-Date.now(),0)}function g(a,b){return"function"==typeof a?a(b):a}function h(a,b){return"function"==typeof a?a(b):a}function i(a,b){let{type:c="all",exact:d,fetchStatus:e,predicate:f,queryKey:g,stale:h}=a;if(g){if(d){if(b.queryHash!==k(g,b.options))return!1}else if(!m(b.queryKey,g))return!1}if("all"!==c){let a=b.isActive();if("active"===c&&!a||"inactive"===c&&a)return!1}return("boolean"!=typeof h||b.isStale()===h)&&(!e||e===b.state.fetchStatus)&&(!f||!!f(b))}function j(a,b){let{exact:c,status:d,predicate:e,mutationKey:f}=a;if(f){if(!b.options.mutationKey)return!1;if(c){if(l(b.options.mutationKey)!==l(f))return!1}else if(!m(b.options.mutationKey,f))return!1}return(!d||b.state.status===d)&&(!e||!!e(b))}function k(a,b){return(b?.queryKeyHashFn||l)(a)}function l(a){return JSON.stringify(a,(a,b)=>p(b)?Object.keys(b).sort().reduce((a,c)=>(a[c]=b[c],a),{}):b)}function m(a,b){return a===b||typeof a==typeof b&&!!a&&!!b&&"object"==typeof a&&"object"==typeof b&&Object.keys(b).every(c=>m(a[c],b[c]))}function n(a,b){if(!b||Object.keys(a).length!==Object.keys(b).length)return!1;for(let c in a)if(a[c]!==b[c])return!1;return!0}function o(a){return Array.isArray(a)&&a.length===Object.keys(a).length}function p(a){if(!q(a))return!1;let b=a.constructor;if(void 0===b)return!0;let c=b.prototype;return!!q(c)&&!!c.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(a)===Object.prototype}function q(a){return"[object Object]"===Object.prototype.toString.call(a)}function r(a){return new Promise(b=>{setTimeout(b,a)})}function s(a,b,c){return"function"==typeof c.structuralSharing?c.structuralSharing(a,b):!1!==c.structuralSharing?function a(b,c){if(b===c)return b;let d=o(b)&&o(c);if(d||p(b)&&p(c)){let e=d?b:Object.keys(b),f=e.length,g=d?c:Object.keys(c),h=g.length,i=d?[]:{},j=new Set(e),k=0;for(let e=0;e<h;e++){let f=d?e:g[e];(!d&&j.has(f)||d)&&void 0===b[f]&&void 0===c[f]?(i[f]=void 0,k++):(i[f]=a(b[f],c[f]),i[f]===b[f]&&void 0!==b[f]&&k++)}return f===h&&k===f?b:i}return c}(a,b):b}function t(a,b,c=0){let d=[...a,b];return c&&d.length>c?d.slice(1):d}function u(a,b,c=0){let d=[b,...a];return c&&d.length>c?d.slice(0,-1):d}var v=Symbol();function w(a,b){return!a.queryFn&&b?.initialPromise?()=>b.initialPromise:a.queryFn&&a.queryFn!==v?a.queryFn:()=>Promise.reject(Error(`Missing queryFn: '${a.queryHash}'`))}function x(a,b){return"function"==typeof a?a(...b):!!a}},18544,a=>{"use strict";a.s(["notifyManager",()=>c]);var b=a=>setTimeout(a,0),c=function(){let a=[],c=0,d=a=>{a()},e=a=>{a()},f=b,g=b=>{c?a.push(b):f(()=>{d(b)})};return{batch:b=>{let g;c++;try{g=b()}finally{--c||(()=>{let b=a;a=[],b.length&&f(()=>{e(()=>{b.forEach(a=>{d(a)})})})})()}return g},batchCalls:a=>(...b)=>{g(()=>{a(...b)})},schedule:g,setNotifyFunction:a=>{d=a},setBatchNotifyFunction:a=>{e=a},setScheduler:a=>{f=a}}}()},33791,a=>{"use strict";a.s(["Subscribable",()=>b]);var b=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(a){return this.listeners.add(a),this.onSubscribe(),()=>{this.listeners.delete(a),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}}},99745,a=>{"use strict";a.s(["focusManager",()=>d]);var b=a.i(33791),c=a.i(42871),d=new class extends b.Subscribable{#a;#b;#c;constructor(){super(),this.#c=a=>{if(!c.isServer&&window.addEventListener){let b=()=>a();return window.addEventListener("visibilitychange",b,!1),()=>{window.removeEventListener("visibilitychange",b)}}}}onSubscribe(){this.#b||this.setEventListener(this.#c)}onUnsubscribe(){this.hasListeners()||(this.#b?.(),this.#b=void 0)}setEventListener(a){this.#c=a,this.#b?.(),this.#b=a(a=>{"boolean"==typeof a?this.setFocused(a):this.onFocus()})}setFocused(a){this.#a!==a&&(this.#a=a,this.onFocus())}onFocus(){let a=this.isFocused();this.listeners.forEach(b=>{b(a)})}isFocused(){return"boolean"==typeof this.#a?this.#a:globalThis.document?.visibilityState!=="hidden"}}},76644,12552,79715,21778,85659,a=>{"use strict";a.s(["Query",()=>m,"fetchState",()=>n],76644);var b=a.i(42871),c=a.i(18544);a.s(["CancelledError",()=>j,"canFetch",()=>i,"createRetryer",()=>k],21778);var d=a.i(99745);a.s(["onlineManager",()=>f],12552);var e=a.i(33791),f=new class extends e.Subscribable{#d=!0;#b;#c;constructor(){super(),this.#c=a=>{if(!b.isServer&&window.addEventListener){let b=()=>a(!0),c=()=>a(!1);return window.addEventListener("online",b,!1),window.addEventListener("offline",c,!1),()=>{window.removeEventListener("online",b),window.removeEventListener("offline",c)}}}}onSubscribe(){this.#b||this.setEventListener(this.#c)}onUnsubscribe(){this.hasListeners()||(this.#b?.(),this.#b=void 0)}setEventListener(a){this.#c=a,this.#b?.(),this.#b=a(this.setOnline.bind(this))}setOnline(a){this.#d!==a&&(this.#d=a,this.listeners.forEach(b=>{b(a)}))}isOnline(){return this.#d}};function g(){let a,b,c=new Promise((c,d)=>{a=c,b=d});function d(a){Object.assign(c,a),delete c.resolve,delete c.reject}return c.status="pending",c.catch(()=>{}),c.resolve=b=>{d({status:"fulfilled",value:b}),a(b)},c.reject=a=>{d({status:"rejected",reason:a}),b(a)},c}function h(a){return Math.min(1e3*2**a,3e4)}function i(a){return(a??"online")!=="online"||f.isOnline()}a.s(["pendingThenable",()=>g],79715);var j=class extends Error{constructor(a){super("CancelledError"),this.revert=a?.revert,this.silent=a?.silent}};function k(a){let c,e=!1,k=0,l=g(),m=()=>d.focusManager.isFocused()&&("always"===a.networkMode||f.isOnline())&&a.canRun(),n=()=>i(a.networkMode)&&a.canRun(),o=a=>{"pending"===l.status&&(c?.(),l.resolve(a))},p=a=>{"pending"===l.status&&(c?.(),l.reject(a))},q=()=>new Promise(b=>{c=a=>{("pending"!==l.status||m())&&b(a)},a.onPause?.()}).then(()=>{c=void 0,"pending"===l.status&&a.onContinue?.()}),r=()=>{let c;if("pending"!==l.status)return;let d=0===k?a.initialPromise:void 0;try{c=d??a.fn()}catch(a){c=Promise.reject(a)}Promise.resolve(c).then(o).catch(c=>{if("pending"!==l.status)return;let d=a.retry??3*!b.isServer,f=a.retryDelay??h,g="function"==typeof f?f(k,c):f,i=!0===d||"number"==typeof d&&k<d||"function"==typeof d&&d(k,c);if(e||!i)return void p(c);k++,a.onFail?.(k,c),(0,b.sleep)(g).then(()=>m()?void 0:q()).then(()=>{e?p(c):r()})})};return{promise:l,status:()=>l.status,cancel:b=>{"pending"===l.status&&(p(new j(b)),a.abort?.())},continue:()=>(c?.(),l),cancelRetry:()=>{e=!0},continueRetry:()=>{e=!1},canStart:n,start:()=>(n()?r():q().then(r),l)}}a.s(["Removable",()=>l],85659);var l=class{#e;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),(0,b.isValidTimeout)(this.gcTime)&&(this.#e=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(a){this.gcTime=Math.max(this.gcTime||0,a??(b.isServer?1/0:3e5))}clearGcTimeout(){this.#e&&(clearTimeout(this.#e),this.#e=void 0)}},m=class extends l{#f;#g;#h;#i;#j;#k;#l;constructor(a){super(),this.#l=!1,this.#k=a.defaultOptions,this.setOptions(a.options),this.observers=[],this.#i=a.client,this.#h=this.#i.getQueryCache(),this.queryKey=a.queryKey,this.queryHash=a.queryHash,this.#f=function(a){let b="function"==typeof a.initialData?a.initialData():a.initialData,c=void 0!==b,d=c?"function"==typeof a.initialDataUpdatedAt?a.initialDataUpdatedAt():a.initialDataUpdatedAt:0;return{data:b,dataUpdateCount:0,dataUpdatedAt:c?d??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:c?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=a.state??this.#f,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#j?.promise}setOptions(a){this.options={...this.#k,...a},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#h.remove(this)}setData(a,c){let d=(0,b.replaceData)(this.state.data,a,this.options);return this.#m({data:d,type:"success",dataUpdatedAt:c?.updatedAt,manual:c?.manual}),d}setState(a,b){this.#m({type:"setState",state:a,setStateOptions:b})}cancel(a){let c=this.#j?.promise;return this.#j?.cancel(a),c?c.then(b.noop).catch(b.noop):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#f)}isActive(){return this.observers.some(a=>!1!==(0,b.resolveEnabled)(a.options.enabled,this))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===b.skipToken||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0&&this.observers.some(a=>"static"===(0,b.resolveStaleTime)(a.options.staleTime,this))}isStale(){return this.getObserversCount()>0?this.observers.some(a=>a.getCurrentResult().isStale):void 0===this.state.data||this.state.isInvalidated}isStaleByTime(a=0){return void 0===this.state.data||"static"!==a&&(!!this.state.isInvalidated||!(0,b.timeUntilStale)(this.state.dataUpdatedAt,a))}onFocus(){let a=this.observers.find(a=>a.shouldFetchOnWindowFocus());a?.refetch({cancelRefetch:!1}),this.#j?.continue()}onOnline(){let a=this.observers.find(a=>a.shouldFetchOnReconnect());a?.refetch({cancelRefetch:!1}),this.#j?.continue()}addObserver(a){this.observers.includes(a)||(this.observers.push(a),this.clearGcTimeout(),this.#h.notify({type:"observerAdded",query:this,observer:a}))}removeObserver(a){this.observers.includes(a)&&(this.observers=this.observers.filter(b=>b!==a),this.observers.length||(this.#j&&(this.#l?this.#j.cancel({revert:!0}):this.#j.cancelRetry()),this.scheduleGc()),this.#h.notify({type:"observerRemoved",query:this,observer:a}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#m({type:"invalidate"})}async fetch(a,c){if("idle"!==this.state.fetchStatus&&this.#j?.status()!=="rejected"){if(void 0!==this.state.data&&c?.cancelRefetch)this.cancel({silent:!0});else if(this.#j)return this.#j.continueRetry(),this.#j.promise}if(a&&this.setOptions(a),!this.options.queryFn){let a=this.observers.find(a=>a.options.queryFn);a&&this.setOptions(a.options)}let d=new AbortController,e=a=>{Object.defineProperty(a,"signal",{enumerable:!0,get:()=>(this.#l=!0,d.signal)})},f=()=>{let a=(0,b.ensureQueryFn)(this.options,c),d=(()=>{let a={client:this.#i,queryKey:this.queryKey,meta:this.meta};return e(a),a})();return(this.#l=!1,this.options.persister)?this.options.persister(a,d,this):a(d)},g=(()=>{let a={fetchOptions:c,options:this.options,queryKey:this.queryKey,client:this.#i,state:this.state,fetchFn:f};return e(a),a})();this.options.behavior?.onFetch(g,this),this.#g=this.state,("idle"===this.state.fetchStatus||this.state.fetchMeta!==g.fetchOptions?.meta)&&this.#m({type:"fetch",meta:g.fetchOptions?.meta}),this.#j=k({initialPromise:c?.initialPromise,fn:g.fetchFn,abort:d.abort.bind(d),onFail:(a,b)=>{this.#m({type:"failed",failureCount:a,error:b})},onPause:()=>{this.#m({type:"pause"})},onContinue:()=>{this.#m({type:"continue"})},retry:g.options.retry,retryDelay:g.options.retryDelay,networkMode:g.options.networkMode,canRun:()=>!0});try{let a=await this.#j.start();if(void 0===a)throw Error(`${this.queryHash} data is undefined`);return this.setData(a),this.#h.config.onSuccess?.(a,this),this.#h.config.onSettled?.(a,this.state.error,this),a}catch(a){if(a instanceof j){if(a.silent)return this.#j.promise;else if(a.revert){if(this.setState({...this.#g,fetchStatus:"idle"}),void 0===this.state.data)throw a;return this.state.data}}throw this.#m({type:"error",error:a}),this.#h.config.onError?.(a,this),this.#h.config.onSettled?.(this.state.data,a,this),a}finally{this.scheduleGc()}}#m(a){let b=b=>{switch(a.type){case"failed":return{...b,fetchFailureCount:a.failureCount,fetchFailureReason:a.error};case"pause":return{...b,fetchStatus:"paused"};case"continue":return{...b,fetchStatus:"fetching"};case"fetch":return{...b,...n(b.data,this.options),fetchMeta:a.meta??null};case"success":let c={...b,data:a.data,dataUpdateCount:b.dataUpdateCount+1,dataUpdatedAt:a.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!a.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};return this.#g=a.manual?c:void 0,c;case"error":let d=a.error;return{...b,error:d,errorUpdateCount:b.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:b.fetchFailureCount+1,fetchFailureReason:d,fetchStatus:"idle",status:"error"};case"invalidate":return{...b,isInvalidated:!0};case"setState":return{...b,...a.state}}};this.state=b(this.state),c.notifyManager.batch(()=>{this.observers.forEach(a=>{a.onQueryUpdate()}),this.#h.notify({query:this,type:"updated",action:a})})}};function n(a,b){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:i(b.networkMode)?"fetching":"paused",...void 0===a&&{error:null,status:"pending"}}}},12794,a=>{"use strict";a.s(["Mutation",()=>e,"getDefaultState",()=>f]);var b=a.i(18544),c=a.i(85659),d=a.i(21778),e=class extends c.Removable{#n;#o;#j;constructor(a){super(),this.mutationId=a.mutationId,this.#o=a.mutationCache,this.#n=[],this.state=a.state||f(),this.setOptions(a.options),this.scheduleGc()}setOptions(a){this.options=a,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(a){this.#n.includes(a)||(this.#n.push(a),this.clearGcTimeout(),this.#o.notify({type:"observerAdded",mutation:this,observer:a}))}removeObserver(a){this.#n=this.#n.filter(b=>b!==a),this.scheduleGc(),this.#o.notify({type:"observerRemoved",mutation:this,observer:a})}optionalRemove(){this.#n.length||("pending"===this.state.status?this.scheduleGc():this.#o.remove(this))}continue(){return this.#j?.continue()??this.execute(this.state.variables)}async execute(a){let b=()=>{this.#m({type:"continue"})};this.#j=(0,d.createRetryer)({fn:()=>this.options.mutationFn?this.options.mutationFn(a):Promise.reject(Error("No mutationFn found")),onFail:(a,b)=>{this.#m({type:"failed",failureCount:a,error:b})},onPause:()=>{this.#m({type:"pause"})},onContinue:b,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#o.canRun(this)});let c="pending"===this.state.status,e=!this.#j.canStart();try{if(c)b();else{this.#m({type:"pending",variables:a,isPaused:e}),await this.#o.config.onMutate?.(a,this);let b=await this.options.onMutate?.(a);b!==this.state.context&&this.#m({type:"pending",context:b,variables:a,isPaused:e})}let d=await this.#j.start();return await this.#o.config.onSuccess?.(d,a,this.state.context,this),await this.options.onSuccess?.(d,a,this.state.context),await this.#o.config.onSettled?.(d,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(d,null,a,this.state.context),this.#m({type:"success",data:d}),d}catch(b){try{throw await this.#o.config.onError?.(b,a,this.state.context,this),await this.options.onError?.(b,a,this.state.context),await this.#o.config.onSettled?.(void 0,b,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,b,a,this.state.context),b}finally{this.#m({type:"error",error:b})}}finally{this.#o.runNext(this)}}#m(a){this.state=(b=>{switch(a.type){case"failed":return{...b,failureCount:a.failureCount,failureReason:a.error};case"pause":return{...b,isPaused:!0};case"continue":return{...b,isPaused:!1};case"pending":return{...b,context:a.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:a.isPaused,status:"pending",variables:a.variables,submittedAt:Date.now()};case"success":return{...b,data:a.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...b,data:void 0,error:a.error,failureCount:b.failureCount+1,failureReason:a.error,isPaused:!1,status:"error"}}})(this.state),b.notifyManager.batch(()=>{this.#n.forEach(b=>{b.onMutationUpdate(a)}),this.#o.notify({mutation:this,type:"updated",action:a})})}};function f(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__115ea36a._.js.map