[{"D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\app\\alerts\\page.tsx": "1", "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\app\\analytics\\page.tsx": "2", "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\app\\layout.tsx": "3", "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\app\\page.tsx": "4", "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\app\\providers.tsx": "5", "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\app\\settings\\page.tsx": "6", "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\components\\AlertsTable.tsx": "7", "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\components\\AnalyticsCharts.tsx": "8", "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\components\\CameraFeed.tsx": "9", "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\components\\DashboardOverview.tsx": "10", "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\components\\HealthCheck.tsx": "11", "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\components\\Layout.tsx": "12", "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\components\\Navigation.tsx": "13", "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\components\\RecentAlerts.tsx": "14", "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\components\\ui\\alert.tsx": "15", "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\components\\ui\\badge.tsx": "16", "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\components\\ui\\button.tsx": "17", "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\components\\ui\\card.tsx": "18", "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\components\\ui\\sonner.tsx": "19", "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\components\\ui\\table.tsx": "20", "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\components\\WebSocketProvider.tsx": "21", "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\hooks\\useApi.ts": "22", "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\hooks\\useWebSocket.ts": "23", "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\lib\\utils.ts": "24", "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\services\\api.ts": "25", "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\types\\index.ts": "26"}, {"size": 770, "mtime": 1755714675722, "results": "27", "hashOfConfig": "28"}, {"size": 503, "mtime": 1755714681493, "results": "29", "hashOfConfig": "28"}, {"size": 926, "mtime": 1755714466111, "results": "30", "hashOfConfig": "28"}, {"size": 509, "mtime": 1755714514955, "results": "31", "hashOfConfig": "28"}, {"size": 792, "mtime": 1755714760238, "results": "32", "hashOfConfig": "28"}, {"size": 4070, "mtime": 1755715296758, "results": "33", "hashOfConfig": "28"}, {"size": 8991, "mtime": 1755715220905, "results": "34", "hashOfConfig": "28"}, {"size": 6167, "mtime": 1755715014562, "results": "35", "hashOfConfig": "28"}, {"size": 6737, "mtime": 1755715286742, "results": "36", "hashOfConfig": "28"}, {"size": 4663, "mtime": 1755715174626, "results": "37", "hashOfConfig": "28"}, {"size": 4656, "mtime": 1755714563310, "results": "38", "hashOfConfig": "28"}, {"size": 368, "mtime": 1755714455462, "results": "39", "hashOfConfig": "28"}, {"size": 4952, "mtime": 1755714812577, "results": "40", "hashOfConfig": "28"}, {"size": 3588, "mtime": 1755714581488, "results": "41", "hashOfConfig": "28"}, {"size": 1614, "mtime": 1755714284599, "results": "42", "hashOfConfig": "28"}, {"size": 1631, "mtime": 1755714283780, "results": "43", "hashOfConfig": "28"}, {"size": 2123, "mtime": 1755714282370, "results": "44", "hashOfConfig": "28"}, {"size": 1989, "mtime": 1755714282418, "results": "45", "hashOfConfig": "28"}, {"size": 564, "mtime": 1755714284702, "results": "46", "hashOfConfig": "28"}, {"size": 2448, "mtime": 1755714282807, "results": "47", "hashOfConfig": "28"}, {"size": 859, "mtime": 1755714749132, "results": "48", "hashOfConfig": "28"}, {"size": 1481, "mtime": 1755714717005, "results": "49", "hashOfConfig": "28"}, {"size": 5223, "mtime": 1755715388755, "results": "50", "hashOfConfig": "28"}, {"size": 166, "mtime": 1755714120813, "results": "51", "hashOfConfig": "28"}, {"size": 1809, "mtime": 1755715307625, "results": "52", "hashOfConfig": "28"}, {"size": 1356, "mtime": 1755715125137, "results": "53", "hashOfConfig": "28"}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "kn0351", {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\app\\alerts\\page.tsx", [], [], "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\app\\analytics\\page.tsx", [], [], "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\app\\layout.tsx", [], [], "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\app\\page.tsx", [], [], "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\app\\providers.tsx", [], [], "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\app\\settings\\page.tsx", [], [], "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\components\\AlertsTable.tsx", ["132"], [], "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\components\\AnalyticsCharts.tsx", [], [], "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\components\\CameraFeed.tsx", [], [], "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\components\\DashboardOverview.tsx", [], [], "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\components\\HealthCheck.tsx", [], [], "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\components\\Layout.tsx", [], [], "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\components\\Navigation.tsx", [], [], "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\components\\RecentAlerts.tsx", [], [], "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\components\\ui\\alert.tsx", [], [], "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\components\\ui\\badge.tsx", [], [], "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\components\\ui\\button.tsx", [], [], "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\components\\ui\\card.tsx", [], [], "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\components\\ui\\sonner.tsx", [], [], "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\components\\ui\\table.tsx", [], [], "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\components\\WebSocketProvider.tsx", [], [], "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\hooks\\useApi.ts", [], [], "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\hooks\\useWebSocket.ts", ["133"], ["134"], "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\lib\\utils.ts", [], [], "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\services\\api.ts", [], [], "D:\\GitHub\\zen\\Wildlife-Anomaly-Detection-using-YOLOv5l\\forntend\\src\\types\\index.ts", [], [], {"ruleId": "135", "severity": 1, "message": "136", "line": 3, "column": 10, "nodeType": null, "messageId": "137", "endLine": 3, "endColumn": 18}, {"ruleId": "135", "severity": 1, "message": "138", "line": 156, "column": 31, "nodeType": null, "messageId": "137", "endLine": 156, "endColumn": 35}, {"ruleId": "139", "severity": 1, "message": "140", "line": 178, "column": 6, "nodeType": "141", "endLine": 178, "endColumn": 8, "suggestions": "142", "suppressions": "143"}, "@typescript-eslint/no-unused-vars", "'useState' is defined but never used.", "unusedVar", "'data' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'connect'. Either include it or remove the dependency array.", "ArrayExpression", ["144"], ["145"], {"desc": "146", "fix": "147"}, {"kind": "148", "justification": "149"}, "Update the dependencies array to be: [connect]", {"range": "150", "text": "151"}, "directive", "", [5129, 5131], "[connect]"]