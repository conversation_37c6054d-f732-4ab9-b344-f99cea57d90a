module.exports=[89578,a=>{a.v({className:"geist_a71539c9-module__T19VSG__className",variable:"geist_a71539c9-module__T19VSG__variable"})},35214,a=>{a.v({className:"geist_mono_8d43a2aa-module__8Li5zG__className",variable:"geist_mono_8d43a2aa-module__8Li5zG__variable"})},23115,a=>{"use strict";a.s(["Providers",()=>b]);let b=(0,a.i(11857).registerClientReference)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/app/providers.tsx <module evaluation>","Providers")},36459,a=>{"use strict";a.s(["Providers",()=>b]);let b=(0,a.i(11857).registerClientReference)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/app/providers.tsx","Providers")},62752,a=>{"use strict";a.i(23115);var b=a.i(36459);a.n(b)},96988,a=>{"use strict";a.s(["Toaster",()=>b]);let b=(0,a.i(11857).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/ui/sonner.tsx <module evaluation>","Toaster")},24257,a=>{"use strict";a.s(["Toaster",()=>b]);let b=(0,a.i(11857).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/components/ui/sonner.tsx","Toaster")},67807,a=>{"use strict";a.i(96988);var b=a.i(24257);a.n(b)},27572,a=>{"use strict";a.s(["default",()=>j,"metadata",()=>i],27572);var b=a.i(7997),c=a.i(89578);let d={className:c.default.className,style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"}};null!=c.default.variable&&(d.variable=c.default.variable);var e=a.i(35214);let f={className:e.default.className,style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"}};null!=e.default.variable&&(f.variable=e.default.variable);var g=a.i(62752),h=a.i(67807);let i={title:"Wildlife Guardian - Animal Poaching Alert System",description:"Real-time wildlife monitoring and poaching detection system"};function j({children:a}){return(0,b.jsx)("html",{lang:"en",className:"dark",children:(0,b.jsx)("body",{className:`${d.variable} ${f.variable} antialiased`,children:(0,b.jsxs)(g.Providers,{children:[a,(0,b.jsx)(h.Toaster,{})]})})})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__f7b8856f._.js.map